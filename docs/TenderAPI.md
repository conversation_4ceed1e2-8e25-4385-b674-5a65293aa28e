# Tender API Documentation

This document provides detailed information about the Tender API endpoints, including request parameters, response formats, and examples.

## Base URL

All API endpoints are relative to the base URL:

```
http://localhost:3001/api
```

## Authentication

Currently, the API does not require authentication. This may change in future versions.

## Response Format

All API responses follow a standard format:

```json
{
  "status": "success",
  "data": { ... },
  "timestamp": "2023-05-14T16:25:15.780Z",
  "requestId": "req-1684082715780-abc123"
}
```

For error responses:

```json
{
  "status": "error",
  "message": "Error message",
  "code": 400,
  "path": "/api/tenders",
  "timestamp": "2023-05-14T16:25:15.780Z",
  "requestId": "req-1684082715780-abc123"
}
```

## Endpoints

### Get All Tenders

Retrieves a list of tenders with optional filtering and pagination.

**URL**: `/tenders`

**Method**: `GET`

**Query Parameters**:

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| status | string | Filter by tender status (NEW, PROCESSING, REVIEWING, SUBMITTED, WON, LOST, CANCELLED) | - |
| category | string | Filter by tender category | - |
| minConfidence | number | Filter by minimum confidence score (0-1) | - |
| page | number | Page number for pagination | 1 |
| limit | number | Number of tenders per page (max 100) | 10 |
| sortBy | string | Field to sort by (relevanceScore, closingDate, publishDate, value, confidence) | relevanceScore |
| sortOrder | string | Sort order (asc, desc) | desc |
| search | string | Search term to filter tenders by title, description, or issuer | - |
| issuer | string | Filter by tender issuer | - |
| source | string | Filter by tender source | - |
| fromDate | string | Filter by publish date (ISO format) | - |
| toDate | string | Filter by publish date (ISO format) | - |

**Example Request**:

```
GET /api/tenders?status=NEW&category=Water%20Treatment&minConfidence=0.7&page=1&limit=10
```

**Example Response**:

```json
{
  "status": "success",
  "data": {
    "tenders": [
      {
        "id": "tender-123",
        "title": "Water Treatment System Installation",
        "description": "Installation of water treatment system for municipal water supply",
        "reference": "WT-2023-123",
        "issuer": "Department of Water Affairs",
        "publishDate": "2023-05-01T00:00:00.000Z",
        "closingDate": "2023-06-01T00:00:00.000Z",
        "status": "NEW",
        "url": "https://example.com/tenders/123",
        "value": 1500000,
        "category": "Water Treatment",
        "tags": ["municipal", "water", "treatment"],
        "confidence": 0.85,
        "metadata": {
          "location": "Gauteng",
          "contactPerson": "John Doe"
        },
        "documents": [
          {
            "title": "Tender Document",
            "url": "https://example.com/tenders/123/document"
          }
        ],
        "requirements": [
          "ISO 9001 Certification",
          "5 years experience in water treatment"
        ]
      }
      // More tenders...
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "totalCount": 45,
      "totalPages": 5,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  },
  "timestamp": "2023-05-14T16:25:15.780Z",
  "requestId": "req-1684082715780-abc123"
}
```

### Get Tender by ID

Retrieves a specific tender by its ID.

**URL**: `/tenders/:id`

**Method**: `GET`

**URL Parameters**:

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Tender ID |

**Example Request**:

```
GET /api/tenders/tender-123
```

**Example Response**:

```json
{
  "status": "success",
  "data": {
    "id": "tender-123",
    "title": "Water Treatment System Installation",
    "description": "Installation of water treatment system for municipal water supply",
    "reference": "WT-2023-123",
    "issuer": "Department of Water Affairs",
    "publishDate": "2023-05-01T00:00:00.000Z",
    "closingDate": "2023-06-01T00:00:00.000Z",
    "status": "NEW",
    "url": "https://example.com/tenders/123",
    "value": 1500000,
    "category": "Water Treatment",
    "tags": ["municipal", "water", "treatment"],
    "confidence": 0.85,
    "metadata": {
      "location": "Gauteng",
      "contactPerson": "John Doe"
    },
    "documents": [
      {
        "title": "Tender Document",
        "url": "https://example.com/tenders/123/document"
      }
    ],
    "requirements": [
      "ISO 9001 Certification",
      "5 years experience in water treatment"
    ]
  },
  "timestamp": "2023-05-14T16:25:15.780Z",
  "requestId": "req-1684082715780-abc123"
}
```

### Update Tender Status

Updates the status of a specific tender.

**URL**: `/tenders/:id/status`

**Method**: `PATCH`

**URL Parameters**:

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Tender ID |

**Request Body**:

```json
{
  "status": "PROCESSING"
}
```

**Example Request**:

```
PATCH /api/tenders/tender-123/status
```

**Example Response**:

```json
{
  "status": "success",
  "data": {
    "message": "Tender status updated to PROCESSING"
  },
  "timestamp": "2023-05-14T16:25:15.780Z",
  "requestId": "req-1684082715780-abc123"
}
```

### Get Tender Categories

Retrieves a list of all unique tender categories.

**URL**: `/tenders/categories`

**Method**: `GET`

**Example Request**:

```
GET /api/tenders/categories
```

**Example Response**:

```json
{
  "status": "success",
  "data": [
    "Water Treatment",
    "Construction",
    "IT Services",
    "Consulting",
    "Maintenance"
  ],
  "timestamp": "2023-05-14T16:25:15.780Z",
  "requestId": "req-1684082715780-abc123"
}
```

### Get Tender Issuers

Retrieves a list of all unique tender issuers.

**URL**: `/tenders/issuers`

**Method**: `GET`

**Example Request**:

```
GET /api/tenders/issuers
```

**Example Response**:

```json
{
  "status": "success",
  "data": [
    "Department of Water Affairs",
    "Eskom",
    "City of Johannesburg",
    "Department of Health"
  ],
  "timestamp": "2023-05-14T16:25:15.780Z",
  "requestId": "req-1684082715780-abc123"
}
```

### Get Tender Sources

Retrieves a list of all unique tender sources.

**URL**: `/tenders/sources`

**Method**: `GET`

**Example Request**:

```
GET /api/tenders/sources
```

**Example Response**:

```json
{
  "status": "success",
  "data": [
    "etenders.gov.za",
    "eskom.co.za",
    "joburg.org.za"
  ],
  "timestamp": "2023-05-14T16:25:15.780Z",
  "requestId": "req-1684082715780-abc123"
}
```

### Get Tender Statistics

Retrieves statistics about the tenders in the database.

**URL**: `/tenders/stats`

**Method**: `GET`

**Example Request**:

```
GET /api/tenders/stats
```

**Example Response**:

```json
{
  "status": "success",
  "data": {
    "totalCount": 45,
    "statusCounts": {
      "NEW": 20,
      "PROCESSING": 10,
      "REVIEWING": 5,
      "SUBMITTED": 5,
      "WON": 3,
      "LOST": 2,
      "CANCELLED": 0
    },
    "categoryCounts": {
      "Water Treatment": 15,
      "Construction": 10,
      "IT Services": 8,
      "Consulting": 7,
      "Maintenance": 5
    },
    "sourceCounts": {
      "etenders.gov.za": 25,
      "eskom.co.za": 15,
      "joburg.org.za": 5
    },
    "averageValue": 1250000,
    "upcomingClosingCount": 12
  },
  "timestamp": "2023-05-14T16:25:15.780Z",
  "requestId": "req-1684082715780-abc123"
}
```

### Refresh Tenders

Triggers a scraping process to refresh the tenders in the database.

**URL**: `/tenders/refresh`

**Method**: `POST`

**Example Request**:

```
POST /api/tenders/refresh
```

**Example Response**:

```json
{
  "status": "success",
  "data": {
    "message": "Successfully scraped 15 tenders",
    "count": 15
  },
  "timestamp": "2023-05-14T16:25:15.780Z",
  "requestId": "req-1684082715780-abc123"
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - The request was malformed or contained invalid parameters |
| 404 | Not Found - The requested resource was not found |
| 500 | Internal Server Error - An error occurred on the server |
