# Python 3.13 Compatibility Guide

This document explains how to run the Mexel scrapers with Python 3.13, addressing the removal of the `cgi` module from the standard library.

## The Issue

Python 3.13 removed the `cgi` module from the standard library, but Scrapy (and its dependency Twisted) still relies on it. This causes import errors when trying to run Scrapy-based scrapers with Python 3.13.

## Solution

We've implemented a compatibility layer that creates a minimal version of the `cgi` module with just enough functionality to satisfy Scrapy's requirements.

## How to Run Scrapers with Python 3.13

### Option 1: Using the Wrapper Scripts (Recommended)

We've created wrapper scripts that automatically install the compatibility layer before importing Scrapy:

1. For running test scrapers:

   ```bash
   python src/python/run_with_compat.py
   ```

2. For running production scrapers:
   ```bash
   python src/python/run_scrapers_wrapper.py
   ```

### Option 2: Run the Setup Script

We've created a setup script that configures the entire Python environment:

```bash
./setup_python_env.sh
```

This script will:

1. Create a virtual environment if it doesn't exist
2. Install all dependencies
3. Set up the CGI compatibility layer if Python 3.13 is detected
4. Create wrapper scripts

### Option 3: Manual Compatibility

If you need to create your own script that uses Scrapy, add this code at the beginning before importing any Scrapy modules:

```python
import sys
import types

if sys.version_info >= (3, 13):
    # Create a minimal cgi module
    cgi_module = types.ModuleType('cgi')
    cgi_module.__file__ = '<built-in>'

    # Add minimal functions
    def parse_header(line):
        if isinstance(line, bytes):
            line = line.decode('iso-8859-1')
        parts = line.split(';')
        key = parts[0].strip()
        pdict = {}
        for p in parts[1:]:
            if '=' in p:
                name, value = p.split('=', 1)
                name = name.strip()
                value = value.strip().strip('"')
                pdict[name] = value
        return key, pdict

    # Add minimal class
    class FieldStorage:
        def __init__(self, *args, **kwargs):
            self.list = []
            self.dict = {}

    # Add to module
    cgi_module.parse_header = parse_header
    cgi_module.FieldStorage = FieldStorage

    # Register the module
    sys.modules['cgi'] = cgi_module
```

## Future Maintenance

This is a temporary solution until Scrapy and Twisted update their dependencies to stop relying on the `cgi` module. In the future, we should:

1. Monitor for updates to Scrapy and Twisted
2. Test with each new Python version
3. Consider switching to alternative scraping libraries if needed

## Troubleshooting

If you encounter issues:

1. Verify your Python version: `python --version`
2. Check if the cgi module is available: `python -c "import cgi; print('Available')" || echo "Not available"`
3. Try running with the compatibility wrapper: `python src/python/run_with_compat.py`
4. Check the logs for specific import errors
