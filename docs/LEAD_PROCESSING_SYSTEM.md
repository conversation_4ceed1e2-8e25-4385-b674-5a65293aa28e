# Lead Processing System Documentation

This document provides a comprehensive overview of the lead processing system in Mexel, including its architecture, workflows, and integration with real-time notifications.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Lead Processing Workflow](#lead-processing-workflow)
4. [Data Models](#data-models)
5. [AI Integration](#ai-integration)
6. [Real-time Notifications](#real-time-notifications)
7. [API Endpoints](#api-endpoints)
8. [Frontend Integration](#frontend-integration)
9. [Testing](#testing)
10. [Troubleshooting](#troubleshooting)

## Overview

The lead processing system is a core component of the Mexel platform that handles the automated analysis, enrichment, and routing of potential sales leads. It uses AI-powered analysis to evaluate leads, generate personalized outreach content, and integrate with the broader agent system.

Key features include:

- Lead intake through API endpoints
- AI-driven lead analysis and categorization
- Sector-specific profiling
- Automatic draft email generation
- Real-time processing notifications via Socket.IO
- Integration with the CRM database

## Architecture

The lead processing system consists of several interconnected components:

```
┌─────────────────┐        ┌────────────────┐        ┌─────────────────┐
│                 │        │                │        │                 │
│   API Endpoints │───────▶│ AgentWorkflow  │───────▶│  OllamaService  │
│  (server.ts)    │        │                │        │                 │
│                 │        └────────────────┘        └─────────────────┘
└─────────────────┘                │                          │
        │                          │                          │
        │                          ▼                          │
        │                 ┌────────────────┐                  │
        │                 │                │                  │
        │                 │   Core Logic   │◀─────────────────┘
        │                 │                │
        │                 └────────────────┘
        │                          │
        │                          │
        ▼                          ▼
┌─────────────────┐        ┌────────────────┐
│                 │        │                │
│  Socket.IO      │        │  Database      │
│  Notifications  │        │  (CRM)         │
│                 │        │                │
└─────────────────┘        └────────────────┘
```

## Lead Processing Workflow

### 1. Lead Intake

Leads enter the system through the `/api/v1/process-lead` endpoint, which accepts a JSON payload with the following structure:

```typescript
interface ThemedLeadProfile {
  id: string;
  fictionalCompanyName: string;
  contactPerson?: string;
  role?: string;
  sector: TargetSector | string;
  fictionalNeeds: string[];
  email?: string;
}
```

### 2. Validation

The system validates the incoming lead data, ensuring all required fields are present and correctly formatted. The validation includes:

- Checking for required fields (id, fictionalCompanyName, sector)
- Validating the sector value against the TargetSector enum
- Ensuring fictionalNeeds is a non-empty array of strings

### 3. Real-time Notification (Start)

As soon as a valid lead is received, the system emits a Socket.IO event to notify listeners that processing has started:

```typescript
io.emit("leadProcessing", {
  status: "started",
  leadId: leadData.id,
  companyName: leadData.fictionalCompanyName,
});
```

### 4. AI-Powered Analysis

The lead is passed to the `AgentWorkflow.processLead()` method, which:

1. Analyzes the lead using AI models via OllamaService
2. Generates a summary of potential value and alignment with Mexel's products
3. Creates a draft email tailored to the lead's sector and needs

### 5. Result Formation

The system collects the analysis results and forms a structured output:

```typescript
interface ProcessedLeadOutput {
  leadId: string;
  sector: TargetSector;
  analysisSummary: string;
  draftEmail: string;
}
```

### 6. Real-time Notification (Completion)

Upon successful processing, the system emits another Socket.IO event with the results:

```typescript
io.emit("leadProcessing", {
  status: "completed",
  leadId: leadData.id,
  companyName: leadData.fictionalCompanyName,
  result: {
    analysisSummary: processedOutput.analysisSummary,
    sector: TargetSector[processedOutput.sector],
  },
});
```

### 7. Error Handling

If an error occurs during processing, the system sends an error notification:

```typescript
io.emit("leadProcessing", {
  status: "error",
  leadId: leadData.id,
  companyName: leadData.fictionalCompanyName,
  error: errorMessage,
});
```

## Data Models

### ThemedLeadProfile

```typescript
export enum TargetSector {
  WaterTreatment,
  PowerPlantWetCooled,
  PowerPlantDryCooled,
  PowerPlantNuclear,
  HVAC,
  Mines,
  OilAndGas,
  Agriculture,
  CoolingTowers,
}

export interface ThemedLeadProfile {
  id: string;
  fictionalCompanyName: string;
  contactPerson?: string;
  role?: string;
  sector: TargetSector;
  fictionalNeeds: string[];
  email?: string;
}
```

### ProcessedLeadOutput

```typescript
export interface ProcessedLeadOutput {
  leadId: string;
  sector: TargetSector;
  analysisSummary: string;
  draftEmail: string;
}
```

## AI Integration

The lead processing system integrates with Ollama, an AI service, to generate insights and draft emails:

### Lead Analysis

```typescript
private async _analyzeLead(leadData: ThemedLeadProfile): Promise<string> {
  const prompt = getLeadAnalysisPrompt(leadData);
  const analysisSummary = await this.ollamaService.generateText(prompt);
  return analysisSummary;
}
```

The analysis prompt is structured to extract insights about the lead's potential value to Mexel based on their sector and needs.

### Email Draft Generation

```typescript
private async _draftInitialEmail(leadData: ThemedLeadProfile, analysisSummary: string): Promise<string> {
  const prompt = generateEmailDraftPrompt(leadData, analysisSummary);
  const draftEmail = await this.ollamaService.generateText(prompt);
  return draftEmail;
}
```

The email drafting system creates personalized outreach emails that address specific needs and propose relevant Mexel solutions.

## Real-time Notifications

The system uses Socket.IO to provide real-time updates on lead processing status. These events allow the frontend to display processing progress and results immediately.

### Event Types

| Event            | Payload                                                                                                             | Description                                    |
| ---------------- | ------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------- |
| `leadProcessing` | `{ status: 'started', leadId: string, companyName: string }`                                                        | Emitted when lead processing begins            |
| `leadProcessing` | `{ status: 'completed', leadId: string, companyName: string, result: { analysisSummary: string, sector: string } }` | Emitted when processing completes successfully |
| `leadProcessing` | `{ status: 'error', leadId: string, companyName: string, error: string }`                                           | Emitted when processing fails                  |

### Server Implementation

The Socket.IO event emission is integrated into the lead processing workflow in `server.ts`:

```typescript
// On start
io.emit("leadProcessing", {
  status: "started",
  leadId: leadData.id,
  companyName: leadData.fictionalCompanyName,
});

// On completion
io.emit("leadProcessing", {
  status: "completed",
  leadId: leadData.id,
  companyName: leadData.fictionalCompanyName,
  result: {
    analysisSummary: processedOutput.analysisSummary,
    sector: TargetSector[processedOutput.sector],
  },
});

// On error
io.emit("leadProcessing", {
  status: "error",
  leadId: leadData.id,
  companyName: leadData.fictionalCompanyName,
  error: error instanceof Error ? error.message : "An unknown error occurred.",
});
```

### Client Implementation

The LeadProcessingMonitor component in the frontend listens for these events and displays them in real-time:

```typescript
// LeadProcessingMonitor.tsx
useEffect(() => {
  // Socket setup code...

  // Listen for lead processing events
  newSocket.on("leadProcessing", (data: LeadProcessingEvent) => {
    // Add timestamp if not provided
    const eventWithTimestamp = {
      ...data,
      timestamp: data.timestamp || new Date().toISOString(),
    };

    // Update the lead events array
    setLeadEvents((prev) => [eventWithTimestamp, ...prev].slice(0, 50));
  });

  // Cleanup code...
}, []);
```

### Accessing Lead Processing Notifications

Users can view real-time lead processing notifications in:

1. The **Lead Management Dashboard** at `/leads`
2. The **Agent Dashboard** at `/agents` under the "Lead Processing" tab

For detailed information on the Socket.IO implementation, see [REALTIME_SOCKET_INTEGRATION.md](./REALTIME_SOCKET_INTEGRATION.md).

## API Endpoints

### Process Lead

```
POST /api/v1/process-lead
```

**Request Body:**

```json
{
  "id": "lead-001",
  "fictionalCompanyName": "ABC Mining Corp",
  "contactPerson": "John Smith",
  "role": "Procurement Manager",
  "sector": "Mines",
  "fictionalNeeds": [
    "Reduce water usage in mineral processing",
    "Improve water recycling efficiency",
    "Comply with new environmental regulations"
  ],
  "email": "<EMAIL>"
}
```

**Success Response (200):**

```json
{
  "leadId": "lead-001",
  "sector": 5,
  "analysisSummary": "ABC Mining Corp operates in the mining sector with specific needs around water management and environmental compliance. Their needs align well with Mexel's water treatment and recycling solutions, particularly our advanced flocculants and mineral extraction aids that minimize water usage.",
  "draftEmail": "Dear John Smith,\n\nI hope this email finds you well. I recently came across ABC Mining Corp and noticed your focus on improving water management processes...[rest of email]"
}
```

**Error Response (400) - Validation Error:**

```json
{
  "error": "Missing required field: sector."
}
```

**Error Response (500) - Processing Error:**

```json
{
  "error": "Failed to process lead",
  "details": "Error connecting to AI service"
}
```

## Frontend Integration

To integrate with the lead processing system from the frontend:

### 1. Socket.IO Connection

```javascript
import { io } from "socket.io-client";

const socket = io("http://localhost:3001");

socket.on("leadProcessing", (data) => {
  if (data.status === "started") {
    // Show processing indicator
    console.log(`Started processing lead: ${data.companyName}`);
  } else if (data.status === "completed") {
    // Show results
    console.log(`Completed processing lead: ${data.companyName}`);
    console.log(`Analysis: ${data.result.analysisSummary}`);
  } else if (data.status === "error") {
    // Show error
    console.error(`Error processing lead: ${data.companyName}`, data.error);
  }
});
```

### 2. Submit Lead for Processing

```javascript
async function submitLead(leadData) {
  try {
    const response = await fetch("http://localhost:3001/api/v1/process-lead", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(leadData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Unknown error");
    }

    return await response.json();
  } catch (error) {
    console.error("Failed to submit lead:", error);
    throw error;
  }
}
```

## Testing

To test the lead processing functionality:

### Manual Testing with Script

The repository includes a test script at `scripts/testPhase1Processing.ts` that sends sample leads to the API endpoint:

```bash
yarn test:phase1
```

### Unit Testing

Unit tests for the AgentWorkflow and lead processing components:

```bash
yarn test -- --testPathPattern=src/tests/core/AgentWorkflow.test.ts
```

### Integration Testing

Integration tests that validate the end-to-end flow:

```bash
yarn test:integration
```

## Troubleshooting

### Common Issues

1. **AI Service Connection Failures**

   If you see errors like "Failed to connect to Ollama service":

   - Check that the Ollama service is running
   - Verify the `OLLAMA_HOST` and `OLLAMA_MODEL` environment variables
   - Check network connectivity between the server and Ollama

2. **Socket.IO Connection Issues**

   If real-time updates aren't working:

   - Check browser console for connection errors
   - Verify the Socket.IO URL matches the server address
   - Check CORS configuration in the backend

3. **Validation Errors**

   If you're seeing validation errors when submitting leads:

   - Ensure all required fields are present
   - Check that the sector value is valid (either a string key or numeric value from the TargetSector enum)
   - Ensure fictionalNeeds is a non-empty array of strings

4. **Slow Processing Times**

   If lead processing is taking too long:

   - Check AI service performance and load
   - Consider caching common analyses
   - Monitor server resources for bottlenecks

---

_Last updated: May 2025_
