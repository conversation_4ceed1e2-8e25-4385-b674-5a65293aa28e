# AI Integration in Mexel

This document outlines the AI integration architecture in the Mexel project.

## Overview

The Mexel project uses DeepSeek as the primary AI provider, with Ollama as an optional backup. The AI integration is designed to be modular and extensible, allowing for easy switching between different AI providers.

## Architecture

The AI integration is built around the following components:

1. **AIService**: The main service that provides AI capabilities to the rest of the application. It abstracts away the details of the underlying AI provider.

2. **DeepSeekService**: A service that interacts with the DeepSeek API to generate text.

3. **Configuration**: The AI configuration is stored in the `config.ts` file and can be overridden using environment variables.

## Configuration

The AI integration can be configured using the following environment variables:

```
# Primary AI provider: 'deepseek' or 'ollama'
AI_PROVIDER=deepseek

# DeepSeek configuration (primary)
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_API_BASE_URL=https://api.deepseek.com
DEEPSEEK_API_VERSION=v1
DEEPSEEK_TIMEOUT=30000
DEEPSEEK_CHAT_MODEL=deepseek-chat
DEEPSEEK_CODER_MODEL=deepseek-coder-v2
DEEPSEEK_INSTRUCT_MODEL=deepseek-instruct
DEEPSEEK_DEFAULT_MODEL=deepseek-chat
DEEPSEEK_MAX_RETRIES=3
DEEPSEEK_INITIAL_RETRY_DELAY=1000
DEEPSEEK_MAX_RETRY_DELAY=30000
DEEPSEEK_RATE_LIMIT=60
DEEPSEEK_ENABLE_RATE_LIMITING=true
DEEPSEEK_STREAM_RESPONSES=false

# Ollama configuration (backup)
OLLAMA_HOST=http://localhost:11434
OLLAMA_ENDPOINT=/api/generate
OLLAMA_MODEL=llama3.2
```

## Usage

To use the AI service in your code, you can import the `AIService` class and call the `generate` method:

```typescript
import { AIService } from '@src/services/AIService';

const aiService = AIService.getInstance();

async function generateText() {
  const response = await aiService.generate({
    prompt: 'Hello, world!',
    system_prompt: 'You are a helpful assistant.',
    temperature: 0.7,
    max_tokens: 500
  });

  console.log(response.text);
}
```

## Error Handling

The AI service includes robust error handling to deal with API errors, network issues, and other problems that might occur when interacting with the AI provider. If the primary AI provider (DeepSeek) fails, the service will automatically fall back to the backup provider (Ollama) if it's configured.

## Extending the AI Integration

To add support for a new AI provider, you would need to:

1. Create a new service class that implements the necessary methods to interact with the new AI provider's API.
2. Update the `AIService` class to use the new service when configured.
3. Update the configuration interface and environment variables to support the new provider.

## Best Practices

1. **API Keys**: Always store API keys in environment variables, never hardcode them in the source code.
2. **Error Handling**: Always handle errors when calling the AI service, as API calls can fail for various reasons.
3. **Rate Limiting**: Be mindful of rate limits imposed by the AI provider and implement appropriate rate limiting in your code.
4. **Prompt Engineering**: Craft your prompts carefully to get the best results from the AI model.
5. **System Prompts**: Use system prompts to provide context and instructions to the AI model.

## Troubleshooting

If you encounter issues with the AI integration, check the following:

1. **API Keys**: Make sure your API keys are correct and have the necessary permissions.
2. **Network**: Check that your application can reach the AI provider's API endpoints.
3. **Rate Limits**: Check if you've hit the rate limits imposed by the AI provider.
4. **Logs**: Check the application logs for error messages related to the AI service.

## References

- [DeepSeek API Documentation](https://docs.deepseek.com/)
- [Ollama Documentation](https://ollama.ai/docs)
