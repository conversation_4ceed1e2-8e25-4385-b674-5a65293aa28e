# HubSpot Integration Setup Guide

This guide will walk you through setting up the HubSpot integration for Mexel using the free tier.

## 1. Create a HubSpot Account

If you don't already have a HubSpot account:

1. Go to [HubSpot's website](https://www.hubspot.com/)
2. Click "Get started free" or "Start free"
3. Sign up for a free account using your business email
4. Complete the onboarding process

## 2. Get Your API Key

Your API key has already been configured in the `.env` file:

```
HUBSPOT_API_KEY=CiRldTEtYmRhOC1lNGYxLTRkYTktODY1Yi02YzIyOTYwMzcxNGYQi_vMRRivodolKhkABeaRguoy9j1vxLvaaJTjAqsgxa67W94zSgNldTE
```

If you need to create a new API key:

1. Log in to your HubSpot account
2. Click on the settings icon (gear) in the top right
3. In the left sidebar, navigate to "Integrations" > "Private Apps"
4. Click "Create private app"
5. Give your app a name (e.g., "Mexel Integration")
6. Add the required scopes:
   - `crm.objects.contacts.read`
   - `crm.objects.contacts.write`
   - `crm.objects.companies.read`
   - `crm.objects.companies.write`
   - `timeline`
7. Click "Create app"
8. Copy the access token (API key) and update it in your `.env` file

## 3. Get Your Portal ID

To find your HubSpot Portal ID:

1. Log in to your HubSpot account
2. Click on the settings icon (gear) in the top right
3. In the left sidebar, navigate to "Account Setup" > "Account Details"
4. Your Hub ID (Portal ID) will be displayed in the "General Information" section
5. Copy this ID and update the `HUBSPOT_PORTAL_ID` in your `.env` file

## 4. Create a Timeline Event Template

To track activities and email events in HubSpot:

1. Log in to your HubSpot account
2. Click on the settings icon (gear) in the top right
3. In the left sidebar, navigate to "Properties" > "Timeline Events"
4. Click "Create event template"
5. Fill in the details:
   - Name: "Mexel Activity"
   - Object type: "Contact"
   - Category: "Interaction"
6. Add the following properties:
   - `activity_type` (Text): The type of activity
   - `email_id` (Text): The ID of the email
   - `event_type` (Text): The type of email event
7. Click "Create"
8. Copy the Event Template ID and update the `HUBSPOT_EMAIL_EVENT_TEMPLATE_ID` in your `.env` file

## 5. Set Up Webhooks (Optional)

To receive events from HubSpot:

1. Log in to your HubSpot account
2. Click on the settings icon (gear) in the top right
3. In the left sidebar, navigate to "Integrations" > "Webhooks"
4. Click "Create webhook"
5. Fill in the details:
   - Name: "Mexel Webhook"
   - Target URL: Your webhook endpoint (e.g., `https://yourdomain.com/api/webhooks/hubspot`)
   - Subscription type: Select the events you want to receive (e.g., "Contact creation", "Contact property change")
6. Click "Create"
7. Copy the webhook secret and update the `HUBSPOT_WEBHOOK_SECRET` in your `.env` file

## 6. HubSpot Free Tier Limitations

Be aware of the following limitations in the HubSpot free tier:

- **Contacts**: Limited to 1,000 contacts
- **Companies**: Unlimited
- **Email**: Limited to 2,000 emails per month
- **Forms**: Limited to 10 forms
- **Lists**: Limited to 5 active lists
- **Reports**: Limited to 10 reports
- **Users**: Limited to 1 user

## 7. Testing the Integration

After setting up the integration:

1. Start your application
2. Create a new lead in your application
3. Check that the lead is synced to HubSpot by going to "Contacts" in your HubSpot account
4. Update the lead in your application and verify the changes are reflected in HubSpot
5. Log an activity for the lead and verify it appears in the contact's timeline in HubSpot

## 8. Troubleshooting

If you encounter issues with the integration:

- Check the application logs for error messages
- Verify your API key is correct
- Ensure you have the necessary scopes enabled for your private app
- Check that your webhook endpoint is accessible from the internet if using webhooks
- Verify you haven't exceeded the free tier limits

## 9. Next Steps

Once the basic integration is working, you can:

1. Create custom properties in HubSpot to store additional lead information
2. Set up workflows in HubSpot to automate marketing and sales processes
3. Use HubSpot's email marketing tools to send campaigns to your leads
4. Set up lead scoring in HubSpot to prioritize your leads
5. Implement Phase 2 of the integration to add more advanced features
