# Real-time Socket.IO Integration Guide

This document provides a comprehensive guide to the Socket.IO integration in the Mexel application, which enables real-time communication between the frontend and backend servers.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Implementation Details](#implementation-details)
   - [Backend Setup](#backend-setup)
   - [Frontend Integration](#frontend-integration)
4. [Available Events](#available-events)
   - [Server to Client Events](#server-to-client-events)
   - [Client to Server Events](#client-to-server-events)
5. [Usage Examples](#usage-examples)
6. [Error Handling](#error-handling)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## Overview

Socket.IO is used in the Mexel application to provide real-time updates and notifications from the server to the client. This enhances the user experience by delivering immediate feedback on long-running processes like lead processing, agent activities, and system status changes.

The implementation follows a standard Socket.IO pattern where:

- The backend emits events when relevant state changes occur
- The frontend listens for these events and updates its UI accordingly
- Both sides can initiate events, though most communication flows from server to client

## Architecture

```
┌────────────────┐                 ┌────────────────┐
│                │                 │                │
│                │  HTTP/WS        │                │
│    Frontend    │<--------------->│    Backend     │
│    (React)     │  Socket.IO      │    (Node.js)   │
│                │                 │                │
└────────────────┘                 └────────────────┘
        ^                                  ^
        │                                  │
        │                                  │
        v                                  v
┌────────────────┐                 ┌────────────────┐
│  User Interface│                 │  Data Sources  │
│  Components    │                 │  (DB, APIs)    │
└────────────────┘                 └────────────────┘
```

## Implementation Details

### Backend Setup

The Socket.IO server is integrated into the Express application in `src/server.ts`. It's set up to:

1. Create an HTTP server from the Express application
2. Attach Socket.IO to this HTTP server
3. Configure CORS to allow connections from the frontend
4. Set up event handlers for connections, disconnections, and custom events

```typescript
// Socket.IO integration in server.ts
import { createServer } from "http";
import { Server as SocketIOServer } from "socket.io";

const app = express();
const httpServer = createServer(app);
const port = process.env.PORT || 3001;

// Initialize Socket.IO
const io = new SocketIOServer(httpServer, {
  cors: {
    origin: "http://localhost:3000", // Frontend URL
    methods: ["GET", "POST"],
  },
});

io.on("connection", (socket) => {
  console.log("🚀 A user connected via Socket.IO:", socket.id);
  socket.emit(
    "greeting",
    `Hello ${socket.id}, welcome to the Socket.IO server!`
  );

  socket.on("disconnect", () => {
    console.log("🔥 User disconnected from Socket.IO:", socket.id);
  });
});

// Start server with HTTP server, not Express app directly
httpServer.listen(port, () => {
  console.log(
    `⚡️[server]: Server (with Socket.IO) is running at http://localhost:${port}`
  );
});
```

### Frontend Integration

To connect to the Socket.IO server from the frontend, we utilize the `socket.io-client` library. The integration is implemented in multiple components:

1. **LeadProcessingMonitor Component**: A dedicated component for monitoring lead processing events.
2. **AgentRealTimeStatus Component**: Handles real-time agent status updates.

#### Socket Client Setup

Here's the basic pattern for setting up a Socket.IO client connection:

```typescript
import { io, Socket } from "socket.io-client";

const SOCKET_URL = process.env.REACT_APP_SOCKET_URL || "http://localhost:3001";

// In a React component
const [socket, setSocket] = useState<Socket | null>(null);
const [connected, setConnected] = useState<boolean>(false);

useEffect(() => {
  const newSocket = io(SOCKET_URL, {
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
  });

  newSocket.on("connect", () => {
    console.log("Connected to Socket.IO server");
    setConnected(true);
  });

  newSocket.on("disconnect", () => {
    console.log("Disconnected from Socket.IO server");
    setConnected(false);
  });

  newSocket.on("connect_error", (err) => {
    console.error("Socket.IO connection error:", err);
    setConnected(false);
  });

  // Event listeners for specific events
  newSocket.on("leadProcessing", (data) => {
    console.log("Received lead processing event:", data);
    // Update state or UI based on the event data
  });

  setSocket(newSocket);

  // Clean up on component unmount
  return () => {
    newSocket.disconnect();
  };
}, []);
```

#### Lead Processing Monitor

The `LeadProcessingMonitor` component specifically handles lead processing events:

```typescript
// In LeadProcessingMonitor.tsx
interface LeadProcessingEvent {
  status: "started" | "completed" | "error";
  leadId: string;
  companyName: string;
  timestamp?: string;
  result?: {
    analysisSummary?: string;
    sector?: string;
  };
  error?: string;
  additionalInfo?: string;
}

// Inside the component
useEffect(() => {
  // Socket setup code...

  // Listen for lead processing events
  newSocket.on("leadProcessing", (data: LeadProcessingEvent) => {
    // Update the lead events array
    setLeadEvents((prev) =>
      [
        { ...data, timestamp: data.timestamp || new Date().toISOString() },
        ...prev,
      ].slice(0, 50)
    ); // Keep last 50 events
  });

  // Cleanup code...
}, []);
```

#### Integration Points

The Socket.IO client integration is available in multiple places:

1. **Lead Dashboard** (`/leads` route): A dedicated page showing all lead processing events.
2. **Agent Dashboard** (`/agents` route): Includes a tab for real-time lead processing.
3. **AgentRealTimeStatus Component**: Used in various dashboards to show agent status.

```javascript
import { io } from "socket.io-client";

// Connect to the backend Socket.IO server
const socket = io("http://localhost:3001");

// Set up event listeners
socket.on("connect", () => {
  console.log("Connected to Socket.IO server");
});

socket.on("greeting", (message) => {
  console.log("Received greeting:", message);
});

socket.on("disconnect", () => {
  console.log("Disconnected from Socket.IO server");
});

// Custom event listeners for specific features
socket.on("leadProcessing", (data) => {
  console.log("Lead processing update:", data);
  // Handle the lead processing update in the UI
});
```

## Available Events

### Server to Client Events

| Event Name             | Payload                                                                                                                 | Description                                 |
| ---------------------- | ----------------------------------------------------------------------------------------------------------------------- | ------------------------------------------- |
| `greeting`             | `string`                                                                                                                | Simple welcome message sent upon connection |
| `leadProcessing`       | `{ status: 'started' \| 'completed' \| 'error', leadId: string, companyName: string, result?: object, error?: string }` | Updates about lead processing status        |
| `agent_status_update`  | `{ agentId: string, status: string, message: string, timestamp: string, level: string }`                                | Updates about agent status changes          |
| `agent_metrics_update` | `{ agentId: string, success_rate: number, error_rate: number, latency: number, timestamp: string }`                     | Updates about agent performance metrics     |

#### Lead Processing Event Details

The `leadProcessing` event provides real-time updates about the processing of leads. The payload includes:

```typescript
interface LeadProcessingEvent {
  status: "started" | "completed" | "error";
  leadId: string;
  companyName: string;
  timestamp?: string;
  result?: {
    analysisSummary?: string;
    sector?: string;
  };
  error?: string;
  additionalInfo?: string;
}
```

#### Common Event Patterns

1. **Process Start/Complete/Error Pattern**:
   Used for long-running processes like lead processing, where the client is notified when a process starts, completes successfully, or encounters an error.

2. **Status Update Pattern**:
   Used for agent status changes, where the client is notified of state changes (running, idle, error).

3. **Metrics Update Pattern**:
   Used for real-time reporting of performance metrics.

### Client to Server Events

Currently, the system primarily uses server-to-client communication. Client-to-server events may be implemented in the future for features like:

- Manual refresh requests
- User-initiated actions that require immediate feedback
- Real-time collaboration features

## Usage Examples

### Handling Lead Processing Updates in React

```jsx
import React, { useEffect, useState } from "react";
import { io } from "socket.io-client";

const LeadProcessingMonitor = () => {
  const [leadUpdates, setLeadUpdates] = useState([]);
  const [socket, setSocket] = useState(null);

  useEffect(() => {
    // Initialize socket connection
    const socketConnection = io("http://localhost:3001");
    setSocket(socketConnection);

    // Clean up connection on unmount
    return () => {
      socketConnection.disconnect();
    };
  }, []);

  useEffect(() => {
    if (!socket) return;

    // Listen for lead processing updates
    socket.on("leadProcessing", (data) => {
      setLeadUpdates((prevUpdates) => [...prevUpdates, data]);
    });

    return () => {
      socket.off("leadProcessing");
    };
  }, [socket]);

  return (
    <div className="lead-processing-monitor">
      <h2>Lead Processing Updates</h2>
      <ul>
        {leadUpdates.map((update, index) => (
          <li key={index} className={`status-${update.status}`}>
            <strong>{update.companyName}</strong> (ID: {update.leadId}):{" "}
            {update.status}
            {update.result && (
              <div className="result">
                <p>Sector: {update.result.sector}</p>
                <p>Analysis: {update.result.analysisSummary}</p>
              </div>
            )}
            {update.error && <div className="error">Error: {update.error}</div>}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default LeadProcessingMonitor;
```

## Error Handling

Socket.IO handles connection issues internally, attempting to reconnect automatically when disconnections occur. However, it's important to handle server-emitted error events properly:

```javascript
socket.on("connect_error", (error) => {
  console.error("Connection error:", error);
  // Notify user of connection issues
});

socket.on("connect_timeout", () => {
  console.error("Connection timeout");
  // Notify user of timeout issues
});

socket.on("error", (error) => {
  console.error("Socket error:", error);
  // Handle generic errors
});
```

## Best Practices

1. **Always clean up socket listeners** when components unmount to prevent memory leaks:

   ```javascript
   useEffect(() => {
     socket.on("eventName", handleEvent);
     return () => {
       socket.off("eventName", handleEvent);
     };
   }, [socket]);
   ```

2. **Handle reconnection scenarios** properly in the frontend, possibly by requesting the latest state on reconnection:

   ```javascript
   socket.on("reconnect", () => {
     console.log("Reconnected to server");
     // Request latest state if needed
   });
   ```

3. **Use namespaces** for logical separation of events if the application grows:

   ```javascript
   // Server
   const leadNamespace = io.of("/leads");
   leadNamespace.on("connection", (socket) => {
     /* ... */
   });

   // Client
   const leadSocket = io("http://localhost:3001/leads");
   ```

4. **Consider using a state management solution** like Redux or Context API to distribute Socket.IO updates throughout the application.

5. **Use typed event data** to ensure proper type checking for event payloads:

   ```typescript
   // Define event interfaces
   interface LeadProcessingEvent {
     status: "started" | "completed" | "error";
     leadId: string;
     // ...other properties
   }

   // Use with Socket.IO
   socket.on("leadProcessing", (data: LeadProcessingEvent) => {
     // TypeScript will ensure data has the correct shape
   });
   ```

6. **Implement error boundaries** for components that rely on Socket.IO data to prevent the entire application from crashing if socket data causes rendering issues.

7. **Use connection status indicators** to show users when the real-time connection is active, disconnected, or reconnecting.

8. **Batch updates** for high-frequency events to avoid excessive re-renders.

## Troubleshooting

### Common Issues and Solutions

1. **WebSocket connection fails, falling back to HTTP polling**

   - Check firewalls and proxy configurations
   - Verify that WebSocket traffic is allowed on your network
   - Check for any middleware that might interfere with WebSocket upgrades

2. **Events not being received**

   - Verify the event names match exactly (case-sensitive)
   - Check browser console for any connection errors
   - Ensure the Socket.IO versions match between client and server

3. **Multiple identical events received**

   - Check that you're not setting up duplicate listeners
   - Make sure to properly clean up listeners when components unmount

4. **High latency**
   - Consider implementing throttling for high-frequency events
   - Use batching for multiple updates that occur in quick succession

---

_This documentation is current as of May 2025. Socket.IO integration may evolve as the application develops._
