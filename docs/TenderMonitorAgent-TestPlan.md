# TenderMonitor Agent Test Plan

## Overview

This document outlines the test plan for the TenderMonitor Agent. It includes unit tests, integration tests, and end-to-end tests to ensure the agent functions correctly.

## Unit Tests

### 1. Agent Initialization

- **Test ID**: TMA-UT-001
- **Description**: Test that the agent initializes correctly
- **Steps**:
  1. Create a new TenderMonitor Agent instance
  2. Call the initialize method
  3. Verify that the agent's status is IDLE
- **Expected Result**: Agent initializes without errors and status is IDLE

### 2. Relevance Scoring

- **Test ID**: TMA-UT-002
- **Description**: Test the relevance scoring algorithm
- **Steps**:
  1. <PERSON>reate test tenders with various titles, descriptions, values, and closing dates
  2. Call the calculateRelevanceScore method for each tender
  3. Verify that the scores are calculated correctly
- **Expected Result**: Tenders with Mexel-relevant keywords, higher values, optimal closing dates, and relevant categories get higher scores

### 3. Tender Processing

- **Test ID**: TMA-UT-003
- **Description**: Test the tender processing logic
- **Steps**:
  1. Create a test tender
  2. Call the processTenderImpl method
  3. Verify that the tender's status is updated
  4. Verify that the tender's requirements are analyzed
  5. Verify that the tender's relevance score is calculated
- **Expected Result**: Tender is processed correctly, status is updated, requirements are analyzed, and relevance score is calculated

### 4. High-Priority Tender Identification

- **Test ID**: TMA-UT-004
- **Description**: Test the identification of high-priority tenders
- **Steps**:
  1. Create a test tender with a high relevance score
  2. Call the processTenderImpl method
  3. Verify that the high_priority_tender event is emitted
- **Expected Result**: high_priority_tender event is emitted for tenders with relevance scores above the threshold

### 5. Notification System

- **Test ID**: TMA-UT-005
- **Description**: Test the notification system
- **Steps**:
  1. Create a test tender with a high relevance score
  2. Call the notifyHighPriorityTender method
  3. Verify that the EmailService's sendTransactionalEmail method is called with the correct parameters
- **Expected Result**: EmailService's sendTransactionalEmail method is called with the correct parameters

## Integration Tests

### 1. ScrapingService Integration

- **Test ID**: TMA-IT-001
- **Description**: Test the integration with the ScrapingService
- **Steps**:
  1. Create a new TenderMonitor Agent instance
  2. Call the execute method
  3. Verify that the ScrapingService's scrapeTenders method is called
  4. Verify that the opportunities are updated with the scraped tenders
- **Expected Result**: ScrapingService's scrapeTenders method is called and opportunities are updated

### 2. EmailService Integration

- **Test ID**: TMA-IT-002
- **Description**: Test the integration with the EmailService
- **Steps**:
  1. Create a new TenderMonitor Agent instance
  2. Call the execute method with a high-priority tender
  3. Verify that the EmailService's sendTransactionalEmail method is called
- **Expected Result**: EmailService's sendTransactionalEmail method is called for high-priority tenders

### 3. Database Integration

- **Test ID**: TMA-IT-003
- **Description**: Test the integration with the database
- **Steps**:
  1. Create a new TenderMonitor Agent instance
  2. Call the execute method
  3. Verify that the tenders are saved to the database
  4. Verify that the tenders can be retrieved from the database
- **Expected Result**: Tenders are saved to and retrieved from the database correctly

## End-to-End Tests

### 1. Full Workflow

- **Test ID**: TMA-E2E-001
- **Description**: Test the full workflow of the TenderMonitor Agent
- **Steps**:
  1. Create a new TenderMonitor Agent instance
  2. Call the initialize method
  3. Call the start method
  4. Call the execute method
  5. Verify that tenders are scraped, processed, and saved to the database
  6. Verify that notifications are sent for high-priority tenders
- **Expected Result**: Full workflow executes without errors

### 2. Cron Job Execution

- **Test ID**: TMA-E2E-002
- **Description**: Test the execution of the agent via cron job
- **Steps**:
  1. Create a new TenderMonitor Agent instance
  2. Call the initialize method
  3. Call the start method
  4. Wait for the cron job to execute
  5. Verify that the execute method is called
- **Expected Result**: Cron job executes the agent's execute method

## Test Data

### 1. High-Priority Tender

```json
{
  "id": "test-tender-1",
  "title": "Water Treatment System for Power Plant",
  "description": "Supply and installation of water treatment chemicals and systems for a power plant cooling tower.",
  "url": "https://example.com/tender1",
  "issuer": "Eskom",
  "status": "NEW",
  "closingDate": "2023-12-31",
  "value": 2500000,
  "category": "Water Treatment",
  "confidence": 0.85
}
```

### 2. Low-Priority Tender

```json
{
  "id": "test-tender-2",
  "title": "Office Supplies",
  "description": "Supply of general office supplies and stationery.",
  "url": "https://example.com/tender2",
  "issuer": "Department of Education",
  "status": "NEW",
  "confidence": 0.3
}
```

## Test Environment

- **Node.js Version**: 16.x or higher
- **Database**: SQLite (in-memory for tests)
- **Mocks**: ScrapingService, EmailService

## Test Execution

1. Run unit tests: `yarn test -- src/tests/agents/TenderMonitorAgent.test.ts`
2. Run integration tests: `yarn test -- src/tests/integration/TenderMonitorAgent.test.ts`
3. Run end-to-end tests: `yarn test -- src/tests/e2e/TenderMonitorAgent.test.ts`

## Test Reporting

Test results will be reported in the console and saved to the `test-results` directory.
