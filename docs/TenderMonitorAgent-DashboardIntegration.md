# TenderMonitor Agent Dashboard Integration

## Overview

This document outlines the integration of the TenderMonitor Agent with the Mexel dashboard. It includes the API endpoints, frontend components, and data flow needed to display tender data in the dashboard.

## API Endpoints

### 1. Get All Tenders

- **Endpoint**: `GET /api/tenders`
- **Description**: Get all tenders
- **Query Parameters**:
  - `status`: Filter by tender status (optional)
  - `category`: Filter by tender category (optional)
  - `minScore`: Filter by minimum relevance score (optional)
  - `page`: Page number for pagination (default: 1)
  - `limit`: Number of tenders per page (default: 10)
  - `sortBy`: Field to sort by (default: 'relevance_score')
  - `sortOrder`: Sort order ('asc' or 'desc', default: 'desc')
- **Response**:
  ```json
  {
    "tenders": [
      {
        "id": "tender-1",
        "title": "Water Treatment System",
        "description": "Supply and installation of water treatment chemicals",
        "issuer": "Eskom",
        "status": "NEW",
        "closingDate": "2023-12-31",
        "value": 2500000,
        "category": "Water Treatment",
        "relevanceScore": 0.85,
        "url": "https://example.com/tender1"
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "limit": 10,
      "pages": 10
    }
  }
  ```

### 2. Get Tender by ID

- **Endpoint**: `GET /api/tenders/:id`
- **Description**: Get a tender by ID
- **Response**:
  ```json
  {
    "id": "tender-1",
    "title": "Water Treatment System",
    "description": "Supply and installation of water treatment chemicals",
    "issuer": "Eskom",
    "status": "NEW",
    "closingDate": "2023-12-31",
    "value": 2500000,
    "category": "Water Treatment",
    "relevanceScore": 0.85,
    "url": "https://example.com/tender1",
    "requirements": [
      "Experience required",
      "Certification required"
    ],
    "documents": [
      {
        "title": "Tender Document",
        "url": "https://example.com/tender1/document"
      }
    ],
    "metadata": {
      "firstSeen": "2023-11-01",
      "lastUpdated": "2023-11-15"
    }
  }
  ```

### 3. Update Tender Status

- **Endpoint**: `PUT /api/tenders/:id/status`
- **Description**: Update a tender's status
- **Request Body**:
  ```json
  {
    "status": "SUBMITTED"
  }
  ```
- **Response**:
  ```json
  {
    "id": "tender-1",
    "status": "SUBMITTED",
    "message": "Tender status updated successfully"
  }
  ```

### 4. Get Tender Statistics

- **Endpoint**: `GET /api/tenders/statistics`
- **Description**: Get statistics about tenders
- **Response**:
  ```json
  {
    "total": 100,
    "byStatus": {
      "NEW": 50,
      "PROCESSING": 20,
      "SUBMITTED": 15,
      "WON": 10,
      "LOST": 5
    },
    "byCategory": {
      "Water Treatment": 40,
      "Chemicals": 30,
      "Power Generation": 20,
      "Mining": 10
    },
    "averageScore": 0.65,
    "highPriorityCount": 25
  }
  ```

## Frontend Components

### 1. Tender Dashboard

- **Component**: `TenderDashboard`
- **Description**: Main dashboard for viewing tenders
- **Features**:
  - Filter tenders by status, category, and relevance score
  - Sort tenders by various fields
  - Pagination for large numbers of tenders
  - Quick view of tender details
  - Export tenders to CSV

### 2. Tender Detail View

- **Component**: `TenderDetail`
- **Description**: Detailed view of a single tender
- **Features**:
  - Display all tender information
  - Update tender status
  - View tender documents
  - Add notes to the tender
  - Track tender progress

### 3. Tender Statistics

- **Component**: `TenderStatistics`
- **Description**: Statistics and charts about tenders
- **Features**:
  - Display tender counts by status and category
  - Show average relevance score
  - Display high-priority tender count
  - Show trends over time

### 4. Tender Notifications

- **Component**: `TenderNotifications`
- **Description**: Notifications about new and high-priority tenders
- **Features**:
  - Display notifications for new tenders
  - Highlight high-priority tenders
  - Mark notifications as read
  - Configure notification preferences

## Data Flow

1. **TenderMonitor Agent** scrapes tenders from external sources
2. Tenders are processed, scored, and saved to the database
3. High-priority tenders trigger notifications
4. **API Server** exposes tender data through REST endpoints
5. **Frontend** fetches tender data from the API and displays it in the dashboard
6. User interactions (e.g., updating tender status) are sent back to the API
7. API updates the database and notifies the TenderMonitor Agent if needed

## Real-Time Updates

Real-time updates will be implemented using Socket.IO:

1. **TenderMonitor Agent** emits events when new tenders are found or processed
2. **Socket.IO Server** listens for these events and broadcasts them to connected clients
3. **Frontend** listens for Socket.IO events and updates the UI accordingly

## Implementation Steps

### Backend

1. Implement API endpoints for tenders
2. Set up Socket.IO for real-time updates
3. Create database queries for tender data
4. Implement authentication and authorization for API endpoints
5. Add validation for API requests
6. Implement error handling for API endpoints
7. Set up logging for API requests

### Frontend

1. Create TenderDashboard component
2. Implement TenderDetail component
3. Create TenderStatistics component
4. Implement TenderNotifications component
5. Set up API client for fetching tender data
6. Implement Socket.IO client for real-time updates
7. Add filtering, sorting, and pagination for tenders
8. Implement responsive design for mobile devices

## Conclusion

The integration of the TenderMonitor Agent with the dashboard will provide a comprehensive view of tender opportunities. Users will be able to see new tenders, track their progress, and receive notifications about high-priority opportunities. The real-time updates will ensure that users always have the latest information about tenders.
