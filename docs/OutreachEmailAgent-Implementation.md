# OutreachEmail Agent Implementation

## Overview

The OutreachEmail Agent is responsible for generating and sending personalized emails for high-priority tenders. It uses the output from the TenderMonitor Agent to identify opportunities and generate relevant email content. This document outlines the implementation of the OutreachEmail Agent and its integration with the TenderMonitor Agent.

## Features

1. **Email Generation**:
   - Generate personalized emails for high-priority tenders
   - Use DeepSeek AI for content generation
   - Implement different email templates for different types of tenders
   - Include relevant information about the tender in the email

2. **Email Template Management**:
   - Create and manage email templates
   - Support for variables in templates
   - Version control for templates
   - A/B testing for different templates

3. **Personalization Logic**:
   - Personalize emails based on lead data
   - Include company-specific information
   - Reference previous interactions
   - Tailor content to the recipient's industry and role

4. **Email Tracking and Analytics**:
   - Track email opens and clicks
   - Monitor response rates
   - Analyze engagement metrics
   - Generate reports on email performance

## Architecture

The OutreachEmail Agent extends the BaseAgent class and implements the AgentWithEmails interface. It interacts with the following services:

1. **EmailService**: For sending and tracking emails
2. **CRMService**: For retrieving lead data
3. **AIService**: For generating email content
4. **DatabaseService**: For storing email templates and tracking data

## Implementation

### OutreachEmail Agent

```typescript
export class OutreachEmailAgent extends BaseAgent implements AgentWithEmails {
    private emailService: EmailService;
    private crmService: CRMService;
    private aiService: AIService;
    private dbService: DatabaseService;

    constructor() {
        super('OutreachEmail', AgentRole.OUTREACH_EMAIL_SENDER);
        
        this.emailService = EmailService.getInstance();
        this.crmService = CRMService.getInstance();
        this.aiService = AIService.getInstance();
        this.dbService = DatabaseService.getInstance();
    }

    // ... implementation details
}
```

### Email Generation

The OutreachEmail Agent will use the DeepSeek AI service to generate personalized email content:

```typescript
async generateEmailContent(tender: Tender, lead: Lead): Promise<string> {
    const prompt = this.createPromptForTender(tender, lead);
    
    const response = await this.aiService.generate({
        prompt,
        system_prompt: "You are a professional sales representative crafting outreach emails."
    });
    
    return response.text;
}

private createPromptForTender(tender: Tender, lead: Lead): string {
    return `Generate a professional email for a sales outreach:
        - Company: ${lead.company}
        - Industry: ${lead.industry}
        - Tender: ${tender.title}
        - Tender Description: ${tender.description}
        - Closing Date: ${tender.closingDate}
        - Value: ${tender.value}
        - Category: ${tender.category}

        The email should be personalized, concise (max 150 words), address their pain points,
        highlight our main benefits, and include a clear call to action.`;
}
```

### Email Template Management

The OutreachEmail Agent will manage email templates using the DatabaseService:

```typescript
async getEmailTemplate(templateName: string): Promise<IEmailTemplate | null> {
    return this.dbService.getEmailTemplateByName(templateName);
}

async saveEmailTemplate(template: IEmailTemplate): Promise<void> {
    await this.dbService.saveEmailTemplate(template);
}

async generateTemplateForTender(tender: Tender): Promise<IEmailTemplate> {
    // Generate template based on tender
    const subject = `Opportunity: ${tender.title}`;
    const body = await this.generateEmailContent(tender, null);
    
    const template: IEmailTemplate = {
        name: `tender_${tender.id}`,
        subject,
        body,
        variables: ['{{lead_name}}', '{{company_name}}', '{{closing_date}}'],
        metadata: {
            tenderId: tender.id,
            category: tender.category,
            generatedAt: new Date().toISOString()
        }
    };
    
    await this.saveEmailTemplate(template);
    return template;
}
```

### Personalization Logic

The OutreachEmail Agent will personalize emails based on lead data:

```typescript
async personalizeEmail(template: IEmailTemplate, lead: Lead): Promise<{ subject: string, body: string }> {
    let subject = template.subject;
    let body = template.body;
    
    // Replace variables
    const variables: Record<string, string> = {
        '{{lead_name}}': lead.name,
        '{{company_name}}': lead.company,
        '{{closing_date}}': tender.closingDate ? new Date(tender.closingDate).toLocaleDateString() : 'N/A',
        // Add more variables as needed
    };
    
    // Replace variables in subject and body
    Object.entries(variables).forEach(([key, value]) => {
        subject = subject.replace(new RegExp(key, 'g'), value);
        body = body.replace(new RegExp(key, 'g'), value);
    });
    
    return { subject, body };
}
```

### Email Tracking and Analytics

The OutreachEmail Agent will track email engagement using the EmailService:

```typescript
async trackEmailEngagement(emailId: string): Promise<EmailEngagement> {
    return this.emailService.getEmailEngagement(emailId);
}

async generateEngagementReport(): Promise<EmailEngagementReport> {
    const emails = await this.emailService.getRecentEmails();
    const engagements = await Promise.all(emails.map(email => this.trackEmailEngagement(email.id)));
    
    // Calculate metrics
    const totalSent = emails.length;
    const totalOpened = engagements.filter(e => e.opened).length;
    const totalClicked = engagements.filter(e => e.clicked).length;
    const totalResponded = engagements.filter(e => e.responded).length;
    
    return {
        totalSent,
        totalOpened,
        totalClicked,
        totalResponded,
        openRate: totalSent > 0 ? totalOpened / totalSent : 0,
        clickRate: totalOpened > 0 ? totalClicked / totalOpened : 0,
        responseRate: totalSent > 0 ? totalResponded / totalSent : 0,
        timestamp: new Date().toISOString()
    };
}
```

## Integration with TenderMonitor Agent

The OutreachEmail Agent will integrate with the TenderMonitor Agent to generate and send emails for high-priority tenders:

```typescript
// In TenderMonitorAgent.ts
this.on('high_priority_tender', async (tender: Tender) => {
    // Notify OutreachEmail Agent
    this.emit('tender_for_outreach', tender);
});

// In OutreachEmailAgent.ts
constructor() {
    // ... other initialization
    
    // Listen for high-priority tenders
    this.tenderMonitorAgent.on('tender_for_outreach', async (tender: Tender) => {
        await this.handleTenderForOutreach(tender);
    });
}

async handleTenderForOutreach(tender: Tender): Promise<void> {
    // Generate email template for the tender
    const template = await this.generateTemplateForTender(tender);
    
    // Find relevant leads
    const leads = await this.findRelevantLeads(tender);
    
    // Send emails to leads
    for (const lead of leads) {
        await this.sendEmailToLead(template, lead, tender);
    }
}

async findRelevantLeads(tender: Tender): Promise<Lead[]> {
    // Find leads that match the tender's category
    return this.crmService.findLeadsByCategory(tender.category);
}

async sendEmailToLead(template: IEmailTemplate, lead: Lead, tender: Tender): Promise<void> {
    // Personalize email
    const { subject, body } = await this.personalizeEmail(template, lead);
    
    // Send email
    await this.emailService.sendEmail({
        to: lead.email,
        subject,
        body,
        metadata: {
            tenderId: tender.id,
            leadId: lead.id,
            templateId: template.id
        }
    });
    
    // Log email sent
    this.logger.info('Email sent to lead', {
        tenderId: tender.id,
        leadId: lead.id,
        templateId: template.id
    });
}
```

## Workflow

1. TenderMonitor Agent identifies a high-priority tender
2. TenderMonitor Agent emits a 'tender_for_outreach' event
3. OutreachEmail Agent receives the event and generates an email template
4. OutreachEmail Agent finds relevant leads
5. OutreachEmail Agent personalizes and sends emails to leads
6. OutreachEmail Agent tracks email engagement
7. OutreachEmail Agent generates reports on email performance

## Next Steps

1. Implement the OutreachEmail Agent
2. Create email templates for different types of tenders
3. Implement personalization logic
4. Set up email tracking and analytics
5. Integrate with the TenderMonitor Agent
6. Test the end-to-end workflow
7. Deploy to production

## Conclusion

The OutreachEmail Agent will automate the process of generating and sending personalized emails for high-priority tenders. By integrating with the TenderMonitor Agent, it will ensure that relevant leads are notified about opportunities that match their interests. The email tracking and analytics will provide insights into the effectiveness of the outreach efforts.
