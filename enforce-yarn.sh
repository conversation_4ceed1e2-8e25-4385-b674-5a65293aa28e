#!/bin/bash

# <PERSON><PERSON><PERSON> to enforce yarn as the only package manager in the project

echo "Enforcing yarn as the package manager for this project..."

# Find and remove all package-lock.json files
echo "Removing all package-lock.json files..."
find . -name "package-lock.json" -type f -delete

# Find all npm debug logs and remove them
echo "Removing all npm debug logs..."
find . -name "npm-debug.log*" -type f -delete

# Create or update .npmrc to prevent npm usage
echo "Creating .npmrc files to prevent npm usage..."
find . -name "package.json" -type f -exec dirname {} \; | while read dir; do
  echo 'package-lock=false' > "$dir/.npmrc"
  echo 'engine-strict=true' >> "$dir/.npmrc"
done

# Create or update .yarnrc.yml to enforce yarn 3.6.3
echo "Creating .yarnrc.yml files to enforce yarn 3.6.3..."
find . -name "package.json" -type f -exec dirname {} \; | while read dir; do
  # Determine the correct yarnPath relative to each directory
  if [ "$dir" = "." ]; then
    # Root directory
    YARN_PATH=".yarn/releases/yarn-3.6.3.cjs"
  else
    # Subdirectories (frontend, shared, etc.)
    YARN_PATH="../.yarn/releases/yarn-3.6.3.cjs"
  fi
  
  # Create .yarnrc.yml file for Yarn 3.6.3
  cat > "$dir/.yarnrc.yml" << EOF
nodeLinker: node-modules
yarnPath: $YARN_PATH
enableGlobalCache: true
npmRegistryServer: "https://registry.yarnpkg.com"
npmAlwaysAuth: false
EOF
done

# Update scripts in the primary package.json files to use yarn
echo "Updating scripts in package.json files to use yarn..."
find . -name "package.json" -type f -exec sed -i '' 's/yarn start/yarn start/g' {} \;
find . -name "package.json" -type f -exec sed -i '' 's/yarn run/yarn run/g' {} \;
find . -name "package.json" -type f -exec sed -i '' 's/yarn test/yarn test/g' {} \;
find . -name "package.json" -type f -exec sed -i '' 's/yarn install/yarn install/g' {} \;
find . -name "package.json" -type f -exec sed -i '' 's/yarn install/yarn install/g' {} \;
find . -name "package.json" -type f -exec sed -i '' 's/yarn add /yarn add /g' {} \;
find . -name "package.json" -type f -exec sed -i '' 's/"npm /"yarn /g' {} \;
find . -name "package.json" -type f -exec sed -i '' 's/yarn build/yarn build/g' {} \;
find . -name "package.json" -type f -exec sed -i '' 's/yarn lint/yarn lint/g' {} \;

# Update shell scripts to use yarn instead of npm
echo "Updating shell scripts to use yarn instead of npm..."
find . -name "*.sh" -type f -exec sed -i '' 's/yarn install/yarn install/g' {} \;
find . -name "*.sh" -type f -exec sed -i '' 's/yarn start/yarn start/g' {} \;
find . -name "*.sh" -type f -exec sed -i '' 's/yarn run/yarn run/g' {} \;
find . -name "*.sh" -type f -exec sed -i '' 's/yarn test/yarn test/g' {} \;
find . -name "*.sh" -type f -exec sed -i '' 's/yarn install/yarn install/g' {} \;
find . -name "*.sh" -type f -exec sed -i '' 's/yarn add /yarn add /g' {} \;
find . -name "*.sh" -type f -exec sed -i '' 's/yarn build/yarn build/g' {} \;
find . -name "*.sh" -type f -exec sed -i '' 's/yarn lint/yarn lint/g' {} \;
find . -name "*.sh" -type f -exec sed -i '' 's/yarn audit/yarn audit/g' {} \;

# Specifically fix the frontend/fix-frontend.sh file
echo "Fixing specific references in fix-frontend.sh..."
if [ -f "fix-frontend.sh" ]; then
  sed -i '' 's/yarn start/yarn start/g' fix-frontend.sh
  sed -i '' 's/yarn run/yarn run/g' fix-frontend.sh
  sed -i '' 's/yarn install/yarn install/g' fix-frontend.sh
  sed -i '' 's/yarn install/yarn install/g' fix-frontend.sh
elif [ -f "frontend/fix-frontend.sh" ]; then
  sed -i '' 's/yarn start/yarn start/g' frontend/fix-frontend.sh
  sed -i '' 's/yarn run/yarn run/g' frontend/fix-frontend.sh
  sed -i '' 's/yarn install/yarn install/g' frontend/fix-frontend.sh
  sed -i '' 's/yarn install/yarn install/g' frontend/fix-frontend.sh
fi

# Add preinstall script to package.json files to prevent npm usage
echo "Adding preinstall script to prevent npm usage..."
find . -name "package.json" -type f | while read file; do
  if ! grep -q "\"preinstall\":" "$file"; then
    # Create a temporary file with the new content
    # First ensure there's a scripts object, then add preinstall
    jq 'if has("scripts") then . else . + {"scripts":{}} end | .scripts += {"preinstall": "echo \"\\n⚠️  This project requires Yarn instead of NPM! \\n\\n📦 Please install Yarn: https://yarnpkg.com/getting-started/install\\n\\n🚀 Then run: yarn install\\n\" && exit 1"}' "$file" > "$file.tmp"
    if [ $? -eq 0 ]; then
      mv "$file.tmp" "$file"
      echo "Updated $file with preinstall script"
    else
      echo "Error updating $file, skipping"
      rm -f "$file.tmp"
    fi
  else
    echo "File $file already has preinstall script"
  fi
done

# Update README.md to mention yarn requirement
echo "Updating README.md to mention yarn requirement..."
if [ -f "README.md" ]; then
  if ! grep -q "## Package Manager" "README.md"; then
    # Append to the end of the README
    cat << 'EOF' >> README.md

## Package Manager

This project uses Yarn as its package manager. Please ensure you have Yarn installed before working with this project.

### Installation

To install Yarn, follow the instructions at [https://yarnpkg.com/getting-started/install](https://yarnpkg.com/getting-started/install).

### Commands

- Install dependencies: `yarn install`
- Run the development server: `yarn start`
- Run tests: `yarn test`

**Note:** Using npm commands will result in errors. Please use Yarn for all package management operations.
EOF
    echo "Updated README.md with yarn requirement information"
  else
    echo "README.md already contains package manager information"
  fi
else
  # Create a new README if it doesn't exist
  cat << 'EOF' > README.md
# Project Overview

## Package Manager

This project uses Yarn as its package manager. Please ensure you have Yarn installed before working with this project.

### Installation

To install Yarn, follow the instructions at [https://yarnpkg.com/getting-started/install](https://yarnpkg.com/getting-started/install).

### Commands

- Install dependencies: `yarn install`
- Run the development server: `yarn start`
- Run tests: `yarn test`

**Note:** Using npm commands will result in errors. Please use Yarn for all package management operations.
EOF
  echo "Created new README.md with yarn requirement information"
fi

# Update .gitignore to ignore npm-specific files
echo "Updating .gitignore to ignore npm-specific files..."
if [ -f ".gitignore" ]; then
  if ! grep -q "# npm (disallowed)" ".gitignore"; then
    # Append to the end of .gitignore
    cat << 'EOF' >> .gitignore

# npm (disallowed)
package-lock.json
npm-debug.log*
.npm
EOF
    echo "Updated .gitignore with npm-specific exclusions"
  else
    echo ".gitignore already contains npm-specific exclusions"
  fi
else
  # Create a new .gitignore if it doesn't exist
  cat << 'EOF' > .gitignore
# npm (disallowed)
package-lock.json
npm-debug.log*
.npm

# Yarn
node_modules
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*
yarn-debug.log*
yarn-error.log*
EOF
  echo "Created new .gitignore with npm and yarn exclusions"
fi

echo "Done enforcing yarn as the package manager!"
echo ""
echo "To install dependencies, run: yarn install"
echo "To start the frontend, run: cd frontend && yarn start"
echo ""
echo "⚠️ npm commands will now fail with an error message."
