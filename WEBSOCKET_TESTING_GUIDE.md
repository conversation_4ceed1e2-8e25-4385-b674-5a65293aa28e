# WebSocket Testing Guide

This guide will help you test the WebSocket connectivity between the frontend and backend.

## Prerequisites

1. Make sure the backend server is running:
   ```
   cd /Users/<USER>/Desktop/Mexel/backend-python
   python run.py
   ```

## Testing Tools

There are several options for testing the WebSocket functionality:

### Option 1: Comprehensive WebSocket Diagnostics (Recommended)

```
cd /Users/<USER>/Desktop/Mexel
node websocket-diagnostics.js
```

This advanced diagnostic tool will:

- Test HTTP connectivity to the server
- Check if WebSocket-related REST endpoints are available
- Test WebSocket connectivity with multiple transport mechanisms
- Verify event handling and message exchange
- Provide a detailed diagnostic summary and recommendations

### Option 2: Simple WebSocket Connection Test

```
cd /Users/<USER>/Desktop/Mexel
node check-websocket-connection.js
```

This script will perform a basic test of the WebSocket connection with nice formatting.

### Option 3: Minimal WebSocket Test

```
cd /Users/<USER>/Desktop/Mexel
node test-websocket-connection.js
```

A simple script that just tests basic connection and events.

### Option 4: Frontend UI Testing

1. Start the frontend application:

   ```
   cd /Users/<USER>/Desktop/Mexel/frontend
   npm start
   ```

2. Open the application in your browser and navigate to the "WebSocket Testing" tab.

## Using the WebSocketTester Component

The WebSocketTester component offers a user-friendly way to test WebSocket functionality:

1. **Basic Connection**:

   - Click the "Connect" button to establish a WebSocket connection
   - You should see a "Connected" status when successful
   - The "Message History" panel will show connection events

2. **Advanced Settings**:

   - Click "Advanced Settings" to reveal additional options
   - You can specify a custom server URL (default is http://localhost:3001)
   - You can toggle debug mode for detailed console logging

3. **Available Actions**:

   - **Send Message**: Sends a basic message to the server
   - **Send Echo**: Sends data that the server will echo back
   - **Send Ping**: Sends a ping request that the server will respond to with a pong
   - **Check Server Status**: Retrieves WebSocket server status via REST API

4. **Reconnection**:
   - If connection is lost, use the "Reconnect" button
   - The component includes automatic retry logic with exponential backoff

## Troubleshooting

### Connection Issues:

- Verify the server is running on the correct port (default 3001)
- Check browser console for any CORS or connection errors
- Ensure Socket.IO versions are compatible between client and server

### Message Exchange Issues:

- Check that event names match between client and server
- Verify the backend socket handlers are properly registered
- Enable debug mode to see detailed logging of socket events

### Network-related Issues:

- Try different transports (WebSocket and Polling)
- Check for network restrictions or firewalls
- Ensure your browser supports WebSockets

If the diagnostics tool shows issues and you've tried the troubleshooting steps, please check the server logs for additional information.

## Verification of Success

Your WebSocket implementation is working correctly if:

1. You can connect successfully from both test scripts and UI
2. You receive expected responses to sent messages
3. The server registers socket connections and disconnections
4. All event handlers work as expected
