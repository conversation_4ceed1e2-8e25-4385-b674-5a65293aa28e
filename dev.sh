#!/bin/bash

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to check command status
check_status() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ $1${NC}"
    else
        echo -e "${RED}✗ $1${NC}"
        exit 1
    fi
}

echo -e "${YELLOW}Setting up Mexel Development Environment...${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Docker is not running! Please start Docker and try again.${NC}"
    exit 1
fi

# Install dependencies
echo -e "\n${YELLOW}Installing dependencies...${NC}"
yarn install
check_status "Yarn dependencies installed"

# Setup TypeScript
echo -e "\n${YELLOW}Setting up TypeScript...${NC}"
yarn setup-typescript
check_status "TypeScript setup completed"

# Run ESLint fix
echo -e "\n${YELLOW}Running ESLint fix...${NC}"
yarn lint:fix
check_status "ESLint fixes applied"

# Build containers
echo -e "\n${YELLOW}Building Docker containers...${NC}"
docker-compose -f docker-compose.dev.yml build
check_status "Docker containers built"

# Start services
echo -e "\n${YELLOW}Starting services...${NC}"
docker-compose -f docker-compose.dev.yml up -d
check_status "Services started"

# Wait for services to be ready
echo -e "\n${YELLOW}Waiting for services to be ready...${NC}"
sleep 5

# Check service health
echo -e "\n${YELLOW}Checking service health...${NC}"
if docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
    echo -e "${GREEN}✓ All services are running${NC}"
else
    echo -e "${RED}✗ Some services failed to start${NC}"
    docker-compose -f docker-compose.dev.yml logs
    exit 1
fi

# Print success message and instructions
echo -e "\n${GREEN}Development environment is ready!${NC}"
echo -e "${GREEN}================================${NC}"
echo -e "${GREEN}Frontend: http://localhost:3000${NC}"
echo -e "${GREEN}Backend API: http://localhost:8000${NC}"
echo -e "${GREEN}Database: localhost:5432${NC}"
echo -e "\n${YELLOW}Useful commands:${NC}"
echo -e "• View logs: ${GREEN}docker-compose -f docker-compose.dev.yml logs -f${NC}"
echo -e "• Stop services: ${GREEN}docker-compose -f docker-compose.dev.yml down${NC}"
echo -e "• Restart service: ${GREEN}docker-compose -f docker-compose.dev.yml restart [service_name]${NC}"
echo -e "• Run tests: ${GREEN}yarn test${NC}"
echo -e "• Lint code: ${GREEN}yarn lint${NC}"
echo -e "• TypeScript check: ${GREEN}yarn tsc${NC}"

# Watch logs
echo -e "\n${YELLOW}Showing logs (Ctrl+C to stop viewing logs)...${NC}"
docker-compose -f docker-compose.dev.yml logs -f
