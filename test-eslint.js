const { ESLint } = require('eslint');
const path = require('path');
const fs = require('fs');

async function main() {
  try {
    console.log('ESLint diagnostics:');
    console.log('- ESLint module path:', require.resolve('eslint'));
    console.log('- @typescript-eslint/parser path:', require.resolve('@typescript-eslint/parser'));
    console.log('- @typescript-eslint/eslint-plugin path:', require.resolve('@typescript-eslint/eslint-plugin'));

    console.log('\nChecking ESLint configuration files:');
    const rootEslintPath = path.join(__dirname, '.eslintrc.js');
    const frontendEslintPath = path.join(__dirname, 'frontend', '.eslintrc.js');

    if (fs.existsSync(rootEslintPath)) {
      console.log('- Root .eslintrc.js exists');
    } else {
      console.log('- Root .eslintrc.js is missing');
    }

    if (fs.existsSync(frontendEslintPath)) {
      console.log('- Frontend .eslintrc.js exists');
    } else {
      console.log('- Frontend .eslintrc.js is missing');
    }

    const eslint = new ESLint({ useEslintrc: true });
    console.log('\nStarting ESLint test...');
    const results = await eslint.lintFiles(['frontend/src/**/*.{ts,tsx}']);
    console.log('ESLint is working properly!');
    console.log(`Found ${results.length} results.`);

    // Filter to just show files with errors
    const filesWithProblems = results.filter(result => result.errorCount > 0);
    console.log(`${filesWithProblems.length} files have problems.`);

    filesWithProblems.forEach(result => {
      console.log(`\n${result.filePath}: ${result.errorCount} errors`);
      result.messages.forEach(message => {
        console.log(`  Line ${message.line}:${message.column} - ${message.message} (${message.ruleId})`);
      });
    });
  } catch (error) {
    console.error('ESLint error:', error);
    console.error('Error details:', error.stack);

    if (error.message.includes('Cannot find module')) {
      console.log('\nTroubleshooting tips:');
      console.log('1. Check if node_modules is properly installed');
      console.log('2. Make sure package.json has the correct dependencies');
      console.log('3. Try running "yarn install" to reinstall dependencies');
    }
  }
}

main().catch(console.error);
