# Mexel System Production Environment Variables
# Copy this file to .env and update with your actual production values

# Node environment: Set to production
NODE_ENV=production

# Server settings
PORT=3001
LOG_LEVEL=info

# Email configuration
EMAIL_SERVICE=brevo
FROM_EMAIL=<EMAIL>
EMAIL_FROM_NAME=Mexel Energy
EMAIL_TRACKING_DOMAIN=email.mexelenergysustain.com
EMAIL_BATCH_SIZE=100
EMAIL_RETRY_ATTEMPTS=3

# Brevo configuration
BREVO_API_KEY=your-brevo-api-key
BREVO_DEFAULT_SENDER=<EMAIL>
BREVO_TEMPLATE_FOLDER=Mexel
BREVO_LIST_IDS=your-brevo-list-id

# Domain configuration
DOMAIN_NAME=mexelenergysustain.com
WEBSITE_URL=https://www.mexelenergysustain.com
COMPANY_NAME=Mexel Energy Sustain
COMPANY_LOGO_URL=https://www.mexelenergysustain.com/logo.png

# Social media links
SOCIAL_LINKEDIN=https://www.linkedin.com/company/mexel-energy-sustain/
SOCIAL_TWITTER=https://twitter.com/MexelEnergySustain
SOCIAL_FACEBOOK=https://www.facebook.com/MexelEnergySustain
SOCIAL_INSTAGRAM=https://www.instagram.com/mexelenergysustain/

# Contact information
CONTACT_EMAIL=<EMAIL>
CONTACT_PHONE=+27 21 123 4567
CONTACT_ADDRESS=Cape Town, South Africa

# Monitoring configuration
CHECK_INTERVAL=0 9 * * *
TASK_ASSESSMENT_INTERVAL=*/15 * * * *

# Thresholds and scoring
HIGH_PRIORITY_TENDER_SCORE=0.8
PERFORMANCE_THRESHOLD=0.8
HIGH_VALUE_DEAL_THRESHOLD=100000
HIGH_QUALITY_LEAD_SCORE=0.7

# Integration webhooks
ZAPIER_WEBHOOK_URL=your-zapier-webhook-url

# HubSpot configuration
HUBSPOT_API_KEY=your-hubspot-api-key
HUBSPOT_EMAIL_EVENT_TEMPLATE_ID=your-hubspot-email-event-template-id
HUBSPOT_PORTAL_ID=your-hubspot-portal-id
HUBSPOT_WEBHOOK_SECRET=your-hubspot-webhook-secret
HUBSPOT_SYNC_ENABLED=true

# Database configuration
DATABASE_PATH=./data/mexel.db
DB_ENABLE_FOREIGN_KEYS=true
DB_BUSY_TIMEOUT=30000

# Scraping configuration
SCRAPER_ENABLED=true
SCRAPER_TYPE=scrapy
SCRAPER_PROVIDER=scrapy
SCRAPER_API_KEY=your-scraper-api-key

# LinkedIn scraper configuration
LINKEDIN_USERNAME=your-linkedin-username
LINKEDIN_PASSWORD=your-linkedin-password

# DeepSeek configuration (primary AI service)
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_API_BASE_URL=https://api.deepseek.com
DEEPSEEK_API_VERSION=v1
DEEPSEEK_TIMEOUT=30000
DEEPSEEK_CHAT_MODEL=deepseek-chat
DEEPSEEK_CODER_MODEL=deepseek-coder-v2
DEEPSEEK_INSTRUCT_MODEL=deepseek-instruct
DEEPSEEK_DEFAULT_MODEL=deepseek-chat
DEEPSEEK_MAX_RETRIES=3
DEEPSEEK_INITIAL_RETRY_DELAY=1000
DEEPSEEK_MAX_RETRY_DELAY=30000
DEEPSEEK_RATE_LIMIT=60
DEEPSEEK_ENABLE_RATE_LIMITING=true
DEEPSEEK_STREAM_RESPONSES=false

# Ollama configuration (backup AI service)
OLLAMA_HOST=http://localhost:11434
OLLAMA_ENDPOINT=/api/generate
OLLAMA_MODEL=llama3.2
USE_LLAMA_BACKUP=true
LLAMA_BACKUP_MODEL=llama3.2
LLAMA_BACKUP_HOST=http://localhost:11434
LLAMA_BACKUP_ENDPOINT=/api/generate

# Performance targets
LEADS_TARGET=50
TENDER_OPPORTUNITIES_TARGET=5
WEBSITE_TRAFFIC_INCREASE=200
SOCIAL_ENGAGEMENT_INCREASE=150

# Email engagement settings
EMAIL_OPEN_FOLLOW_UP_DELAY=86400000
EMAIL_CLICK_FOLLOW_UP_DELAY=172800000
EMAIL_NO_RESPONSE_DELAY=432000000
EMAIL_MAX_FOLLOW_UPS=3

# Notification settings
NOTIFICATION_EMAIL=<EMAIL>
ENABLE_EMAIL_NOTIFICATIONS=true
