#!/usr/bin/env node

/**
 * WebSocket Connection Tester for Mexel
 *
 * This script tests connectivity to the WebSocket server.
 * Run this script after starting the backend server.
 */

const { io } = require("socket.io-client");
const readline = require("readline");

// ANSI color codes for terminal output
const colors = {
  reset: "\x1b[0m",
  bright: "\x1b[1m",
  dim: "\x1b[2m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  magenta: "\x1b[35m",
  cyan: "\x1b[36m",
  white: "\x1b[37m",
};

const c = (text, color) => `${color}${text}${colors.reset}`;

console.log(c("=============================================", colors.cyan));
console.log(
  c("  Mexel WebSocket Connection Test", colors.bright + colors.cyan)
);
console.log(c("=============================================", colors.cyan));
console.log("");
console.log(c("→ Connecting to WebSocket server...", colors.yellow));

// Connection timeout
const connectionTimeout = setTimeout(() => {
  console.log(c("✕ Connection timed out after 10 seconds", colors.red));
  process.exit(1);
}, 10000);

// Create Socket.IO client
const socket = io("http://localhost:3001", {
  transports: ["websocket", "polling"],
  reconnectionAttempts: 3,
  timeout: 10000,
});

let testsPassed = 0;
let testsFailed = 0;

function runTestSuite() {
  const tests = [
    {
      name: "Generic message",
      event: "message",
      data: "Test message from connection checker",
      responseEvent: "response",
      timeout: 5000,
    },
    {
      name: "Echo test",
      event: "echo",
      data: { text: "Echo test", timestamp: new Date().toISOString() },
      responseEvent: "echo_response",
      timeout: 5000,
    },
    {
      name: "Ping test",
      event: "ping",
      data: null,
      responseEvent: "pong",
      timeout: 5000,
    },
  ];

  let currentTestIndex = 0;

  function runNextTest() {
    if (currentTestIndex >= tests.length) {
      // All tests completed
      finishTesting();
      return;
    }

    const test = tests[currentTestIndex];
    console.log(c(`→ Running test: ${test.name}...`, colors.yellow));

    // Set up response listener
    const responseListener = (response) => {
      clearTimeout(testTimeout);
      console.log(
        c(`✓ Received ${test.responseEvent} response:`, colors.green)
      );
      console.log(c("  " + JSON.stringify(response), colors.dim));
      testsPassed++;

      // Remove listener to avoid conflicts with next tests
      socket.off(test.responseEvent, responseListener);

      // Run next test after a short delay
      setTimeout(() => {
        currentTestIndex++;
        runNextTest();
      }, 500);
    };

    // Set timeout for this test
    const testTimeout = setTimeout(() => {
      console.log(
        c(
          `✕ Test failed: No response for ${test.name} after ${
            test.timeout / 1000
          }s`,
          colors.red
        )
      );
      testsFailed++;
      socket.off(test.responseEvent, responseListener);

      // Continue with next test despite failure
      currentTestIndex++;
      runNextTest();
    }, test.timeout);

    // Register response listener and send test event
    socket.on(test.responseEvent, responseListener);
    socket.emit(test.event, test.data);
    console.log(c(`  Sent ${test.event} event:`, colors.dim));
    console.log(c("  " + JSON.stringify(test.data), colors.dim));
  }

  function finishTesting() {
    console.log("");
    console.log(
      c("=============================================", colors.cyan)
    );
    console.log(c("  Test Results:", colors.bright + colors.white));
    console.log(c(`  ✓ Passed: ${testsPassed}`, colors.green));
    console.log(c(`  ✕ Failed: ${testsFailed}`, colors.red));
    console.log(
      c("=============================================", colors.cyan)
    );

    // Ask user if they want to disconnect
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    rl.question(
      c("\nPress Enter to disconnect and exit...", colors.yellow),
      () => {
        socket.disconnect();
        rl.close();
        process.exit(0);
      }
    );
  }

  // Start the first test
  runNextTest();
}

// Socket event handlers
socket.on("connect", () => {
  clearTimeout(connectionTimeout);
  console.log(c("✓ Connected successfully!", colors.green));
  console.log(c(`  Socket ID: ${socket.id}`, colors.dim));
  console.log("");

  // Start test suite after connection
  setTimeout(runTestSuite, 1000);
});

socket.on("welcome", (data) => {
  console.log(c("✓ Received welcome message:", colors.green));
  console.log(c("  " + JSON.stringify(data), colors.dim));
});

socket.on("disconnect", () => {
  console.log(c("→ Disconnected from server", colors.yellow));
});

socket.on("connect_error", (err) => {
  clearTimeout(connectionTimeout);
  console.log(c("✕ Connection error: " + err.message, colors.red));
  console.log("");
  console.log(c("Troubleshooting tips:", colors.bright + colors.white));
  console.log(c("1. Make sure the backend server is running", colors.white));
  console.log(
    c("2. Check if the server is running on port 3001", colors.white)
  );
  console.log(
    c(
      "3. Verify that Socket.IO is properly configured on the server",
      colors.white
    )
  );
  console.log("");

  process.exit(1);
});

// Handle manual termination
process.on("SIGINT", () => {
  console.log(c("\n→ Test interrupted, disconnecting...", colors.yellow));
  socket.disconnect();
  process.exit(0);
});
