{"name": "mexel", "version": "0.1.0", "private": true, "workspaces": ["frontend", "shared"], "engines": {"node": ">=18.0.0", "npm": ">=10.0.0"}, "scripts": {"start": "npm run start --workspace=@mexel/frontend", "build": "npm run build --workspace=@mexel/frontend", "test": "npm run test:ci --workspace=@mexel/frontend", "test:unit": "jest", "eject": "react-scripts eject", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "tsc": "tsc --noEmit", "setup-typescript": "node setup-ts-sdk.js", "install:all": "npm install && npm install --workspaces"}, "dependencies": {"@babel/core": "7.14.8", "@babel/plugin-syntax-flow": "7.14.5", "@babel/plugin-transform-react-jsx": "7.14.9", "@babel/runtime": "7.14.8", "@testing-library/dom": "8.20.0", "@testing-library/jest-dom": "5.17.0", "@testing-library/react": "13.4.0", "@testing-library/user-event": "13.5.0", "axios": "1.6.2", "better-sqlite3": "^9.6.0", "glob": "^11.0.2", "react": "18.2.0", "react-dom": "18.2.0", "react-scripts": "5.0.1", "web-vitals": "2.1.4"}, "devDependencies": {"@jest/globals": "29.7.0", "@jest/types": "29.6.3", "@types/jest": "29.5.0", "@types/node": "18.15.11", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@typescript-eslint/eslint-plugin": "5.57.1", "@typescript-eslint/parser": "5.57.1", "eslint": "8.38.0", "eslint-config-react-app": "7.0.1", "eslint-import-resolver-typescript": "3.5.5", "eslint-plugin-flowtype": "8.0.3", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "identity-obj-proxy": "3.0.0", "jest": "29.7.0", "jest-environment-node": "29.7.0", "jest-junit": "16.0.0", "jest-watch-typeahead": "2.2.2", "ts-jest": "29.1.1", "typescript": "4.9.5"}, "overrides": {"typescript": "4.9.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "packageManager": "npm@10.9.2"}