# TypeScript Configuration Guide for Mexel

This document explains the TypeScript configuration structure used in the Mexel project.

## Configuration Structure

The project uses a hierarchical TypeScript configuration approach:

1. **Base Configuration**: `tsconfig.base.json` in the project root
2. **Project-Specific Configurations**:
   - Root: `tsconfig.json` (for server/utility scripts)
   - Frontend: `frontend/tsconfig.json`
   - Shared: `shared/tsconfig.json`

## Base Configuration (`tsconfig.base.json`)

The base configuration provides common settings used across all TypeScript projects:

- Target: ES2018
- Module: ESNext
- React JSX support
- Common type checking rules
- Path aliases for imports

This file is extended by all other configurations.

## Root Configuration (`tsconfig.json`)

Configures TypeScript for server-side and utility scripts:

- Extends the base configuration
- Uses CommonJS modules (for Node.js compatibility)
- Outputs declaration files
- Includes specific path mappings for server-side code

## Frontend Configuration (`frontend/tsconfig.json`)

Configures TypeScript for the React frontend:

- Extends the base configuration
- Uses ES5 target for broader browser compatibility
- Includes React-specific settings
- Has frontend-specific path aliases

## Shared Configuration (`shared/tsconfig.json`)

Configures TypeScript for code shared between frontend and backend:

- Extends the base configuration
- Uses CommonJS modules for compatibility
- Generates declaration files for type sharing
- Configures output for library usage

## Path Aliases

The project uses consistent path aliases across configurations:

- `@/*`: Points to the local `src` directory
- `@shared/*`: Points to the shared code
- Additional specialized aliases in the root config

## Building TypeScript Code

Use these commands to build TypeScript code:

- `yarn build`: Build the root project
- `yarn build:all`: Build all projects (root, shared, frontend)
- `yarn build:shared`: Build only the shared package
- `yarn build:frontend`: Build only the frontend package

## Type Checking

Use these commands to check TypeScript without generating output files:

- `yarn check:ts`: Check the root project
- `yarn check:ts:all`: Check all projects
- `yarn check:ts:shared`: Check only the shared package
- `yarn check:ts:frontend`: Check only the frontend package

## Type Definitions

Global type definitions are found in:
- Root: `types.d.ts`
- Shared: `shared/types/`

## Troubleshooting

### Empty Module Files

TypeScript files need to be proper modules to work with the TypeScript compiler. If you get errors like:

```
error TS1208: 'file.ts' cannot be compiled under '--isolatedModules' because it is considered a global script file.
```

You can fix this by:

1. Adding proper imports/exports to the file
2. Adding an empty export statement: `export {};`
3. Running the automated fix script: `yarn fix:ts:modules`

### Type Export Errors

When adding new types to the shared package, make sure to:
1. Export them in their respective type files
2. Update `shared/index.ts` to re-export them

## Best Practices

1. **Add new dependencies**: When adding new libraries, ensure types are installed
2. **Path aliases**: Use the defined path aliases for imports
3. **Shared code**: Place shared types in the shared package
4. **TypeScript version**: All projects use a compatible TypeScript version
5. **Module format**: Ensure all TypeScript files have at least one import or export