# API Connectivity Testing Guide

This guide explains how to test the connectivity between the React frontend and the FastAPI backend.

## Starting the Backend Server

1. Open a terminal and navigate to the project root directory:

   ```bash
   cd /Users/<USER>/Desktop/Mexel
   ```

2. Run the backend server using the provided script:

   ```bash
   ./start-backend.sh
   ```

   Alternatively, you can run it directly:

   ```bash
   cd /Users/<USER>/Desktop/Mexel/backend-python
   python3 run.py
   ```

3. The server should start on port 3001. You should see output indicating that the server is running.

## Testing with the ApiTester Component

1. Make sure your frontend development server is running:

   ```bash
   cd /Users/<USER>/Desktop/Mexel/frontend
   yarn start
   ```

2. Open the browser and navigate to the frontend application (typically at http://localhost:3000)

3. Navigate to the test page (should be the default landing page)

4. Use the "Test Health Endpoint" button to verify basic connectivity

   - If successful, you'll see a JSON response with status "ok"
   - If unsuccessful, you'll see an error message

5. Use the "Test Example Endpoint" button to verify data retrieval
   - If successful, you'll see example data returned from the backend
   - If unsuccessful, you'll see an error message

## Troubleshooting

If you encounter connection issues:

1. **Verify Backend Status:** Make sure the backend server is running on port 3001.

2. **Check Console:** Open your browser's developer tools (F12) and look for any error messages in the console.

3. **CORS Issues:** If you see CORS-related errors, verify that the backend has the correct CORS settings in `app/main.py`.

4. **Network Tab:** In developer tools, check the Network tab to see the actual HTTP requests and responses.

5. **Restart Both Servers:** Sometimes restarting both the frontend and backend servers can resolve connectivity issues.

6. **Verify Routes:** Ensure that the API endpoints in `apiService.ts` match the routes defined in the backend.
