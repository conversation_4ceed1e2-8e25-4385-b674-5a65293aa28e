#!/bin/zsh

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Function to check container logs
check_logs() {
    local service=$1
    echo -e "${YELLOW}Checking $service logs...${NC}"
    docker-compose -f docker-compose.dev.yml logs --tail=100 $service
}

# Function to restart a service
restart_service() {
    local service=$1
    echo -e "${YELLOW}Restarting $service...${NC}"
    docker-compose -f docker-compose.dev.yml restart $service
}

# Function to check service health
check_health() {
    echo -e "${YELLOW}Checking service health...${NC}"
    
    # Check Frontend
    if curl -s http://localhost:3000 > /dev/null; then
        echo -e "${GREEN}Frontend is running${NC}"
    else
        echo -e "${RED}Frontend is not responding${NC}"
    fi
    
    # Check Backend
    if curl -s http://localhost:8000/docs > /dev/null; then
        echo -e "${GREEN}Backend is running${NC}"
    else
        echo -e "${RED}Backend is not responding${NC}"
    fi
    
    # Check Database
    if docker-compose -f docker-compose.dev.yml exec db pg_isready -U postgres > /dev/null 2>&1; then
        echo -e "${GREEN}Database is running${NC}"
    else
        echo -e "${RED}Database is not responding${NC}"
    fi
}

# Function to check resource usage
check_resources() {
    echo -e "${YELLOW}Checking container resource usage...${NC}"
    docker stats --no-stream $(docker-compose -f docker-compose.dev.yml ps -q)
}

# Main menu
case "$1" in
    "logs")
        check_logs $2
        ;;
    "restart")
        restart_service $2
        ;;
    "health")
        check_health
        ;;
    "resources")
        check_resources
        ;;
    *)
        echo -e "${YELLOW}Development Utilities${NC}"
        echo "Usage: ./dev-utils.sh [command] [service]"
        echo ""
        echo "Commands:"
        echo "  logs [service]    - View logs for a service"
        echo "  restart [service] - Restart a service"
        echo "  health           - Check health of all services"
        echo "  resources        - Check resource usage"
        ;;
esac
