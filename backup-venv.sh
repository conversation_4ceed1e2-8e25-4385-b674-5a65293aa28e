#!/bin/bash

# Script to backup Python dependencies before recreating virtual environment
# Created on May 26, 2025

echo "===== Backing up Python dependencies ====="

# Check if we're in the right directory
if [ ! -d "./backend-python" ]; then
  echo "Error: Must be run from the Mexel project root directory"
  exit 1
fi

# Create backup directory
BACKUP_DIR="./backend-python/venv-backup-$(date +%Y%m%d%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "1. Creating backup in $BACKUP_DIR..."

# Backup current requirements
if [ -f "./backend-python/requirements.txt" ]; then
  cp ./backend-python/requirements.txt "$BACKUP_DIR/"
  echo "   Backed up requirements.txt"
fi

# Backup all custom modules that might be in the existing venv
if [ -d "./backend-python/venv/lib/python3.11/site-packages/app" ]; then
  mkdir -p "$BACKUP_DIR/site-packages"
  cp -r ./backend-python/venv/lib/python3.11/site-packages/app "$BACKUP_DIR/site-packages/"
  echo "   Backed up app module from site-packages"
fi

# Create a complete freeze of current dependencies
if [ -d "./backend-python/venv" ]; then
  cd ./backend-python
  source venv/bin/activate
  pip freeze > "$BACKUP_DIR/pip-freeze.txt"
  deactivate
  cd ..
  echo "   Created complete pip freeze backup"
fi

echo ""
echo "===== Backup completed ====="
echo "You can now run the fix script with:"
echo "./fix-venv-paths.sh"
echo ""
echo "If you need to restore packages later, you can use:"
echo "pip install -r $BACKUP_DIR/pip-freeze.txt"
