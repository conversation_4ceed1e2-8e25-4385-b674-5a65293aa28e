#!/bin/bash

# <PERSON><PERSON>t to start the backend application

# Set the script name
SCRIPT_NAME="start-backend.sh"

# Check if the script exists
if [ ! -f "$SCRIPT_NAME" ]; then
  echo "Error: Script '$SCRIPT_NAME' not found."
  exit 1
fi

# Change the script's permissions to make it executable
chmod +x "$SCRIPT_NAME"

# Check if the chmod command was successful
if [ $? -ne 0 ]; then
  echo "Error: Failed to change permissions of '$SCRIPT_NAME'."
  exit 1
fi

# Navigate to backend directory
cd ./backend-python

# Check if directory change was successful
if [ $? -ne 0 ]; then
  echo "Error: Failed to change directory to backend-python. Does it exist?"
  exit 1
fi

# Activate virtual environment if it exists
if [ -d "venv311" ]; then
  source venv311/bin/activate
  echo "Activated Python virtual environment"
fi

# Run the backend application
# Try with python3 first (common on macOS), fall back to python if needed
if command -v python3 &>/dev/null; then
  echo "Using python3 to start the application"
  python3 run.py
elif command -v python &>/dev/null; then
  echo "Using python to start the application"
  python run.py
else
  echo "Error: Neither python nor python3 commands are available. Please install Python."
  exit 1
fi

# Check if the command was successful
if [ $? -ne 0 ]; then
  echo "Error: Failed to start the backend application."
  exit 1
fi

echo "Backend started successfully."
