# Starting Mexel Development Servers

This guide provides detailed instructions on how to start both the backend and frontend development servers for the Mexel application.

## Starting the Backend Server

1. Open a new terminal window
2. Navigate to the project root directory:

   ```bash
   cd /Users/<USER>/Desktop/Mexel
   ```

3. Run the backend server using the start script:

   ```bash
   ./start-backend.sh
   ```

   Alternatively, you can start it directly with Python:

   ```bash
   cd /Users/<USER>/Desktop/Mexel/backend-python
   python3 run.py
   ```

4. You should see output indicating that the server is running on port 3001.

## Starting the Frontend Server

1. Open a new terminal window (different from the backend terminal)
2. Navigate to the frontend directory:

   ```bash
   cd /Users/<USER>/Desktop/Mexel/frontend
   ```

3. Start the React development server:

   ```bash
   yarn start
   ```

4. The frontend development server should start and automatically open a browser window to http://localhost:3000

## Verifying Server Status

### Backend Server

To verify the backend server is running:

```bash
curl http://localhost:3001/health
```

You should see a response like:

```json
{ "status": "ok", "message": "API is operational", "version": "1.0.0" }
```

### Frontend Server

The frontend server is running if:

- You can access http://localhost:3000 in your browser
- You see the Mexel Frontend page with the API Tester component

## Testing API Connectivity

1. Navigate to http://localhost:3000 in your browser
2. You should see the Minimal Frontend page with the API Tester component
3. Click the "Test Health Endpoint" button to test connectivity to the backend
4. Click the "Test Example Endpoint" button to test data retrieval from the backend

## Troubleshooting

If you encounter issues:

1. **Backend not starting:**

   - Check if Python and required dependencies are installed
   - Look for error messages in the terminal output
   - Ensure port 3001 is not already in use

2. **Frontend not starting:**

   - Ensure Node.js and Yarn are installed
   - Check for any error messages in the terminal
   - Verify that all dependencies are installed with `yarn install`

3. **API connectivity issues:**
   - Ensure both servers are running
   - Check browser console for any CORS or network errors
   - Verify that the backend server URL is correctly set in the frontend code
