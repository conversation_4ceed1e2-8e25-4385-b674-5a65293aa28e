name: E2E Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        cache: 'yarn'
    
    - name: Install dependencies
      run: yarn install
    
    - name: Install Playwright
      run: yarn playwright install --with-deps
    
    - name: Start services
      run: |
        docker-compose -f docker-compose.dev.yml up -d
        yarn wait-on http://localhost:3000
    
    - name: Run Playwright tests
      run: yarn test:e2e
      env:
        CI: true
    
    - uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30
