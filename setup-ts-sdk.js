// setup-ts-sdk.js
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Paths
const rootDir = __dirname;
const vscodeDir = path.join(rootDir, '.vscode');
const settingsPath = path.join(vscodeDir, 'settings.json');
const nodeModulesPath = path.join(rootDir, 'node_modules');

// Ensure .vscode directory exists
if (!fs.existsSync(vscodeDir)) {
  fs.mkdirSync(vscodeDir, { recursive: true });
}

// Read existing settings or create new ones
let settings = {};
if (fs.existsSync(settingsPath)) {
  try {
    const settingsData = fs.readFileSync(settingsPath, 'utf8');
    settings = JSON.parse(settingsData);
  } catch (e) {
    console.error('Error parsing settings.json:', e);
  }
}

// Update settings
const updatedSettings = {
  ...settings,
  "eslint.nodePath": "node_modules",
  "eslint.workingDirectories": [
    { "directory": ".", "changeProcessCWD": true },
    { "directory": "./frontend", "changeProcessCWD": true },
    { "directory": "./shared", "changeProcessCWD": true }
  ],
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "typescript.tsserver.experimental.enableProjectDiagnostics": true,
  "typescript.disableAutomaticTypeAcquisition": false,
  "typescript.tsserver.maxTsServerMemory": 4096,
  "typescript.tsserver.log": "off",
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.enabled": true,
  "typescript.tsserver.useSyntaxServer": "auto"
};

// Write updated settings
try {
  fs.writeFileSync(settingsPath, JSON.stringify(updatedSettings, null, 2));
  console.log('Updated .vscode/settings.json successfully.');
} catch (e) {
  console.error('Error writing settings.json:', e);
}

// Check for missing ESLint plugins
console.log('Checking for missing ESLint plugins...');
const requiredPlugins = [
  'eslint-plugin-import',
  'eslint-plugin-react',
  'eslint-plugin-react-hooks',
  'eslint-import-resolver-typescript'
];

const missingPlugins = [];
for (const plugin of requiredPlugins) {
  const pluginPath = path.join(nodeModulesPath, plugin);
  if (!fs.existsSync(pluginPath)) {
    missingPlugins.push(plugin);
  }
}

if (missingPlugins.length > 0) {
  console.log(`Installing missing ESLint plugins: ${missingPlugins.join(', ')}`);
  try {
    execSync(`yarn add --dev ${missingPlugins.join(' ')}`, { stdio: 'inherit' });
    console.log('Successfully installed missing plugins.');
  } catch (e) {
    console.error('Error installing plugins:', e);
  }
} else {
  console.log('All required ESLint plugins are installed.');
}

console.log('Setup complete. Please restart VS Code for the changes to take effect.');
