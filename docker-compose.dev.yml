version: '3.9'

services:
  db:
    image: postgres:15
    container_name: mexel_db_dev
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: mexel
    ports:
      - '5433:5432'
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres -d mexel']
      interval: 5s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend-python
      dockerfile: Dockerfile
    container_name: mexel_backend_dev
    ports:
      - '8000:8000'
    environment:
      - DATABASE_URL=**************************************/mexel
      - PYTHONUNBUFFERED=1
      - DEBUG=1
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./backend-python:/app
      - ./backend-python/static:/app/static
      # Don't override the site-packages directory where Python packages are installed
      - /app/venv/lib/python3.11/site-packages
    command: ['uvicorn', 'app.main:app', '--host', '0.0.0.0', '--port', '8000', '--reload']

  frontend:
    build:
      context: ./frontend
      target: dev
      dockerfile: Dockerfile
    container_name: mexel_frontend_dev
    ports:
      - '3000:3000'
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000/ws
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    volumes:
      # Mount everything except .yarnrc.yml
      - ./frontend/.yarn:/app/.yarn:delegated
      - ./frontend/public:/app/public:delegated
      - ./frontend/src:/app/src:delegated
      - ./frontend/package.json:/app/package.json:delegated
      - ./frontend/tsconfig.json:/app/tsconfig.json:delegated
      - ./tsconfig.base.json:/app/tsconfig.base.json:delegated
      - ./frontend/nginx.conf:/app/nginx.conf:delegated
      - frontend_node_modules:/app/node_modules
    command: ['yarn', 'start']
    depends_on:
      - backend

volumes:
  postgres_data_dev:
  frontend_node_modules:
