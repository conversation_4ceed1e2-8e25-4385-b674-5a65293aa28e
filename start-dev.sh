#!/bin/bash

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting Mexel Development Environment...${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Docker is not running! Please start Docker and try again.${NC}"
    exit 1
fi

# Function to check if containers are running
check_containers() {
    if docker ps | grep -q "mexel_.*_dev"; then
        return 0
    else
        return 1
    fi
}

# Function to stop containers
stop_containers() {
    echo -e "${YELLOW}Stopping existing containers...${NC}"
    docker-compose -f docker-compose.dev.yml down
}

# Check if containers are already running
if check_containers; then
    echo -e "${YELLOW}Development containers are already running.${NC}"
    read -p "Do you want to restart them? (y/n): " answer
    if [[ "$answer" == "y" ]]; then
        stop_containers
    else
        echo -e "${YELLOW}Using existing containers.${NC}"
        exit 0
    fi
fi

# Build and start containers
echo -e "${YELLOW}Building and starting services...${NC}"
docker-compose -f docker-compose.dev.yml up --build -d

# Check if services started successfully
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Development environment started successfully!${NC}"
    echo -e "${GREEN}================================${NC}"
    echo -e "${GREEN}Frontend: http://localhost:3000${NC}"
    echo -e "${GREEN}Backend API: http://localhost:8000${NC}"
    echo -e "${GREEN}Database: localhost:5432${NC}"
    echo -e "\nUseful commands:"
    echo -e "${YELLOW}View logs:${NC} docker-compose -f docker-compose.dev.yml logs -f"
    echo -e "${YELLOW}Stop services:${NC} docker-compose -f docker-compose.dev.yml down"
    echo -e "${YELLOW}Restart specific service:${NC} docker-compose -f docker-compose.dev.yml restart [service_name]"
else
    echo -e "${RED}Failed to start services. Check docker-compose logs for details.${NC}"
    exit 1
fi

exit 0
