{"extends": "../.eslintrc.json", "parserOptions": {"project": "./tsconfig.json"}, "rules": {"@typescript-eslint/explicit-module-boundary-types": "warn", "@typescript-eslint/no-explicit-any": "error", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "pathGroups": [{"pattern": "@shared/**", "group": "internal", "position": "after"}], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}]}, "overrides": [{"files": ["**/*.ts"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "ignoreRestSiblings": true}]}}, {"files": ["**/*.spec.ts", "**/*.test.ts"], "env": {"jest": true}, "rules": {"@typescript-eslint/no-explicit-any": "off"}}]}