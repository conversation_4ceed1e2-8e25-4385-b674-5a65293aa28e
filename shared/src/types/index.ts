// Shared types for the Mexel application

// Database-related types
export interface DatabaseRecord {
  id: string;
  created_at: string;
  updated_at: string;
}

// API Response types
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

// Agent types
export interface Agent {
  id: string;
  name: string;
  status: 'active' | 'inactive' | 'pending';
  type: string;
  performance: {
    efficiency: number;
    accuracy: number;
    throughput: number;
  };
  created_at: string;
  updated_at: string;
}

export type AgentStatus = Agent['status'];

export interface AgentMetrics extends Agent {
  avgResponseTime: number;
  completedTasks: number;
  failedTasks: number;
  cpuUsage?: number;
  memoryUsage?: number;
  role?: string; // Added role
  successRate?: number; // Added successRate
  errorRate?: number; // Added errorRate
  latency?: number; // Added latency
  lastCheckTime?: string; // Added lastCheckTime
}

// Lead types
export interface Lead {
  id: string;
  name: string;
  email: string;
  company?: string;
  status: 'new' | 'contacted' | 'qualified' | 'converted' | 'lost';
  source: string;
  created_at: string;
  updated_at: string;
}

// Tender types
export interface Tender {
  id: string;
  title: string;
  description: string;
  deadline: string;
  value: number;
  status: 'open' | 'in_progress' | 'submitted' | 'won' | 'lost';
  created_at: string;
  updated_at: string;
}

// Email types
export interface Email {
  id: string;
  subject: string;
  body: string;
  recipient: string;
  sender: string;
  status: 'draft' | 'sent' | 'delivered' | 'failed';
  created_at: string;
  sent_at?: string;
}

// Analytics types
export interface AnalyticsData {
  metric: string;
  value: number;
  timestamp: string;
  metadata?: Record<string, any>;
}

// Chart data types
export interface ChartDataPoint {
  x: string | number;
  y: number;
  label?: string;
}

export interface ChartSeries {
  name: string;
  data: ChartDataPoint[];
  color?: string;
}

// WebSocket message types
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  id?: string;
}

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user' | 'viewer';
  permissions: string[];
  created_at: string;
  last_login?: string;
}

// Configuration types
export interface AppConfig {
  environment: 'development' | 'staging' | 'production';
  apiUrl: string;
  websocketUrl: string;
  features: {
    [key: string]: boolean;
  };
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// Notification types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// Task types
export interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignee?: string;
  created_at: string;
  due_date?: string;
  completed_at?: string;
}

// SEO types
export interface SEOInsight {
  id: string;
  url: string;
  title: string;
  description: string;
  keywords: string[];
  score: number;
  issues: string[];
  recommendations: string[];
  created_at: string;
}

// Pagination types
export interface PaginationParams {
  page: number;
  limit: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Filter types
export interface FilterOption {
  label: string;
  value: string | number;
  count?: number;
}

export interface FilterGroup {
  key: string;
  label: string;
  options: FilterOption[];
  type: 'single' | 'multiple' | 'range' | 'date';
}

// Export utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
