// This is a minimal type declaration file for recharts
// It's a workaround to prevent TypeScript compilation errors
declare module 'recharts' {
  // Add minimal type definitions as needed
  export const LineChart: any;
  export const Line: any;
  export const AreaChart: any;
  export const Area: any;
  export const BarChart: any;
  export const Bar: any;
  export const PieChart: any;
  export const Pie: any;
  export const XAxis: any;
  export const YAxis: any;
  export const CartesianGrid: any;
  export const Tooltip: any;
  export const Legend: any;
  export const ResponsiveContainer: any;
  // Add other components as needed
}
