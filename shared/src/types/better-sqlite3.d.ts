// Type declarations for better-sqlite3

declare module 'better-sqlite3' {
  interface Database {
    prepare<BindParameters extends unknown[] | {} = unknown[]>(source: string): Statement<BindParameters>;
    exec(source: string): this;
    close(): this;
    defaultSafeIntegers(value?: boolean): boolean;
    loadExtension(path: string, entryPoint?: string): this;
    serialize(options?: SerializeOptions): Buffer;
    function(name: string, fn: (...params: any[]) => any): this;
    function(name: string, options: FunctionOptions, fn: (...params: any[]) => any): this;
    aggregate(name: string, options: AggregateOptions): this;
    pragma(source: string, options?: PragmaOptions): any;
    backup(destinationFile: string, options?: BackupOptions): Promise<BackupMetadata>;
    table(name: string, options?: TableOptions): this;
    transaction<F extends (...args: any[]) => any>(fn: F): Transaction<F>;
    memory: boolean;
    readonly: boolean;
    name: string;
    open: boolean;
    inTransaction: boolean;
  }

  interface DatabaseConstructor {
    new (filename: string, options?: Options): Database;
    (filename: string, options?: Options): Database;
  }

  interface Statement<BindParameters extends unknown[] | {} = unknown[]> {
    database: Database;
    source: string;
    reader: boolean;
    readonly: boolean;
    busy: boolean;

    run(...params: BindParameters extends unknown[] ? BindParameters : [BindParameters]): RunResult;
    get(...params: BindParameters extends unknown[] ? BindParameters : [BindParameters]): any;
    all(...params: BindParameters extends unknown[] ? BindParameters : [BindParameters]): any[];
    iterate(...params: BindParameters extends unknown[] ? BindParameters : [BindParameters]): IterableIterator<any>;
    pluck(toggleState?: boolean): this;
    expand(toggleState?: boolean): this;
    raw(toggleState?: boolean): this;
    bind(...params: BindParameters extends unknown[] ? BindParameters : [BindParameters]): this;
    columns(): ColumnDefinition[];
    safeIntegers(toggleState?: boolean): this;
  }

  interface Transaction<F extends (...args: any[]) => any = (...args: any[]) => any> {
    (...args: Parameters<F>): ReturnType<F>;
    default(...args: Parameters<F>): ReturnType<F>;
    deferred(...args: Parameters<F>): ReturnType<F>;
    immediate(...args: Parameters<F>): ReturnType<F>;
    exclusive(...args: Parameters<F>): ReturnType<F>;
  }

  interface RunResult {
    changes: number;
    lastInsertRowid: number | bigint;
  }

  interface ColumnDefinition {
    name: string;
    column: string | null;
    table: string | null;
    database: string | null;
    type: string | null;
  }

  interface Options {
    memory?: boolean;
    readonly?: boolean;
    fileMustExist?: boolean;
    timeout?: number;
    verbose?: (message?: any, ...additionalArgs: any[]) => void;
    nativeBinding?: string;
  }

  interface SerializeOptions {
    attached?: string;
  }

  interface FunctionOptions {
    varargs?: boolean;
    deterministic?: boolean;
    safeIntegers?: boolean;
    directOnly?: boolean;
  }

  interface AggregateOptions {
    start?: any;
    step: (total: any, next: any) => any;
    inverse?: (total: any, dropped: any) => any;
    result?: (total: any) => any;
    safeIntegers?: boolean;
    deterministic?: boolean;
    directOnly?: boolean;
    varargs?: boolean;
  }

  interface PragmaOptions {
    simple?: boolean;
  }

  interface BackupOptions {
    progress?: (info: BackupMetadata) => number;
  }

  interface BackupMetadata {
    totalPages: number;
    remainingPages: number;
  }

  interface TableOptions {
    column?: string;
    parameters?: any[];
  }

  const Database: DatabaseConstructor;
  export = Database;
}
