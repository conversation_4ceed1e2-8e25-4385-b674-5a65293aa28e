// Node.js type declarations for shared workspace

declare namespace NodeJS {
  interface Timeout {
    ref(): this;
    unref(): this;
    hasRef(): boolean;
    refresh(): this;
    [Symbol.toPrimitive](): number;
  }

  interface Immediate {
    ref(): this;
    unref(): this;
    hasRef(): boolean;
    _onImmediate: Function;
  }

  interface Process extends EventEmitter {
    stdout: WriteStream;
    stderr: WriteStream;
    stdin: ReadStream;
    argv: string[];
    execArgv: string[];
    execPath: string;
    abort(): never;
    chdir(directory: string): void;
    cwd(): string;
    /**
     * The `process.hrtime()` method returns the current high-resolution real time in a `[seconds, nanoseconds]` tuple Array.
     * It is relative to an arbitrary time in the past. It is not related to the time of day and therefore not subject to clock drift.
     * The primary use is for measuring performance between intervals.
     * @param time An optional `[seconds, nanoseconds]` tuple Array returned from a previous `process.hrtime()` call to diff with the current time.
     */
    hrtime(time?: [number, number]): [number, number];
    exit(code?: number): never;
    getgid(): number;
    setgid(id: number | string): void;
    getuid(): number;
    setuid(id: number | string): void;
    version: string;
    versions: ProcessVersions;
    config: {
      target_defaults: {
        cflags: any[];
        default_configuration: string;
        defines: string[];
        include_dirs: string[];
        libraries: string[];
      };
      variables: {
        clang: number;
        host_arch: string;
        node_install_npm: boolean;
        node_install_waf: boolean;
        node_prefix: string;
        node_shared_openssl: boolean;
        node_shared_v8: boolean;
        node_shared_zlib: boolean;
        node_use_dtrace: boolean;
        node_use_etw: boolean;
        node_use_openssl: boolean;
        target_arch: string;
        v8_no_strict_aliasing: number;
        v8_use_snapshot: boolean;
        visibility: string;
      };
    };
    kill(pid: number, signal?: string | number): boolean;
    pid: number;
    ppid: number;
    title: string;
    arch: string;
    platform: Platform;
    mainModule?: Module;
    memoryUsage(): MemoryUsage;
    cpuUsage(previousValue?: CpuUsage): CpuUsage;
    nextTick(callback: Function, ...args: any[]): void;
    umask(mask?: number): number;
    uptime(): number;
    hrtime(): [number, number];
    domain: Domain;
    send?(message: any, sendHandle?: any, options?: { swallowErrors?: boolean; }, callback?: (error: Error | null) => void): boolean;
    disconnect(): void;
    connected: boolean;
    allowedNodeEnvironmentFlags: ReadonlySet<string>;
    env: ProcessEnv;
    features: {
      inspector: boolean;
      debug: boolean;
      uv: boolean;
      ipv6: boolean;
      tls_alpn: boolean;
      tls_sni: boolean;
      tls_ocsp: boolean;
      tls: boolean;
    };
    debugPort: number;
  }

  interface ProcessVersions {
    http_parser: string;
    node: string;
    v8: string;
    ares: string;
    uv: string;
    zlib: string;
    modules: string;
    openssl: string;
  }

  interface MemoryUsage {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
    arrayBuffers: number;
  }

  interface CpuUsage {
    user: number;
    system: number;
  }

  interface ProcessEnv {
    [key: string]: string | undefined;
  }

  type Platform = 'aix' | 'android' | 'darwin' | 'freebsd' | 'linux' | 'openbsd' | 'sunos' | 'win32' | 'cygwin' | 'netbsd';

  interface WriteStream {
    readonly writable: boolean;
    write(buffer: Uint8Array | string, cb?: (err?: Error | null) => void): boolean;
    write(str: string, encoding?: BufferEncoding, cb?: (err?: Error | null) => void): boolean;
    end(cb?: () => void): void;
    end(data: string | Uint8Array, cb?: () => void): void;
    end(str: string, encoding?: BufferEncoding, cb?: () => void): void;
  }

  interface ReadStream {
    readonly readable: boolean;
    read(size?: number): string | Buffer;
    setEncoding(encoding: BufferEncoding): this;
    pause(): this;
    resume(): this;
    isPaused(): boolean;
  }

  interface Domain {
    run<T>(fn: (...args: any[]) => T, ...args: any[]): T;
    add(emitter: EventEmitter | Timer): void;
    remove(emitter: EventEmitter | Timer): void;
    bind<T extends Function>(cb: T): T;
    intercept<T extends Function>(cb: T): T;
  }

  interface Timer {
    hasRef(): boolean;
    ref(): this;
    unref(): this;
  }

  interface Module {
    exports: any;
    require: Require;
    id: string;
    filename: string;
    loaded: boolean;
    parent: Module | null;
    children: Module[];
    paths: string[];
  }

  interface Require {
    (id: string): any;
    resolve: RequireResolve;
    cache: any;
    extensions: any;
    main: Module | undefined;
  }

  interface RequireResolve {
    (id: string, options?: { paths?: string[]; }): string;
    paths(request: string): string[] | null;
  }
}

declare var process: NodeJS.Process;
declare var global: NodeJS.Global;
declare var __filename: string;
declare var __dirname: string;

type BufferEncoding = "ascii" | "utf8" | "utf-8" | "utf16le" | "ucs2" | "ucs-2" | "base64" | "latin1" | "binary" | "hex";

interface Buffer extends Uint8Array {
  write(string: string, offset?: number, length?: number, encoding?: BufferEncoding): number;
  toString(encoding?: BufferEncoding, start?: number, end?: number): string;
  toJSON(): { type: 'Buffer'; data: number[] };
  equals(otherBuffer: Uint8Array): boolean;
  compare(otherBuffer: Uint8Array, targetStart?: number, targetEnd?: number, sourceStart?: number, sourceEnd?: number): number;
  copy(targetBuffer: Uint8Array, targetStart?: number, sourceStart?: number, sourceEnd?: number): number;
  slice(start?: number, end?: number): Buffer;
  writeUIntLE(value: number, offset: number, byteLength: number): number;
  writeUIntBE(value: number, offset: number, byteLength: number): number;
  writeIntLE(value: number, offset: number, byteLength: number): number;
  writeIntBE(value: number, offset: number, byteLength: number): number;
  readUIntLE(offset: number, byteLength: number): number;
  readUIntBE(offset: number, byteLength: number): number;
  readIntLE(offset: number, byteLength: number): number;
  readIntBE(offset: number, byteLength: number): number;
  readUInt8(offset: number): number;
  readUInt16LE(offset: number): number;
  readUInt16BE(offset: number): number;
  readUInt32LE(offset: number): number;
  readUInt32BE(offset: number): number;
  readInt8(offset: number): number;
  readInt16LE(offset: number): number;
  readInt16BE(offset: number): number;
  readInt32LE(offset: number): number;
  readInt32BE(offset: number): number;
  readFloatLE(offset: number): number;
  readFloatBE(offset: number): number;
  readDoubleLE(offset: number): number;
  readDoubleBE(offset: number): number;
  writeUInt8(value: number, offset: number): number;
  writeUInt16LE(value: number, offset: number): number;
  writeUInt16BE(value: number, offset: number): number;
  writeUInt32LE(value: number, offset: number): number;
  writeUInt32BE(value: number, offset: number): number;
  writeInt8(value: number, offset: number): number;
  writeInt16LE(value: number, offset: number): number;
  writeInt16BE(value: number, offset: number): number;
  writeInt32LE(value: number, offset: number): number;
  writeInt32BE(value: number, offset: number): number;
  writeFloatLE(value: number, offset: number): number;
  writeFloatBE(value: number, offset: number): number;
  writeDoubleLE(value: number, offset: number): number;
  writeDoubleBE(value: number, offset: number): number;
  fill(value: string | Uint8Array | number, offset?: number, end?: number, encoding?: BufferEncoding): this;
  indexOf(value: string | number | Uint8Array, byteOffset?: number, encoding?: BufferEncoding): number;
  lastIndexOf(value: string | number | Uint8Array, byteOffset?: number, encoding?: BufferEncoding): number;
  includes(value: string | number | Buffer, byteOffset?: number, encoding?: BufferEncoding): boolean;
  keys(): IterableIterator<number>;
  values(): IterableIterator<number>;
  entries(): IterableIterator<[number, number]>;
  swap16(): Buffer;
  swap32(): Buffer;
  swap64(): Buffer;
}

declare module "events" {
  export class EventEmitter {
    addListener(event: string | symbol, listener: (...args: any[]) => void): this;
    on(event: string | symbol, listener: (...args: any[]) => void): this;
    once(event: string | symbol, listener: (...args: any[]) => void): this;
    removeListener(event: string | symbol, listener: (...args: any[]) => void): this;
    off(event: string | symbol, listener: (...args: any[]) => void): this;
    removeAllListeners(event?: string | symbol): this;
    setMaxListeners(n: number): this;
    getMaxListeners(): number;
    listeners(event: string | symbol): Function[];
    rawListeners(event: string | symbol): Function[];
    emit(event: string | symbol, ...args: any[]): boolean;
    listenerCount(event: string | symbol): number;
    prependListener(event: string | symbol, listener: (...args: any[]) => void): this;
    prependOnceListener(event: string | symbol, listener: (...args: any[]) => void): this;
    eventNames(): Array<string | symbol>;
  }
  export = EventEmitter;
}
