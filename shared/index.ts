// Export all types from the shared package
export * from './types/agent';
export * from './types/analytics';
export * from './types/content';
export * from './types/email';
export * from './types/lead';
export * from './types/tender';

// Re-export specific types as needed for backward compatibility
export type { AgentRole, AgentMetrics, TaskResult } from './types/agent';
export type { AnalyticsData } from './types/analytics';
export type { Lead, LeadStatus } from './types/lead';
export type { Tender, TenderStatus } from './types/tender';