export enum LeadStatus {
  NEW = 'NEW',
  CONTACTED = 'CONTACTED'
}

export interface Lead {
  id: string;
  email: string;
  status: LeadStatus;
}

// Extended interface with additional properties for backward compatibility
export interface ExtendedLead extends Lead {
    firstName?: string;
    lastName?: string;
    company?: string;
    companyName?: string;
    contactName?: string; // Optional since it's computed by DB
    position?: string;
    industry?: string;
    lastContactDate?: Date;
    recentActivity?: string;
    companySize?: string;
    contactRole?: string;
    region?: string;
    systems?: string[];
}