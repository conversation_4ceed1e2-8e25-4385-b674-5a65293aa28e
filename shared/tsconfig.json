{"extends": "../tsconfig.json", "compilerOptions": {"composite": true, "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "declaration": true, "outDir": "./dist", "rootDir": ".", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "typeRoots": ["./node_modules/@types", "../node_modules/@types", "./src/types", "../types"], "types": ["node"]}, "include": ["./**/*", "./src/types/**/*.d.ts"], "exclude": ["node_modules", "dist"]}