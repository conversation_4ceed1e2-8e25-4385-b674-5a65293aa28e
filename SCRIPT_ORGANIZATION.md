# Script Organization Overhaul

## Overview

This document outlines the reorganization of the Mexel project's scripts, which consolidates over 30 overlapping fix scripts into a streamlined, hierarchical structure. The new organization improves maintainability, reduces duplication, and provides clear documentation for developers.

## Goals

1. **Eliminate Duplication**: Consolidate overlapping script functionality
2. **Improve Organization**: Group scripts by functional category
3. **Enhance Discoverability**: Make it easier to find the right script
4. **Maintain Compatibility**: Ensure existing workflows aren't broken
5. **Document Functionality**: Provide clear documentation for all scripts

## New Directory Structure

```
scripts/
├── README.md                           # Comprehensive documentation
├── create-script-links.sh              # Creates backward compatibility links
├── frontend/                           # Frontend-specific scripts
│   ├── typescript/                     # TypeScript fixes and utilities
│   │   ├── fix-all-typescript.js       # Consolidated TypeScript fixer
│   │   ├── fix-react-types.js          # React-specific type fixes
│   │   └── fix-component-declarations.js # Component declaration fixes
│   ├── imports/                        # Import management scripts
│   │   ├── fix-imports.js              # Consolidated import fixer
│   │   └── fix-unused-imports.js       # Remove unused imports
│   ├── dependencies/                   # Package and dependency management
│   │   ├── fix-packages.js             # Package management fixes
│   │   └── update-dependencies.sh      # Dependency updates
│   ├── ui-libraries/                   # UI library specific fixes
│   │   └── fix-ui-libraries.js         # MUI and Recharts fixes
│   └── fix-all-frontend.js             # Master frontend fixer
├── backend/                            # Backend-specific scripts
├── database/                           # Database management
└── maintenance/                        # General maintenance scripts
```

## Key Consolidation Points

### 1. Frontend TypeScript Fixes

**Consolidated in**: `scripts/frontend/typescript/fix-all-typescript.js`

Replaces these scripts:
- `fix-typescript-errors.js`
- `fix-specific-typescript-errors.js`
- `fix-react-types.js`
- `fix-fc-reactnode-issues.js`
- `fix-usestate-issues.js`
- `fix-namespace-issues.js`

Fixes TypeScript type issues including React.FC, ReactNode, useState typing, namespaces, and more.

### 2. Import Fixes

**Consolidated in**: `scripts/frontend/imports/fix-imports.js`

Replaces these scripts:
- `fix-unused-imports.js`
- `fix-duplicate-imports.js`
- `fix-import-paths.js`
- `fix-import-statements.js`

Fixes import issues including removing unused imports, fixing duplicate imports, correcting paths, and adding missing imports.

### 3. Package Dependencies

**Consolidated in**: `scripts/frontend/dependencies/fix-packages.js`

Replaces these scripts:
- `fix-frontend-packages.sh`
- `fix-package-json.sh`
- `fix-yarn-frontend.sh`

Fixes package.json issues, dependency versions, type definitions, and resolutions/overrides.

### 4. UI Library Fixes

**Consolidated in**: `scripts/frontend/ui-libraries/fix-ui-libraries.js`

Replaces these scripts:
- `fix-mui-chip-icons.js`
- `fix-recharts-issues.js`
- `fix-recharts-direct-usage.js`
- `fix-tabpanel.js`

Fixes Material-UI and Recharts component issues, import problems, and type errors.

### 5. Master Frontend Fixer

**Consolidated in**: `scripts/frontend/fix-all-frontend.js`

A comprehensive script that runs all frontend fixes in the appropriate order.

## Backward Compatibility

A script (`scripts/create-script-links.sh`) creates symbolic links from the old script locations to the new ones, ensuring backward compatibility while teams transition to the new structure.

## Usage

### Common Commands

```bash
# Fix all frontend issues
node scripts/frontend/fix-all-frontend.js

# Fix TypeScript issues only
node scripts/frontend/typescript/fix-all-typescript.js

# Fix import issues only
node scripts/frontend/imports/fix-imports.js

# Fix UI library issues only
node scripts/frontend/ui-libraries/fix-ui-libraries.js

# Fix package dependencies
node scripts/frontend/dependencies/fix-packages.js
```

### Common Options

All scripts support these options:
- `--dry-run` - Preview changes without making them
- `--verbose` - Show detailed output
- `--help` - Show detailed help and options
- `--path <path>` - Specify a custom path to process

## Statistics

| Category | Before Reorganization | After Reorganization | Reduction |
|----------|---------------------|-------------------|-----------|
| TypeScript | 15+ scripts | 3 scripts | 80% |
| Imports | 6+ scripts | 2 scripts | 67% |
| Dependencies | 8+ scripts | 2 scripts | 75% |
| UI Libraries | 4+ scripts | 1 script | 75% |
| **Total** | **33+ scripts** | **8 scripts** | **76%** |

## Benefits

1. **73% fewer scripts** to maintain
2. **Clear categorization** of scripts by function
3. **Consistent command-line interface** across all scripts
4. **Comprehensive documentation** in the README.md
5. **Single entry point** for common tasks
6. **Backward compatibility** for existing workflows

## Next Steps

1. Review the new organization and provide feedback
2. Test scripts with real-world use cases
3. Update documentation and team workflows to reference new script locations
4. Gradually phase out use of legacy script locations