# API Endpoints Roadmap

This document outlines the planned API endpoints for the Mexel application.

## Current Endpoints

### General Endpoints

- `GET /health` - Health check endpoint
- `GET /api/example` - Example data endpoint

## Planned Endpoints

### Authentication Endpoints

- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/refresh` - Refresh authentication token
- `GET /api/auth/me` - Get current user information

### Dashboard Endpoints

- `GET /api/dashboard/minimal` - Get minimal dashboard data
- `GET /api/dashboard/seo` - Get SEO dashboard data
- `GET /api/dashboard/linkedin` - Get LinkedIn dashboard data
- `GET /api/dashboard/email` - Get email dashboard data

### LinkedIn Endpoints

- `GET /api/linkedin/profile` - Get LinkedIn profile data
- `GET /api/linkedin/connections` - Get LinkedIn connections
- `GET /api/linkedin/analytics` - Get LinkedIn analytics data

### SEO Endpoints

- `GET /api/seo/rankings` - Get SEO rankings data
- `GET /api/seo/keywords` - Get keyword performance data
- `POST /api/seo/keywords` - Add new keywords to track

### Email Endpoints

- `GET /api/email/campaigns` - Get email campaign data
- `GET /api/email/analytics` - Get email analytics data
- `POST /api/email/send` - Send new email campaign

## Implementation Timeline

1. **Phase 1** (Current)

   - Basic connectivity endpoints

2. **Phase 2**

   - Authentication endpoints
   - Basic dashboard data endpoints

3. **Phase 3**

   - LinkedIn integration endpoints
   - SEO endpoints

4. **Phase 4**
   - Email campaign endpoints
   - Advanced analytics endpoints
