// eslint-checker.js
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Function to check if a file exists
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

// Function to find the actual ESLint binary
function findEslintBin() {
  const possiblePaths = [
    path.join(__dirname, 'node_modules', '.bin', 'eslint'),
    path.join(__dirname, 'frontend', 'node_modules', '.bin', 'eslint'),
    path.join(__dirname, 'shared', 'node_modules', '.bin', 'eslint')
  ];

  for (const binPath of possiblePaths) {
    if (fileExists(binPath)) {
      return binPath;
    }
  }

  return null;
}

// Check ESLint configuration
function checkEslintConfig() {
  const configFiles = [
    {path: path.join(__dirname, '.eslintrc.js'), name: 'Root .eslintrc.js'},
    {path: path.join(__dirname, '.eslintrc.json'), name: 'Root .eslintrc.json'},
    {path: path.join(__dirname, 'frontend', '.eslintrc.js'), name: 'Frontend .eslintrc.js'},
    {path: path.join(__dirname, 'shared', '.eslintrc.js'), name: 'Shared .eslintrc.js'}
  ];

  console.log('ESLint Configuration Files:');
  configFiles.forEach(file => {
    if (fileExists(file.path)) {
      console.log(`✓ ${file.name} exists`);

      // Try to read the file
      try {
        const content = fs.readFileSync(file.path, 'utf8');
        if (content.includes('@typescript-eslint/parser')) {
          console.log(`  - Uses TypeScript parser`);
        }
        if (content.includes('@typescript-eslint/eslint-plugin')) {
          console.log(`  - Uses TypeScript ESLint plugin`);
        }
      } catch (error) {
        console.log(`  - Error reading file: ${error.message}`);
      }
    } else {
      console.log(`✗ ${file.name} does not exist`);
    }
  });
}

// Check ESLint dependencies
function checkEslintDependencies() {
  const dependencies = [
    'eslint',
    '@typescript-eslint/parser',
    '@typescript-eslint/eslint-plugin',
    'eslint-plugin-react',
    'eslint-plugin-react-hooks'
  ];

  console.log('ESLint Dependencies:');

  dependencies.forEach(dep => {
    const rootPath = path.join(__dirname, 'node_modules', dep);
    const frontendPath = path.join(__dirname, 'frontend', 'node_modules', dep);

    if (fileExists(rootPath)) {
      console.log(`✓ ${dep} found in root node_modules`);
    } else if (fileExists(frontendPath)) {
      console.log(`✓ ${dep} found in frontend node_modules`);
    } else {
      console.log(`✗ ${dep} not found`);
    }
  });
}

// Try to run ESLint using npx
function tryRunEslint() {
  console.log('\nAttempting to run ESLint:');

  try {
    // Find the ESLint binary
    const eslintBin = findEslintBin();

    if (!eslintBin) {
      console.log('❌ Could not find ESLint binary in node_modules');
      return;
    }

    console.log(`✓ Found ESLint binary at: ${eslintBin}`);

    // Try to run ESLint on a simple file
    const testFile = path.join(__dirname, 'frontend', 'src', 'App.tsx');

    if (!fileExists(testFile)) {
      console.log(`❌ Test file ${testFile} does not exist`);
      return;
    }

    console.log(`Running ESLint on ${testFile}`);

    try {
      const output = execSync(`${eslintBin} --no-ignore ${testFile}`, {
        encoding: 'utf8',
        stdio: 'pipe',
        timeout: 10000  // 10 seconds timeout
      });

      console.log('✓ ESLint ran successfully!');
      console.log('Output:');
      console.log(output || '(no output - file is clean)');
    } catch (error) {
      console.log('✓ ESLint ran with errors/warnings (this is expected if there are linting issues):');
      console.log(error.stdout || error.message);
    }
  } catch (error) {
    console.log(`❌ Failed to run ESLint: ${error.message}`);
  }
}

// Main function
function main() {
  console.log('==== ESLint Checker ====\n');

  checkEslintConfig();
  console.log('');
  checkEslintDependencies();
  tryRunEslint();

  console.log('\n==== ESLint Check Complete ====');
}

main();
