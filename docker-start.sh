#!/bin/bash
# Start Docker services for Mexel project

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting Mexel services...${NC}"
echo -e "${YELLOW}================================${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Docker is not running! Please start Docker and try again.${NC}"
    exit 1
fi

# Define project root directory
PROJECT_ROOT=$(dirname "$0")
cd "$PROJECT_ROOT" || exit 1

# Check if containers are already running
if docker ps | grep -q "mexel_"; then
    echo -e "${YELLOW}Some Mexel containers are already running.${NC}"
    read -p "Do you want to restart all containers? (y/n): " answer
    if [[ "$answer" == "y" ]]; then
        echo -e "${YELLOW}Stopping existing containers...${NC}"
        docker-compose down
    else
        echo -e "${YELLOW}Using existing containers.${NC}"
    fi
fi

# Start all services
echo -e "${YELLOW}Starting services with docker-compose...${NC}"
docker-compose up --build -d

# Check if services started correctly
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Services started successfully!${NC}"
    echo -e "${GREEN}================================${NC}"
    echo -e "${GREEN}Frontend is available at: http://localhost:3000${NC}"
    echo -e "${GREEN}Backend API is available at: http://localhost:8000${NC}"
    echo -e "${GREEN}To view logs, run: docker-compose logs -f${NC}"
    echo -e "${GREEN}To stop services, run: docker-compose down${NC}"
else
    echo -e "${RED}Failed to start services. Check docker-compose logs for details.${NC}"
    exit 1
fi

exit 0
