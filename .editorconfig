# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
charset = utf-8
trim_trailing_whitespace = true

# TypeScript and JavaScript files
[*.{ts,tsx,js,jsx}]
indent_style = space
indent_size = 2
max_line_length = 100

# Python files
[*.py]
indent_style = space
indent_size = 4
max_line_length = 88

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# Shell scripts
[*.sh]
end_of_line = lf
indent_style = space
indent_size = 2

# SQL files
[*.sql]
indent_style = space
indent_size = 2

# Documentation
[*.{txt,rst}]
indent_style = space
indent_size = 4

# Makefiles require tabs
[Makefile]
indent_style = tab

# Dependencies and build output
[{package.json,package-lock.json,yarn.lock}]
indent_style = space
indent_size = 2

# Configuration files
[*.{eslintrc,prettierrc,babelrc}]
indent_style = space
indent_size = 2