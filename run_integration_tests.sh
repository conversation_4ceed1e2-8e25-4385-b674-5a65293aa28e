#!/bin/zsh
# Run integration tests for both frontend and backend

echo "Starting integration tests..."

# Start backend server in background
cd backend-python
python run.py &
BACKEND_PID=$!

# Wait for backend to start
sleep 5

# Start frontend in background
cd ../frontend
yarn start &
FRONTEND_PID=$!

# Wait for frontend to start
sleep 10

# Run backend integration tests
echo "\nRunning backend integration tests..."
cd ..
python test_integration.py

# Run frontend integration tests
echo "\nRunning frontend integration tests..."
cd frontend
yarn test:e2e

# Cleanup
kill $BACKEND_PID
kill $FRONTEND_PID

echo "\nIntegration tests completed."
