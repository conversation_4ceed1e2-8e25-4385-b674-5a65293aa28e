# Mexel Agent System

The Mexel Agent System is a collection of autonomous agents that work together to monitor tenders, generate outreach emails, and provide analytics for the Mexel platform.

## Agents

The system consists of the following agents:

1. **TenderMonitorAgent**: Monitors tender sources, processes tenders, and notifies about high-priority opportunities.
2. **OutreachEmailAgent**: Generates and sends personalized emails based on tenders and leads.
3. **AnalyticsAgent**: Collects and analyzes data, generates reports, and provides insights.
4. **CoordinatorAgent**: Orchestrates the execution of other agents based on schedules.

## Setup

### Prerequisites

- Node.js 16+
- npm or yarn
- SQLite

### Installation

1. Install dependencies:

```bash
yarn install
```

2. Set up environment variables:

Copy the `.env.example` file to `.env` and update the values as needed.

3. Run database migrations:

```bash
yarn migrations
```

## Usage

### Starting the Agent System

To start the entire agent system:

```bash
yarn start-agents
```

To start specific agents:

```bash
yarn start-agents -- tender
yarn start-agents -- email
yarn start-agents -- analytics
```

### Testing the Agent System

To test the entire agent system:

```bash
yarn test-agents
```

To test specific agents:

```bash
yarn test-agents -- tender
yarn test-agents -- email
yarn test-agents -- analytics
```

## Configuration

The agent system can be configured using environment variables:

- `TENDER_MONITOR_INTERVAL`: Interval for the TenderMonitorAgent in milliseconds (default: 3600000)
- `OUTREACH_EMAIL_INTERVAL`: Interval for the OutreachEmailAgent in milliseconds (default: 1800000)
- `ANALYTICS_INTERVAL`: Interval for the AnalyticsAgent in milliseconds (default: 86400000)
- `NOTIFICATION_EMAIL`: Email address for notifications (default: <EMAIL>)
- `REPORT_RECIPIENTS`: Comma-separated list of email addresses for reports (default: <EMAIL>)

## Database Schema

The agent system uses SQLite for data storage. The database schema includes tables for:

- Leads
- Activities
- Sequences
- Tags
- Email Templates
- Tenders
- Emails
- Analytics

## Development

### Adding a New Agent

To add a new agent:

1. Create a new agent class that extends `BaseAgent`
2. Implement the required methods: `initializeImpl()`, `executeImpl()`, `shutdownImpl()`, and `resetImpl()`
3. Register the agent with the `CoordinatorAgent`

### Adding a New Migration

To add a new database migration:

1. Create a new migration file in the `src/migrations` directory
2. Export a `migration` object with `version`, `description`, `sql`, and `rollbackSql` properties
3. Run the migrations using the `yarn migrations` command

## Troubleshooting

### Common Issues

- **Database connection errors**: Check that the database path is correct and the directory exists
- **Email sending errors**: Check the SMTP configuration in the `.env` file
- **Agent initialization errors**: Check that all required services are available and properly configured

### Logs

Logs are written to the console by default. The log level can be configured using the `LOG_LEVEL` environment variable.

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
