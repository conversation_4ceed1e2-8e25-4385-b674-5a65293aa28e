# Agent Instructions for Mexel Project

## Project Overview
Mexel Lead Discovery System - AI-powered marketing system for water treatment chemicals with tender monitoring, lead generation, and LinkedIn integration.

## Important Commands

### Server Management
```bash
# Start Backend
cd backend-python && python3 run.py

# Start Frontend (workaround for React Scripts issues)
cd frontend && python3 -m http.server 3000 --directory public

# Check Health
curl http://localhost:3001/api/health
curl http://localhost:3000
```

### Development Commands
```bash
# Type checking
cd frontend && yarn tsc --noEmit

# Install dependencies (YARN ONLY)
yarn install

# Run tests (when configuration fixed)
cd frontend && yarn dlx playwright test
```

### Troubleshooting
```bash
# Kill processes
lsof -ti:3001 | xargs kill -9  # Backend
lsof -ti:3000 | xargs kill -9  # Frontend

# Check ports
lsof -i :3001
lsof -i :3000
```

## Code Style & Conventions

### Package Manager
- **ALWAYS use Yarn** - npm is disabled in this project
- Use `yarn dlx` for one-time package execution
- Never suggest npm commands

### TypeScript
- Project uses strict TypeScript configuration
- Custom type declarations in `frontend/src/types/`
- Skip lib checking enabled for compatibility

### Architecture Patterns
- React functional components with hooks
- Material UI for styling
- RESTful API design
- SQLite for data persistence

## Known Issues & Workarounds

### Node.js Compatibility
- Running Node v22.14.0 which causes React Scripts issues
- Workaround: Use Python HTTP server for frontend
- Consider recommending Node v18 LTS downgrade

### Package Dependencies
- react-scripts missing files - fixed with custom scripts
- recharts types - custom declaration file created
- Playwright version conflicts - needs resolution

### Backend API
- Many endpoints return 404 (not implemented)
- Only /api/health works currently
- Need to implement: /api/tenders, /api/system-status, /api/products

## Recent Progress Context

### What Was Fixed
1. TypeScript recharts compilation errors
2. React Scripts missing files 
3. Server startup issues
4. Basic connectivity between frontend/backend

### Current State
- Both servers running and accessible
- Basic HTML frontend loading
- Backend health endpoint working
- Ready for API endpoint implementation

### Next Priorities
1. Implement missing backend API endpoints
2. Fix Playwright test configuration
3. Resolve remaining TypeScript syntax errors
4. Test full integration flow

## File Locations

### Key Configuration Files
- `frontend/tsconfig.json` - TypeScript config
- `frontend/package.json` - Dependencies and scripts
- `backend-python/app/config/mcp_settings.json` - Backend config
- `playwright.config.ts` - Test configuration

### Custom Files Created This Session
- `frontend/src/types/recharts.d.ts` - Type declarations
- `frontend/fix-react-scripts.js` - React Scripts workaround
- `PROJECT_STATUS.md` - Current session status

### Important Directories
- `frontend/src/tests/` - Integration and E2E tests
- `backend-python/app/services/` - Backend services
- `frontend/src/components/` - React components
- `docs/` - Project documentation

## Testing Strategy
- Integration tests using Playwright
- Tests expect both servers running on localhost:3000 and :3001
- Current test files have Playwright configuration conflicts
- Tests cover: navigation, forms, real-time updates, API connectivity