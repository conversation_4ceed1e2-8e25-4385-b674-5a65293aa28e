# Mexel FastAPI Backend with LangChain and AI Integration

This document outlines the AI and LinkedIn integration in the Mexel FastAPI backend.

## Overview

The backend has been implemented with:

- FastAPI for the RESTful API framework
- LangChain for flexible AI model integration
- Socket.IO for real-time updates
- LinkedIn API integration (simulated, to be implemented)

## Key Components

### AI Service Layer

The AI service layer provides a uniform interface to different AI models:

1. **Base Service Interface**: `AIServiceBase` defines the interface for all AI services
2. **Provider-Specific Implementations**:
   - `OpenAIService`: Integration with OpenAI models
   - `OllamaService`: Integration with local Ollama models
   - `AIServiceFactory`: Factory to create the appropriate service based on config

### LangChain Integration

LangChain is integrated to provide:

- Chain-based workflows for complex AI tasks
- Seamless switching between AI providers
- Structured output from AI models

### LinkedIn Integration

The LinkedIn service provides:

- Content generation for LinkedIn posts
- Simulated posting functionality (to be replaced with actual API)
- Analytics retrieval (simulated, to be replaced with actual API)

## API Endpoints

### AI Endpoints

- `POST /api/v1/ai/generate`: Generate text using AI
- `POST /api/v1/ai/embeddings`: Generate embeddings for text
- `POST /api/v1/ai/analyze/tender`: Analyze tender text using AI
- `POST /api/v1/ai/linkedin/post`: Generate a LinkedIn post

### LinkedIn Endpoints

- `POST /api/v1/linkedin/generate`: Generate LinkedIn post content
- `POST /api/v1/linkedin/post`: Post to LinkedIn (simulated)
- `GET /api/v1/linkedin/stats`: Get LinkedIn stats (simulated)

## Environment Configuration

Set these environment variables to configure AI services:

```bash
# AI Provider (openai, ollama)
AI_PROVIDER=openai

# OpenAI Config
OPENAI_API_KEY=your-api-key
OPENAI_MODEL=gpt-4

# Ollama Config
OLLAMA_HOST=http://localhost:11434
OLLAMA_ENDPOINT=/api/generate
OLLAMA_MODEL=llama3

# LinkedIn Config
LINKEDIN_USERNAME=your-username
LINKEDIN_PASSWORD=your-password
```

## Installation

Install the AI-specific requirements:

```bash
pip install -r requirements-ai.txt
```

## Usage Examples

### Generate Text with AI

```python
from app.services.ai.base import AIServiceFactory

# Get the configured AI service
ai_service = AIServiceFactory.create_service()

# Generate text
result = await ai_service.generate_text(
    prompt="Write a summary of water treatment technologies",
    system_prompt="You are an expert in chemical engineering",
    temperature=0.7,
    max_tokens=500
)

print(result["text"])
```

### Analyze a Tender

```python
from app.services.ai.langchain_agents import TenderAnalysisAgent

# Create the tender analysis agent
agent = TenderAnalysisAgent()

# Analyze a tender
analysis = await agent.analyze_tender(
    tender_text="Request for proposal for water treatment chemicals...",
    company_products=["BioDisperse 3399", "AquaGuard Pro"],
    expertise_areas=["Cooling tower treatment", "Boiler water treatment"]
)

print(f"Relevance: {analysis['relevance_score']}")
print(f"Matched products: {analysis['product_matches']}")
```

### Generate LinkedIn Post

```python
from app.services.linkedin_service import LinkedInService
from app.services.ai.base import AIServiceFactory

# Get dependencies
ai_service = AIServiceFactory.create_service()
linkedin_service = LinkedInService()

# Generate post content
post = await LinkedInService.generate_post_content(
    topic="Water sustainability practices",
    audience="Environmental engineers",
    tone="professional",
    include_hashtags=True,
    ai_service=ai_service
)

print(post["content"])
print(post["hashtags"])
```

## Next Steps

1. Implement actual LinkedIn API integration using OAuth
2. Add more LangChain-based agents for specific tasks
3. Implement streaming responses for real-time AI generation
4. Add more AI providers (Claude, local models)
5. Enhance the Socket.IO integration for real-time updates
