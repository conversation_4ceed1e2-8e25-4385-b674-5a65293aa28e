# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.env
.venv/
venv/
ENV/
env/
*.egg-info/
dist/
build/
.eggs/
.pytest_cache/
htmlcov/
.coverage
coverage.xml

# IDE
.idea/
.vscode/
*.swp
*.swo

# Docker
Dockerfile
.dockerignore
docker-compose*

# Git
.git/
.gitignore

# Logs and databases
*.log
*.sqlite3
*.db

# Temporary files
*.tmp
*~
.DS_Store
*~

# OS
.DS_Store
Thumbs.db

# Test
.pytest_cache/
.coverage
htmlcov/
.tox/

# Logs
*.log

# Local development
local_settings.py
db.sqlite3
*.sqlite3

# Node modules (if any)
node_modules/

# Git
.git/
.gitignore

# Docker
Dockerfile
docker-compose*.yml
