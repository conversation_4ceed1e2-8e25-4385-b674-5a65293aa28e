# A simple script to generate a hashed password for testing
from passlib.context import CryptContext
import sys

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password):
    return pwd_context.hash(password)

password = "password123"
if len(sys.argv) > 1:
    password = sys.argv[1]

hashed_password = get_password_hash(password)
print(f"Password: {password}")
print(f"Hashed: {hashed_password}")
