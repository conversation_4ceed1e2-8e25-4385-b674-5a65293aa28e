pyproject.toml
setup.cfg
setup.py
app/__init__.py
app/main.py
app/models.py
app.egg-info/PKG-INFO
app.egg-info/SOURCES.txt
app.egg-info/dependency_links.txt
app.egg-info/requires.txt
app.egg-info/top_level.txt
app/api/__init__.py
app/api/auth.py
app/core/__init__.py
app/core/config.py
app/core/database.py
app/core/db.py
app/core/migrations/__init__.py
app/core/migrations/data_migration.py
app/models/__init__.py
app/models/user.py
app/schemas/__init__.py
app/schemas/auth.py
app/services/__init__.py
app/services/auth_service.py
app/services/crm_service.py
app/services/email_service.py
tests/test_services.py