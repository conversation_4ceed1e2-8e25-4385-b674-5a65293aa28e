"""Initial migration

Revision ID: 001
Revises: 
Create Date: 2025-05-26 18:30:15.123456

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic
revision = '001'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Create enum types
    op.execute("CREATE TYPE userstatus AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED')")
    op.execute("CREATE TYPE tenderstatus AS ENUM ('NEW', 'PROCESSING', 'IN_REVIEW', 'SUBMITTED', 'WON', 'LOST', 'CANCELLED')")
    op.execute("CREATE TYPE tendersource AS ENUM ('ETENDERS', 'ESKOM', 'TRANSNET', 'MANUAL')")
    op.execute("CREATE TYPE submissionstatus AS ENUM ('DRAFT', 'PENDING', 'SUBMITTED', 'ACCEPTED', 'REJECTED')")
    op.execute("CREATE TYPE activitytype AS ENUM ('CREATED', 'UPDATED', 'STATUS_CHANGED', 'DOCUMENT_ADDED', 'COMMENT_ADDED')")
    op.execute("CREATE TYPE userrole AS ENUM ('ADMIN', 'MANAGER', 'ANALYST', 'USER')")
    
    # Create users table
    op.create_table(
        'users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('role', sa.Enum('ADMIN', 'MANAGER', 'ANALYST', 'USER', name='userrole'), nullable=True),
        sa.Column('hashed_password', sa.String(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_superuser', sa.Boolean(), nullable=True),
        sa.Column('last_login', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('email')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    
    # Create tenders table
    op.create_table(
        'tenders',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('title', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('url', sa.String(), nullable=True),
        sa.Column('value', sa.Float(), nullable=True),
        sa.Column('status', sa.Enum('NEW', 'PROCESSING', 'IN_REVIEW', 'SUBMITTED', 'WON', 'LOST', 'CANCELLED', name='tenderstatus'), nullable=True),
        sa.Column('source', sa.Enum('ETENDERS', 'ESKOM', 'TRANSNET', 'MANUAL', name='tendersource'), nullable=False),
        sa.Column('deadline', sa.DateTime(), nullable=True),
        sa.Column('publish_date', sa.DateTime(), nullable=True),
        sa.Column('organization', sa.String(), nullable=True),
        sa.Column('location', sa.String(), nullable=True),
        sa.Column('relevance_score', sa.Float(), nullable=True),
        sa.Column('confidence_score', sa.Float(), nullable=True),
        sa.Column('keywords', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('metadata', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('documents', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create tender_submissions table
    op.create_table(
        'tender_submissions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('tender_id', sa.String(), nullable=False),
        sa.Column('submitted_by', sa.Integer(), nullable=False),
        sa.Column('submission_date', sa.DateTime(), nullable=True),
        sa.Column('status', sa.Enum('DRAFT', 'PENDING', 'SUBMITTED', 'ACCEPTED', 'REJECTED', name='submissionstatus'), nullable=True),
        sa.Column('proposal_content', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('supporting_documents', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('feedback', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['submitted_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['tender_id'], ['tenders.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create tender_activities table
    op.create_table(
        'tender_activities',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('tender_id', sa.String(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('activity_type', sa.Enum('CREATED', 'UPDATED', 'STATUS_CHANGED', 'DOCUMENT_ADDED', 'COMMENT_ADDED', name='activitytype'), nullable=True),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('metadata', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['tender_id'], ['tenders.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

def downgrade():
    op.drop_table('tender_activities')
    op.drop_table('tender_submissions')
    op.drop_table('tenders')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    
    # Drop enum types
    op.execute('DROP TYPE activitytype')
    op.execute('DROP TYPE submissionstatus')
    op.execute('DROP TYPE tendersource')
    op.execute('DROP TYPE tenderstatus')
    op.execute('DROP TYPE userstatus')
    op.execute('DROP TYPE userrole')
