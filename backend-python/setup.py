from setuptools import setup, find_packages

setup(
    name="app",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "fastapi>=0.109.0",
        "uvicorn>=0.27.0",
        "sqlalchemy>=2.0.25",
        "pydantic>=2.11.4",
        "python-jose[cryptography]>=3.3.0",  # Added [cryptography]
        "passlib[bcrypt]>=1.7.4",            # Added [bcrypt]
        "python-multipart>=0.0.6",
        "email-validator>=2.1.0",
        "psycopg2-binary>=2.9.9",
        "alembic>=1.13.1"                   # Added alembic
    ],
    python_requires=">=3.11",
)
