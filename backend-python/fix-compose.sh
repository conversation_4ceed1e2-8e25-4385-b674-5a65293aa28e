#!/bin/bash
cat > docker-compose.yml << 'END'
services:
  db:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: mexel
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/mexel
      - PYTHONUNBUFFERED=1
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./:/app:ro
      - ./static:/app/static

volumes:
  db_data:
END
