# A simple script to create a test user with proper password hashing
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from passlib.context import CryptContext
import sys
import os

# Set up the database connection
DATABASE_URL = "sqlite:////Users/<USER>/Desktop/Mexel/data/mexel.db"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

# Set up the password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Create the user table if it doesn't exist
db.execute(text("""
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT UNIQUE NOT NULL,
    name TEXT,
    role TEXT,
    hashed_password TEXT NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    is_superuser BOOLEAN DEFAULT 0
)
"""))

# Try to add name and role columns if they don't exist (for existing tables)
try:
    db.execute(text("ALTER TABLE users ADD COLUMN name TEXT"))
    db.execute(text("ALTER TABLE users ADD COLUMN role TEXT"))
    db.commit() # Commit alter table changes separately
    sys.stderr.write("Added name and role columns to existing users table if they were missing.\n")
except Exception as e:
    sys.stderr.write(f"Could not add name/role columns, they might already exist: {e}\n")
    db.rollback() # Rollback if alter fails


# Generate a hashed password
password = "password123"
hashed_password = pwd_context.hash(password)
sys.stderr.write(f"Password: {password}\n")
sys.stderr.write(f"Hashed: {hashed_password}\n")

# Check if the user already exists
existing_user = db.execute(text("SELECT id FROM users WHERE email='<EMAIL>'")).fetchone()
if existing_user:
    # Update the existing user
    db.execute(
        text("UPDATE users SET hashed_password=:hashed_password, name=:name, role=:role WHERE email='<EMAIL>'"),
        {"hashed_password": hashed_password, "name": "Admin User", "role": "admin"}
    )
    sys.stderr.write("Updated <NAME_EMAIL>\n")
else:
    # Create a new user
    db.execute(
        text("INSERT INTO users (email, name, role, hashed_password, is_active, is_superuser) VALUES (:email, :name, :role, :hashed_password, :is_active, :is_superuser)"),
        {
            "email": "<EMAIL>",
            "name": "Admin User",
            "role": "admin",
            "hashed_password": hashed_password,
            "is_active": True,
            "is_superuser": True
        }
    )
    sys.stderr.write("Created <NAME_EMAIL>\n")

# Commit the changes
db.commit()
db.close()

sys.stderr.write("Done!\n")
