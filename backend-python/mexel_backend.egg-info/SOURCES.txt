pyproject.toml
setup.cfg
setup.py
app/__init__.py
app/main.py
app/models.py
app/py.typed
app/api/__init__.py
app/api/auth.py
app/core/__init__.py
app/core/config.py
app/core/database.py
app/core/db.py
app/core/db.pyi
app/core/migrations/__init__.py
app/core/migrations/data_migration.py
app/core/migrations/env.py
app/core/migrations/versions/001_create_base_tables.py
app/core/migrations/versions/__init__.py
app/models/__init__.py
app/models/user.py
app/schemas/__init__.py
app/schemas/auth.py
app/services/__init__.py
app/services/auth_service.py
app/services/crm_service.py
app/services/email_service.py
mexel_backend.egg-info/PKG-INFO
mexel_backend.egg-info/SOURCES.txt
mexel_backend.egg-info/dependency_links.txt
mexel_backend.egg-info/top_level.txt
tests/test_services.py