"""Database dependencies and configurations"""
from typing import Generator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import Session as SQLAlchemySession # Keep this alias

__all__ = ['get_db', 'SessionLocal', 'SQLAlchemySession'] # Ensure SQLAlchemySession is in __all__

SQLALCHEMY_DATABASE_URL = "sqlite:///./app.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[SQLAlchemySession, None, None]: # Use SQLAlchemySession for type hint
    """Database dependency"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
