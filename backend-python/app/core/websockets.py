"""
WebSocket event handlers for Socket.IO integration.
These handlers provide real-time communication between frontend and backend.
"""
from typing import Dict, Any, Optional
import json
import logging
import asyncio
from datetime import datetime

# Set up logging
logger = logging.getLogger(__name__)

class SocketManager:
    """
    Manager class for Socket.IO events and connections
    """
    def __init__(self, sio):
        self.sio = sio
        self.connected_clients = {}
        self.register_handlers()
        
    def register_handlers(self):
        """Register all socket event handlers"""
        @self.sio.event
        async def connect(sid, environ):
            """Handle client connection"""
            client_info = {
                'sid': sid,
                'connected_at': datetime.now().isoformat(),
                'ip': environ.get('REMOTE_ADDR', 'unknown'),
                'user_agent': environ.get('HTTP_USER_AGENT', 'unknown'),
            }
            self.connected_clients[sid] = client_info
            logger.info(f"Client connected: {sid}")
            await self.sio.emit('welcome', {'message': 'Welcome to Mexel WebSocket Server!'}, to=sid)
            
        @self.sio.event
        async def disconnect(sid):
            """Handle client disconnection"""
            if sid in self.connected_clients:
                del self.connected_clients[sid]
            logger.info(f"Client disconnected: {sid}")
            
        @self.sio.event
        async def message(sid, data):
            """Handle generic messages"""
            logger.info(f"Message from {sid}: {data}")
            await self.sio.emit('response', {'data': f"Server received: {data}"}, to=sid)
            
        @self.sio.event
        async def echo(sid, data):
            """Echo the received data back to the client"""
            logger.info(f"Echo request from {sid}")
            await self.sio.emit('echo_response', data, to=sid)
            
        @self.sio.event
        async def ping(sid, data=None):
            """Respond to ping requests with timestamp"""
            timestamp = datetime.now().isoformat()
            await self.sio.emit('pong', {
                'timestamp': timestamp,
                'message': 'Server is alive'
            }, to=sid)
            
        @self.sio.event
        async def join_room(sid, room):
            """Join a specific room for group messaging"""
            if isinstance(room, str) and room:
                await self.sio.enter_room(sid, room)
                logger.info(f"Client {sid} joined room: {room}")
                await self.sio.emit('room_joined', {'room': room}, to=sid)
                # Notify room members
                await self.sio.emit('user_joined', {'sid': sid}, room=room, skip_sid=sid)
            
        @self.sio.event
        async def leave_room(sid, room):
            """Leave a specific room"""
            if isinstance(room, str) and room:
                await self.sio.leave_room(sid, room)
                logger.info(f"Client {sid} left room: {room}")
                await self.sio.emit('room_left', {'room': room}, to=sid)
                # Notify room members
                await self.sio.emit('user_left', {'sid': sid}, room=room)
                
        @self.sio.event
        async def room_message(sid, data):
            """Send message to a specific room"""
            room = data.get('room')
            message = data.get('message')
            if room and message:
                logger.info(f"Room message from {sid} to room {room}: {message}")
                await self.sio.emit('room_message', {
                    'sender': sid,
                    'message': message,
                    'timestamp': datetime.now().isoformat()
                }, room=room)
                
    async def broadcast_message(self, event: str, data: Dict[str, Any], room: Optional[str] = None):
        """
        Broadcast a message to all connected clients or specific room
        
        Args:
            event: Event name
            data: Data to send
            room: Optional room to send to
        """
        if room:
            await self.sio.emit(event, data, room=room)
        else:
            await self.sio.emit(event, data)
            
    async def send_to_client(self, sid: str, event: str, data: Dict[str, Any]):
        """
        Send a message to a specific client
        
        Args:
            sid: Session ID of the client
            event: Event name
            data: Data to send
        """
        await self.sio.emit(event, data, to=sid)
        
    def get_connected_clients(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all connected clients"""
        return self.connected_clients
