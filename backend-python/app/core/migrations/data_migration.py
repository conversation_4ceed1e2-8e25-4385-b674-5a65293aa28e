#!/usr/bin/env python3
"""
Data migration script to transfer data from SQLite to PostgreSQL
"""
import sqlite3
import json
from datetime import datetime
try:
    import psycopg2
    from psycopg2.extras import execute_values
except ImportError as e:
    raise ImportError(
        "Could not import psycopg2. Please install it with: "
        "pip install psycopg2-binary"
    ) from e

from app.core.config import settings

def migrate_data():
    # Connect to SQLite database
    sqlite_conn = sqlite3.connect('../../data/mexel.db')
    sqlite_cur = sqlite_conn.cursor()
    
    # Connect to PostgreSQL database
    pg_conn = psycopg2.connect(settings.DATABASE_URL)
    pg_cur = pg_conn.cursor()
    
    try:
        # Migrate users
        sqlite_cur.execute("SELECT id, email, name, hashed_password, role, created_at, updated_at FROM users")
        users = sqlite_cur.fetchall()
        
        if users:
            execute_values(pg_cur, 
                """
                INSERT INTO users (id, email, name, hashed_password, role, created_at, updated_at)
                VALUES %s
                ON CONFLICT (id) DO NOTHING
                """,
                users
            )
        
        # Migrate leads
        sqlite_cur.execute("""
            SELECT id, email, first_name, last_name, company, position, industry, 
                   status, created_at, updated_at 
            FROM leads
        """)
        leads = sqlite_cur.fetchall()
        
        if leads:
            execute_values(pg_cur, 
                """
                INSERT INTO leads (id, email, first_name, last_name, company, 
                                 position, industry, status, created_at, updated_at)
                VALUES %s
                ON CONFLICT (id) DO NOTHING
                """,
                leads
            )
        
        # Migrate tenders
        sqlite_cur.execute("""
            SELECT id, title, description, source, url, issuer, status,
                   publish_date, closing_date, created_at, updated_at
            FROM tenders
        """)
        tenders = sqlite_cur.fetchall()
        
        if tenders:
            execute_values(pg_cur, 
                """
                INSERT INTO tenders (id, title, description, source, url, issuer, 
                                   status, publish_date, closing_date, created_at, updated_at)
                VALUES %s
                ON CONFLICT (id) DO NOTHING
                """,
                tenders
            )
        
        # Commit the transaction
        pg_conn.commit()
        print("Data migration completed successfully!")
        
    except Exception as e:
        pg_conn.rollback()
        print(f"Error during migration: {e}")
        raise
    
    finally:
        sqlite_cur.close()
        sqlite_conn.close()
        pg_cur.close()
        pg_conn.close()

if __name__ == "__main__":
    migrate_data()
