from pydantic import BaseModel, field_validator
from typing import List, Optional, Union, Dict, Any
import os
import json
from functools import lru_cache
from pathlib import Path
from app.schemas.mcp import (
    MCPSettings,
    MCPContextWindow,
    MCPModelSettings
)

class Settings(BaseModel):
    PROJECT_NAME: str = "Mexel Lead Discovery System"
    PROJECT_DESCRIPTION: str = "AI-powered marketing system with tender monitoring capabilities for Mexel Energy Sustain's chemical products."
    VERSION: str = "1.0.0"
    
    # MCP Settings
    MCP_SETTINGS_PATH: str = os.getenv("MCP_SETTINGS_PATH", str(Path(__file__).parent.parent / "config" / "mcp_settings.json"))
    MCP_SETTINGS: Dict[str, Any] = {}
    
    # API settings
    API_V1_STR: str = "/api/v1"
    # Change to List[str] since we want to allow any valid URL string
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost",
        "http://localhost:3000",
    ]
    
    # Database settings
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "mexel")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "mexel123")
    POSTGRES_SERVER: str = os.getenv("POSTGRES_SERVER", "localhost")
    POSTGRES_PORT: str = os.getenv("POSTGRES_PORT", "5432")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "mexel")
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL",
        f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_SERVER}:{POSTGRES_PORT}/{POSTGRES_DB}"
    )
    
    # Authentication settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "secret-key")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    ALGORITHM: str = "HS256"
    
    # AI Provider
    AI_PROVIDER: str = os.getenv("AI_PROVIDER", "openai")  # openai, ollama, deepseek
    
    # OpenAI
    OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-4")
    
    # Ollama
    OLLAMA_HOST: str = os.getenv("OLLAMA_HOST", "http://localhost:11434")
    OLLAMA_ENDPOINT: str = os.getenv("OLLAMA_ENDPOINT", "/api/generate")
    OLLAMA_MODEL: str = os.getenv("OLLAMA_MODEL", "llama3")
    
    # DeepSeek
    DEEPSEEK_API_KEY: Optional[str] = os.getenv("DEEPSEEK_API_KEY")
    DEEPSEEK_API_BASE_URL: str = os.getenv("DEEPSEEK_API_BASE_URL", "https://api.deepseek.com")
    DEEPSEEK_API_VERSION: str = os.getenv("DEEPSEEK_API_VERSION", "v1")
    DEEPSEEK_DEFAULT_MODEL: str = os.getenv("DEEPSEEK_DEFAULT_MODEL", "deepseek-chat")
    
    # LinkedIn
    LINKEDIN_USERNAME: Optional[str] = os.getenv("LINKEDIN_USERNAME")
    LINKEDIN_PASSWORD: Optional[str] = os.getenv("LINKEDIN_PASSWORD")
    
    # Email (Brevo)
    EMAIL_SERVICE: str = os.getenv("EMAIL_SERVICE", "brevo")
    FROM_EMAIL: str = os.getenv("FROM_EMAIL", "<EMAIL>")
    
    # HubSpot
    HUBSPOT_API_KEY: Optional[str] = os.getenv("HUBSPOT_API_KEY")
    HUBSPOT_PORTAL_ID: Optional[str] = os.getenv("HUBSPOT_PORTAL_ID")
    
    @field_validator("BACKEND_CORS_ORIGINS", mode='before')
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str):
            if not v.startswith("["):
                return [i.strip() for i in v.split(",")]
            return eval(v)  # Safely evaluate string representation of list
        elif isinstance(v, list):
            return v
        raise ValueError(f"Invalid CORS origins format: {v}")
    
    @field_validator("MCP_SETTINGS")
    @classmethod
    def validate_mcp_settings(cls, v: Dict[str, Any]) -> Dict[str, Any]:
        """Validate MCP settings schema."""
        if not v:
            # Return default settings
            return {
                "version": "1.0",
                "context_window": {
                    "max_tokens": 8192,
                    "reserved_tokens_percentage": 0.2
                },
                "model": {
                    "provider": "openai",
                    "model_name": "gpt-4",
                    "temperature": 0.7,
                    "max_tokens": 1000,
                    "top_p": 0.95,
                    "frequency_penalty": 0.0,
                    "presence_penalty": 0.0
                },
                "system_prompt": "You are a helpful assistant specialized in chemical water treatment technology.",
                "tools_enabled": False,
                "tool_schemas": []
            }
        
        # Validate required fields
        if not isinstance(v, dict):
            raise ValueError("MCP settings must be a dictionary")
        
        required_fields = ["version", "model"]
        missing_fields = [field for field in required_fields if field not in v]
        if missing_fields:
            raise ValueError(f"Missing required MCP settings fields: {', '.join(missing_fields)}")
            
        # Ensure model settings are correct
        model = v.get("model", {})
        if not isinstance(model, dict):
            raise ValueError("Model settings must be a dictionary")
            
        required_model_fields = ["provider", "model_name"]
        missing_model_fields = [field for field in required_model_fields if field not in model]
        if missing_model_fields:
            raise ValueError(f"Missing required model fields: {', '.join(missing_model_fields)}")
            
        return v
    
    class Config:
        case_sensitive = True

def load_mcp_settings(settings_path: str) -> Dict[str, Any]:
    """Load MCP settings from a JSON file.
    
    Args:
        settings_path (str): Path to the settings JSON file.
        
    Returns:
        Dict[str, Any]: Loaded settings or empty dict if file not found.
    """
    try:
        if os.path.exists(settings_path):
            with open(settings_path, 'r') as f:
                return json.load(f)
        else:
            print(f"Warning: MCP settings file not found at {settings_path}")
            return {}
    except Exception as e:
        print(f"Error loading MCP settings: {e}")
        return {}

@lru_cache()
def get_settings() -> Settings:
    """Get application settings with MCP settings loaded.
    
    Returns:
        Settings: Application settings instance.
    """
    settings_instance = Settings()
    # Load MCP settings if available
    settings_instance.MCP_SETTINGS = load_mcp_settings(settings_instance.MCP_SETTINGS_PATH)
    return settings_instance

# MCP Settings
def get_mcp_settings() -> MCPSettings:
    """Get MCP settings from config or create default"""
    try:
        return MCPSettings(
            version="1.0",
            context_window=MCPContextWindow(
                max_tokens=8192,
                reserved_tokens_percentage=0.2
            ),
            model=MCPModelSettings(
                provider="openai",
                model_name="gpt-4",
                temperature=0.7,
                max_tokens=1000,
                top_p=0.95,
                frequency_penalty=0.0,
                presence_penalty=0.0,
                stop_sequences=[]
            ),
            system_prompt="You are a helpful assistant specialized in chemical water treatment technology.",
            tools_enabled=False,
            tool_schemas=[]
        )
    except Exception as e:
        print(f"Error loading MCP settings: {e}")
        # Return default settings with required fields
        return MCPSettings(
            version="1.0",
            context_window=MCPContextWindow(
                max_tokens=8192,
                reserved_tokens_percentage=0.2
            ),
            model=MCPModelSettings(
                provider="openai",
                model_name="gpt-4",
                temperature=0.7,
                max_tokens=1000,
                top_p=0.95,
                frequency_penalty=0.0,
                presence_penalty=0.0,
                stop_sequences=[]
            ),
            tools_enabled=False,
            tool_schemas=[]
        )

settings = get_settings()
mcp_settings = get_mcp_settings()