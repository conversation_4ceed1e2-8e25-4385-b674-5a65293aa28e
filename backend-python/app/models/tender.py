from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, JSON, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from app.core.database import Base

class TenderStatus(str, enum.Enum):
    NEW = "NEW"
    PROCESSING = "PROCESSING"
    IN_REVIEW = "IN_REVIEW"
    SUBMITTED = "SUBMITTED"
    WON = "WON"
    LOST = "LOST"
    CANCELLED = "CANCELLED"

class TenderSource(str, enum.Enum):
    ETENDERS = "ETENDERS"
    ESKOM = "ESKOM"
    TRANSNET = "TRANSNET"
    MANUAL = "MANUAL"

class Tender(Base):
    __tablename__ = "tenders"

    id = Column(String, primary_key=True)
    title = Column(String, nullable=False)
    description = Column(String)
    url = Column(String)
    value = Column(Float)
    status = Column(Enum(TenderStatus), default=TenderStatus.NEW)
    source = Column(Enum(TenderSource), nullable=False)
    deadline = Column(DateTime)
    publish_date = Column(DateTime, default=datetime.utcnow)
    
    organization = Column(String)
    location = Column(String)
    relevance_score = Column(Float)
    confidence_score = Column(Float)
    
    keywords = Column(JSON)  # Array of strings
    metadata = Column(JSON)  # Additional metadata
    documents = Column(JSON)  # Array of document objects
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    submissions = relationship("TenderSubmission", back_populates="tender")
    activities = relationship("TenderActivity", back_populates="tender")
    
    def __repr__(self):
        return f"<Tender {self.title}>"
