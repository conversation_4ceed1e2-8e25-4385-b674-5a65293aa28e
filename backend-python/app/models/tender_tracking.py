from sqlalchemy import Column, Integer, String, DateTime, Foreign<PERSON>ey, JSO<PERSON>, Enum
from sqlalchemy.orm import relationship
from datetime import datetime, timezone
import enum
from app.core.database import Base

class SubmissionStatus(str, enum.Enum):
    DRAFT = "DRAFT"
    PENDING = "PENDING"
    SUBMITTED = "SUBMITTED"
    ACCEPTED = "ACCEPTED"
    REJECTED = "REJECTED"

class TenderSubmission(Base):
    __tablename__ = "tender_submissions"

    id = Column(Integer, primary_key=True)
    tender_id = Column(String, ForeignKey("tenders.id"), nullable=False)
    submitted_by = Column(Integer, ForeignKey("users.id"), nullable=False)

    submission_date = Column(DateTime)
    status = Column(Enum(SubmissionStatus), default=SubmissionStatus.DRAFT)

    proposal_content = Column(JSON)  # The actual submission content
    supporting_documents = Column(JSON)  # Array of document references
    feedback = Column(String)

    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    tender = relationship("Tender", back_populates="submissions")
    user = relationship("User", back_populates="submissions")

class ActivityType(str, enum.Enum):
    CREATED = "CREATED"
    UPDATED = "UPDATED"
    STATUS_CHANGED = "STATUS_CHANGED"
    DOCUMENT_ADDED = "DOCUMENT_ADDED"
    COMMENT_ADDED = "COMMENT_ADDED"

class TenderActivity(Base):
    __tablename__ = "tender_activities"

    id = Column(Integer, primary_key=True)
    tender_id = Column(String, ForeignKey("tenders.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    activity_type = Column(Enum(ActivityType))
    description = Column(String)
    metadata = Column(JSON)  # Additional activity details

    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    tender = relationship("Tender", back_populates="activities")
    user = relationship("User", back_populates="activities")
