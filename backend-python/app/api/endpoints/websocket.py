from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List
import logging

# Set up logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# We'll inject the socket manager through a dependency
async def get_socket_manager():
    from app.main import socket_manager
    return socket_manager

@router.get("/status")
async def get_websocket_status(socket_manager=Depends(get_socket_manager)):
    """
    Get WebSocket server status including number of connected clients
    """
    connected_clients = socket_manager.get_connected_clients()
    return {
        "status": "online",
        "connected_clients": len(connected_clients),
        "server_info": {
            "socket_io_version": "4.x",  # Update with actual version
            "asgi_mode": "async",
        }
    }

@router.get("/clients")
async def get_connected_clients(socket_manager=Depends(get_socket_manager)):
    """
    Get information about all connected WebSocket clients
    """
    connected_clients = socket_manager.get_connected_clients()
    return {
        "clients": list(connected_clients.values()),
        "total": len(connected_clients)
    }

@router.post("/broadcast")
async def broadcast_message(message: Dict[str, Any], socket_manager=Depends(get_socket_manager)):
    """
    Broadcast a message to all connected clients
    """
    event = message.get("event", "broadcast")
    data = message.get("data", {})
    room = message.get("room")
    
    try:
        await socket_manager.broadcast_message(event, data, room)
        return {
            "success": True,
            "message": "Broadcast message sent",
            "recipients": "all" if not room else f"room: {room}"
        }
    except Exception as e:
        logger.error(f"Broadcast error: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to broadcast message: {str(e)}"
        )
