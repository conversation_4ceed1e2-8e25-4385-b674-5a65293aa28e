from fastapi import APIRouter, HTTPException
from typing import Dict, Any, List
from datetime import datetime, timedelta
import random

router = APIRouter()

@router.get("/health")
def check_health() -> Dict[str, Any]:
    """
    Health check endpoint to verify the API is running
    """
    return {
        "status": "ok",
        "message": "API is operational",
        "version": "1.0.0"
    }

@router.get("/example")
def get_example_data() -> Dict[str, Any]:
    """
    Example endpoint that returns test data
    """
    return {
        "examples": [
            {
                "id": 1,
                "name": "Example 1",
                "description": "This is an example item",
                "value": 42
            },
            {
                "id": 2,
                "name": "Example 2",
                "description": "Another example item",
                "value": 73
            },
            {
                "id": 3,
                "name": "Example 3",
                "description": "A third example item",
                "value": 128
            }
        ],
        "metadata": {
            "total": 3,
            "source": "Mexel API Example"
        }
    }

@router.get("/tenders")
def get_tenders() -> Dict[str, Any]:
    """
    Get tender data for the dashboard
    """
    # Generate mock tender data
    base_date = datetime.now()
    tenders = []
    
    tender_templates = [
        {
            "title": "Water Treatment Chemical Supply - Municipal Contract",
            "issuer": "City of Cape Town",
            "sector": "Municipal Water",
            "location": "Cape Town, Western Cape",
            "status": "Open"
        },
        {
            "title": "Industrial Water Purification Systems",
            "issuer": "Eskom Holdings SOC Ltd",
            "sector": "Power Generation",
            "location": "Johannesburg, Gauteng", 
            "status": "Open"
        },
        {
            "title": "Wastewater Treatment Chemicals Procurement",
            "issuer": "Rand Water",
            "sector": "Water Utilities",
            "location": "Vereeniging, Gauteng",
            "status": "Open"
        },
        {
            "title": "Cooling Tower Chemical Treatment Services",
            "issuer": "Sasol Limited",
            "sector": "Chemical Manufacturing",
            "location": "Secunda, Mpumalanga",
            "status": "Open"
        },
        {
            "title": "Mining Water Treatment Solutions",
            "issuer": "Anglo American Platinum",
            "sector": "Mining",
            "location": "Rustenburg, North West",
            "status": "Evaluation"
        }
    ]
    
    for i, template in enumerate(tender_templates):
        tender = {
            "id": f"T{1000 + i}",
            "reference": f"RFP-{2024}-{1000 + i}",
            "title": template["title"],
            "issuer": template["issuer"],
            "sector": template["sector"],
            "location": template["location"],
            "status": template["status"],
            "publicationDate": (base_date - timedelta(days=random.randint(1, 30))).strftime("%Y-%m-%d"),
            "closingDate": (base_date + timedelta(days=random.randint(15, 60))).strftime("%Y-%m-%d"),
            "relevanceScore": random.randint(75, 95),
            "description": f"Tender for {template['title'].lower()} as issued by {template['issuer']}. This opportunity involves supply and delivery of water treatment chemicals and related services.",
            "requirements": [
                "BBBEE Level 4 or higher certification required",
                "Minimum 5 years experience in water treatment chemicals",
                "ISO 9001:2015 certification mandatory",
                "Local content requirements apply"
            ],
            "keywords": ["water treatment", "chemicals", "municipal", "industrial"]
        }
        tenders.append(tender)
    
    return {
        "data": tenders,
        "metadata": {
            "total": len(tenders),
            "lastUpdated": datetime.now().isoformat(),
            "source": "Mexel Tender Monitoring System"
        }
    }

@router.get("/system-status")
def get_system_status() -> Dict[str, Any]:
    """
    Get system status information
    """
    return {
        "data": {
            "status": "Operational",
            "runningAgents": 4,
            "agentCount": 5,
            "lastUpdated": datetime.now().isoformat(),
            "uptime": "2 days, 14 hours",
            "services": {
                "tenderMonitor": {"status": "running", "lastCheck": datetime.now().isoformat()},
                "aiService": {"status": "running", "lastCheck": datetime.now().isoformat()},
                "emailService": {"status": "running", "lastCheck": datetime.now().isoformat()},
                "database": {"status": "running", "lastCheck": datetime.now().isoformat()},
                "webSocket": {"status": "running", "lastCheck": datetime.now().isoformat()}
            },
            "metrics": {
                "tendersProcessed": 142,
                "leadsGenerated": 38,
                "apiRequests": 1247,
                "errorRate": 0.02
            }
        }
    }

@router.get("/products")
def get_products() -> Dict[str, Any]:
    """
    Get Mexel product information
    """
    products = [
        {
            "id": "MEX-001",
            "name": "AquaClean Pro",
            "category": "Coagulants",
            "effectiveness": "High",
            "growth": 15.2,
            "description": "Advanced aluminum-based coagulant for water treatment"
        },
        {
            "id": "MEX-002", 
            "name": "BioFloc Plus",
            "category": "Flocculants",
            "effectiveness": "High",
            "growth": 12.8,
            "description": "Biodegradable polymer flocculant for enhanced settling"
        },
        {
            "id": "MEX-003",
            "name": "pH Balance 7",
            "category": "pH Adjusters",
            "effectiveness": "Medium",
            "growth": 8.4,
            "description": "Precision pH adjustment chemical for optimal treatment"
        },
        {
            "id": "MEX-004",
            "name": "ChlorGuard",
            "category": "Disinfectants", 
            "effectiveness": "High",
            "growth": 18.7,
            "description": "Stabilized chlorine compound for water disinfection"
        },
        {
            "id": "MEX-005",
            "name": "ScaleStop",
            "category": "Scale Inhibitors",
            "effectiveness": "Medium",
            "growth": 6.2,
            "description": "Anti-scaling agent for industrial water systems"
        },
        {
            "id": "MEX-006",
            "name": "ClearFlow",
            "category": "Clarifiers",
            "effectiveness": "High", 
            "growth": 22.1,
            "description": "High-performance water clarification solution"
        }
    ]
    
    return {
        "data": products,
        "metadata": {
            "total": len(products),
            "categories": list(set(p["category"] for p in products)),
            "lastUpdated": datetime.now().isoformat()
        }
    }