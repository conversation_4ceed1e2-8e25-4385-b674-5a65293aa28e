from fastapi import APIRouter, HTTPException
from typing import Dict, Any, List
from datetime import datetime, timedelta
import random

router = APIRouter()

@router.get("/health")
def check_health() -> Dict[str, Any]:
    """
    Health check endpoint to verify the API is running
    """
    return {
        "status": "ok",
        "message": "API is operational",
        "version": "1.0.0"
    }

@router.get("/example")
def get_example_data() -> Dict[str, Any]:
    """
    Example endpoint that returns test data
    """
    return {
        "examples": [
            {
                "id": 1,
                "name": "Example 1",
                "description": "This is an example item",
                "value": 42
            },
            {
                "id": 2,
                "name": "Example 2",
                "description": "Another example item",
                "value": 73
            },
            {
                "id": 3,
                "name": "Example 3",
                "description": "A third example item",
                "value": 128
            }
        ],
        "metadata": {
            "total": 3,
            "source": "Mexel API Example"
        }
    }
