from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import Dict, Any, List, Optional
from app.services.linkedin_service import LinkedInService
from app.services.ai.base import AIServiceBase, AIServiceFactory
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

# Dependency to get LinkedIn service
def get_linkedin_service() -> LinkedInService:
    return LinkedInService()

# Dependency to get AI service
def get_ai_service() -> AIServiceBase:
    return AIServiceFactory.create_service()

# Request and response models
class LinkedInPostGenerateRequest(BaseModel):
    topic: str = Field(..., description="Topic for the LinkedIn post")
    audience: Optional[str] = Field("Industry professionals", description="Target audience")
    tone: Optional[str] = Field("professional", description="Tone of the post")
    include_hashtags: bool = Field(True, description="Whether to include hashtags")

class LinkedInPostGenerateResponse(BaseModel):
    content: str = Field(..., description="Generated LinkedIn post content")
    hashtags: List[str] = Field([], description="Suggested hashtags")

class LinkedInPostRequest(BaseModel):
    content: str = Field(..., description="Content to post on LinkedIn")
    image_url: Optional[str] = Field(None, description="URL to image to include in post")

class LinkedInStatsResponse(BaseModel):
    followers: int = Field(..., description="Number of followers")
    engagement_rate: float = Field(..., description="Engagement rate")
    post_impressions_last_30d: int = Field(..., description="Post impressions in last 30 days")
    profile_views_last_30d: int = Field(..., description="Profile views in last 30 days")
    status: str = Field(..., description="Status of the request")
    message: Optional[str] = Field(None, description="Additional message")

# API endpoints
@router.post("/generate", response_model=LinkedInPostGenerateResponse)
async def generate_linkedin_post(
    request: LinkedInPostGenerateRequest,
    linkedin_service: LinkedInService = Depends(get_linkedin_service),
    ai_service: AIServiceBase = Depends(get_ai_service)
):
    """Generate content for a LinkedIn post."""
    try:
        result = await LinkedInService.generate_post_content(
            topic=request.topic,
            audience=request.audience,
            tone=request.tone,
            include_hashtags=request.include_hashtags,
            ai_service=ai_service
        )
        
        return LinkedInPostGenerateResponse(
            content=result.get("content", ""),
            hashtags=result.get("hashtags", [])
        )
    except Exception as e:
        logger.error(f"Error generating LinkedIn post: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/post")
async def post_to_linkedin(
    request: LinkedInPostRequest,
    linkedin_service: LinkedInService = Depends(get_linkedin_service)
):
    """Post content to LinkedIn (simulation)."""
    try:
        result = await linkedin_service.post_content(
            content=request.content,
            image_url=request.image_url
        )
        
        return result
    except Exception as e:
        logger.error(f"Error posting to LinkedIn: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats", response_model=LinkedInStatsResponse)
async def get_linkedin_stats(
    linkedin_service: LinkedInService = Depends(get_linkedin_service)
):
    """Get LinkedIn stats for the company page."""
    try:
        stats = await linkedin_service.get_company_stats()
        return LinkedInStatsResponse(**stats)
    except Exception as e:
        logger.error(f"Error getting LinkedIn stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
