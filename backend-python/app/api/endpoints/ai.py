from fastapi import APIRout<PERSON>, Depends, HTTPException, BackgroundTasks
from typing import Dict, Any, List, Optional
from app.services.ai.base import AIServiceBase, AIServiceFactory
from app.schemas.ai import TextGenerationRequest, TextGenerationResponse
from app.schemas.ai import KeywordExtractionRequest, KeywordExtractionResponse
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

# Dependency to get AI service
def get_ai_service() -> AIServiceBase:
    return AIServiceFactory.create_service()

@router.post("/generate", response_model=TextGenerationResponse)
async def generate_text(
    request: TextGenerationRequest,
    ai_service: AIServiceBase = Depends(get_ai_service)
):
    """Generate text using AI."""
    try:
        result = await ai_service.generate_text(
            prompt=request.prompt,
            system_prompt=request.system_prompt,
            temperature=request.temperature,
            max_tokens=request.max_tokens
        )
        
        return TextGenerationResponse(
            text=result.get("text", ""),
            model=result.get("model", ""),
            provider=result.get("provider", ""),
            error=result.get("error", False)
        )
    except Exception as e:
        logger.error(f"Error generating text: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/embeddings")
async def generate_embeddings(
    texts: List[str],
    ai_service: AIServiceBase = Depends(get_ai_service)
):
    """Generate embeddings for a list of texts."""
    try:
        embeddings = await ai_service.generate_embeddings(texts)
        return {"embeddings": embeddings}
    except Exception as e:
        logger.error(f"Error generating embeddings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# LinkedIn content endpoints
class LinkedInPostRequest(BaseModel):
    topic: str = Field(..., description="Topic for the LinkedIn post")
    audience: Optional[str] = Field(None, description="Target audience")
    tone: Optional[str] = Field("professional", description="Tone of the post")
    include_hashtags: bool = Field(True, description="Whether to include hashtags")
    max_length: int = Field(500, description="Maximum length in characters")

class LinkedInPostResponse(BaseModel):
    content: str = Field(..., description="Generated LinkedIn post content")
    hashtags: List[str] = Field(..., description="Suggested hashtags")

@router.post("/linkedin/post", response_model=LinkedInPostResponse)
async def generate_linkedin_post(
    request: LinkedInPostRequest,
    ai_service: AIServiceBase = Depends(get_ai_service)
):
    """Generate a LinkedIn post using AI."""
    try:
        # Create system prompt for LinkedIn post generation
        system_prompt = """
        You are a professional content writer specializing in LinkedIn posts for the 
        chemical water treatment industry. Create engaging, informative content that 
        highlights expertise and provides value to the reader. The content should be 
        conversational yet professional.
        """
        
        # Create user prompt with specific instructions
        prompt = f"""
        Create a LinkedIn post about {request.topic}.
        
        Target audience: {request.audience if request.audience else "Industry professionals and potential clients"}
        Tone: {request.tone}
        Maximum length: {request.max_length} characters
        
        The post should be engaging, include a hook at the beginning, and end with a 
        call to action. If appropriate, mention Mexel Energy Sustain as a provider 
        of chemical water treatment solutions.
        """
        
        # Generate the post content
        result = await ai_service.generate_text(
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=0.7,
            max_tokens=1000
        )
        
        post_content = result.get("text", "").strip()
        
        # Generate hashtags in a separate call if requested
        hashtags = []
        if request.include_hashtags:
            hashtag_prompt = f"Generate 5-7 relevant hashtags for a LinkedIn post about {request.topic} in the water treatment industry. Return only the hashtags as a comma-separated list without explanation."
            
            hashtag_result = await ai_service.generate_text(
                prompt=hashtag_prompt,
                temperature=0.6,
                max_tokens=200
            )
            
            # Process the hashtags
            hashtag_text = hashtag_result.get("text", "").strip()
            hashtags = [
                tag.strip().replace('#', '') 
                for tag in hashtag_text.split(',')
                if tag.strip()
            ]
            hashtags = ['#' + tag for tag in hashtags]
        
        return LinkedInPostResponse(
            content=post_content,
            hashtags=hashtags
        )
    except Exception as e:
        logger.error(f"Error generating LinkedIn post: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Define request and response models for tender analysis
class TenderAnalysisRequest(BaseModel):
    tender_text: str = Field(..., description="Tender text to analyze")
    company_products: Optional[List[str]] = Field(None, description="List of company products to match against")
    company_expertise: Optional[List[str]] = Field(None, description="Areas of company expertise")

class TenderAnalysisResponse(BaseModel):
    relevance_score: float = Field(..., description="Score from 0-1 indicating relevance")
    product_matches: List[str] = Field(..., description="Products that match the tender")
    key_requirements: List[str] = Field(..., description="Key requirements from the tender")
    recommended_approach: str = Field(..., description="AI-recommended approach")
    summary: str = Field(..., description="Brief summary of the tender")

@router.post("/analyze/tender", response_model=TenderAnalysisResponse)
async def analyze_tender(
    request: TenderAnalysisRequest,
    ai_service: AIServiceBase = Depends(get_ai_service)
):
    """Analyze a tender document using AI."""
    try:
        # Default products if none provided
        company_products = request.company_products or [
            "BioDisperse™ 3399", 
            "FerroSafe™ 3200",
            "CleanBioTech 5500",
            "AquaGuard Pro",
            "ScalePrev XT",
            "CoilClean Ultra"
        ]
        
        # Default expertise areas if none provided
        expertise_areas = request.company_expertise or [
            "Water treatment chemicals",
            "Cooling tower treatment",
            "Boiler water treatment",
            "Membrane cleaning chemicals",
            "Wastewater treatment solutions",
            "Chemical supply chain management"
        ]
        
        # Create the system prompt
        system_prompt = """
        You are an AI assistant specialized in analyzing water treatment tenders for Mexel Energy Sustain.
        Analyze the tender text provided and generate structured insights that will help the company 
        decide whether to pursue this opportunity and how to approach it.
        """
        
        # Create the user prompt with specific instructions
        prompt = f"""
        Analyze the following tender text for a water treatment chemicals company:

        TENDER TEXT:
        {request.tender_text}

        COMPANY PRODUCTS:
        {', '.join(company_products)}

        AREAS OF EXPERTISE:
        {', '.join(expertise_areas)}

        Provide the following information in your analysis:
        1. A relevance score between 0 and 1 (where 1 is highly relevant)
        2. Which company products match the tender requirements
        3. Key requirements from the tender
        4. A recommended approach for responding to this tender
        5. A brief summary of the tender (max 100 words)

        Format your response as a JSON object with the following keys:
        relevance_score, product_matches, key_requirements, recommended_approach, summary
        """
        
        # Generate the analysis
        result = await ai_service.generate_text(
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=0.3,
            max_tokens=1500
        )
        
        # Extract the JSON response
        import json
        import re
        
        # Get the response text
        response_text = result.get("text", "").strip()
        
        # Try to extract JSON object if it's embedded in text
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            response_text = json_match.group(0)
            
        try:
            analysis = json.loads(response_text)
            
            # Ensure all required fields are present
            required_fields = ['relevance_score', 'product_matches', 'key_requirements', 
                               'recommended_approach', 'summary']
            for field in required_fields:
                if field not in analysis:
                    analysis[field] = "" if field != 'relevance_score' else 0.0
                    if field in ['product_matches', 'key_requirements']:
                        analysis[field] = []
            
            return TenderAnalysisResponse(**analysis)
        except json.JSONDecodeError:
            # Fallback if JSON parsing fails
            logger.error(f"Failed to parse JSON response: {response_text}")
            return TenderAnalysisResponse(
                relevance_score=0.5,
                product_matches=["Unable to determine"],
                key_requirements=["Unable to extract"],
                recommended_approach="Unable to generate recommendation from AI response.",
                summary="The AI was unable to properly analyze this tender. Please review the text manually."
            )
            
    except Exception as e:
        logger.error(f"Error analyzing tender: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
