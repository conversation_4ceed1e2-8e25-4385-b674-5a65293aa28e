from email.message import EmailMessage
import smtplib
from pydantic import EmailStr
from typing import Optional
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

class EmailService:
    def __init__(self):
        self.smtp_server = settings.SMTP_SERVER
        self.smtp_port = settings.SMTP_PORT
        self.smtp_user = settings.SMTP_USER
        self.smtp_password = settings.SMTP_PASSWORD
        
    async def send_email(
        self,
        to: EmailStr,
        subject: str,
        body: str,
        from_email: Optional[EmailStr] = None
    ) -> bool:
        msg = EmailMessage()
        msg["Subject"] = subject
        msg["From"] = from_email or settings.DEFAULT_FROM_EMAIL
        msg["To"] = to
        msg.set_content(body)

        try:
            async with aiosmtplib.SMTP(
                hostname=self.smtp_server,
                port=self.smtp_port,
                use_tls=True
            ) as server:
                await server.login(self.smtp_user, self.smtp_password)
                await server.send_message(msg)
            return True
        except Exception as e:
            logger.error(f"Email send failed: {str(e)}")
            return False