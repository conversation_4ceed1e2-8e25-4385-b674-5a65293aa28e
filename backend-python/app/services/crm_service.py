from typing import Optional, Dict, Any
import httpx
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

class CRMService:
    def __init__(self):
        self.base_url = "https://api.hubapi.com"
        self.headers = {
            "Authorization": f"Bearer {settings.HUBSPOT_API_KEY}",
            "Content-Type": "application/json"
        }
    
    async def create_contact(self, contact_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        url = f"{self.base_url}/crm/v3/objects/contacts"
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=contact_data, headers=self.headers)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"Failed to create contact: {str(e)}")
            return None

    async def get_contact(self, contact_id: str) -> Optional[Dict[str, Any]]:
        url = f"{self.base_url}/crm/v3/objects/contacts/{contact_id}"
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=self.headers)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get contact: {str(e)}")
            return None

    # Add methods for companies/deals as needed