from typing import Dict, Any, Optional, List
import logging
from app.core.config import settings
from app.services.ai.base import AIServiceBase
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.language_models import BaseChatModel

logger = logging.getLogger(__name__)

class OpenAIService(AIServiceBase):
    """OpenAI service implementation."""
    
    def __init__(self):
        """Initialize the OpenAI service."""
        from pydantic.v1 import SecretStr
        import os
        
        # Get API key
        api_key = settings.OPENAI_API_KEY
        if not api_key:
            logger.error("OpenAI API key not configured.")
            raise ValueError("OpenAI API key not found in settings.")
            
        # Set API key in environment for LangChain
        if isinstance(api_key, SecretStr):
            os.environ["OPENAI_API_KEY"] = api_key.get_secret_value()
        else:
            os.environ["OPENAI_API_KEY"] = str(api_key)
        
        # Use MCP settings for model if available
        if settings.MCP_SETTINGS and "model" in settings.MCP_SETTINGS:
            model_settings = settings.MCP_SETTINGS["model"]
            if model_settings.get("provider", "").lower() == "openai":
                self.model = model_settings.get("model_name", settings.OPENAI_MODEL)
            else:
                self.model = settings.OPENAI_MODEL
        else:
            self.model = settings.OPENAI_MODEL
        
    async def generate_text(self, 
                     prompt: str, 
                     system_prompt: Optional[str] = None,
                     temperature: float = 0.7,
                     max_tokens: int = 500) -> Dict[str, Any]:
        """Generate text using OpenAI.
        
        Args:
            prompt (str): User prompt.
            system_prompt (Optional[str]): System instructions.
            temperature (float): Controls randomness.
            max_tokens (int): Maximum number of tokens to generate.
            
        Returns:
            Dict[str, Any]: Response with generated text and metadata.
        """
        try:
            # Initialize chat model with correct parameters
            params = {
                "model": self.model,
                "temperature": temperature,
            }
            if max_tokens:
                params["model_kwargs"] = {"max_tokens": max_tokens}
            
            chat = ChatOpenAI(**params)
            
            messages = []
            if system_prompt:
                messages.append(SystemMessage(content=system_prompt))
            messages.append(HumanMessage(content=prompt))
            
            response = chat.invoke(messages)
            
            # Handle the response correctly based on type
            if hasattr(response, 'content'):
                content = response.content
            else:
                # For newer versions of langchain that might return AIMessage
                content = str(response)
            
            return {
                "text": content,
                "model": self.model,
                "provider": "openai"
            }
        except Exception as e:
            logger.error(f"Error in OpenAI text generation: {str(e)}")
            return {
                "text": f"Error generating text: {str(e)}",
                "error": True,
                "provider": "openai"
            }
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for texts using OpenAI.
        
        Args:
            texts (List[str]): Texts to generate embeddings for.
            
        Returns:
            List[List[float]]: Embedding vectors.
        """
        try:
            embeddings = OpenAIEmbeddings(model="text-embedding-3-small")
            result = embeddings.embed_documents(texts)
            return result
        except Exception as e:
            logger.error(f"Error in OpenAI embeddings generation: {str(e)}")
            # Return empty embeddings on error
            return [[0.0] * 1536] * len(texts)
