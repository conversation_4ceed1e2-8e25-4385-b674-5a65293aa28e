from typing import Dict, Any, Optional, List
import logging
import httpx
from app.core.config import settings
from app.services.ai.base import AIServiceBase

logger = logging.getLogger(__name__)

class OllamaService(AIServiceBase):
    """Ollama service implementation for local LLM usage."""
    
    def __init__(self):
        """Initialize the Ollama service."""
        self.host = settings.OLLAMA_HOST
        self.endpoint = settings.OLLAMA_ENDPOINT
        self.model = settings.OLLAMA_MODEL
        self.api_url = f"{self.host}{self.endpoint}"
        
    async def generate_text(self, 
                     prompt: str, 
                     system_prompt: Optional[str] = None,
                     temperature: float = 0.7,
                     max_tokens: int = 500) -> Dict[str, Any]:
        """Generate text using Ollama.
        
        Args:
            prompt (str): User prompt.
            system_prompt (Optional[str]): System instructions.
            temperature (float): Controls randomness.
            max_tokens (int): Maximum number of tokens to generate.
            
        Returns:
            Dict[str, Any]: Response with generated text and metadata.
        """
        try:
            # Construct the full prompt with system instructions if provided
            full_prompt = prompt
            if system_prompt:
                full_prompt = f"{system_prompt}\n\n{prompt}"
                
            # Prepare the request payload
            payload = {
                "model": self.model,
                "prompt": full_prompt,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False
            }
            
            # Make the API request
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(self.api_url, json=payload)
                response.raise_for_status()
                result = response.json()
            
            # Extract the generated text from the response
            generated_text = result.get("response", "")
            
            return {
                "text": generated_text,
                "model": self.model,
                "provider": "ollama"
            }
            
        except Exception as e:
            logger.error(f"Error in Ollama text generation: {str(e)}")
            return {
                "text": f"Error generating text with Ollama: {str(e)}",
                "error": True,
                "provider": "ollama"
            }
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for texts using Ollama.
        
        Note: This is a simplified implementation as Ollama's embedding 
        support varies by model.
        
        Args:
            texts (List[str]): Texts to generate embeddings for.
            
        Returns:
            List[List[float]]: Embedding vectors (simplified).
        """
        try:
            # Ollama embedding endpoint
            embedding_url = f"{self.host}/api/embeddings"
            
            results = []
            async with httpx.AsyncClient(timeout=60.0) as client:
                for text in texts:
                    payload = {
                        "model": self.model,
                        "prompt": text
                    }
                    
                    response = await client.post(embedding_url, json=payload)
                    response.raise_for_status()
                    result = response.json()
                    
                    # Extract embeddings from response
                    # The actual response format depends on the Ollama version/model
                    embedding = result.get("embedding", [])
                    if not embedding:
                        # If no embedding returned, use a placeholder
                        embedding = [0.0] * 384  # Default dimension
                    
                    results.append(embedding)
            
            return results
        except Exception as e:
            logger.error(f"Error in Ollama embeddings generation: {str(e)}")
            # Return empty embeddings on error
            return [[0.0] * 384] * len(texts)
