from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import <PERSON>r<PERSON>utputParser, JsonOutputParser
from langchain_openai import ChatOpenAI
from langchain_ollama import ChatOllama
from typing import Dict, Any, List, Optional
import json
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

class TenderAnalysisAgent:
    """Agent for analyzing tender documents using LangChain."""
    
    def __init__(self):
        """Initialize the agent with appropriate models based on config."""
        self.ai_provider = settings.AI_PROVIDER.lower()
        
        # Set up the appropriate LLM based on configuration
        if self.ai_provider == "openai":
            self.llm = ChatOpenAI(
                model=settings.OPENAI_MODEL,
                temperature=0.2,
                openai_api_key=settings.OPENAI_API_KEY
            )
        elif self.ai_provider == "ollama":
            self.llm = ChatOllama(
                model=settings.OLLAMA_MODEL,
                base_url=settings.OLLAMA_HOST,
                temperature=0.2
            )
        else:
            # Default to OpenAI if provider not recognized
            logger.warning(f"Unrecognized AI provider: {self.ai_provider}. Falling back to OpenAI.")
            self.llm = ChatOpenAI(
                model=settings.OPENAI_MODEL,
                temperature=0.2,
                openai_api_key=settings.OPENAI_API_KEY
            )
        
        # Set up the prompt templates and chains
        self._setup_chains()
    
    def _setup_chains(self):
        """Set up the LangChain chains for different analysis tasks."""
        
        # Tender analysis prompt template
        tender_analysis_prompt = ChatPromptTemplate.from_template("""
        You're an expert in analyzing tender documents for a water treatment chemicals company.
        
        Company Products:
        {products}
        
        Areas of Expertise:
        {expertise_areas}
        
        Analyze the following tender document:
        {tender_text}
        
        Provide a detailed analysis in the following JSON format:
        {{
            "relevance_score": <float between 0 and 1>,
            "product_matches": [<list of products that match the tender>],
            "key_requirements": [<list of key requirements>],
            "recommended_approach": "<your recommendation>",
            "summary": "<brief summary of the tender>"
        }}
        
        Ensure your JSON is valid and properly formatted.
        """)
        
        # Set up the tender analysis chain with JSON output parser
        json_parser = JsonOutputParser()
        self.tender_analysis_chain = tender_analysis_prompt | self.llm | json_parser
        
        # Keyword extraction prompt template
        keyword_extraction_prompt = ChatPromptTemplate.from_template("""
        Extract the most relevant keywords from the following text. Focus on technical terms,
        product names, services, and industry-specific terminology.
        
        Text:
        {text}
        
        Return exactly {max_keywords} keywords as a JSON array of strings.
        """)
        
        # Set up the keyword extraction chain
        self.keyword_extraction_chain = keyword_extraction_prompt | self.llm | json_parser
    
    async def analyze_tender(
        self, 
        tender_text: str,
        company_products: Optional[List[str]] = None,
        expertise_areas: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Analyze a tender document and provide structured insights.
        
        Args:
            tender_text: The text of the tender document to analyze
            company_products: List of company products to match against
            expertise_areas: Areas of company expertise
            
        Returns:
            Dict containing analysis results
        """
        # Default products if none provided
        if not company_products:
            company_products = [
                "BioDisperse™ 3399", 
                "FerroSafe™ 3200",
                "CleanBioTech 5500",
                "AquaGuard Pro",
                "ScalePrev XT",
                "CoilClean Ultra"
            ]
        
        # Default expertise areas if none provided
        if not expertise_areas:
            expertise_areas = [
                "Water treatment chemicals",
                "Cooling tower treatment",
                "Boiler water treatment",
                "Membrane cleaning chemicals",
                "Wastewater treatment solutions",
                "Chemical supply chain management"
            ]
        
        try:
            # Run the tender analysis chain
            result = await self.tender_analysis_chain.ainvoke({
                "tender_text": tender_text,
                "products": ", ".join(company_products),
                "expertise_areas": ", ".join(expertise_areas)
            })
            
            return result
        except Exception as e:
            logger.error(f"Error in tender analysis: {str(e)}")
            # Return a default response on error
            return {
                "relevance_score": 0.0,
                "product_matches": [],
                "key_requirements": ["Error in analysis"],
                "recommended_approach": f"Error analyzing tender: {str(e)}",
                "summary": "An error occurred during analysis. Please try again or analyze manually."
            }
    
    async def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """
        Extract keywords from text.
        
        Args:
            text: The text to extract keywords from
            max_keywords: Maximum number of keywords to extract
            
        Returns:
            List of extracted keywords
        """
        try:
            # Run the keyword extraction chain
            result = await self.keyword_extraction_chain.ainvoke({
                "text": text,
                "max_keywords": max_keywords
            })
            
            # Ensure we have a list of strings
            if isinstance(result, list):
                return result
            return []
        except Exception as e:
            logger.error(f"Error extracting keywords: {str(e)}")
            return []
