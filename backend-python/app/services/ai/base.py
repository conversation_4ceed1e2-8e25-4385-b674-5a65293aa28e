from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

class AIServiceBase(ABC):
    """Base class for AI service integrations."""
    
    @abstractmethod
    async def generate_text(self, 
                      prompt: str, 
                      system_prompt: Optional[str] = None,
                      temperature: float = 0.7,
                      max_tokens: int = 500) -> Dict[str, Any]:
        """Generate text using the AI model.
        
        Args:
            prompt (str): The user prompt to generate text from.
            system_prompt (Optional[str]): System instructions for the model.
            temperature (float): Controls randomness in generation.
            max_tokens (int): Maximum number of tokens to generate.
            
        Returns:
            Dict[str, Any]: Response with generated text and metadata.
        """
        pass
    
    @abstractmethod
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of texts.
        
        Args:
            texts (List[str]): List of texts to generate embeddings for.
            
        Returns:
            List[List[float]]: List of embedding vectors.
        """
        pass


class AIServiceFactory:
    """Factory for creating AI service instances based on configuration."""
    
    @staticmethod
    def create_service() -> AIServiceBase:
        """Create and return an AI service based on configuration.
        
        Returns:
            AIServiceBase: An instance of an AI service.
            
        Note:
            Uses MCP settings to configure the AI service.
        """
        provider = settings.AI_PROVIDER.lower()
        
        # Import all service implementations upfront
        from app.services.ai.openai_service import OpenAIService
        
        if provider == "openai":
            return OpenAIService()
        elif provider == "ollama":
            try:
                from app.services.ai.ollama_service import OllamaService
                return OllamaService()
            except ImportError:
                logger.warning("Ollama service not available. Falling back to OpenAI.")
                return OpenAIService()
        elif provider == "deepseek":
            from app.services.ai.deepseek_service import DeepSeekService
            return DeepSeekService()
        else:
            logger.warning(f"Unknown AI provider: {provider}. Falling back to OpenAI.")
            from app.services.ai.openai_service import OpenAIService
            return OpenAIService()
