from sqlalchemy import Column, String, Integer, Float, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class Tender(Base):
    __tablename__ = "tenders"

    id = Column(String, primary_key=True, index=True)
    title = Column(String)
    description = Column(String)
    url = Column(String)
    source = Column(String)
    closing_date = Column(String)
    scrape_date = Column(String)
    status = Column(String)
    contact_person = Column(String, nullable=True)
    contact_email = Column(String, nullable=True)
    location = Column(String, nullable=True)
    estimated_value = Column(Float, nullable=True)
    categories = Column(ARRAY(String))
    requirements = Column(ARRAY(String))
    documents = Column(ARRAY(String))
    reference = Column(String, nullable=True)
    opportunity_score = Column(Float, nullable=True)
    relevance_score = Column(Float, nullable=True)
    historical_avg_value = Column(Float, nullable=True)
    historical_success_rate = Column(Float, nullable=True)