"""
Model Context Protocol (MCP) Settings Schema
This file defines the schema for MCP settings used by AI services.
"""
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Union, Any

class MCPInputSettings(BaseModel):
    """Model Context Protocol input settings."""
    max_input_tokens: int = Field(4096, description="Maximum number of input tokens")
    truncation_strategy: str = Field("end", description="Strategy for truncating input")

class MCPOutputSettings(BaseModel):
    """Model Context Protocol output settings."""
    max_output_tokens: int = Field(1000, description="Maximum number of output tokens")
    enable_sampling: bool = Field(True, description="Whether to enable sampling")
    temperature: float = Field(0.7, description="Temperature for sampling")
    top_p: float = Field(0.95, description="Top-p sampling parameter")
    top_k: int = Field(40, description="Top-k sampling parameter")

class MCPProviderSettings(BaseModel):
    """Model Context Protocol provider settings."""
    provider_name: str = Field("openai", description="AI provider name")
    model_name: str = Field("gpt-4", description="Model name")
    api_key: Optional[str] = Field(None, description="API key")
    api_base_url: Optional[str] = Field(None, description="API base URL")
    timeout_seconds: int = Field(30, description="API timeout in seconds")

class MCPSettings(BaseModel):
    """Model Context Protocol settings."""
    version: str = Field("1.0", description="MCP schema version")
    input_settings: MCPInputSettings = Field(
        default_factory=lambda: MCPInputSettings(
            max_input_tokens=4096,
            truncation_strategy="end"
        ),
        description="Input settings"
    )
    output_settings: MCPOutputSettings = Field(
        default_factory=lambda: MCPOutputSettings(
            max_output_tokens=1000,
            enable_sampling=True,
            temperature=0.7,
            top_p=0.95,
            top_k=40
        ),
        description="Output settings"
    )
    provider_settings: MCPProviderSettings = Field(
        default_factory=lambda: MCPProviderSettings(
            provider_name="openai",
            model_name="gpt-4",
            api_key=None,
            api_base_url=None,
            timeout_seconds=30
        ),
        description="Provider settings"
    )
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "version": "1.0",
                "input_settings": {
                    "max_input_tokens": 4096,
                    "truncation_strategy": "end"
                },
                "output_settings": {
                    "max_output_tokens": 1000,
                    "enable_sampling": True,
                    "temperature": 0.7,
                    "top_p": 0.95,
                    "top_k": 40
                },
                "provider_settings": {
                    "provider_name": "openai",
                    "model_name": "gpt-4",
                    "timeout_seconds": 30
                }
            }
        },
        "protected_namespaces": ()
    }
