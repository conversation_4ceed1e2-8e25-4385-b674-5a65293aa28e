from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

class TextGenerationRequest(BaseModel):
    prompt: str = Field(..., description="User prompt for text generation")
    system_prompt: Optional[str] = Field(None, description="System instructions for the AI")
    temperature: float = Field(0.7, description="Controls randomness (0.0-1.0)")
    max_tokens: int = Field(500, description="Maximum number of tokens to generate")

class TextGenerationResponse(BaseModel):
    text: str = Field(..., description="Generated text content")
    model: Optional[str] = Field(None, description="Model used for generation")
    provider: Optional[str] = Field(None, description="AI provider (openai, ollama, etc.)")
    error: Optional[bool] = Field(False, description="Whether an error occurred")

class KeywordExtractionRequest(BaseModel):
    text: str = Field(..., description="Text to extract keywords from")
    max_keywords: int = Field(10, description="Maximum number of keywords to extract")

class KeywordExtractionResponse(BaseModel):
    keywords: List[str] = Field(..., description="Extracted keywords")
