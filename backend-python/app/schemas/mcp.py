from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union

class MCPContextWindow(BaseModel):
    """Model Context Protocol context window settings."""
    max_tokens: int = Field(8192, description="Maximum number of tokens in the context window")
    reserved_tokens_percentage: float = Field(0.2, description="Percentage of tokens reserved for the response")

class MCPModelSettings(BaseModel):
    """Model Context Protocol model settings."""
    provider: str = Field("openai", description="AI provider (openai, ollama, etc.)")
    model_name: str = Field(..., description="Name of the model to use")
    temperature: float = Field(0.7, description="Controls randomness in generation")
    max_tokens: Optional[int] = Field(1000, description="Maximum number of tokens to generate")
    top_p: Optional[float] = Field(0.95, description="Nucleus sampling parameter")
    frequency_penalty: Optional[float] = Field(0.0, description="Frequency penalty parameter")
    presence_penalty: Optional[float] = Field(0.0, description="Presence penalty parameter")
    stop_sequences: Optional[List[str]] = Field(default_factory=list, description="Sequences that stop generation")

class MCPSettings(BaseModel):
    """Model Context Protocol settings schema."""
    version: str = Field("1.0", description="MCP schema version")
    context_window: MCPContextWindow = Field(
        default_factory=lambda: MCPContextWindow(
            max_tokens=8192,
            reserved_tokens_percentage=0.2
        ),
        description="Context window settings"
    )
    model: MCPModelSettings = Field(..., description="Model settings")
    system_prompt: Optional[str] = Field(
        default="You are a helpful assistant specialized in chemical water treatment technology.",
        description="Default system prompt"
    )
    tools_enabled: bool = Field(False, description="Whether tools are enabled")
    tool_schemas: Optional[List[Dict[str, Any]]] = Field(default_factory=list, description="Tool schemas")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "version": "1.0",
                "context_window": {
                    "max_tokens": 8192,
                    "reserved_tokens_percentage": 0.2
                },
                "model": {
                    "provider": "openai",
                    "model_name": "gpt-4",
                    "temperature": 0.7
                },
                "system_prompt": "You are a helpful assistant.",
                "tools_enabled": False
            }
        },
        "protected_namespaces": ()
    }
