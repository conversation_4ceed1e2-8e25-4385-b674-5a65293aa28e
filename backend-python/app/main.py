from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import socketio
from app.core.config import settings
from app.api import auth
from app.api.endpoints import ai, linkedin, general, websocket

# Create Socket.IO server instance
sio = socketio.AsyncServer(async_mode='asgi', cors_allowed_origins=settings.BACKEND_CORS_ORIGINS)
socket_app = socketio.ASGIApp(sio)

app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.PROJECT_DESCRIPTION,
    version=settings.VERSION
)

# Set up CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix=settings.API_V1_STR + "/auth", tags=["auth"])
app.include_router(ai.router, prefix=settings.API_V1_STR + "/ai", tags=["ai"])
app.include_router(linkedin.router, prefix=settings.API_V1_STR + "/linkedin", tags=["linkedin"])
app.include_router(general.router, prefix="/api", tags=["general"])
app.include_router(websocket.router, prefix="/api/websocket", tags=["websocket"])

# Mount Socket.IO application
app.mount("/socket.io", socket_app)

# Add static files directory for uploaded files if it exists
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except RuntimeError:
    import os
    os.makedirs("static", exist_ok=True)
    app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/")
def read_root():
    return {"message": "Welcome to the Mexel API"}

@app.get("/health")
def health_check():
    """Root health check endpoint for quick API status verification"""
    return {
        "status": "ok",
        "message": "API is operational"
    }

# Import and use enhanced Socket.IO manager
from app.core.websockets import SocketManager

# Initialize Socket.IO manager with all event handlers
socket_manager = SocketManager(sio)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3001)