# ===========================================================
# Web Framework and API 
# ===========================================================
fastapi==0.109.0               # High-performance API framework based on Starlette
uvicorn[standard]==0.27.0      # ASGI server for FastAPI
python-multipart==0.0.6        # Multipart form parser for file uploads
starlette==0.35.1              # Base ASGI framework for FastAPI
websockets==12.0               # WebSocket support for real-time communication
python-socketio==5.11.0        # Socket.IO integration for real-time communication

# ===========================================================
# Database 
# ===========================================================
sqlalchemy==2.0.31             # ORM for database operations
psycopg[binary]==3.1.18        # PostgreSQL driver (pure Python)
psycopg2-binary==2.9.9         # PostgreSQL driver (compiled)
alembic==1.13.1                # Database migration tool
SQLAlchemy-Utils==0.41.1       # SQLAlchemy helper utilities
asyncpg==0.29.0                # Asynchronous PostgreSQL driver for better performance

# ===========================================================
# Environment and Configuration
# ===========================================================
python-dotenv==1.0.0           # Environment variable loading from .env files
pydantic==2.8.2                # Data validation and settings management
pydantic-settings==2.3.0       # Pydantic integration for settings

# ===========================================================
# Authentication and Security
# ===========================================================
python-jose[cryptography]==3.3.0  # JWT token handling (OAuth compatible)
passlib[bcrypt]==1.7.4         # Password hashing utilities
bcrypt==4.1.2                  # Password hashing algorithm
cryptography==42.0.0           # Cryptographic primitives
itsdangerous==2.1.2            # Signing data for security

# ===========================================================
# Validation and Data Processing
# ===========================================================
email-validator==2.1.0         # Email validation library
pydantic[email]==2.8.2         # Extended email validation for Pydantic
phonenumbers==8.13.30          # Phone number validation and formatting

# ===========================================================
# API Documentation
# ===========================================================
typing-extensions==4.10.0      # Backported typing features
openapi-schema-pydantic==1.2.4 # OpenAPI schema generation

# ===========================================================
# Testing
# ===========================================================
pytest==7.4.3                  # Testing framework
pytest-cov==4.1.0              # Test coverage reporting
pytest-asyncio==0.23.4         # Async test support
httpx==0.26.0                  # HTTP client for testing
factory-boy==3.3.0             # Test data generation

# ===========================================================
# Background Tasks and Job Processing
# ===========================================================
celery==5.3.6                  # Distributed task queue
flower==2.0.1                  # Celery monitoring tool
redis==5.0.1                   # Redis client (for Celery and caching)
aioredis==2.0.1                # Async Redis client 

# ===========================================================
# Utilities
# ===========================================================
tenacity==8.2.3                # Retry library for robust operations
python-dateutil==2.8.2         # Date manipulation utilities
loguru==0.7.2                  # Better logging library
pandas==2.2.0                  # Data analysis and manipulation
faker==22.0.0                  # Fake data generation for development

# ===========================================================
# Performance and Monitoring
# ===========================================================
prometheus-client==0.19.0      # Metrics and monitoring
opentelemetry-api==1.21.0      # Distributed tracing
opentelemetry-sdk==1.21.0      # Telemetry implementation
sentry-sdk==1.40.5             # Error tracking

# ===========================================================
# File Handling and Storage
# ===========================================================
pillow==10.2.0                 # Image processing
python-magic==0.4.27           # File type detection
boto3==1.34.69                 # AWS S3 and other AWS services
