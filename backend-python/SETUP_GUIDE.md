# Step 1: Python Environment Setup Guide

This document provides detailed instructions for setting up the Python environment for the new Mexel backend.

## Prerequisites

- Python 3.9+ installed
- pip package manager
- PostgreSQL (to be installed during this process)

## A. Create and Configure the Python Virtual Environment

```bash
# 1. Create the directory for the new backend
mkdir -p ~/Desktop/Mexel/new-backend
cd ~/Desktop/Mexel/new-backend

# 2. Create a Python virtual environment
python -m venv venv

# 3. Activate the virtual environment
source venv/bin/activate

# 4. Verify activation (should show the Python path in the venv directory)
which python
# Expected output: /Users/<USER>/Desktop/Mexel/new-backend/venv/bin/python

# 5. Install core dependencies
pip install fastapi uvicorn[standard] python-socketio sqlalchemy alembic psycopg2-binary python-dotenv pytest pytest-cov

# 6. Install AI-related dependencies
pip install langchain openai anthropic

# 7. Verify installations
pip list

# 8. Freeze the dependencies for future reference
pip freeze > requirements.txt
```

## B. Create Basic Project Structure

```bash
# Create the project directory structure
mkdir -p app/{api,models,schemas,services,utils,agents,core}
mkdir -p tests/{api,models,services,agents}
mkdir -p scripts/migration
```

## C. Create Configuration Files

1. Create `.env` file:

```bash
touch .env
```

Add the following content to the `.env` file:

```
# Database Configuration
DATABASE_URL=postgresql://localhost/mexel

# API Configuration
API_PORT=3001
FRONTEND_URL=http://localhost:3000

# LLM Configuration
OPENAI_API_KEY=your_openai_api_key_here
```

2. Create `.gitignore` file:

```bash
touch .gitignore
```

Add the following content to the `.gitignore` file:

```
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
ENV/
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.sqlite
*.sqlite3
*.db

# Logs
logs/
*.log

# Testing
.coverage
htmlcov/
.pytest_cache/
```

## D. Verify the Setup

1. Create a simple test script to verify the environment:

```bash
touch test_setup.py
```

Add the following content to `test_setup.py`:

```python
import sys
import importlib.metadata # Use importlib.metadata for package checking

def check_python_version():
    python_version_full = sys.version.replace('\\n', ' ')
    print(f"Python Version: {python_version_full.split(' ')[0]}") # Keep it to one line

    major, minor = sys.version_info[:2]
    if major < 3 or (major == 3 and minor < 9):
        print("WARNING: Python version 3.9+ is recommended!")
        return False
    else:
        print("✅ Python version is 3.9 or higher.")
        return True

def get_package_version(package_name):
    try:
        return importlib.metadata.version(package_name)
    except importlib.metadata.PackageNotFoundError:
        return None

def check_installed_packages():
    required_packages = [
        'fastapi',
        'uvicorn',
        'python-socketio', # Package name for pip
        'sqlalchemy',
        'alembic',
        'psycopg2-binary', # Package name for pip
        'python-dotenv',   # Package name for pip
        'pytest',
        'pytest-cov',
        'langchain',
        'openai',
        'anthropic'
    ]

    all_found = True

    print("\\nChecking installed packages:")
    for package_name in required_packages:
        version = get_package_version(package_name)
        if version:
            # For python-socketio, the import name is 'socketio'
            # For psycopg2-binary, the import name is 'psycopg2'
            # For python-dotenv, the import name is 'dotenv'
            # This script checks installation via pip names, which is correct.
            print(f"✅ {package_name}: {version}")
        else:
            print(f"❌ {package_name}: Not installed")
            all_found = False
    return all_found

if __name__ == "__main__":
    print("--- Python Environment Setup Verification ---")
    version_ok = check_python_version()
    packages_ok = check_installed_packages()

    print("-" * 40)
    if version_ok and packages_ok:
        print("✅ Python environment setup verification successful!")
        sys.exit(0)
    else:
        print("❌ Python environment setup verification failed. Please review errors.")
        sys.exit(1)
```

Run the test script:

```bash
# Activate the virtual environment first if not already active
# source venv/bin/activate
python test_setup.py
```

**Expected Output (example):**

```
--- Python Environment Setup Verification ---
Python Version: 3.1X.X # Your Python version
✅ Python version is 3.9 or higher.

Checking installed packages:
✅ fastapi: 0.XX.X
✅ uvicorn: 0.XX.X
✅ python-socketio: 5.X.X
✅ sqlalchemy: 2.X.X
✅ alembic: 1.X.X
✅ psycopg2-binary: 2.X.X
✅ python-dotenv: 1.X.X
✅ pytest: 8.X.X
✅ pytest-cov: X.X.X
✅ langchain: 0.X.X
✅ openai: 1.X.X
✅ anthropic: 0.X.X
----------------------------------------
✅ Python environment setup verification successful!
```

**Actual Verification Result (as of last run):**
The script ran successfully, confirming all packages are installed and the Python version is appropriate.

2. Verify the project structure:

```bash
find . -type d -not -path "*/\.*" | sort
```

## E. Next Steps

After successfully setting up the Python environment:

1. Proceed to PostgreSQL database setup
2. Create the basic FastAPI application
3. Set up API routes and Socket.IO integration

## Troubleshooting

### Issue: Package installation fails

- Ensure your Python version is compatible with the packages
- Try updating pip: `pip install --upgrade pip`
- Check for any package-specific requirements in their documentation

### Issue: Virtual environment not activating properly

- Ensure you're using the correct activation command for your shell
- Try creating a new virtual environment from scratch

### Issue: Import errors when running Python code

- Verify the package is installed: `pip list | grep package_name`
- Check if your Python interpreter is correctly pointing to the virtual environment
