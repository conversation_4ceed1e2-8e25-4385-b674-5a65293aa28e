import pytest
from app.services.email_service import EmailService
from app.services.crm_service import CRMService
from app.core.config import settings
from pydantic import EmailStr

@pytest.mark.asyncio
async def test_email_service():
    service = EmailService()
    test_email = EmailStr("<EMAIL>")
    
    # Test with default settings
    result = await service.send_email(
        to=test_email,
        subject="Test Email",
        body="This is a test message"
    )
    
    assert isinstance(result, bool)

@pytest.mark.asyncio
async def test_crm_service():
    service = CRMService()
    
    # Test contact creation
    test_contact = {
        "properties": {
            "email": "<EMAIL>",
            "firstname": "Test",
            "lastname": "User"
        }
    }
    
    result = await service.create_contact(test_contact)
    assert result is None  # Should fail with default invalid API key

if __name__ == "__main__":
    pytest.main(["-v"])