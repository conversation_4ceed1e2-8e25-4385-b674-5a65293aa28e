#!/bin/bash

# This script installs the AI and LangChain dependencies for the Mexel backend

echo "Setting up <PERSON> and <PERSON><PERSON>hain for Mexel backend..."

# Check if Python virtual environment exists
if [ -d "venv" ]; then
    echo "Using existing virtual environment..."
    source venv/bin/activate
elif [ -d "venv311" ]; then
    echo "Using existing Python 3.11 virtual environment..."
    source venv311/bin/activate
else
    echo "Creating new virtual environment..."
    python -m venv venv
    source venv/bin/activate
fi

# Install base requirements if they're not already installed
if ! pip show fastapi > /dev/null 2>&1; then
    echo "Installing base requirements..."
    pip install -r requirements.txt
fi

# Install AI-specific requirements
echo "Installing AI and LangChain requirements..."
pip install -r requirements-ai.txt

# Check if the LangChain and OpenAI packages are properly installed
echo "Verifying installation..."
python -c "import langchain; import openai; print(f'LangChain version: {langchain.__version__}')"

echo "Setting up environment variables..."
# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "Creating .env file with AI configuration..."
    cat > .env << EOF
# AI Provider (openai, ollama)
AI_PROVIDER=openai

# OpenAI Config - replace with your actual key
OPENAI_API_KEY=sk-dummy-for-testing
OPENAI_MODEL=gpt-4

# Ollama Config
OLLAMA_HOST=http://localhost:11434
OLLAMA_ENDPOINT=/api/generate
OLLAMA_MODEL=llama3

# Copy other settings from .env.example as needed
EOF
fi

echo "Setup complete! You can now run the FastAPI backend with 'python -m app.main'"
