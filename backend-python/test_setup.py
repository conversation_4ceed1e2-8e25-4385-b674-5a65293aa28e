# /Users/<USER>/Desktop/Mexel/backend-python/test_setup.py
import sys
import importlib.metadata # Use importlib.metadata for package checking

def check_python_version():
    python_version_full = sys.version.replace('\n', ' ')
    print(f"Python Version: {python_version_full.split(' ')[0]}") # Keep it to one line

    major, minor = sys.version_info[:2]
    if major < 3 or (major == 3 and minor < 9):
        print("WARNING: Python version 3.9+ is recommended!")
        return False
    else:
        print("✅ Python version is 3.9 or higher.")
        return True

def get_package_version(package_name):
    try:
        return importlib.metadata.version(package_name)
    except importlib.metadata.PackageNotFoundError:
        return None

def check_installed_packages():
    required_packages = [
        'fastapi',
        'uvicorn',
        'python-socketio', # Package name for pip
        'sqlalchemy',
        'alembic',
        'psycopg2-binary', # Package name for pip
        'python-dotenv',   # Package name for pip
        'pytest',
        'pytest-cov',
        'langchain',
        'openai',
        'anthropic'
    ]

    all_found = True

    print("\nChecking installed packages:")
    for package_name in required_packages:
        version = get_package_version(package_name)
        if version:
            # For python-socketio, the import name is 'socketio'
            # For psycopg2-binary, the import name is 'psycopg2'
            # For python-dotenv, the import name is 'dotenv'
            # This script checks installation via pip names, which is correct.
            print(f"✅ {package_name}: {version}")
        else:
            print(f"❌ {package_name}: Not installed")
            all_found = False
    return all_found

if __name__ == "__main__":
    print("--- Python Environment Setup Verification ---")
    version_ok = check_python_version()
    packages_ok = check_installed_packages()

    print("-" * 40)
    if version_ok and packages_ok:
        print("✅ Python environment setup verification successful!")
        sys.exit(0)
    else:
        print("❌ Python environment setup verification failed. Please review errors.")
        sys.exit(1)
