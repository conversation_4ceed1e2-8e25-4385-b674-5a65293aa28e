# Application Settings
PROJECT_NAME="Full Stack Application"
PROJECT_DESCRIPTION="Full stack application with FastAPI backend and React frontend"
VERSION="1.0.0"

# Database Configuration
DATABASE_URL="postgresql://user:password@localhost:5432/mydb"

# Authentication Settings
SECRET_KEY="your-secret-key-here"
ACCESS_TOKEN_EXPIRE_MINUTES=11520  # 8 days
ALGORITHM="HS256"

# CORS Origins
BACKEND_CORS_ORIGINS='["http://localhost", "http://localhost:3000"]'