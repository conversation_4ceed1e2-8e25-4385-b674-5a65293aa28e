#!/usr/bin/env python3
"""
Mexel Backend Server Entry Point
"""
import os
import sys
import logging
import uvicorn
from pathlib import Path

# Add the project root directory to the Python path
project_root = str(Path(__file__).parent.absolute())
sys.path.insert(0, project_root)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Run the application server"""
    try:
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=int(os.getenv("PORT", "3001")),  # Changed default port to 3001 to match frontend config
            reload=True,
            reload_dirs=[project_root],
            log_level="info",
            workers=1
        )
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
