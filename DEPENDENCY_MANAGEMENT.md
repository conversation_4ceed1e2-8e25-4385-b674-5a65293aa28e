# Dependency Management Guide for Mexel

## Overview

This document outlines the dependency management approach for the Mexel project. It covers how dependencies are organized, how to maintain them, and best practices for adding or updating dependencies.

## Table of Contents
1. [Project Structure](#project-structure)
2. [Yarn Workspaces](#yarn-workspaces)
3. [Dependency Management Principles](#dependency-management-principles)
4. [Common Tasks](#common-tasks)
5. [Version Management](#version-management)
6. [Troubleshooting](#troubleshooting)

## Project Structure

The Mexel project is organized as a monorepo using Yarn workspaces:

```
Mexel/
├── package.json         # Root package.json with workspaces configuration
├── frontend/            # Frontend workspace
│   └── package.json     # Frontend-specific dependencies
├── shared/              # Shared code workspace
│   └── package.json     # Shared code dependencies
└── scripts/             # Project management scripts
    ├── setup-yarn.js    # Script to set up Yarn
    └── manage-workspaces.js # Workspace management utilities
```

## Yarn Workspaces

We use Yarn 3+ with workspaces to manage dependencies across packages:

### Key Benefits
- **Deduplication**: Dependencies are hoisted to the root, reducing duplication
- **Consistency**: All packages use the same version of dependencies
- **Simplified Management**: Internal dependencies are easy to reference

### Workspace Configuration

The workspaces are configured in the root `package.json`:

```json
{
  "workspaces": [
    "frontend",
    "shared"
  ]
}
```

### Referencing Workspaces

Internal workspace dependencies should use the `workspace:*` protocol:

```json
{
  "dependencies": {
    "shared": "workspace:*"
  }
}
```

## Dependency Management Principles

### 1. Single Source of Truth

- **Root package.json**: Contains all common dependencies and devDependencies
- **Resolutions**: Used to enforce specific versions of packages across the workspace
- **Workspace package.json**: Contains only workspace-specific dependencies

### 2. Dependency Types

- **Common Dependencies**: Defined in the root package.json (react, typescript, etc.)
- **Workspace-Specific**: Defined in the workspace's package.json
- **Peer Dependencies**: Used for shared packages to declare compatibility

### 3. Version Pinning Strategy

- **Direct Dependencies**: Use caret ranges (`^`) for most dependencies
- **Sensitive Dependencies**: Use tilde ranges (`~`) for packages where minor updates might break (e.g., TypeScript)
- **Resolutions**: Used to pin exact versions when necessary for security or compatibility

## Common Tasks

### Setting Up Yarn

To set up Yarn correctly for the project:

```bash
node scripts/setup-yarn.js
```

### Installing Dependencies

To install all dependencies:

```bash
yarn install
```

To add a dependency to a specific workspace:

```bash
yarn workspace frontend add react-query
```

To add a dependency to all workspaces:

```bash
yarn workspaces foreach -p add lodash
```

### Updating Dependencies

To check for outdated dependencies:

```bash
yarn outdated
```

To update dependencies interactively:

```bash
yarn upgrade-interactive
```

### Synchronizing Dependencies

To synchronize dependencies across workspaces:

```bash
yarn workspaces:sync
```

### Finding Duplicates

To check for duplicate dependencies:

```bash
yarn deps:check
```

To fix duplicate dependencies:

```bash
yarn deps:fix
```

### Running Workspace Health Checks

To run comprehensive workspace health checks:

```bash
yarn workspaces:doctor
```

## Version Management

### Node.js Version

- **Minimum Required**: >=18.0.0
- **Recommended**: Latest LTS version

### Yarn Version

- **Required**: >=3.0.0
- **Configured Version**: 3.6.3

### Managing Versions with Resolutions

The root `package.json` contains a `resolutions` field that enforces specific versions of dependencies:

```json
"resolutions": {
  "typescript": "^5.0.4",
  "socket.io-client": "^4.7.5",
  "recharts": "^2.15.3",
  "react": "^18.2.0",
  "react-dom": "^18.2.0"
}
```

## Troubleshooting

### Common Issues

#### Yarn Command Not Found

If the Yarn command is not found:

```bash
node scripts/setup-yarn.js
```

#### Dependency Conflicts

If you encounter dependency conflicts:

1. Check the `resolutions` field in the root package.json
2. Run `yarn deps:check` to identify duplicates
3. Run `yarn workspaces:doctor` for a comprehensive health check

#### Workspace Not Recognized

If a workspace is not recognized:

1. Ensure it's listed in the `workspaces` field in root package.json
2. Check that the workspace has a valid package.json
3. Run `yarn workspaces:validate` to check workspace configuration

### Advanced Troubleshooting

For advanced dependency issues:

```bash
# Clear the cache
yarn cache clean

# Reinstall everything from scratch
rm -rf node_modules .yarn/cache
yarn install
```

## Best Practices

1. **Keep Dependencies Minimal**: Only add dependencies you really need
2. **Regular Updates**: Schedule regular dependency updates
3. **Security Audits**: Run `yarn npm audit` periodically
4. **Test After Updates**: Always test after updating dependencies
5. **Document Breaking Changes**: When updating a major version, document any breaking changes

## Additional Resources

- [Yarn Documentation](https://yarnpkg.com/getting-started)
- [Yarn Workspaces Guide](https://yarnpkg.com/features/workspaces)
- [npm Semver Calculator](https://semver.npmjs.com/)