# Package Management Guide for Mexel

This document explains the package management approach used in the Mexel project.

## Overview

The Mexel project uses Yarn workspaces to manage dependencies across multiple packages:

- **Root**: Main project configuration
- **Frontend**: React frontend application
- **Shared**: Shared types and utilities

## Workspaces Configuration

Workspaces are configured in the root `package.json`:

```json
{
  "workspaces": [
    "frontend",
    "shared"
  ]
}
```

This configuration allows Yarn to:
- Install all dependencies in a single operation
- Hoist common dependencies to the root `node_modules`
- Enable cross-linking between local packages

## Dependency Management

### Resolutions

We use the `resolutions` field in the root `package.json` to enforce consistent dependency versions across all packages:

```json
"resolutions": {
  "typescript": "^5.0.4",
  "socket.io-client": "^4.7.5",
  "recharts": "^2.15.3",
  "react": "^18.2.0",
  "react-dom": "^18.2.0"
}
```

### Shared Package

The `shared` package is used by the frontend and is linked locally:

```json
"dependencies": {
  "shared": "file:../shared"
}
```

## Yarn Configuration

Yarn is configured using a root `.yarnrc.yml` file with these key settings:

- `nodeLinker: node-modules`: Uses classic node_modules layout
- Package extensions for resolving peer dependency issues
- Custom plugins for workspace management

## Common Scripts

These scripts are available for package management:

### Root Project

```bash
# Install all dependencies
yarn install

# List all workspaces
yarn workspaces:list

# Synchronize dependencies across workspaces
yarn workspaces:sync

# Validate workspace configuration
yarn workspaces:validate

# Run a command in all workspaces
yarn workspaces:run <command>

# Build all packages
yarn build:all
```

### Individual Packages

Each package has its own scripts but can be run from the root:

```bash
# Run a script in a specific workspace
yarn workspace frontend start
yarn workspace shared build
```

## Version Management

All packages maintain synchronized version numbers:

- All packages use version `1.0.0`
- Version bumps should be coordinated across all packages
- The `workspaces:sync` script helps ensure version consistency

## Dependency Guidelines

1. **Adding Dependencies**:
   ```bash
   # Add to a specific workspace
   yarn workspace frontend add axios
   
   # Add to all workspaces
   yarn workspaces foreach add lodash
   
   # Add as dev dependency
   yarn workspace shared add -D @types/lodash
   ```

2. **Removing Dependencies**:
   ```bash
   yarn workspace frontend remove unused-package
   ```

3. **Updating Dependencies**:
   ```bash
   # Update in all workspaces
   yarn upgrade-interactive
   
   # Then synchronize
   yarn workspaces:sync
   ```

## Troubleshooting

### Dependency Conflicts

If you encounter dependency conflicts:

1. Check for version mismatches with `yarn workspaces:validate`
2. Update the root `resolutions` field if needed
3. Run `yarn workspaces:sync` to propagate changes
4. Reinstall dependencies with `yarn install`

### Hoisting Issues

If a package can't find a dependency:

1. Add it explicitly to the package's dependencies
2. Run `yarn install`

## Best Practices

1. **Always use Yarn**: All scripts enforce Yarn usage
2. **Prefer workspace commands**: Use `yarn workspace` instead of changing directories
3. **Keep dependencies consistent**: Update the root resolutions when adding new shared dependencies
4. **Shared code**: Place shared code in the `shared` package
5. **Regular audits**: Run `yarn audit` periodically to check for vulnerabilities