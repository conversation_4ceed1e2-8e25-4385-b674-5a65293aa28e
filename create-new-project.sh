#!/bin/bash

echo "================== NEW PROJECT SETUP =================="
echo "This script will create a new React app and help you port your code"

# Set directory paths
CURRENT_DIR=/Users/<USER>/Desktop/Mexel
NEW_DIR=/Users/<USER>/Desktop/Mexel-Fresh

# Create new project directory and initialize React
mkdir -p "$NEW_DIR"
cd "$NEW_DIR"

echo "Creating a new React application with TypeScript..."
npx create-react-app . --template typescript

# Install dependencies that match your current project
echo "Installing required dependencies..."
npm install @mui/material @mui/icons-material @emotion/react @emotion/styled
npm install @mui/lab @mui/system @mui/x-date-pickers
npm install axios chart.js date-fns react-beautiful-dnd react-color
npm install react-chartjs-2 react-router-dom recharts socket.io-client

# Create directory structure similar to your current project
echo "Creating directory structure..."
mkdir -p src/components
mkdir -p src/types
mkdir -p src/utils
mkdir -p src/assets
mkdir -p src/contexts
mkdir -p src/hooks

# Create a script to help you copy files and make TypeScript fixes
cat > port-code.js << 'EOL'
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// Source and destination directories
const SOURCE_DIR = '/Users/<USER>/Desktop/Mexel';
const DEST_DIR = '/Users/<USER>/Desktop/Mexel-Fresh';

// Components to copy
const componentsToCopy = [
  'src/components/**/*',
  'src/types/**/*',
  'src/utils/**/*',
  'src/contexts/**/*',
  'src/hooks/**/*'
];

// Function to copy files
function copyFiles() {
  for (const pattern of componentsToCopy) {
    const command = `cp -r ${SOURCE_DIR}/${pattern} ${DEST_DIR}/${pattern.split('/')[0]}/`;
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error copying ${pattern}: ${error.message}`);
        return;
      }
      console.log(`Copied ${pattern} successfully`);
    });
  }
}

// Fix TypeScript 'as any' problems
function fixTypeScriptIssues() {
  const tsFiles = [];
  function findTsFiles(dir) {
    const files = fs.readdirSync(dir);
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      if (stat.isDirectory()) {
        findTsFiles(filePath);
      } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
        tsFiles.push(filePath);
      }
    }
  }

  findTsFiles(path.join(DEST_DIR, 'src'));

  for (const file of tsFiles) {
    try {
      let content = fs.readFileSync(file, 'utf8');

      // Fix malformed 'as any' syntax
      content = content.replace(/\(([^)]+) as any\)/g, '($1)');
      content = content.replace(/(\w+) as any\)/g, '$1)');

      // Fix type assertions
      content = content.replace(/(\w+) as any([;,])/g, '$1$2');
      content = content.replace(/(\w+) as any([ )])/g, '$1$2');

      fs.writeFileSync(file, content, 'utf8');
      console.log(`Fixed TypeScript issues in ${file}`);
    } catch (err) {
      console.error(`Error processing file ${file}: ${err.message}`);
    }
  }
}

// Main function
function main() {
  console.log('Starting code porting process...');
  copyFiles();

  // Wait for copying to complete before fixing TypeScript issues
  setTimeout(() => {
    console.log('Fixing TypeScript issues...');
    fixTypeScriptIssues();
    console.log('Code porting complete!');
  }, 5000);
}

main();
EOL

echo "================== SETUP COMPLETE =================="
echo "New React app created at: $NEW_DIR"
echo ""
echo "To port your code, run:"
echo "cd $NEW_DIR && node port-code.js"
echo ""
echo "After that, you can start the app with:"
echo "npm start"
