# Node modules and dependencies
node_modules/
.yarn/
**/.yarn
**/node_modules
**/.pnp.*

# Build directories
dist/
build/
coverage/
.next/
out/
**/dist

# Generated files
**/*.min.js
**/*.bundle.js
**/*.js.map
**/*.d.ts.map
**/*.d.ts
!**/types/**/*.d.ts

# Virtual environments
.venv/
venv/
**/.venv
**/venv

# Project specific
backend-python/
# Don't ignore shared and frontend which have their own ESLint configs
# !shared/
# !frontend/

# Backup and temporary files
*.bak
*.swp
*.tmp
*~

# Test directories
test-results/
playwright-report/
**/test-results
**/playwright-report

# Configuration files
**/.vscode
**/.idea
.github/

# Cache files
.eslintcache
.stylelintcache
.parcel-cache/

# Miscellaneous
.DS_Store
.env
.env.*
!.env.example