# Yarn Package Manager Policy

## Overview

This project uses Yarn exclusively as its package manager. NPM is not allowed in this project.

## Why Yarn?

We've chosen Yarn for this project for several reasons:

1. **Consistency**: Yarn provides more consistent installs across different environments.
2. **Performance**: Yarn can be faster than npm, especially with parallel installations.
3. **Workspace support**: Better monorepo support for our project structure.
4. **Security**: Yarn's lock file provides more security and stability.

## Guidelines

1. **Always use Yarn commands**:

   - Use `yarn add` instead of `npm install`
   - Use `yarn` instead of `npm install`
   - Use `yarn run` instead of `npm run`

2. **Never use npm commands**:

   - The project has been configured to prevent the use of npm
   - All package.json files include a preinstall script that will fail if npm is used

3. **Enforcing Yarn**:
   - `.npmrc` files are set up to prevent npm usage
   - `.yarnrc` files enforce yarn configurations
   - The `enforce-yarn.sh` script can be run to ensure full yarn compliance

## Setting Up New Developers

When onboarding new developers:

1. Ensure they have Yarn installed: `brew install yarn` or similar
2. Run `./enforce-yarn.sh` to enforce yarn policy
3. Run `yarn install` at the project root

## Troubleshooting

If you encounter issues:

1. Delete `node_modules` directories: `find . -name "node_modules" -type d -exec rm -rf {} +`
2. Run `./enforce-yarn.sh` script
3. Run `yarn install` to reinstall dependencies

## Migration From npm

If you have existing npm dependencies, migrate to yarn:

1. Remove all `package-lock.json` files
2. Run `yarn install` to generate yarn.lock

## Questions

If you have any questions about the Yarn policy, please refer to this document or contact the project maintainers.
