#!/bin/zsh

# test-websocket.sh - <PERSON>ript to test WebSocket connectivity
# This script starts the backend server and runs the WebSocket diagnostics tool

# Color codes for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo "${BLUE}=======================================================${NC}"
echo "${BLUE}         Mexel WebSocket Connection Tester             ${NC}"
echo "${BLUE}=======================================================${NC}"

# Check if backend directory exists
if [ ! -d "./backend-python" ]; then
  echo "${RED}Error: backend-python directory not found.${NC}"
  echo "Please run this script from the root directory of the Mexel project."
  exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
  echo "${RED}Error: Node.js is not installed.${NC}"
  echo "Please install Node.js to use the WebSocket test tools."
  exit 1
fi

# Check if required JavaScript files exist
if [ ! -f "./websocket-diagnostics.js" ]; then
  echo "${RED}Error: websocket-diagnostics.js not found.${NC}"
  echo "Make sure all WebSocket test scripts are in the project root."
  exit 1
fi

# Check if socket.io-client is installed
if ! npm list socket.io-client | grep -q "socket.io-client"; then
  echo "${YELLOW}Installing socket.io-client...${NC}"
  npm install --save-dev socket.io-client
fi

# Function to check if port is in use
is_port_in_use() {
  lsof -i:"$1" &> /dev/null
  return $?
}

# First check if backend server is already running
if is_port_in_use 3001; then
  echo "${YELLOW}Port 3001 is already in use. The backend server might be running.${NC}"
  echo "Do you want to:"
  echo "1) Continue with diagnostics (assuming server is running)"
  echo "2) Stop existing process and start fresh"
  echo "3) Exit"
  read choice
  
  case $choice in
    1)
      echo "${GREEN}Continuing with diagnostics...${NC}"
      ;;
    2)
      echo "${YELLOW}Stopping process on port 3001...${NC}"
      kill -9 $(lsof -t -i:3001) 2> /dev/null
      echo "${GREEN}Starting backend server...${NC}"
      cd backend-python && python run.py &
      SERVER_PID=$!
      cd ..
      sleep 2
      ;;
    *)
      echo "Exiting."
      exit 0
      ;;
  esac
else
  # Start backend server
  echo "${GREEN}Starting backend server...${NC}"
  cd backend-python && python run.py &
  SERVER_PID=$!
  cd ..
  echo "${YELLOW}Waiting for server to initialize (5 seconds)...${NC}"
  sleep 5
fi

echo "${GREEN}Running WebSocket diagnostics...${NC}"
node websocket-diagnostics.js

# Clean up server process if we started it
if [ -n "$SERVER_PID" ]; then
  echo "${YELLOW}Stopping backend server...${NC}"
  kill $SERVER_PID
fi

echo "${BLUE}=======================================================${NC}"
echo "${GREEN}Testing complete!${NC}"
echo "${BLUE}=======================================================${NC}"
