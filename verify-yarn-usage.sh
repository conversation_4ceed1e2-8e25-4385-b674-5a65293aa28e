#!/bin/bash

# Verify that yarn is being used properly

echo "Verifying yarn usage in project..."

# Check if yarn is installed
if ! command -v yarn &> /dev/null; then
    echo "❌ Yarn is not installed. Please install yarn first:"
    echo "   https://yarnpkg.com/getting-started/install"
    exit 1
fi

# Check for package-lock.json files
PACKAGE_LOCKS=$(find . -name "package-lock.json" -type f)
if [ -n "$PACKAGE_LOCKS" ]; then
    echo "❌ Found package-lock.json files, which indicates npm usage:"
    echo "$PACKAGE_LOCKS"
    echo "   Please run enforce-yarn.sh script again."
else
    echo "✅ No package-lock.json files found."
fi

# Check for preinstall hooks in package.json files
MISSING_HOOKS=0
for pkg in $(find . -name "package.json" -type f); do
    if ! grep -q "preinstall" "$pkg"; then
        echo "❌ Missing preinstall hook in $pkg"
        MISSING_HOOKS=1
    fi
done

if [ $MISSING_HOOKS -eq 0 ]; then
    echo "✅ All package.json files have preinstall hooks."
else
    echo "   Please run enforce-yarn.sh script again."
fi

# Check for yarn.lock files
MISSING_LOCKS=0
for dir in $(find . -name "package.json" -type f -not -path "*/node_modules/*" -exec dirname {} \;); do
    if [ ! -f "$dir/yarn.lock" ]; then
        echo "❌ Missing yarn.lock in $dir"
        MISSING_LOCKS=1
    fi
done

if [ $MISSING_LOCKS -eq 0 ]; then
    echo "✅ All package directories have yarn.lock files."
else
    echo "   Please run 'yarn install' in the missing directories."
fi

echo "Verification complete!"
