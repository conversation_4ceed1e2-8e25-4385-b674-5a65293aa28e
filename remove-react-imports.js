#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function removeReactImportsFromServices() {
  const servicesDir = '/Users/<USER>/Desktop/Mexel/frontend/src/services';

  function processDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);

    items.forEach(item => {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        processDirectory(fullPath);
      } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
        processFile(fullPath);
      }
    });
  }

  function processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');

      // Remove React import lines from service files
      const lines = content.split('\n');
      const filteredLines = lines.filter(line => {
        // Remove lines that import React but aren't using JSX
        return !line.trim().startsWith("import React from 'react';");
      });

      const newContent = filteredLines.join('\n');

      if (newContent !== content) {
        fs.writeFileSync(filePath, newContent);
        console.log(`✅ Removed React import from: ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  try {
    processDirectory(servicesDir);
    console.log('✨ Completed removing unnecessary React imports from service files');
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

removeReactImportsFromServices();
