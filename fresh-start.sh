#!/bin/bash

echo "================== FRESH START SETUP =================="
echo "This script will clean up Node.js modules and set up a simplified workspace"

# Kill any running Node processes (except VS Code helpers)
echo "Killing any running Node.js processes..."
pkill -f "mcp-server|context7-mcp|npm|yarn" || true

# Clean up node_modules directories
echo "Cleaning up node_modules directories..."
find . -name "node_modules" -type d -exec rm -rf {} +
find . -name ".yarn" -type d -exec rm -rf {} +
find . -name ".pnp.*" -type f -delete
find . -name "yarn-error.log" -type f -delete
find . -name ".yarnrc.yml" -type f -delete
find . -name ".yarnrc" -type f -delete

# Remove yarn.lock files and tsconfig.tsbuildinfo
echo "Removing yarn.lock files and tsconfig.tsbuildinfo..."
find . -name "*.lock" -type f -delete
find . -name "package-lock.json" -type f -delete
find . -name "tsconfig.tsbuildinfo" -type f -delete

# Create a simpler .yarnrc.yml
echo "Creating a simplified .yarnrc.yml..."
cat > .yarnrc.yml << 'EOL'
nodeLinker: node-modules
nmMode: classic
EOL

# Modify package.json to use simpler workspace configuration
echo "Updating root package.json..."
node -e '
const fs = require("fs");
const path = require("path");
const packageJson = JSON.parse(fs.readFileSync("package.json", "utf8"));

// Simplify configuration
packageJson.packageManager = "yarn@3.6.3";
if (packageJson.engines) {
  delete packageJson.engines.yarn;
  packageJson.engines.npm = ">=6.0.0";
}

// Write updated package.json
fs.writeFileSync("package.json", JSON.stringify(packageJson, null, 2));
'

# Create/update frontend .npmrc to use node-modules
echo "Updating frontend configuration..."
echo "node-linker=node-modules" > frontend/.npmrc

# Install dependencies using yarn 3.6.3
echo "Installing dependencies using Yarn 3.6.3..."
yarn set version 3.6.3
yarn install

echo "================== SETUP COMPLETE =================="
echo "You can now try running:"
echo "cd frontend && yarn start"
echo ""
echo "If you still encounter issues, consider:"
echo "1. Using npm directly instead of yarn"
echo "2. Creating a fresh project with create-react-app and copying over your source files"
