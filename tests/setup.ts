import { jest, beforeAll, afterAll } from '@jest/globals';
import type { Config } from '@jest/types';

// Extend the timeout for all tests to 2 minutes
jest.setTimeout(120000);

// Store original env vars to restore after tests
const originalEnv = { ...process.env };

beforeAll(() => {
  // Set test environment variables
  Object.defineProperty(process.env, 'NODE_ENV', {
    value: 'test',
    writable: true,
    enumerable: true,
    configurable: true,
  });
  process.env.LOG_LEVEL = 'error'; // Reduce logging noise during tests
});

afterAll(() => {
  // Restore original env vars
  Object.defineProperty(process.env, 'NODE_ENV', {
    value: originalEnv.NODE_ENV,
    writable: true,
    enumerable: true,
    configurable: true,
  });
  process.env.LOG_LEVEL = originalEnv.LOG_LEVEL;

  // Remove any test-specific listeners to prevent memory leaks
  process.removeAllListeners('unhandledRejection');
  process.removeAllListeners('uncaughtException');
});

// Global error handlers for test process
process.on('unhandledRejection', (error: Error | unknown) => {
  console.error(
    'Unhandled Promise Rejection during tests:',
    error instanceof Error
      ? {
          message: error.message,
          stack: error.stack,
          name: error.name,
        }
      : error
  );
  // Fail tests on unhandled rejections
  process.exit(1);
});

process.on('uncaughtException', (error: Error) => {
  console.error('Uncaught Exception during tests:', {
    message: error.message,
    stack: error.stack,
    name: error.name,
  });
  // Fail tests on uncaught exceptions
  process.exit(1);
});

// Utility function to wait for async operations
export const waitForAsync = (ms: number): Promise<void> =>
  new Promise(resolve => setTimeout(resolve, ms));

// Utility function to wait for a condition
export const waitFor = async (
  condition: () => boolean | Promise<boolean>,
  timeout: number = 5000,
  interval: number = 100
): Promise<boolean> => {
  const startTime = Date.now();
  while (Date.now() - startTime < timeout) {
    if (await condition()) return true;
    await waitForAsync(interval);
  }
  return false;
};
