import { test, expect } from '@playwright/test';
import { TenderStatus } from '@/types/tender';

test.describe('Tender Monitoring System E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // Login
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'admin123');
    await page.click('[data-testid="login-button"]');
    await page.waitForSelector('[data-testid="dashboard"]');
  });

  test('should display tender dashboard with live data', async ({ page }) => {
    // Navigate to tenders page
    await page.click('[data-testid="tenders-nav"]');
    
    // Verify tender list is loaded
    const tenderList = page.locator('[data-testid="tender-list"]');
    await expect(tenderList).toBeVisible();
    
    // Check tender statistics
    const statsPanel = page.locator('[data-testid="tender-stats"]');
    await expect(statsPanel).toBeVisible();
    await expect(statsPanel.locator('text=Total Tenders')).toBeVisible();
    await expect(statsPanel.locator('text=High Priority')).toBeVisible();
  });

  test('should filter and sort tenders', async ({ page }) => {
    await page.click('[data-testid="tenders-nav"]');
    
    // Apply filters
    await page.click('[data-testid="filter-button"]');
    await page.selectOption('[data-testid="status-filter"]', TenderStatus.OPEN);
    await page.fill('[data-testid="value-min"]', '100000');
    await page.click('[data-testid="apply-filters"]');
    
    // Verify filtered results
    const tenderItems = page.locator('[data-testid="tender-item"]');
    const count = await tenderItems.count();
    expect(count).toBeGreaterThan(0);
    
    // Sort by value
    await page.click('[data-testid="sort-value"]');
    const firstValue = await tenderItems.first().locator('[data-testid="tender-value"]').innerText();
    const lastValue = await tenderItems.last().locator('[data-testid="tender-value"]').innerText();
    expect(parseInt(firstValue)).toBeGreaterThan(parseInt(lastValue));
  });

  test('should display tender details and updates', async ({ page }) => {
    await page.click('[data-testid="tenders-nav"]');
    
    // Open first tender
    await page.click('[data-testid="tender-item"]');
    
    // Verify tender details
    const detailsPage = page.locator('[data-testid="tender-details"]');
    await expect(detailsPage).toBeVisible();
    await expect(page.locator('[data-testid="tender-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="tender-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="tender-value"]')).toBeVisible();
    
    // Update tender status
    await page.click('[data-testid="update-status"]');
    await page.selectOption('[data-testid="status-select"]', TenderStatus.IN_PROGRESS);
    await page.click('[data-testid="save-status"]');
    
    // Verify status update
    await expect(page.locator(`[data-testid="status-badge"]:has-text("${TenderStatus.IN_PROGRESS}")`)).toBeVisible();
  });

  test('should handle real-time tender updates', async ({ page, context }) => {
    // Open two pages to test real-time updates
    const secondPage = await context.newPage();
    await secondPage.goto('/');
    await secondPage.fill('[data-testid="email"]', '<EMAIL>');
    await secondPage.fill('[data-testid="password"]', 'admin123');
    await secondPage.click('[data-testid="login-button"]');
    
    // Navigate to tenders on both pages
    await page.click('[data-testid="tenders-nav"]');
    await secondPage.click('[data-testid="tenders-nav"]');
    
    // Update tender on first page
    await page.click('[data-testid="tender-item"]');
    await page.click('[data-testid="update-status"]');
    await page.selectOption('[data-testid="status-select"]', TenderStatus.IN_REVIEW);
    await page.click('[data-testid="save-status"]');
    
    // Verify update appears on second page
    await expect(secondPage.locator(`[data-testid="status-badge"]:has-text("${TenderStatus.IN_REVIEW}")`))
      .toBeVisible({ timeout: 5000 });
  });

  test('should process new tender submissions', async ({ page }) => {
    await page.click('[data-testid="tenders-nav"]');
    await page.click('[data-testid="new-tender"]');
    
    // Fill tender form
    await page.fill('[data-testid="tender-title-input"]', 'E2E Test Tender');
    await page.fill('[data-testid="tender-description-input"]', 'Test tender for E2E testing');
    await page.fill('[data-testid="tender-value-input"]', '500000');
    await page.fill('[data-testid="tender-deadline-input"]', '2024-12-31');
    await page.fill('[data-testid="tender-organization-input"]', 'Test Corp');
    await page.fill('[data-testid="tender-location-input"]', 'Cape Town');
    
    // Submit tender
    await page.click('[data-testid="submit-tender"]');
    
    // Verify submission
    await expect(page.locator('text=Tender created successfully')).toBeVisible();
    
    // Verify tender appears in list
    await page.click('[data-testid="tenders-nav"]');
    await expect(page.locator('text=E2E Test Tender')).toBeVisible();
  });

  test('should handle tender analysis and scoring', async ({ page }) => {
    await page.click('[data-testid="tenders-nav"]');
    await page.click('[data-testid="new-tender"]');
    
    // Create relevant tender
    await page.fill('[data-testid="tender-title-input"]', 'Chemical Processing Equipment');
    await page.fill('[data-testid="tender-description-input"]', 'Industrial chemical processing equipment for water treatment');
    await page.fill('[data-testid="tender-value-input"]', '1000000');
    await page.fill('[data-testid="tender-deadline-input"]', '2024-12-31');
    await page.click('[data-testid="submit-tender"]');
    
    // Verify analysis results
    await expect(page.locator('[data-testid="relevance-score"]')).toBeVisible();
    const score = await page.locator('[data-testid="relevance-score"]').innerText();
    expect(parseFloat(score)).toBeGreaterThan(0.7);
    
    // Verify tender appears in high priority section
    await page.click('[data-testid="tenders-nav"]');
    await page.click('[data-testid="priority-filter"]');
    await expect(page.locator('text=Chemical Processing Equipment')).toBeVisible();
  });

  test('should generate and display tender reports', async ({ page }) => {
    await page.click('[data-testid="reports-nav"]');
    
    // Generate tender report
    await page.click('[data-testid="generate-report"]');
    await page.selectOption('[data-testid="report-type"]', 'tender-analysis');
    await page.fill('[data-testid="date-from"]', '2024-01-01');
    await page.fill('[data-testid="date-to"]', '2024-12-31');
    await page.click('[data-testid="run-report"]');
    
    // Verify report content
    await expect(page.locator('[data-testid="report-content"]')).toBeVisible();
    await expect(page.locator('[data-testid="tender-metrics"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-rate"]')).toBeVisible();
    
    // Export report
    await page.click('[data-testid="export-report"]');
    const download = await page.waitForEvent('download');
    expect(download.suggestedFilename()).toContain('tender-analysis');
  });
});