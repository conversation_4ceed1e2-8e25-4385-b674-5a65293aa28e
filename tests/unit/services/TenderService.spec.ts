import { describe, expect, it, jest, beforeEach } from '@jest/globals';
import { TenderService } from '@/services/TenderService';
import { ITender, TenderStatus, TenderSource } from '@/types/tender';
import { TenderRepository } from '@/repositories/TenderRepository';

// Mock the repository
jest.mock('@/repositories/TenderRepository');

describe('TenderService', () => {
  let tenderService: TenderService;
  let mockRepository: jest.Mocked<TenderRepository>;

  const mockTender: ITender = {
    id: '1',
    title: 'Test Tender',
    description: 'Chemical processing equipment needed',
    value: 100000,
    status: TenderStatus.OPEN,
    source: TenderSource.ETENDERS,
    deadline: new Date('2024-12-31'),
    createdAt: new Date(),
    updatedAt: new Date(),
    relevanceScore: 0,
    keywords: ['chemical', 'processing', 'equipment'],
    location: 'South Africa',
    organization: 'Test Corp'
  };

  beforeEach(() => {
    mockRepository = {
      findById: jest.fn(),
      findAll: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findByKeywords: jest.fn(),
      findByStatus: jest.fn()
    };

    tenderService = new TenderService(mockRepository);
  });

  describe('calculateRelevanceScore', () => {
    it('should return high score for relevant keywords', () => {
      const tender = {
        ...mockTender,
        title: 'Chemical Water Treatment',
        description: 'Industrial cleaning solutions needed'
      };

      const score = tenderService.calculateRelevanceScore(tender);
      expect(score).toBeGreaterThan(0.7);
    });

    it('should return low score for irrelevant content', () => {
      const tender = {
        ...mockTender,
        title: 'Office Supplies',
        description: 'Paper and stationery items'
      };

      const score = tenderService.calculateRelevanceScore(tender);
      expect(score).toBeLessThan(0.3);
    });

    it('should consider tender value in scoring', () => {
      const highValueTender = {
        ...mockTender,
        value: 1000000,
        title: 'Chemical Processing',
      };

      const lowValueTender = {
        ...mockTender,
        value: 10000,
        title: 'Chemical Processing',
      };

      const highScore = tenderService.calculateRelevanceScore(highValueTender);
      const lowScore = tenderService.calculateRelevanceScore(lowValueTender);

      expect(highScore).toBeGreaterThan(lowScore);
    });
  });

  describe('findRelevantTenders', () => {
    it('should return tenders above relevance threshold', async () => {
      const mockTenders = [
        { ...mockTender, relevanceScore: 0.8 },
        { ...mockTender, relevanceScore: 0.3 },
        { ...mockTender, relevanceScore: 0.9 }
      ];

      mockRepository.findAll.mockResolvedValue(mockTenders);

      const relevantTenders = await tenderService.findRelevantTenders();
      expect(relevantTenders).toHaveLength(2);
      expect(relevantTenders[0].relevanceScore).toBeGreaterThan(0.7);
      expect(relevantTenders[1].relevanceScore).toBeGreaterThan(0.7);
    });
  });

  describe('processTender', () => {
    it('should process and store new tender', async () => {
      const newTender = { ...mockTender, id: undefined };
      mockRepository.create.mockResolvedValue({ ...mockTender, id: '2' });

      const result = await tenderService.processTender(newTender);

      expect(result.id).toBeDefined();
      expect(result.relevanceScore).toBeDefined();
      expect(mockRepository.create).toHaveBeenCalled();
    });

    it('should update existing tender', async () => {
      mockRepository.findById.mockResolvedValue(mockTender);
      mockRepository.update.mockResolvedValue({ ...mockTender, updatedAt: new Date() });

      const result = await tenderService.processTender(mockTender);

      expect(result.id).toBe(mockTender.id);
      expect(result.updatedAt).not.toBe(mockTender.updatedAt);
      expect(mockRepository.update).toHaveBeenCalled();
    });

    it('should handle tender with missing fields', async () => {
      const incompleteTender = {
        title: 'Incomplete Tender',
        description: 'Missing fields'
      };

      await expect(tenderService.processTender(incompleteTender as ITender))
        .rejects.toThrow('Invalid tender data');
    });
  });

  describe('analyzeTenderContent', () => {
    it('should extract relevant keywords', () => {
      const tender = {
        ...mockTender,
        description: 'Need industrial chemical processing equipment for water treatment'
      };

      const analysis = tenderService.analyzeTenderContent(tender);
      expect(analysis.keywords).toContain('chemical');
      expect(analysis.keywords).toContain('processing');
      expect(analysis.keywords).toContain('water treatment');
    });

    it('should identify tender category', () => {
      const tender = {
        ...mockTender,
        description: 'Supply and installation of industrial chemical processing equipment'
      };

      const analysis = tenderService.analyzeTenderContent(tender);
      expect(analysis.category).toBe('CHEMICAL_PROCESSING');
    });
  });

  describe('validateTender', () => {
    it('should validate required fields', () => {
      const invalidTender = {
        ...mockTender,
        title: '',
        value: -1000
      };

      const validation = tenderService.validateTender(invalidTender);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Title is required');
      expect(validation.errors).toContain('Value must be positive');
    });

    it('should validate date fields', () => {
      const pastDeadlineTender = {
        ...mockTender,
        deadline: new Date('2020-01-01')
      };

      const validation = tenderService.validateTender(pastDeadlineTender);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Deadline must be in the future');
    });
  });

  describe('getTenderMetrics', () => {
    it('should calculate tender statistics', async () => {
      const mockTenders = [
        { ...mockTender, value: 100000, relevanceScore: 0.8 },
        { ...mockTender, value: 200000, relevanceScore: 0.9 },
        { ...mockTender, value: 150000, relevanceScore: 0.7 }
      ];

      mockRepository.findAll.mockResolvedValue(mockTenders);

      const metrics = await tenderService.getTenderMetrics();
      expect(metrics.totalTenders).toBe(3);
      expect(metrics.averageValue).toBe(150000);
      expect(metrics.highRelevanceTenders).toBe(2);
    });
  });
});