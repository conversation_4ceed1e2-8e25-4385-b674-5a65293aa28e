import { describe, expect, it, beforeAll, afterAll } from '@jest/globals';
import { TenderService } from '@/services/TenderService';
import { TenderRepository } from '@/repositories/TenderRepository';
import { NotificationService } from '@/services/NotificationService';
import { Database } from '@/database';
import { ITender, TenderStatus, TenderSource } from '@/types/tender';
import { TenderScraper } from '@/scrapers/TenderScraper';
import { config } from '@/config';

describe('Tender Monitoring Integration Tests', () => {
  let db: Database;
  let tenderService: TenderService;
  let tenderRepository: TenderRepository;
  let notificationService: NotificationService;
  let scraper: TenderScraper;

  beforeAll(async () => {
    // Initialize test database
    db = new Database({
      ...config.database,
      filename: ':memory:' // Use in-memory SQLite for tests
    });
    await db.initialize();

    // Initialize services
    tenderRepository = new TenderRepository(db);
    notificationService = new NotificationService(config.notifications);
    tenderService = new TenderService(tenderRepository, notificationService);
    scraper = new TenderScraper(config.scraping);
  });

  afterAll(async () => {
    await db.close();
  });

  describe('Full Tender Monitoring Cycle', () => {
    it('should scrape, process, and store new tenders', async () => {
      // Mock external tender source
      const mockTenderData = {
        title: 'Industrial Chemical Supply',
        description: 'Supply of industrial-grade chemicals for water treatment',
        value: 500000,
        deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        source: TenderSource.ETENDERS,
        organization: 'Water Treatment Corp',
        location: 'Cape Town, South Africa'
      };

      // 1. Scrape tenders
      const scrapedTenders = await scraper.scrape();
      expect(scrapedTenders.length).toBeGreaterThan(0);

      // 2. Process and store tenders
      const processedTenders = await Promise.all(
        scrapedTenders.map(tender => tenderService.processTender(tender))
      );

      // 3. Verify storage
      const storedTenders = await tenderRepository.findAll();
      expect(storedTenders.length).toBe(processedTenders.length);

      // 4. Verify tender processing
      const testTender = processedTenders[0];
      expect(testTender.relevanceScore).toBeDefined();
      expect(testTender.status).toBe(TenderStatus.NEW);

      // 5. Verify notifications
      const notifications = await notificationService.getNotifications();
      expect(notifications.some(n => n.tenderId === testTender.id)).toBe(true);
    });

    it('should update existing tenders with new information', async () => {
      // Create initial tender
      const initialTender: ITender = {
        id: '123',
        title: 'Chemical Processing Equipment',
        description: 'Industrial chemical processing equipment needed',
        value: 750000,
        status: TenderStatus.NEW,
        source: TenderSource.ETENDERS,
        deadline: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
        createdAt: new Date(),
        updatedAt: new Date(),
        relevanceScore: 0.85,
        keywords: ['chemical', 'processing', 'industrial'],
        location: 'Johannesburg',
        organization: 'Industrial Corp'
      };

      await tenderService.processTender(initialTender);

      // Update tender
      const updatedTender = {
        ...initialTender,
        value: 800000,
        description: 'Updated: Industrial chemical processing equipment needed urgently',
        status: TenderStatus.UPDATED
      };

      const result = await tenderService.processTender(updatedTender);

      // Verify updates
      expect(result.value).toBe(800000);
      expect(result.status).toBe(TenderStatus.UPDATED);
      expect(result.updatedAt).not.toBe(initialTender.updatedAt);

      // Verify notification for update
      const notifications = await notificationService.getNotifications();
      expect(notifications.some(n => 
        n.tenderId === result.id && 
        n.type === 'TENDER_UPDATED'
      )).toBe(true);
    });

    it('should handle tender relevance scoring correctly', async () => {
      // Create tenders with varying relevance
      const tenders = [
        {
          title: 'Chemical Water Treatment',
          description: 'Industrial chemical processing for water treatment',
          value: 1000000
        },
        {
          title: 'Office Supplies',
          description: 'General office supplies and stationery',
          value: 50000
        },
        {
          title: 'Industrial Chemicals',
          description: 'Bulk chemical supply for industrial processes',
          value: 2000000
        }
      ];

      const processedTenders = await Promise.all(
        tenders.map(tender => tenderService.processTender({
          ...tender,
          source: TenderSource.ETENDERS,
          deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          status: TenderStatus.NEW,
          createdAt: new Date(),
          updatedAt: new Date()
        } as ITender))
      );

      // Get relevant tenders
      const relevantTenders = await tenderService.findRelevantTenders();

      // Verify relevance scoring
      expect(relevantTenders.length).toBe(2); // Should only include chemical-related tenders
      expect(relevantTenders.every(t => t.relevanceScore >= 0.7)).toBe(true);
      expect(relevantTenders.some(t => t.title.includes('Office Supplies'))).toBe(false);
    });

    it('should maintain data consistency across the monitoring cycle', async () => {
      // 1. Initial tender creation
      const tender = await tenderService.processTender({
        title: 'Chemical Supply Contract',
        description: 'Long-term chemical supply contract for industrial use',
        value: 1500000,
        source: TenderSource.ETENDERS,
        deadline: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
        status: TenderStatus.NEW,
        createdAt: new Date(),
        updatedAt: new Date()
      } as ITender);

      // 2. Verify initial storage
      const storedTender = await tenderRepository.findById(tender.id);
      expect(storedTender).toBeDefined();
      expect(storedTender.value).toBe(1500000);

      // 3. Update tender
      const updatedTender = {
        ...tender,
        value: 1600000,
        status: TenderStatus.UPDATED
      };
      await tenderService.processTender(updatedTender);

      // 4. Verify consistency after update
      const finalTender = await tenderRepository.findById(tender.id);
      expect(finalTender.value).toBe(1600000);
      expect(finalTender.status).toBe(TenderStatus.UPDATED);

      // 5. Verify tender history
      const history = await tenderService.getTenderHistory(tender.id);
      expect(history.length).toBe(2);
      expect(history[0].value).toBe(1500000);
      expect(history[1].value).toBe(1600000);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle duplicate tenders gracefully', async () => {
      const tender = {
        title: 'Duplicate Tender Test',
        description: 'Test tender for duplicate handling',
        value: 100000,
        source: TenderSource.ETENDERS,
        deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        status: TenderStatus.NEW,
        createdAt: new Date(),
        updatedAt: new Date()
      } as ITender;

      // Create initial tender
      const first = await tenderService.processTender(tender);

      // Attempt to create duplicate
      const second = await tenderService.processTender(tender);

      expect(first.id).toBe(second.id);
      
      // Verify only one record exists
      const stored = await tenderRepository.findAll();
      expect(stored.filter(t => t.title === tender.title)).toHaveLength(1);
    });

    it('should handle invalid tender data appropriately', async () => {
      const invalidTender = {
        title: '', // Invalid: empty title
        description: 'Test tender',
        value: -1000, // Invalid: negative value
        source: TenderSource.ETENDERS,
        deadline: new Date(Date.now() - 24 * 60 * 60 * 1000), // Invalid: past deadline
        status: TenderStatus.NEW,
        createdAt: new Date(),
        updatedAt: new Date()
      } as ITender;

      await expect(tenderService.processTender(invalidTender))
        .rejects.toThrow('Invalid tender data');

      // Verify invalid tender was not stored
      const stored = await tenderRepository.findAll();
      expect(stored.some(t => t.title === '')).toBe(false);
    });
  });
});