#!/bin/bash

# <PERSON>ript to set up basic working frontend for Mexel

echo "🔧 Setting up basic frontend environment..."

MEXEL_ROOT="/Users/<USER>/Desktop/Mexel"
FRONTEND_DIR="$MEXEL_ROOT/frontend"

cd "$MEXEL_ROOT"

# Ensure yarn is installed
echo "🧶 Ensuring Yarn is available..."
if ! command -v yarn &> /dev/null; then
    echo "❌ Yarn is not installed. Please install Yarn first."
    exit 1
fi

# Ensure we have the necessary core dependencies
echo "📦 Installing core dependencies..."
cd "$FRONTEND_DIR"

# Remove node_modules to ensure a clean install
rm -rf node_modules
rm -rf .yarn/cache

# Create a basic package.json that contains only what we need for a minimal app
cat > package.json << 'EOL'
{
  "name": "@mexel/frontend",
  "version": "1.0.0",
  "private": true,
  "dependencies": {
    "@emotion/react": "^11.11.1",
    "@emotion/styled": "^11.11.0",
    "@mui/material": "^5.15.15",
    "@mui/system": "^5.15.15",
    "axios": "^1.9.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "5.0.1"
  },
  "devDependencies": {
    "@types/node": "^20.12.12",
    "@types/react": "^18.2.58",
    "@types/react-dom": "^18.2.19",
    "typescript": "^5.4.2"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  }
}
EOL

# Update yarn config to use node-modules nodeLinker
cat > .yarnrc.yml << 'EOL'
nodeLinker: node-modules
npmRegistryServer: "https://registry.yarnpkg.com"
npmAlwaysAuth: false
EOL

# Install the minimal dependencies
echo "🌱 Installing minimal dependencies with Yarn..."
yarn install --force

echo "✅ Setup complete! You can now run 'cd $FRONTEND_DIR && yarn start' to start the minimal frontend."
