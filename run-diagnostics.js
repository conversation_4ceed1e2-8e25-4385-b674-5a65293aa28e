// run-diagnostics.js
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function checkNodeModulesStructure() {
  const rootDir = __dirname;
  const rootNodeModules = path.join(rootDir, 'node_modules');
  const frontendNodeModules = path.join(rootDir, 'frontend', 'node_modules');
  const sharedNodeModules = path.join(rootDir, 'shared', 'node_modules');

  console.log('Checking node_modules structure:');
  console.log(`- Root node_modules exists: ${fs.existsSync(rootNodeModules)}`);
  console.log(`- Frontend node_modules exists: ${fs.existsSync(frontendNodeModules)}`);
  console.log(`- Shared node_modules exists: ${fs.existsSync(sharedNodeModules)}`);

  const requiredModules = ['typescript', 'eslint', '@typescript-eslint/parser', '@typescript-eslint/eslint-plugin'];
  const modulePaths = [];

  requiredModules.forEach(module => {
    try {
      const resolvedPath = require.resolve(module);
      modulePaths.push(`- ${module}: ${resolvedPath}`);
    } catch (error) {
      modulePaths.push(`- ${module}: NOT FOUND`);
    }
  });

  console.log('\nModule resolution:');
  modulePaths.forEach(path => console.log(path));
}

function runTypeScriptDiagnostics() {
  console.log('\n==== TypeScript Diagnostics ====');

  try {
    // Check TypeScript version
    console.log('\nTypeScript Version:');
    try {
      const tsVersion = require('typescript').version;
      console.log(`- Version from require: ${tsVersion}`);
    } catch (error) {
      console.log(`- Failed to get version via require: ${error.message}`);
    }

    // Try running tsc --version (may fail if not in PATH)
    try {
      const tscVersionOutput = execSync('npx tsc --version', { encoding: 'utf8' });
      console.log(`- Version from npx: ${tscVersionOutput.trim()}`);
    } catch (error) {
      console.log('- Failed to run npx tsc --version');
    }

    // List tsconfig files
    console.log('\nTypeScript Config Files:');
    ['tsconfig.json', 'tsconfig.base.json', 'frontend/tsconfig.json', 'shared/tsconfig.json'].forEach(configPath => {
      const fullPath = path.join(__dirname, configPath);
      if (fs.existsSync(fullPath)) {
        console.log(`- ${configPath} exists`);
        try {
          const config = JSON.parse(fs.readFileSync(fullPath, 'utf8'));
          if (config.extends) {
            console.log(`  extends: ${config.extends}`);
          }
          if (config.compilerOptions && config.compilerOptions.typeRoots) {
            console.log(`  typeRoots: ${JSON.stringify(config.compilerOptions.typeRoots)}`);
          }
        } catch (err) {
          console.log(`  Error parsing: ${err.message}`);
        }
      } else {
        console.log(`- ${configPath} does NOT exist`);
      }
    });

    // Check if Node.js types are available
    const typesDir = path.join(__dirname, 'types');
    const nodeTypesInRoot = path.join(__dirname, 'node_modules', '@types', 'node');
    const nodeTypesInFrontend = path.join(__dirname, 'frontend', 'node_modules', '@types', 'node');

    console.log('\nNode.js Types:');
    console.log(`- Custom types dir exists: ${fs.existsSync(typesDir)}`);
    console.log(`- @types/node in root: ${fs.existsSync(nodeTypesInRoot)}`);
    console.log(`- @types/node in frontend: ${fs.existsSync(nodeTypesInFrontend)}`);

    // Try running tsc in noEmit mode for diagnostics only
    console.log('\nRunning TypeScript type check:');
    try {
      const tscOutput = execSync('npx tsc --noEmit', { encoding: 'utf8', stdio: 'pipe' });
      console.log('TypeScript check completed without errors');
    } catch (error) {
      console.log('TypeScript errors found:');
      if (error.stdout) {
        // Show just the first 5 errors to avoid flooding the console
        const lines = error.stdout.split('\n');
        const errorLines = lines.filter(line => line.includes('error TS'));
        const displayLines = errorLines.slice(0, 5);
        displayLines.forEach(line => console.log(`  ${line}`));

        if (errorLines.length > 5) {
          console.log(`  ... and ${errorLines.length - 5} more errors`);
        }

        // Check for specific error types
        const processErrors = errorLines.filter(line => line.includes('Cannot find name \'process\''));
        if (processErrors.length > 0) {
          console.log(`  Found ${processErrors.length} 'Cannot find name 'process'' errors`);
        }
      }
    }
  } catch (error) {
    console.error('Error running TypeScript diagnostics:', error);
  }
}

function runESLintDiagnostics() {
  console.log('\n==== ESLint Diagnostics ====');

  try {
    // Check ESLint version
    console.log('\nESLint Version:');
    try {
      const eslintVersion = require('eslint').ESLint.version;
      console.log(`- Version from require: ${eslintVersion}`);
    } catch (error) {
      console.log(`- Failed to get version via require: ${error.message}`);
    }

    // Check ESLint config files
    console.log('\nESLint Config Files:');
    ['.eslintrc.js', '.eslintrc.json', 'frontend/.eslintrc.js', 'shared/.eslintrc.js'].forEach(configPath => {
      const fullPath = path.join(__dirname, configPath);
      if (fs.existsSync(fullPath)) {
        console.log(`- ${configPath} exists`);

        if (configPath.endsWith('.js')) {
          try {
            const config = require(fullPath);
            console.log(`  extends: ${JSON.stringify(config.extends)}`);
            console.log(`  parser: ${config.parser}`);
          } catch (err) {
            console.log(`  Error requiring: ${err.message}`);
          }
        }
      } else {
        console.log(`- ${configPath} does NOT exist`);
      }
    });

    // Try running simple ESLint check
    console.log('\nRunning ESLint check:');
    try {
      require('./test-eslint');
    } catch (error) {
      console.log(`Error loading test-eslint.js: ${error.message}`);
    }
  } catch (error) {
    console.error('Error running ESLint diagnostics:', error);
  }
}

// Main execution
console.log('===== Running Diagnostics for TypeScript and ESLint =====\n');

console.log('==== Node Modules Structure ====');
checkNodeModulesStructure();

runTypeScriptDiagnostics();
runESLintDiagnostics();

console.log('\n===== Diagnostics Complete =====');
