// Using any to avoid dependency on @jest/types package
const config: any = {
  // Global options applicable to all projects
  coverageDirectory: '<rootDir>/coverage',
  collectCoverageFrom: [
    'frontend/src/**/*.{ts,tsx}',
    'shared/src/**/*.{ts,tsx}',
    'src/**/*.{ts,tsx}', // For tests in the root /src directory
    'tests/**/*.{ts,tsx}', // For tests in the root /tests directory
    '!**/node_modules/**',
    '!**/dist/**',
    '!**/*.d.ts',
    '!frontend/src/types/**',
    '!frontend/src/**/index.ts',
    '!frontend/src/main.ts', // Or .tsx
    '!shared/src/**/index.ts',
  ],
  coverageThreshold: {
    // Example, adjust as needed
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  transform: {
    '^.+\\\\.tsx?$': ['ts-jest', { diagnostics: false }], // Apply ts-jest with diagnostics off globally
  },
  watchPlugins: ['jest-watch-typeahead/filename', 'jest-watch-typeahead/testname'],
  reporters: [
    // Example reporters, adjust as needed
    'default',
    [
      'jest-junit',
      {
        outputDirectory: 'reports/junit',
        outputName: 'jest-junit.xml',
        ancestorSeparator: ' › ',
        uniqueOutputName: 'false',
        suiteNameTemplate: '{filepath}',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
      },
    ],
  ],
  verbose: true,
  testTimeout: 10000,
  maxWorkers: '50%',

  projects: [
    // Frontend project configuration
    {
      displayName: 'frontend',
      preset: 'ts-jest',
      testEnvironment: 'jsdom',
      rootDir: '<rootDir>/frontend', // Paths below are relative to <rootDir>/frontend
      setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'], // -> frontend/src/setupTests.ts
      moduleNameMapper: {
        '^@/(.*)$': '<rootDir>/src/$1', // -> frontend/src/$1
        '\\\\.(css|less|scss|sass)$': 'identity-obj-proxy',
      },
      globals: {
        'ts-jest': {
          tsconfig: '<rootDir>/tsconfig.json', // -> frontend/tsconfig.json
          diagnostics: false, // Can override global diagnostics setting if needed
        },
      },
      testMatch: ['<rootDir>/src/**/?(*.)+(spec|test).[jt]s?(x)'], // -> frontend/src/...
      testPathIgnorePatterns: ['/node_modules/', '/dist/', '/coverage/', '/src/tests/e2e/'],
      coveragePathIgnorePatterns: ['/node_modules/', '/dist/', '/coverage/', '/src/tests/e2e/'],
    },
    // Node/Shared/Backend project configuration (tests outside frontend)
    {
      displayName: 'node',
      preset: 'ts-jest',
      testEnvironment: 'node',
      rootDir: '<rootDir>', // Monorepo root
      setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'], // -> Mexel/tests/setup.ts (if it exists)
      moduleNameMapper: {
        '^@/(.*)$': '<rootDir>/frontend/src/$1', // Allow importing from frontend via @ alias
        '^@shared/(.*)$': '<rootDir>/shared/src/$1',
        '\\\\.(css|less|scss|sass)$': 'identity-obj-proxy',
      },
      globals: {
        'ts-jest': {
          tsconfig: '<rootDir>/tsconfig.json', // -> Mexel/tsconfig.json
          diagnostics: false,
        },
      },
      testMatch: [
        '<rootDir>/src/**/?(*.)+(spec|test).[jt]s?(x)', // For Mexel/src/
        '<rootDir>/tests/**/?(*.)+(spec|test).[jt]s?(x)', // For Mexel/tests/
        '<rootDir>/shared/src/**/?(*.)+(spec|test).[jt]s?(x)', // For Mexel/shared/src/
      ],
      testPathIgnorePatterns: [
        '/node_modules/',
        '/dist/',
        '/coverage/',
        '/frontend/', // Important: Exclude frontend tests from this project
        '/e2e/', // General e2e exclusion
        // Add specific backend/shared e2e test path ignores if necessary
      ],
    },
  ],
};

export default config;
