#!/usr/bin/env node

/**
 * WebSocket Diagnostics Tool for Mexel
 *
 * A comprehensive tool to diagnose WebSocket connectivity issues.
 * Includes network testing, connection retries, and detailed diagnostics.
 */

const { io } = require("socket.io-client");
const http = require("http");
const readline = require("readline");

// ANSI color codes for terminal output
const colors = {
  reset: "\x1b[0m",
  bright: "\x1b[1m",
  dim: "\x1b[2m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  magenta: "\x1b[35m",
  cyan: "\x1b[36m",
  white: "\x1b[37m",
};

const c = (text, color) => `${color}${text}${colors.reset}`;
const SERVER_URL = "http://localhost:3001";

// Header
console.log(
  c("=================================================", colors.cyan)
);
console.log(
  c(
    "  Mexel WebSocket Comprehensive Diagnostic Tool",
    colors.bright + colors.cyan
  )
);
console.log(
  c("=================================================", colors.cyan)
);
console.log("");

// Test summary structure
const testSummary = {
  httpConnection: { status: "pending", details: null },
  webSocketConnection: { status: "pending", details: null },
  restApiEndpoints: { status: "pending", details: [] },
  eventHandling: { status: "pending", details: [] },
  latency: { status: "pending", details: null },
  transportCompatibility: { status: "pending", details: [] },
};

// Store for discovered routes
let discoveredRoutes = [];

// Run diagnostic tests in sequence
async function runDiagnostics() {
  await testHttpConnection();
  await testRestEndpoints();
  await testWebSocketConnection();

  // Print summary at the end
  printSummary();
}

// Test 1: Basic HTTP connectivity
function testHttpConnection() {
  return new Promise((resolve) => {
    console.log(c("→ Testing HTTP connectivity to server...", colors.yellow));

    const req = http.get(`${SERVER_URL}/health`, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        if (res.statusCode === 200) {
          console.log(c("✓ HTTP connection successful", colors.green));
          console.log(c(`  Response: ${data}`, colors.dim));
          testSummary.httpConnection = {
            status: "success",
            details: { statusCode: res.statusCode, data: JSON.parse(data) },
          };
        } else {
          console.log(
            c(
              `✕ HTTP connection failed with status: ${res.statusCode}`,
              colors.red
            )
          );
          testSummary.httpConnection = {
            status: "failed",
            details: { statusCode: res.statusCode, data },
          };
        }
        resolve();
      });
    });

    req.on("error", (err) => {
      console.log(c(`✕ HTTP request error: ${err.message}`, colors.red));
      testSummary.httpConnection = {
        status: "error",
        details: { error: err.message },
      };
      resolve();
    });

    req.setTimeout(5000, () => {
      req.destroy();
      console.log(c("✕ HTTP request timed out after 5 seconds", colors.red));
      testSummary.httpConnection = {
        status: "timeout",
        details: { error: "Request timed out after 5 seconds" },
      };
      resolve();
    });
  });
}

// Test 2: Check REST API endpoints related to WebSockets
function testRestEndpoints() {
  return new Promise((resolve) => {
    if (testSummary.httpConnection.status !== "success") {
      console.log(
        c(
          "→ Skipping REST API tests due to HTTP connection failure",
          colors.yellow
        )
      );
      testSummary.restApiEndpoints = {
        status: "skipped",
        details: "HTTP connection failed",
      };
      resolve();
      return;
    }

    console.log(
      c("→ Testing WebSocket-related REST endpoints...", colors.yellow)
    );

    const endpointsToTest = ["/api/websocket/status", "/api/websocket/clients"];

    let testsCompleted = 0;

    endpointsToTest.forEach((endpoint) => {
      http
        .get(`${SERVER_URL}${endpoint}`, (res) => {
          let data = "";

          res.on("data", (chunk) => {
            data += chunk;
          });

          res.on("end", () => {
            if (res.statusCode === 200) {
              console.log(
                c(`✓ Endpoint ${endpoint} is available`, colors.green)
              );
              try {
                const jsonData = JSON.parse(data);
                console.log(
                  c(
                    `  Response: ${JSON.stringify(jsonData).substring(
                      0,
                      100
                    )}...`,
                    colors.dim
                  )
                );
                testSummary.restApiEndpoints.details.push({
                  endpoint,
                  status: "success",
                  statusCode: res.statusCode,
                  data: jsonData,
                });
                discoveredRoutes.push(endpoint);
              } catch (e) {
                console.log(
                  c(`  Warning: Could not parse JSON response`, colors.yellow)
                );
                testSummary.restApiEndpoints.details.push({
                  endpoint,
                  status: "invalid_json",
                  statusCode: res.statusCode,
                });
              }
            } else {
              console.log(
                c(
                  `✕ Endpoint ${endpoint} failed with status: ${res.statusCode}`,
                  colors.red
                )
              );
              testSummary.restApiEndpoints.details.push({
                endpoint,
                status: "failed",
                statusCode: res.statusCode,
              });
            }

            testsCompleted++;
            if (testsCompleted === endpointsToTest.length) {
              const allSuccessful = testSummary.restApiEndpoints.details.every(
                (endpoint) => endpoint.status === "success"
              );
              testSummary.restApiEndpoints.status = allSuccessful
                ? "success"
                : "partial";
              resolve();
            }
          });
        })
        .on("error", (err) => {
          console.log(
            c(
              `✕ Error testing endpoint ${endpoint}: ${err.message}`,
              colors.red
            )
          );
          testSummary.restApiEndpoints.details.push({
            endpoint,
            status: "error",
            error: err.message,
          });

          testsCompleted++;
          if (testsCompleted === endpointsToTest.length) {
            testSummary.restApiEndpoints.status = "partial";
            resolve();
          }
        });
    });
  });
}

// Test 3: WebSocket connectivity with multiple transports
function testWebSocketConnection() {
  return new Promise((resolve) => {
    console.log(c("→ Testing WebSocket connectivity...", colors.yellow));

    // Test with different transport mechanisms
    const transports = [
      ["websocket"],
      ["polling"],
      ["websocket", "polling"], // Fallback mode
    ];

    let currentTransportIndex = 0;
    let successfulTransports = [];
    let failedTransports = [];

    function testNextTransport() {
      if (currentTransportIndex >= transports.length) {
        // All transports tested
        testSummary.transportCompatibility.details = {
          successful: successfulTransports,
          failed: failedTransports,
        };

        if (successfulTransports.length > 0) {
          testSummary.transportCompatibility.status = "success";
          console.log(
            c(
              `✓ Compatible transports: ${successfulTransports.join(", ")}`,
              colors.green
            )
          );

          // Now test event handling with the first successful transport
          testEventHandling(successfulTransports[0]).then(resolve);
        } else {
          testSummary.transportCompatibility.status = "failed";
          testSummary.webSocketConnection.status = "failed";
          console.log(c("✕ No compatible transports found", colors.red));
          resolve();
        }
        return;
      }

      const transport = transports[currentTransportIndex];
      const transportName = Array.isArray(transport)
        ? transport.join("+")
        : transport;

      console.log(c(`→ Testing transport: ${transportName}...`, colors.yellow));

      // Create socket with current transport
      const socket = io(SERVER_URL, {
        transports: transport,
        reconnectionAttempts: 1,
        timeout: 5000,
        forceNew: true,
      });

      // Set timeout for this connection attempt
      const timeout = setTimeout(() => {
        console.log(
          c(`✕ Connection with ${transportName} timed out`, colors.red)
        );
        failedTransports.push(transportName);
        socket.disconnect();
        currentTransportIndex++;
        testNextTransport();
      }, 5000);

      socket.on("connect", () => {
        clearTimeout(timeout);
        console.log(
          c(`✓ Successfully connected with ${transportName}`, colors.green)
        );
        successfulTransports.push(transportName);

        // Test latency
        const startTime = Date.now();
        socket.emit("ping");

        socket.once("pong", () => {
          const latency = Date.now() - startTime;
          console.log(c(`  Latency: ${latency}ms`, colors.dim));
          testSummary.latency = {
            status: "success",
            details: { transport: transportName, latency },
          };

          socket.disconnect();
          currentTransportIndex++;
          testNextTransport();
        });

        // If no pong received within 2 seconds, continue anyway
        setTimeout(() => {
          if (testSummary.latency.status === "pending") {
            console.log(
              c(`  Warning: No pong response received`, colors.yellow)
            );
            testSummary.latency = {
              status: "timeout",
              details: { transport: transportName },
            };
            socket.disconnect();
            currentTransportIndex++;
            testNextTransport();
          }
        }, 2000);
      });

      socket.on("connect_error", (err) => {
        clearTimeout(timeout);
        console.log(
          c(
            `✕ Connection error with ${transportName}: ${err.message}`,
            colors.red
          )
        );
        failedTransports.push(transportName);
        socket.disconnect();
        currentTransportIndex++;
        testNextTransport();
      });
    }

    // Start testing with first transport
    testNextTransport();
  });
}

// Test WebSocket event handling
function testEventHandling(transport) {
  return new Promise((resolve) => {
    console.log(
      c(
        `→ Testing WebSocket event handling with ${transport}...`,
        colors.yellow
      )
    );

    const socket = io(SERVER_URL, {
      transports: Array.isArray(transport) ? transport : [transport],
      reconnectionAttempts: 1,
      timeout: 5000,
    });

    const events = [
      { name: "welcome", received: false },
      {
        name: "echo_response",
        received: false,
        emit: { event: "echo", data: { text: "Diagnostic test" } },
      },
      {
        name: "response",
        received: false,
        emit: { event: "message", data: "Diagnostic test message" },
      },
      { name: "pong", received: false, emit: { event: "ping" } },
    ];

    socket.on("connect", () => {
      console.log(c("✓ Connected for event testing", colors.green));
      testSummary.webSocketConnection = {
        status: "success",
        details: { socketId: socket.id },
      };

      // Register event handlers
      events.forEach((event) => {
        socket.on(event.name, (data) => {
          console.log(c(`✓ Received ${event.name} event:`, colors.green));
          console.log(c(`  ${JSON.stringify(data)}`, colors.dim));
          event.received = true;
          event.data = data;
        });
      });

      // Emit events that should trigger responses
      events.forEach((event) => {
        if (event.emit) {
          console.log(c(`→ Emitting ${event.emit.event}`, colors.yellow));
          socket.emit(event.emit.event, event.emit.data);
        }
      });

      // Check after 3 seconds which events we received
      setTimeout(() => {
        testSummary.eventHandling.details = events.map((event) => ({
          event: event.name,
          received: event.received,
          data: event.data,
        }));

        const successfulEvents = events.filter((e) => e.received).length;
        console.log(
          c(
            `✓ Received ${successfulEvents}/${events.length} events`,
            colors.green
          )
        );

        testSummary.eventHandling.status =
          successfulEvents > 0 ? "success" : "failed";
        if (successfulEvents === 0) {
          console.log(c("✕ No events were received", colors.red));
        }

        socket.disconnect();
        resolve();
      }, 3000);
    });

    socket.on("connect_error", (err) => {
      console.log(
        c(`✕ Connection error during event testing: ${err.message}`, colors.red)
      );
      testSummary.webSocketConnection = {
        status: "error",
        details: { error: err.message },
      };
      testSummary.eventHandling = {
        status: "failed",
        details: "Could not connect for event testing",
      };
      resolve();
    });
  });
}

// Print diagnostic summary
function printSummary() {
  console.log("");
  console.log(
    c("=================================================", colors.cyan)
  );
  console.log(c("  Diagnostic Summary", colors.bright + colors.cyan));
  console.log(
    c("=================================================", colors.cyan)
  );

  const getStatusColor = (status) => {
    switch (status) {
      case "success":
        return colors.green;
      case "partial":
        return colors.yellow;
      case "failed":
        return colors.red;
      case "error":
        return colors.red;
      case "timeout":
        return colors.yellow;
      case "skipped":
        return colors.dim;
      default:
        return colors.dim;
    }
  };

  Object.entries(testSummary).forEach(([test, result]) => {
    const statusColor = getStatusColor(result.status);
    console.log(
      c(`  ${test}: `, colors.bright) + c(result.status, statusColor)
    );
  });

  console.log("");
  console.log(c("  Recommendation:", colors.bright + colors.white));

  if (
    testSummary.webSocketConnection.status === "success" &&
    testSummary.eventHandling.status === "success"
  ) {
    console.log(
      c("  ✓ WebSocket connection is working correctly", colors.green)
    );
    console.log(
      c("    You can now test the frontend WebSocket integration", colors.green)
    );
  } else if (testSummary.httpConnection.status !== "success") {
    console.log(c("  ✕ Cannot connect to the backend server", colors.red));
    console.log(
      c("    • Check if the server is running on port 3001", colors.white)
    );
    console.log(c("    • Verify there are no network issues", colors.white));
    console.log(
      c("    • Make sure the server is not blocked by a firewall", colors.white)
    );
  } else if (testSummary.webSocketConnection.status !== "success") {
    console.log(c("  ✕ WebSocket connection failed", colors.red));
    console.log(
      c(
        "    • Check if Socket.IO is properly configured on the server",
        colors.white
      )
    );
    console.log(
      c("    • Verify CORS settings allow WebSocket connections", colors.white)
    );
    console.log(
      c("    • Check for protocol compatibility issues", colors.white)
    );
  } else {
    console.log(
      c("  ⚠ Some WebSocket functionality may have issues", colors.yellow)
    );
    console.log(
      c("    • Check specific event handlers on the server", colors.white)
    );
    console.log(
      c(
        "    • Review the WebSocket implementation in both frontend and backend",
        colors.white
      )
    );
  }

  console.log("");
  console.log(
    c("=================================================", colors.cyan)
  );

  // Ask if user wants to exit
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  rl.question(c("\nPress Enter to exit...", colors.yellow), () => {
    rl.close();
    process.exit(0);
  });
}

// Start the diagnostic tests
runDiagnostics().catch((err) => {
  console.log(
    c(`An error occurred during diagnostics: ${err.message}`, colors.red)
  );
  console.log(err.stack);
  process.exit(1);
});
