// Simple Socket.IO client to test the WebSocket server
const { io } = require("socket.io-client");

console.log("Connecting to WebSocket server...");
const socket = io("http://localhost:3001", {
  transports: ["websocket"],
  reconnectionAttempts: 3,
  timeout: 5000,
});

socket.on("connect", () => {
  console.log("✅ Connected to WebSocket server!");
  console.log("Socket ID:", socket.id);

  // Send a test message
  console.log("Sending test message...");
  socket.emit("message", "Hello from test client");

  // Send an echo request
  console.log("Sending echo request...");
  socket.emit("echo", {
    text: "Echo test",
    timestamp: new Date().toISOString(),
  });

  // Send a ping
  console.log("Sending ping...");
  socket.emit("ping");
});

socket.on("welcome", (data) => {
  console.log("Received welcome message:", data);
});

socket.on("response", (data) => {
  console.log("Received response:", data);
});

socket.on("echo_response", (data) => {
  console.log("Received echo response:", data);
});

socket.on("pong", (data) => {
  console.log("Received pong:", data);
});

socket.on("disconnect", () => {
  console.log("Disconnected from server");
});

socket.on("connect_error", (err) => {
  console.error("Connection error:", err.message);
});

// Disconnect after 10 seconds
setTimeout(() => {
  console.log("Test complete, disconnecting...");
  socket.disconnect();
  process.exit(0);
}, 10000);

// Handle process termination
process.on("SIGINT", () => {
  console.log("Caught interrupt signal, disconnecting...");
  socket.disconnect();
  process.exit(0);
});
