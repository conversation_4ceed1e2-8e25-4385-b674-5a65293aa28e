# Mexel System Environment Variables
# Copy this file to .env and update with your actual values

# Node environment: development, test, or production
NODE_ENV=development

# Server settings
PORT=3000
LOG_LEVEL=info

# Email configuration
EMAIL_SERVICE=brevo
FROM_EMAIL=<EMAIL>
EMAIL_FROM_NAME=Mexel Energy
EMAIL_TRACKING_DOMAIN=email.mexelenergy.com
EMAIL_BATCH_SIZE=100
EMAIL_RETRY_ATTEMPTS=3

# Brevo configuration
BREVO_API_KEY=your-brevo-api-key
BREVO_DEFAULT_SENDER=your-default-sender-email
BREVO_TEMPLATE_FOLDER=Mexel
BREVO_LIST_IDS=list1,list2

# SMTP configuration (using Brevo SMTP)
SMTP_HOST=smtp-relay.brevo.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_FROM_NAME=Mexel Energy
SMTP_FROM_EMAIL=<EMAIL>
SMTP_USER=your-smtp-username
SMTP_PASS=your-smtp-password

# Notification email for system alerts
NOTIFICATION_EMAIL=<EMAIL>

# AI Provider Configuration
# Primary AI provider: 'deepseek' or 'ollama'
AI_PROVIDER=deepseek

# DeepSeek configuration (primary)
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_API_BASE_URL=https://api.deepseek.com
DEEPSEEK_API_VERSION=v1
DEEPSEEK_TIMEOUT=30000
DEEPSEEK_CHAT_MODEL=deepseek-chat
DEEPSEEK_CODER_MODEL=deepseek-coder-v2
DEEPSEEK_INSTRUCT_MODEL=deepseek-instruct
DEEPSEEK_DEFAULT_MODEL=deepseek-chat
DEEPSEEK_MAX_RETRIES=3
DEEPSEEK_INITIAL_RETRY_DELAY=1000
DEEPSEEK_MAX_RETRY_DELAY=30000
DEEPSEEK_RATE_LIMIT=60
DEEPSEEK_ENABLE_RATE_LIMITING=true
DEEPSEEK_STREAM_RESPONSES=false

# Ollama configuration (backup)
OLLAMA_HOST=http://localhost:11434
OLLAMA_ENDPOINT=/api/generate
OLLAMA_MODEL=llama3.2

# Performance targets
LEADS_TARGET=50
TENDER_OPPORTUNITIES_TARGET=5
WEBSITE_TRAFFIC_INCREASE=200
SOCIAL_ENGAGEMENT_INCREASE=150

# Monitoring configuration
CHECK_INTERVAL=0 9 * * *
TASK_ASSESSMENT_INTERVAL=*/15 * * * *

# Thresholds and scoring
HIGH_PRIORITY_TENDER_SCORE=0.8
PERFORMANCE_THRESHOLD=0.8
HIGH_VALUE_DEAL_THRESHOLD=100000
HIGH_QUALITY_LEAD_SCORE=0.7

# Integration webhooks
ZAPIER_WEBHOOK_URL=your-zapier-webhook-url

# HubSpot configuration
HUBSPOT_API_KEY=your-hubspot-api-key
HUBSPOT_EMAIL_EVENT_TEMPLATE_ID=your-hubspot-email-event-template-id
HUBSPOT_PORTAL_ID=your-hubspot-portal-id

# Scraping configuration
# eTenders and Eskom configuration

# Scraper type: scrapy or playwright
SCRAPER_TYPE=scrapy

# LinkedIn scraper configuration
LINKEDIN_USERNAME=your-linkedin-username
LINKEDIN_PASSWORD=your-linkedin-password

# Proxy settings (optional)
USE_PROXY=false
PROXY_HOST=
PROXY_PORT=
PROXY_USERNAME=
PROXY_PASSWORD=

# Scraper settings
MAX_CONCURRENT_REQUESTS=16
DOWNLOAD_DELAY=3
USER_AGENT_ROTATION=true
RESPECT_ROBOTS_TXT=true

# Database settings
SQLITE_PATH=data/mexel.db
DB_ENABLE_FOREIGN_KEYS=true
DB_MAX_POOL_SIZE=10
DB_BUSY_TIMEOUT=30000