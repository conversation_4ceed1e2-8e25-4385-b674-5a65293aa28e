// Re-export shared types and utilities
// export * from '@shared/dist'; // Temporarily commented out
// export * from './database'; // Temporarily commented out
// export * from './services'; // Temporarily commented out
// export * from './types'; // Temporarily commented out
// export * from './utils'; // Temporarily commented out


// Version information
export const VERSION = '1.0.0';
export const BUILD_DATE = new Date().toISOString();

// Environment configuration
export const isDevelopment = process.env.NODE_ENV === 'development';
export const isProduction = process.env.NODE_ENV === 'production';
export const isTest = process.env.NODE_ENV === 'test';

// Initialize logger
// import { logger } from './utils/logger'; // Temporarily commented out
// logger.info(`Starting Mexel v${VERSION} (${process.env.NODE_ENV})`); // Temporarily commented out
console.log(`Starting Mexel v${VERSION} (${process.env.NODE_ENV})`); // Fallback logging
