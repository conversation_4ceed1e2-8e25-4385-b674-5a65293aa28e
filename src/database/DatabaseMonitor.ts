import Database from 'better-sqlite3';
import EventEmitter from 'events'; // Changed import statement
// import { logger } from '../utils/logger'; // Temporarily commented out

export interface DatabaseMetrics {
  queryCount: number;
  slowQueries: number;
  errors: number;
  avgQueryTime: number;
  cacheHitRatio: number;
  connectionCount: number;
  transactionCount: number;
  rollbackCount: number;
  deadlockCount: number;
  writeOperations: number;
  readOperations: number;
  diskUsage: number;
  cacheSize: number;
  walSize: number;
}

export interface QueryMetrics {
  sql: string;
  executionTime: number;
  timestamp: Date;
  parameters?: any[];
  error?: Error;
}

export class DatabaseMonitor extends EventEmitter {
  private db: Database;
  private metrics: DatabaseMetrics = {
    queryCount: 0,
    slowQueries: 0,
    errors: 0,
    avgQueryTime: 0,
    cacheHitRatio: 0,
    connectionCount: 0,
    transactionCount: 0,
    rollbackCount: 0,
    deadlockCount: 0,
    writeOperations: 0,
    readOperations: 0,
    diskUsage: 0,
    cacheSize: 0,
    walSize: 0,
  };

  private queryHistory: QueryMetrics[] = [];
  private slowQueryThreshold: number = 100; // milliseconds
  private maxQueryHistory: number = 1000;
  private monitoringInterval: number | null = null; // Changed from NodeJS.Timeout to number

  constructor(db: Database, config?: { slowQueryThreshold?: number; maxQueryHistory?: number }) {
    super();
    this.db = db;
    if (config?.slowQueryThreshold) this.slowQueryThreshold = config.slowQueryThreshold;
    if (config?.maxQueryHistory) this.maxQueryHistory = config.maxQueryHistory;
  }

  startMonitoring(interval: number = 60000): void {
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, interval);

    this.setupQueryInterceptor();
    // logger.info('Database monitoring started'); // Temporarily commented out
  }

  stopMonitoring(): void {
    if (this.monitoringInterval) {
      global.clearInterval(this.monitoringInterval); // Use global.clearInterval
      this.monitoringInterval = null;
    }
    // logger.info('Database monitoring stopped'); // Temporarily commented out
  }

  private setupQueryInterceptor(): void {
    const originalPrepare = this.db.prepare.bind(this.db);
    this.db.prepare = (sql: string) => {
      const statement = originalPrepare(sql);
      const originalRun = statement.run.bind(statement);
      const originalGet = statement.get.bind(statement);
      const originalAll = statement.all.bind(statement);

      statement.run = (...params: any[]) => {
        const start = Date.now(); // Use Date.now() instead of process.hrtime()
        try {
          const result = originalRun(...params);
          const duration = Date.now() - start; // Duration in milliseconds
          this.recordQuery(sql, duration, params);
          this.metrics.writeOperations++;
          return result;
        } catch (error) {
          this.recordQueryError(sql, error as Error, params);
          throw error;
        }
      };

      statement.get = (...params: any[]) => {
        const start = Date.now(); // Use Date.now() instead of process.hrtime()
        try {
          const result = originalGet(...params);
          const duration = Date.now() - start; // Duration in milliseconds
          this.recordQuery(sql, duration, params);
          this.metrics.readOperations++;
          return result;
        } catch (error) {
          this.recordQueryError(sql, error as Error, params);
          throw error;
        }
      };

      statement.all = (...params: any[]) => {
        const start = Date.now(); // Use Date.now() instead of process.hrtime()
        try {
          const result = originalAll(...params);
          const duration = Date.now() - start; // Duration in milliseconds
          this.recordQuery(sql, duration, params);
          this.metrics.readOperations++;
          return result;
        } catch (error) {
          this.recordQueryError(sql, error as Error, params);
          throw error;
        }
      };

      return statement;
    };
  }

  private recordQuery(sql: string, duration: number, parameters?: any[]): void {
    const executionTime = duration; // Duration is now in milliseconds
    this.metrics.queryCount++;
    this.metrics.avgQueryTime = (this.metrics.avgQueryTime * (this.metrics.queryCount - 1) + executionTime) / this.metrics.queryCount;

    if (executionTime > this.slowQueryThreshold) {
      this.metrics.slowQueries++;
      this.emit('slowQuery', { sql, executionTime, parameters });
    }

    this.queryHistory.push({
      sql,
      executionTime,
      timestamp: new Date(),
      parameters,
    });

    if (this.queryHistory.length > this.maxQueryHistory) {
      this.queryHistory.shift();
    }
  }

  private recordQueryError(sql: string, error: Error, parameters?: any[]): void {
    this.metrics.errors++;
    this.emit('queryError', { sql, error, parameters });

    if (error.message.includes('deadlock')) {
      this.metrics.deadlockCount++;
    }
  }

  private collectMetrics(): void {
    try {
      // Collect system metrics
      const pageSize = this.db.pragma('page_size', { simple: true }) as number;
      const pageCount = this.db.pragma('page_count', { simple: true }) as number;
      const cacheSize = this.db.pragma('cache_size', { simple: true }) as number;
      const walSize = this.db.pragma('wal_size', { simple: true }) as number || 0;

      this.metrics.diskUsage = pageSize * pageCount;
      this.metrics.cacheSize = cacheSize * pageSize;
      this.metrics.walSize = walSize;

      // Calculate cache hit ratio
      const stats = this.db.pragma('stats', { simple: false }) as any[];
      if (stats && stats.length > 0) {
        const hits = stats.reduce((sum, stat) => sum + (stat.cache_hit || 0), 0);
        const misses = stats.reduce((sum, stat) => sum + (stat.cache_miss || 0), 0);
        this.metrics.cacheHitRatio = hits / (hits + misses) || 0;
      }

      this.emit('metrics', this.metrics);
    } catch (error) {
      // logger.error('Error collecting database metrics:', error); // Temporarily commented out
    }
  }

  getMetrics(): DatabaseMetrics {
    return { ...this.metrics };
  }

  getQueryHistory(): QueryMetrics[] {
    return [...this.queryHistory];
  }

  getSlowQueries(): QueryMetrics[] {
    return this.queryHistory.filter(q => q.executionTime > this.slowQueryThreshold);
  }

  analyzeQueryPatterns(): {
    commonQueries: { sql: string; count: number }[];
    avgExecutionTimes: { sql: string; time: number }[];
  } {
    const queryCount = new Map<string, number>();
    const queryTimes = new Map<string, number[]>();

    this.queryHistory.forEach(query => {
      queryCount.set(query.sql, (queryCount.get(query.sql) || 0) + 1);
      const times = queryTimes.get(query.sql) || [];
      times.push(query.executionTime);
      queryTimes.set(query.sql, times);
    });

    const commonQueries = Array.from(queryCount.entries())
      .map(([sql, count]) => ({ sql, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const avgExecutionTimes = Array.from(queryTimes.entries())
      .map(([sql, times]) => ({
        sql,
        time: times.reduce((sum, time) => sum + time, 0) / times.length,
      }))
      .sort((a, b) => b.time - a.time)
      .slice(0, 10);

    return { commonQueries, avgExecutionTimes };
  }

  resetMetrics(): void {
    this.metrics = {
      queryCount: 0,
      slowQueries: 0,
      errors: 0,
      avgQueryTime: 0,
      cacheHitRatio: 0,
      connectionCount: 0,
      transactionCount: 0,
      rollbackCount: 0,
      deadlockCount: 0,
      writeOperations: 0,
      readOperations: 0,
      diskUsage: 0,
      cacheSize: 0,
      walSize: 0,
    };
    this.queryHistory = [];
  }
}
