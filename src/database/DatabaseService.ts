import { default as Database, default as sqlite3, Statement } from 'better-sqlite3';
import { createDatabaseSchema } from './schema';

export interface DatabaseConfig {
  filename: string;
  memory?: boolean;
  readonly?: boolean;
  timeout?: number;
  verbose?: boolean;
}

export class DatabaseService {
  private db!: Database;
  private statements: Map<string, Statement> = new Map();
  private config: DatabaseConfig;

  constructor(config: DatabaseConfig) {
    this.config = {
      timeout: 5000,
      verbose: process.env.NODE_ENV === 'development',
      ...config,
    };
  }

  async initialize(): Promise<void> {
    try {
      this.db = sqlite3(this.config.filename, {
        memory: this.config.memory,
        readonly: this.config.readonly,
        timeout: this.config.timeout,
        verbose: this.config.verbose ? console.log : undefined,
      });

      // Enable WAL mode for better performance
      this.db.pragma('journal_mode = WAL');

      // Optimize performance
      this.db.pragma('synchronous = NORMAL');
      this.db.pragma('temp_store = MEMORY');
      this.db.pragma('mmap_size = 30000000000');
      this.db.pragma('cache_size = -2000'); // Use 2MB of memory for cache

      // Create schema if not exists
      createDatabaseSchema(this.db);

      // Prepare commonly used statements
      this.prepareStatements();

      // logger.info('Database initialized successfully'); // Temporarily commented out
    } catch (error) {
      // logger.error('Failed to initialize database:', error); // Temporarily commented out
      // throw new DatabaseError('Database initialization failed', { cause: error }); // Temporarily commented out
      console.error('Failed to initialize database:', error); // Fallback logging
      throw new Error('Database initialization failed'); // Fallback error
    }
  }

  private prepareStatements(): void {
    // Tender queries
    this.prepare('findTenderById', 'SELECT * FROM tenders WHERE id = ?');
    this.prepare('findTendersByStatus', 'SELECT * FROM tenders WHERE status = ?');
    this.prepare(
      'insertTender',
      `INSERT INTO tenders (
        id, title, description, value, status, source,
        deadline, organization, location, relevance_score,
        keywords, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
    );
    this.prepare(
      'updateTender',
      `UPDATE tenders SET
        title = ?, description = ?, value = ?, status = ?,
        deadline = ?, organization = ?, location = ?,
        relevance_score = ?, keywords = ?, updated_at = ?
      WHERE id = ?`
    );

    // Email template queries
    this.prepare('findTemplateByName', 'SELECT * FROM email_templates WHERE name = ?');
    this.prepare(
      'insertTemplate',
      `INSERT INTO email_templates (
        id, name, subject, body, metadata,
        follow_up_schedule_days, ab_test_campaign_id,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`
    );

    // Performance metrics queries
    this.prepare(
      'insertMetric',
      `INSERT INTO performance_metrics (
        id, metric_name, metric_value, timestamp, metadata
      ) VALUES (?, ?, ?, ?, ?)`
    );
    this.prepare(
      'getMetricsByTimeRange',
      `SELECT * FROM performance_metrics
       WHERE metric_name = ?
       AND timestamp BETWEEN ? AND ?
       ORDER BY timestamp DESC`
    );
  }

  private prepare(name: string, sql: string): Statement {
    const stmt = this.db.prepare(sql);
    this.statements.set(name, stmt);
    return stmt;
  }

  getStatement(name: string): Statement {
    const stmt = this.statements.get(name);
    if (!stmt) {
      throw new Error(`Prepared statement '${name}' not found`);
    }
    return stmt;
  }

  transaction<T>(fn: (db: Database) => T): T {
    const transaction = this.db.transaction(fn);
    return transaction();
  }

  query<T = any>(sql: string, params?: any[]): T[] {
    try {
      const stmt = this.db.prepare(sql);
      return stmt.all(...(params || []));
    } catch (error) {
      console.error('Query error:', { sql, params, error });
      throw new Error('Query execution failed'); // Fallback error
    }
  }

  queryOne<T = any>(sql: string, params?: any[]): T | undefined {
    try {
      const stmt = this.db.prepare(sql);
      return stmt.get(...(params || []));
    } catch (error) {
      console.error('Query error:', { sql, params, error });
      throw new Error('Query execution failed'); // Fallback error
    }
  }

  execute(sql: string, params?: any[]): number {
    try {
      const stmt = this.db.prepare(sql);
      const result = stmt.run(...(params || []));
      return result.changes;
    } catch (error) {
      console.error('Execute error:', { sql, params, error });
      throw new Error('Statement execution failed'); // Fallback error
    }
  }

  async backup(destination: string): Promise<void> {
    try {
      await this.db.backup(destination);
      console.log('Database backup completed successfully');
    } catch (error) {
      console.error('Backup error:', error);
      throw new Error('Database backup failed'); // Fallback error
    }
  }

  async vacuum(): Promise<void> {
    try {
      this.db.pragma('optimize');
      this.db.pragma('vacuum');
      console.log('Database vacuum completed successfully');
    } catch (error) {
      console.error('Vacuum error:', error);
      throw new Error('Database vacuum failed'); // Fallback error
    }
  }

  async analyze(): Promise<void> {
    try {
      this.db.pragma('analysis_limit = 1000');
      this.db.pragma('analyze');
      console.log('Database analysis completed successfully');
    } catch (error) {
      console.error('Analysis error:', error);
      throw new Error('Database analysis failed'); // Fallback error
    }
  }

  async healthCheck(): Promise<{ status: string; version: number; size: number }> {
    try {
      const version = this.db.pragma('user_version', { simple: true });
      const pageSize = this.db.pragma('page_size', { simple: true });
      const pageCount = this.db.pragma('page_count', { simple: true });
      const size = pageSize * pageCount;

      return {
        status: 'healthy',
        version,
        size,
      };
    } catch (error) {
      console.error('Health check error:', error);
      throw new Error('Database health check failed'); // Fallback error
    }
  }

  close(): void {
    if (this.db) {
      this.statements.clear();
      this.db.close();
    }
  }
}
