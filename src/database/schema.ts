import Database from 'better-sqlite3';

export interface SchemaDefinition {
  version: number;
  tables: TableDefinition[];
  indexes: IndexDefinition[];
  constraints: ConstraintDefinition[];
}

export interface TableDefinition {
  name: string;
  columns: ColumnDefinition[];
  primaryKey?: string[];
}

export interface ColumnDefinition {
  name: string;
  type: string;
  nullable?: boolean;
  default?: string;
  unique?: boolean;
  references?: {
    table: string;
    column: string;
    onDelete?: 'CASCADE' | 'SET NULL' | 'RESTRICT';
    onUpdate?: 'CASCADE' | 'SET NULL' | 'RESTRICT';
  };
}

export interface IndexDefinition {
  name: string;
  table: string;
  columns: string[];
  unique?: boolean;
}

export interface ConstraintDefinition {
  name: string;
  table: string;
  type: 'UNIQUE' | 'CHECK' | 'FOREIGN KEY';
  columns: string[];
  definition: string;
}

export const schema: SchemaDefinition = {
  version: 1,
  tables: [
    {
      name: 'migrations',
      columns: [
        { name: 'id', type: 'INTEGER', nullable: false },
        { name: 'name', type: 'TEXT', nullable: false },
        { name: 'status', type: 'TEXT', nullable: false, default: "'PENDING'" },
        { name: 'applied_at', type: 'TEXT', nullable: true },
      ],
      primaryKey: ['id'],
    },
    {
      name: 'tenders',
      columns: [
        { name: 'id', type: 'TEXT', nullable: false },
        { name: 'title', type: 'TEXT', nullable: false },
        { name: 'description', type: 'TEXT', nullable: false },
        { name: 'value', type: 'INTEGER', nullable: false },
        { name: 'status', type: 'TEXT', nullable: false },
        { name: 'source', type: 'TEXT', nullable: false },
        { name: 'deadline', type: 'TEXT', nullable: false },
        { name: 'organization', type: 'TEXT', nullable: false },
        { name: 'location', type: 'TEXT', nullable: true },
        { name: 'relevance_score', type: 'REAL', nullable: false, default: '0' },
        { name: 'keywords', type: 'TEXT', nullable: true },
        { name: 'created_at', type: 'TEXT', nullable: false },
        { name: 'updated_at', type: 'TEXT', nullable: false },
      ],
      primaryKey: ['id'],
    },
    {
      name: 'tender_history',
      columns: [
        { name: 'id', type: 'INTEGER', nullable: false },
        { name: 'tender_id', type: 'TEXT', nullable: false },
        { name: 'status', type: 'TEXT', nullable: false },
        { name: 'value', type: 'INTEGER', nullable: false },
        { name: 'updated_at', type: 'TEXT', nullable: false },
        { name: 'updated_by', type: 'TEXT', nullable: true },
      ],
      primaryKey: ['id'],
    },
    {
      name: 'email_templates',
      columns: [
        { name: 'id', type: 'TEXT', nullable: false },
        { name: 'name', type: 'TEXT', nullable: false },
        { name: 'subject', type: 'TEXT', nullable: false },
        { name: 'body', type: 'TEXT', nullable: false },
        { name: 'metadata', type: 'TEXT', nullable: true },
        { name: 'follow_up_schedule_days', type: 'INTEGER', nullable: true },
        { name: 'ab_test_campaign_id', type: 'TEXT', nullable: true },
        { name: 'created_at', type: 'TEXT', nullable: false },
        { name: 'updated_at', type: 'TEXT', nullable: false },
      ],
      primaryKey: ['id'],
    },
    {
      name: 'notifications',
      columns: [
        { name: 'id', type: 'TEXT', nullable: false },
        { name: 'type', type: 'TEXT', nullable: false },
        { name: 'title', type: 'TEXT', nullable: false },
        { name: 'message', type: 'TEXT', nullable: false },
        { name: 'status', type: 'TEXT', nullable: false, default: "'PENDING'" },
        { name: 'recipient', type: 'TEXT', nullable: false },
        { name: 'metadata', type: 'TEXT', nullable: true },
        { name: 'created_at', type: 'TEXT', nullable: false },
        { name: 'sent_at', type: 'TEXT', nullable: true },
      ],
      primaryKey: ['id'],
    },
    {
      name: 'performance_metrics',
      columns: [
        { name: 'id', type: 'TEXT', nullable: false },
        { name: 'metric_name', type: 'TEXT', nullable: false },
        { name: 'metric_value', type: 'REAL', nullable: false },
        { name: 'timestamp', type: 'TEXT', nullable: false },
        { name: 'metadata', type: 'TEXT', nullable: true },
      ],
      primaryKey: ['id'],
    },
  ],
  indexes: [
    {
      name: 'idx_tenders_status',
      table: 'tenders',
      columns: ['status'],
    },
    {
      name: 'idx_tenders_deadline',
      table: 'tenders',
      columns: ['deadline'],
    },
    {
      name: 'idx_tenders_relevance',
      table: 'tenders',
      columns: ['relevance_score'],
    },
    {
      name: 'idx_tender_history_tender_id',
      table: 'tender_history',
      columns: ['tender_id'],
    },
    {
      name: 'idx_email_templates_name',
      table: 'email_templates',
      columns: ['name'],
      unique: true,
    },
    {
      name: 'idx_notifications_status',
      table: 'notifications',
      columns: ['status'],
    },
    {
      name: 'idx_notifications_recipient',
      table: 'notifications',
      columns: ['recipient'],
    },
    {
      name: 'idx_performance_metrics_name_timestamp',
      table: 'performance_metrics',
      columns: ['metric_name', 'timestamp'],
    },
  ],
  constraints: [
    {
      name: 'fk_tender_history_tender_id',
      table: 'tender_history',
      type: 'FOREIGN KEY',
      columns: ['tender_id'],
      definition: 'REFERENCES tenders(id) ON DELETE CASCADE',
    },
    {
      name: 'check_tender_value',
      table: 'tenders',
      type: 'CHECK',
      columns: ['value'],
      definition: 'value >= 0',
    },
    {
      name: 'check_tender_relevance',
      table: 'tenders',
      type: 'CHECK',
      columns: ['relevance_score'],
      definition: 'relevance_score >= 0 AND relevance_score <= 1',
    },
  ],
};

export function generateCreateTableSQL(table: TableDefinition): string {
  const columns = table.columns
    .map(col => {
      let sql = `${col.name} ${col.type}`;
      if (!col.nullable) sql += ' NOT NULL';
      if (col.default) sql += ` DEFAULT ${col.default}`;
      if (col.unique) sql += ' UNIQUE';
      if (col.references) {
        sql += ` REFERENCES ${col.references.table}(${col.references.column})`;
        if (col.references.onDelete) sql += ` ON DELETE ${col.references.onDelete}`;
        if (col.references.onUpdate) sql += ` ON UPDATE ${col.references.onUpdate}`;
      }
      return sql;
    })
    .join(',\n  ');

  const primaryKey = table.primaryKey ?
    `,\n  PRIMARY KEY (${table.primaryKey.join(', ')})` : '';

  return `CREATE TABLE IF NOT EXISTS ${table.name} (\n  ${columns}${primaryKey}\n);`;
}

export function generateCreateIndexSQL(index: IndexDefinition): string {
  const unique = index.unique ? 'UNIQUE ' : '';
  return `CREATE ${unique}INDEX IF NOT EXISTS ${index.name} ON ${index.table} (${index.columns.join(', ')});`;
}

export function generateConstraintSQL(constraint: ConstraintDefinition): string {
  if (constraint.type === 'FOREIGN KEY') {
    return `ALTER TABLE ${constraint.table} ADD CONSTRAINT ${constraint.name} ${constraint.type} (${constraint.columns.join(', ')}) ${constraint.definition};`;
  }
  return `ALTER TABLE ${constraint.table} ADD CONSTRAINT ${constraint.name} ${constraint.type} (${constraint.definition});`;
}

export function createDatabaseSchema(db: Database): void {
  db.transaction(() => {
    // Create tables
    schema.tables.forEach(table => {
      db.exec(generateCreateTableSQL(table));
    });

    // Create indexes
    schema.indexes.forEach(index => {
      db.exec(generateCreateIndexSQL(index));
    });

    // Add constraints
    schema.constraints.forEach(constraint => {
      db.exec(generateConstraintSQL(constraint));
    });
  })();
}
