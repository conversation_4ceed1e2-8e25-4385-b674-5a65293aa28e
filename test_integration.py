#!/usr/bin/env python3
"""
Integration Test Script for Mexel Lead Discovery System
Tests both HTTP REST API endpoints and Socket.IO WebSocket connectivity
"""
import requests
import socketio
import json
import time
import asyncio
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:3001"
SOCKETIO_URL = "http://localhost:3001"
VERBOSE = True  # Set to True for detailed logging

def log(message):
    """Log messages if verbose mode is enabled"""
    if VERBOSE:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")

def test_api_health():
    """Test the API health endpoint"""
    log("Testing API health endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        response.raise_for_status()
        log(f"Health endpoint response: {json.dumps(response.json(), indent=2)}")
        return True
    except Exception as e:
        log(f"ERROR: Health endpoint test failed: {e}")
        return False

def test_example_endpoint():
    """Test the example data endpoint"""
    log("Testing example data endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/api/example")
        response.raise_for_status()
        log(f"Example endpoint response: {json.dumps(response.json(), indent=2)}")
        return True
    except Exception as e:
        log(f"ERROR: Example endpoint test failed: {e}")
        return False

def test_websocket_status():
    """Test the websocket status endpoint"""
    log("Testing websocket status endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/api/websocket/status")
        response.raise_for_status()
        log(f"WebSocket status response: {json.dumps(response.json(), indent=2)}")
        return True
    except Exception as e:
        log(f"ERROR: WebSocket status test failed: {e}")
        return False

def test_ai_text_generation():
    """Test a simple AI text generation endpoint"""
    log("Testing AI text generation endpoint...")
    try:
        # Try the simpler text generation endpoint first
        payload = {
            "prompt": "Write a short paragraph about water treatment efficiency.",
            "system_prompt": "You are a helpful assistant specialized in chemical water treatment technology.",
            "temperature": 0.7,
            "max_tokens": 200
        }
        
        log(f"Sending request to /api/v1/ai/generate with payload: {json.dumps(payload, indent=2)}")
        response = requests.post(f"{API_BASE_URL}/api/v1/ai/generate", json=payload)
        
        # Check if successful
        if response.status_code == 200:
            log(f"AI text generation response: {json.dumps(response.json(), indent=2)}")
            return True
        else:
            log(f"ERROR: AI text generation failed with status code {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        log(f"ERROR: AI text generation test failed: {e}")
        # RequestException objects have a response attribute
        if e.response is not None:
            log(f"Response status: {e.response.status_code}")
            log(f"Response body: {e.response.text}")
        return False
    except Exception as e:
        log(f"ERROR: AI text generation test failed: {e}")
        return False

def test_linkedin_post_generation():
    """Test the LinkedIn post generation endpoint"""
    log("Testing LinkedIn post generation endpoint...")
    try:
        payload = {
            "topic": "Water treatment efficiency",
            "audience": "Industry professionals", 
            "tone": "professional",
            "include_hashtags": True,
            "max_length": 400
        }
        
        log(f"Sending request to /api/v1/ai/linkedin/post with payload: {json.dumps(payload, indent=2)}")
        response = requests.post(f"{API_BASE_URL}/api/v1/ai/linkedin/post", json=payload)
        
        # Check if successful
        if response.status_code == 200:
            log(f"LinkedIn post generation response: {json.dumps(response.json(), indent=2)}")
            return True
        else:
            log(f"ERROR: LinkedIn post generation failed with status code {response.status_code}: {response.text}")
            # This might be expected if OpenAI credentials are not set up
            log("NOTE: This failure might be expected if OpenAI API credentials are not configured.")
            return False
            
    except requests.exceptions.RequestException as e:
        log(f"ERROR: LinkedIn post generation test failed: {e}")
        # RequestException objects have a response attribute
        if e.response is not None:
            log(f"Response status: {e.response.status_code}")
            log(f"Response body: {e.response.text}")
        # Don't fail the entire test if this particular endpoint doesn't work
        log("NOTE: This failure might be expected if OpenAI API credentials are not configured.")
        return False
    except Exception as e:
        log(f"ERROR: LinkedIn post generation test failed: {e}")
        log("NOTE: This failure might be expected if OpenAI API credentials are not configured.")
        return False

async def test_socketio_connection():
    """Test Socket.IO connection and basic events"""
    log("Testing Socket.IO connection...")
    
    # Create events to track Socket.IO responses
    connected_event = asyncio.Event()
    connection_error_event = asyncio.Event()
    welcome_received = asyncio.Event()
    pong_received = asyncio.Event()
    echo_received = asyncio.Event()
    
    # Create a Socket.IO client
    sio = socketio.AsyncClient()
    connection_error_message = ""
    
    @sio.event
    async def connect():
        log("Socket.IO Connected!")
        connected_event.set()
    
    @sio.event
    async def connect_error(data):
        nonlocal connection_error_message
        error_msg = str(data)
        log(f"Socket.IO Connection Error: {error_msg}")
        connection_error_message = error_msg
        connection_error_event.set()
    
    @sio.event
    async def disconnect():
        log("Socket.IO Disconnected!")
    
    @sio.event
    async def welcome(data):
        log(f"Welcome message received: {data}")
        welcome_received.set()
    
    @sio.event
    async def pong(data):
        log(f"Pong response received: {data}")
        pong_received.set()
    
    @sio.event
    async def echo_response(data):
        log(f"Echo response received: {data}")
        echo_received.set()
    
    # Connect to Socket.IO server
    try:
        log(f"Attempting to connect to Socket.IO server at {SOCKETIO_URL}...")
        await sio.connect(SOCKETIO_URL, wait_timeout=8)
        
        # Wait for either connected event or connection error
        try:
            # Wait for either connection success or failure
            done, pending = await asyncio.wait(
                [
                    asyncio.create_task(connected_event.wait()),
                    asyncio.create_task(connection_error_event.wait())
                ],
                timeout=8,
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Cancel any pending tasks
            for task in pending:
                task.cancel()
                
            if connection_error_event.is_set():
                log(f"ERROR: Socket.IO connection failed: {connection_error_message}")
                return False
                
            if not connected_event.is_set():
                log("ERROR: Socket.IO connection timed out - neither success nor error event was triggered")
                return False
                
        except asyncio.TimeoutError:
            log("ERROR: Socket.IO connection timed out waiting for connection status")
            return False
        
        # Wait for welcome message
        try:
            await asyncio.wait_for(welcome_received.wait(), timeout=5)
            log("Successfully received welcome message")
        except asyncio.TimeoutError:
            log("WARNING: Did not receive welcome message, but continuing with tests")
        
        # Test ping-pong communication
        try:
            # Send ping request
            log("Sending ping request...")
            await sio.emit('ping')
            
            # Wait for pong response
            await asyncio.wait_for(pong_received.wait(), timeout=5)
            log("Ping-pong test successful!")
        except asyncio.TimeoutError:
            log("ERROR: Did not receive pong response")
            await sio.disconnect()
            return False
        
        # Test echo functionality
        try:
            # Send echo request
            echo_data = {"text": "Test echo message", "timestamp": datetime.now().isoformat()}
            log(f"Sending echo request with data: {echo_data}")
            await sio.emit('echo', echo_data)
            
            # Wait for echo response
            await asyncio.wait_for(echo_received.wait(), timeout=5)
            log("Echo test successful!")
        except asyncio.TimeoutError:
            log("ERROR: Did not receive echo response")
            await sio.disconnect()
            return False
        
        # All tests passed
        log("All Socket.IO tests passed successfully!")
        await sio.disconnect()
        return True
        
    except Exception as e:
        log(f"ERROR in Socket.IO test: {e}")
        try:
            # Try to disconnect if connected
            if sio.connected:
                await sio.disconnect()
        except:
            pass
        return False

async def run_tests():
    """Run all integration tests"""
    log("Starting Mexel integration tests...")
    
    results = {
        "api_health": test_api_health(),
        "example_endpoint": test_example_endpoint(),
        "websocket_status": test_websocket_status(),
        "ai_text_generation": test_ai_text_generation(),
        "linkedin_post": test_linkedin_post_generation(),
        "socketio": await test_socketio_connection(),
    }
    
    # Print summary
    log("\n=== TEST RESULTS ===")
    all_passed = True
    passed_count = 0
    failed_count = 0
    
    # Calculate padding for alignment
    max_length = max(len(test) for test in results.keys())
    
    # Print each test result
    for test, passed in results.items():
        status = "✓ PASSED" if passed else "✗ FAILED"
        status_color = "" if passed else ""  # Terminal colors not used for compatibility
        
        if passed:
            passed_count += 1
        else:
            failed_count += 1
            all_passed = False
            
        # Format with padding for alignment
        log(f"{test.ljust(max_length + 2)}: {status}")
    
    # Print summary statistics
    log("\n=== SUMMARY ===")
    total = len(results)
    log(f"Total Tests:  {total}")
    log(f"Passed:       {passed_count}")
    log(f"Failed:       {failed_count}")
    
    # Print overall status
    if all_passed:
        log("\nOVERALL STATUS: PASSED - All tests completed successfully!")
    else:
        log("\nOVERALL STATUS: FAILED - Some tests did not pass.")
        log("Check the individual test results above for details on failures.")
    
    return all_passed

if __name__ == "__main__":
    log("Starting Integration Tests...")
    asyncio.run(run_tests())
