// Type declarations for libraries without official TypeScript definitions

declare module "simple-statistics" {
  export function mean(data: number[]): number;
  export function standardDeviation(data: number[]): number;
  export function linearRegression(data: [number, number][]): {
    m: number;
    b: number;
  };
  export function sampleCorrelation(x: number[], y: number[]): number;
}

declare module "node-cron" {
  interface ScheduledTask {
    stop(): void;
  }

  export function schedule(
    cronExpression: string,
    task: () => void
  ): ScheduledTask;
  export function validate(cronExpression: string): boolean;
}

// Declare missing properties on Lead and utility functions
interface RandomUUIDOptions {
  disableEntropyCache?: boolean;
}

// Using interface merging instead of namespace declaration to avoid conflict with DOM lib
interface Crypto {
  randomUUID(options?: RandomUUIDOptions): string;
}
