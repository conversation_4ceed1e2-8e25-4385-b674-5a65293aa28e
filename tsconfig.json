{
  "compilerOptions": {
    "target": "ES2020", // Updated target
    "module": "ESNext", // Updated module
    "moduleResolution": "node", // Changed back to node for compatibility
    "resolveJsonModule": true, // Added
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true, // Added
    "strict": true, // Added
    "skipLibCheck": true, // Added
    "baseUrl": ".",
    "rootDir": ".", // Retained from existing
    // "outDir": "./dist", // Consider if a root-level outDir is needed or if projects handle their own
    "typeRoots": ["./node_modules/@types", "./types"],
    "types": ["node", "jest"],
    "paths": {
      "@shared/*": ["./shared/src/*"], // Adjusted to point to src
      // Consider if root @/* is needed or if frontend handles its own
      // "@/*": ["./src/*"],
      // "@components/*": ["./src/components/*"], // These seem frontend specific
      // "@services/*": ["./src/services/*"],   // These seem frontend specific
      // "@utils/*": ["./src/utils/*"],       // These seem frontend specific
      "@custom_types/*": ["./types/*"] // Renamed for clarity if "./types" contains custom types
    }
  },
  "include": [
    "src/**/*", // If you have a root src folder for backend/shared utilities
    "shared/src/**/*", // Include shared source
    "tests/**/*", // If you have a root tests folder
    "jest.config.ts", // Include root jest config
    "types/**/*.d.ts"
    // "*.js", // Be specific about which .js files to include if necessary
    // "newTypeDefFile.d.ts" // Include if this file exists and is needed
  ],
  "exclude": [
    "node_modules",
    "dist",
    "coverage", // Added coverage
    "frontend" // Exclude frontend as it has its own tsconfig and is referenced
  ],
  "references": [
    { "path": "./frontend" },
    { "path": "./shared" }
    // If you have scripts that are TypeScript and need to be part of the build:
    // { "path": "./scripts" } // Assuming scripts has its own tsconfig.json
  ],
  "compileOnSave": false
}
