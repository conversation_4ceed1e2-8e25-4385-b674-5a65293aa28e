# TypeScript and ESLint Troubleshooting Guide

This guide provides steps to resolve common TypeScript and ESLint integration issues in this monorepo project.

## Common TypeScript Issues

### "Cannot find name 'process'" or "Cannot find name 'require'"

These errors occur when TypeScript cannot find Node.js type definitions.

**Solution:**

1. Check that the `types` array includes "node" in your `tsconfig.json`:
   ```json
   "types": ["node", "jest"]
   ```

2. Ensure `typeRoots` is correctly set to find Node.js types:
   ```json
   "typeRoots": [
     "./node_modules/@types",
     "../node_modules/@types",
     "./types"
   ]
   ```

3. Run the `fix-module-resolution.js` script:
   ```bash
   node fix-module-resolution.js
   ```
   This will create fallback type definitions if needed.

### Missing Jest Types

**Solution:**

1. Add "jest" to the `types` array in your `tsconfig.json`:
   ```json
   "types": ["node", "jest"]
   ```

2. Run the `fix-module-resolution.js` script which creates fallback Jest type definitions.

## Common ESLint Issues

### ESLint Cannot Find Plugin or Parser

**Solution:**

1. Check your `.eslintrc.js` configuration to ensure it's correctly set up.

2. Run the diagnostic script to check for module resolution issues:
   ```bash
   node run-diagnostics.js
   ```

3. Make sure the parser options in `.eslintrc.js` are correctly configured:
   ```javascript
   parserOptions: {
     project: ['./tsconfig.json', './frontend/tsconfig.json', './shared/tsconfig.json'],
     tsconfigRootDir: __dirname
   }
   ```

## VS Code Integration Issues

If VS Code isn't working correctly with TypeScript or ESLint:

1. Make sure the `.vscode/settings.json` file has these settings:
   ```json
   {
     "typescript.tsdk": "node_modules/typescript/lib",
     "eslint.nodePath": "node_modules",
     "typescript.tsserver.experimental.enableProjectDiagnostics": true,
     "typescript.tsserver.watchOptions": {
       "watchFile": "useFsEvents",
       "watchDirectory": "useFsEvents",
       "fallbackPolling": "dynamicPriorityPolling"
     }
   }
   ```

2. Run the VS Code settings fix script:
   ```bash
   node fix-vscode-settings.js
   ```

3. Check the ESLint extension settings in VS Code to ensure it's enabled for your project.

## Environment Variables

Setting these environment variables in `frontend/.env` can help with development:

```
SKIP_PREFLIGHT_CHECK=true
TSC_COMPILE_ON_ERROR=true
```

## Manual Fixes for Syntax Errors

For TypeScript syntax errors like:
- `const Component: = () => {}`
- `(value as any) => {}`

Run the syntax fix script:
```bash
node fix-typescript-syntax.js
```

## Complete Diagnostics

To run a full diagnostic check on your project:
```bash
node run-diagnostics.js
```

This will check:
- Node modules structure
- TypeScript configuration and version
- ESLint configuration and version
- TypeScript and ESLint errors

## Working Without yarn install

If you cannot run `yarn install`:

1. Run the module resolution fix script:
   ```bash
   node fix-module-resolution.js
   ```

2. Ensure VS Code settings are correct:
   ```bash
   node fix-vscode-settings.js
   ```

3. Check which errors remain:
   ```bash
   node run-diagnostics.js
   ```
