{
  "extends": "./tsconfig.base.json",
  "compilerOptions": {
    "module": "CommonJS",
    "outDir": "./dist",
    "rootDir": ".",
    "declaration": true,
    "noEmit": false,
    "typeRoots": ["./node_modules/@types", "./types"],
    "types": ["node", "jest"]
    "paths": {
      "@/*": ["./src/*"],
      "@shared/*": ["./shared/*"],
      "@components/*": ["./src/components/*"],
      "@services/*": ["./src/services/*"],
      "@utils/*": ["./src/utils/*"],
      "@types/*": ["./types/*"]
    }
  },
  "include": [
    "src/**/*",
    "types/**/*.d.ts",
    "*.js",
    "scripts/**/*.js",
    "scripts/**/*.ts"
  ],
  "exclude": ["node_modules", "dist", "frontend", "shared"]
}
