{"compilerOptions": {"target": "es2019", "module": "esnext", "lib": ["es6", "dom", "es2019"], "jsx": "react-jsx", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "allowJs": true, "typeRoots": ["./node_modules/@types", "./frontend/node_modules/@types"], "types": ["node", "jest"], "paths": {"axios": ["./node_modules/axios/index.d.ts"]}, "noImplicitAny": false, "ignoreDeprecations": "5.0", "useUnknownInCatchVariables": false}}