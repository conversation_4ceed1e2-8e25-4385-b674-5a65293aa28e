{"extends": "./tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "module": "CommonJS", "outDir": "./dist", "rootDir": ".", "declaration": true, "noEmit": false, "esModuleInterop": true, "typeRoots": ["./node_modules/@types", "./types"], "types": ["node", "jest"], "paths": {"@/*": ["./src/*"], "@shared/dist": ["./shared/dist/index"], "@components/*": ["./src/components/*"], "@services/*": ["./src/services/*"], "@utils/*": ["./src/utils/*"], "@types/*": ["./types/*"]}}, "include": ["src/**/*", "types/**/*.d.ts", "*.js", "newTypeDefFile.d.ts"], "exclude": ["node_modules", "dist", "frontend", "shared", "scripts/**/*.js"]}