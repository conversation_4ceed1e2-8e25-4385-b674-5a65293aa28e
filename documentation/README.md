# Mexel Documentation

## Overview
Welcome to the Mexel documentation. This repository contains comprehensive documentation for the Mexel Lead Discovery System, an AI-powered marketing system with tender monitoring capabilities.

## Table of Contents

### Getting Started
- [Quick Start Guide](./guides/quick-start.md)
- [Installation Guide](./guides/installation.md)
- [Configuration Guide](./guides/configuration.md)

### Architecture
- [System Architecture](./architecture/system-overview.md)
- [Database Schema](./architecture/database.md)
- [API Documentation](./architecture/api.md)
- [Security Architecture](./architecture/security.md)

### Development
- [Development Setup](./development/setup.md)
- [Coding Standards](./development/coding-standards.md)
- [Testing Guide](./development/testing.md)
- [Deployment Guide](./development/deployment.md)

### Features
- [Tender Monitoring](./features/tender-monitoring.md)
- [Lead Generation](./features/lead-generation.md)
- [AI Integration](./features/ai-integration.md)
- [Analytics & Reporting](./features/analytics.md)

### API Reference
- [REST API](./api/rest.md)
- [WebSocket API](./api/websocket.md)
- [Integration Guide](./api/integration.md)

### Maintenance
- [Monitoring Guide](./maintenance/monitoring.md)
- [Backup & Recovery](./maintenance/backup-recovery.md)
- [Troubleshooting](./maintenance/troubleshooting.md)

## Project Status
Current Version: 1.0.0
Last Updated: 2024-02-14

## Contributing
Please read our [Contributing Guide](./CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## License
This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

## Support
For support and questions, please:
1. Check the [Troubleshooting Guide](./maintenance/troubleshooting.md)
2. Search existing [Issues](https://github.com/mexel/issues)
3. Create a new issue if needed

## Quick Links
- [Project Roadmap](./ROADMAP.md)
- [Changelog](./CHANGELOG.md)
- [Security Policy](./SECURITY.md)
- [API Status](https://status.mexel.com)
- [Developer Portal](https://developers.mexel.com)

## Directory Structure
```
documentation/
├── guides/          # User and setup guides
├── architecture/    # System architecture documentation
├── development/     # Development guidelines and standards
├── features/        # Feature documentation
├── api/            # API documentation
└── maintenance/    # System maintenance guides
```

For the most up-to-date documentation, always refer to the [online documentation](https://docs.mexel.com).