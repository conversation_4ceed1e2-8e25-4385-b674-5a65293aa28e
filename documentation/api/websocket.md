# Mexel WebSocket API Documentation

## Overview

The Mexel WebSocket API provides real-time updates for tender monitoring, lead management, and system notifications. This document details the WebSocket connection, events, and message formats.

## Connection

### WebSocket URL
```
wss://api.mexel.com/v1/ws
```
Development:
```
ws://localhost:3001/v1/ws
```

### Authentication
Connect with either:
1. Authentication token in URL:
```
wss://api.mexel.com/v1/ws?token=<your_token>
```

2. Authentication header (preferred):
```
Authorization: Bearer <your_token>
```

## Events

### System Events

#### Connection Events
```javascript
// Server -> Client: Connection established
{
  "type": "connection:established",
  "data": {
    "connection_id": "string",
    "timestamp": "ISO date"
  }
}

// Server -> Client: Connection error
{
  "type": "connection:error",
  "data": {
    "code": "string",
    "message": "string"
  }
}

// Server -> Client: Heartbeat
{
  "type": "heartbeat",
  "data": {
    "timestamp": "ISO date"
  }
}
```

### Tender Events

#### Tender Updated
```javascript
{
  "type": "tender:updated",
  "data": {
    "id": "string",
    "title": "string",
    "status": "string",
    "value": "number",
    "relevance_score": "number",
    "updated_at": "ISO date"
  }
}
```

#### New Tender
```javascript
{
  "type": "tender:new",
  "data": {
    "id": "string",
    "title": "string",
    "description": "string",
    "value": "number",
    "deadline": "ISO date",
    "status": "string",
    "relevance_score": "number",
    "source": "string",
    "created_at": "ISO date"
  }
}
```

### Lead Events

#### Lead Created
```javascript
{
  "type": "lead:created",
  "data": {
    "id": "string",
    "company": "string",
    "status": "string",
    "score": "number",
    "source": "string",
    "created_at": "ISO date"
  }
}
```

#### Lead Updated
```javascript
{
  "type": "lead:updated",
  "data": {
    "id": "string",
    "status": "string",
    "score": "number",
    "updated_at": "ISO date"
  }
}
```

### Notification Events

#### New Notification
```javascript
{
  "type": "notification:new",
  "data": {
    "id": "string",
    "type": "string",
    "message": "string",
    "priority": "string",
    "created_at": "ISO date"
  }
}
```

### Analytics Events

#### Performance Update
```javascript
{
  "type": "analytics:performance",
  "data": {
    "metric": "string",
    "value": "number",
    "change": "number",
    "timestamp": "ISO date"
  }
}
```

## Client Messages

### Subscribe to Events
```javascript
// Client -> Server
{
  "action": "subscribe",
  "channels": ["tenders", "leads", "notifications"]
}

// Server -> Client
{
  "type": "subscription:success",
  "data": {
    "channels": ["tenders", "leads", "notifications"]
  }
}
```

### Unsubscribe from Events
```javascript
// Client -> Server
{
  "action": "unsubscribe",
  "channels": ["tenders"]
}

// Server -> Client
{
  "type": "subscription:updated",
  "data": {
    "channels": ["leads", "notifications"]
  }
}
```

## Error Handling

### Error Message Format
```javascript
{
  "type": "error",
  "data": {
    "code": "string",
    "message": "string",
    "details": "object (optional)"
  }
}
```

### Common Error Codes
- `4001`: Authentication failed
- `4002`: Invalid message format
- `4003`: Invalid subscription
- `4004`: Rate limit exceeded
- `5001`: Internal server error

## Connection Management

### Heartbeat
- Server sends heartbeat every 30 seconds
- Client should respond within 5 seconds
- Connection closed after 3 missed heartbeats

### Reconnection
- Implement exponential backoff
- Start with 1-second delay
- Maximum delay of 30 seconds
- Reset delay after successful connection

## Best Practices

1. Message Processing
   - Validate message format
   - Handle unknown message types
   - Queue messages during reconnection

2. Connection Management
   - Monitor connection health
   - Implement heartbeat responses
   - Handle reconnection gracefully

3. Error Handling
   - Log all errors
   - Implement reconnection strategy
   - Maintain message order

4. Performance
   - Batch subscription changes
   - Limit concurrent connections
   - Monitor message rate

## Examples

### Connection with Authentication
```javascript
const ws = new WebSocket('wss://api.mexel.com/v1/ws');
ws.onopen = () => {
  ws.send(JSON.stringify({
    action: 'authenticate',
    token: 'your_token'
  }));
};
```

### Event Subscription
```javascript
ws.send(JSON.stringify({
  action: 'subscribe',
  channels: ['tenders', 'leads']
}));
```

### Message Handling
```javascript
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  switch (message.type) {
    case 'tender:new':
      handleNewTender(message.data);
      break;
    case 'lead:updated':
      handleLeadUpdate(message.data);
      break;
  }
};
```

## Rate Limits

- Maximum 100 messages per minute per connection
- Maximum 10 concurrent connections per API key
- Subscription limited to 50 channels per connection

## SDK Support

Official WebSocket client libraries:
- JavaScript/TypeScript
- Python
- Java
- C#

## Support

- Documentation: https://docs.mexel.com/websocket
- Support Email: <EMAIL>
- Status Page: https://status.mexel.com

For implementation examples, see the [WebSocket Integration Guide](./websocket-integration.md).