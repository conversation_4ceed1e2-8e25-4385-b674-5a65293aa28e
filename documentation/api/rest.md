# Mexel REST API Documentation

## API Overview

Base URL: `https://api.mexel.com/v1` or `http://localhost:3001/v1` (development)

## Authentication

All API requests require authentication using one of these methods:

1. <PERSON><PERSON> (preferred)
```
Authorization: Bearer <your_token>
```

2. API Key
```
X-API-Key: <your_api_key>
```

## Endpoints

### Tender Management

#### List Tenders
```http
GET /tenders
```

Query Parameters:
- `page` (integer): Page number for pagination (default: 1)
- `limit` (integer): Results per page (default: 20)
- `sort` (string): Sort field (options: relevance, date, value)
- `order` (string): Sort order (asc/desc)
- `status` (string): Filter by status
- `since` (ISO date): Filter by date

Response:
```json
{
  "data": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "value": "number",
      "deadline": "ISO date",
      "status": "string",
      "relevance_score": "number",
      "source": "string",
      "created_at": "ISO date",
      "updated_at": "ISO date"
    }
  ],
  "meta": {
    "total": "number",
    "page": "number",
    "limit": "number",
    "pages": "number"
  }
}
```

#### Get Tender
```http
GET /tenders/{id}
```

Response: Single tender object

#### Update Tender
```http
PATCH /tenders/{id}
```

Request Body:
```json
{
  "status": "string",
  "notes": "string",
  "priority": "number"
}
```

### Lead Management

#### List Leads
```http
GET /leads
```

Query Parameters:
- `page` (integer): Page number
- `limit` (integer): Results per page
- `status` (string): Filter by status
- `source` (string): Filter by source

Response:
```json
{
  "data": [
    {
      "id": "string",
      "company": "string",
      "contact": "string",
      "email": "string",
      "phone": "string",
      "status": "string",
      "source": "string",
      "score": "number",
      "created_at": "ISO date",
      "updated_at": "ISO date"
    }
  ],
  "meta": {
    "total": "number",
    "page": "number",
    "limit": "number"
  }
}
```

#### Create Lead
```http
POST /leads
```

Request Body:
```json
{
  "company": "string",
  "contact": "string",
  "email": "string",
  "phone": "string",
  "source": "string",
  "notes": "string"
}
```

### Analytics

#### Get Overview
```http
GET /analytics/overview
```

Response:
```json
{
  "tenders": {
    "total": "number",
    "active": "number",
    "won": "number",
    "lost": "number"
  },
  "leads": {
    "total": "number",
    "new": "number",
    "converted": "number",
    "conversion_rate": "number"
  },
  "performance": {
    "response_time_avg": "number",
    "success_rate": "number"
  }
}
```

#### Get Reports
```http
GET /analytics/reports
```

Query Parameters:
- `type` (string): Report type (tender/lead/performance)
- `from` (ISO date): Start date
- `to` (ISO date): End date
- `format` (string): Output format (json/csv)

### Notifications

#### Get Notifications
```http
GET /notifications
```

Query Parameters:
- `unread` (boolean): Filter unread notifications
- `type` (string): Notification type

Response:
```json
{
  "data": [
    {
      "id": "string",
      "type": "string",
      "message": "string",
      "read": "boolean",
      "created_at": "ISO date"
    }
  ],
  "meta": {
    "total": "number",
    "unread": "number"
  }
}
```

#### Mark as Read
```http
POST /notifications/{id}/read
```

### System Configuration

#### Get Configuration
```http
GET /config
```

Response:
```json
{
  "notification_settings": {
    "email_enabled": "boolean",
    "push_enabled": "boolean",
    "quiet_hours": {
      "start": "string",
      "end": "string"
    }
  },
  "tender_settings": {
    "min_value": "number",
    "keywords": ["string"],
    "excluded_keywords": ["string"]
  },
  "lead_settings": {
    "auto_assignment": "boolean",
    "scoring_rules": {
      "min_score": "number",
      "factors": {
        "company_size": "number",
        "industry": "number",
        "location": "number"
      }
    }
  }
}
```

#### Update Configuration
```http
PATCH /config
```

Request Body: Partial configuration object

## Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "string",
    "message": "string",
    "details": "object (optional)"
  }
}
```

### Common Error Codes
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `429`: Too Many Requests
- `500`: Internal Server Error

## Rate Limiting

- Default: 100 requests per minute
- Burst: 200 requests per minute
- Headers:
  - `X-RateLimit-Limit`
  - `X-RateLimit-Remaining`
  - `X-RateLimit-Reset`

## Versioning

- API version in URL: `/v1`
- Version header: `Accept: application/vnd.mexel.v1+json`

## Pagination

All list endpoints support pagination with:
- `page`: Page number (1-based)
- `limit`: Items per page (default: 20, max: 100)

Response includes metadata:
```json
{
  "meta": {
    "total": "number",
    "page": "number",
    "limit": "number",
    "pages": "number"
  }
}
```

## WebSocket Events

Some endpoints support real-time updates via WebSocket:
- `tender:updated`
- `lead:created`
- `notification:new`

See [WebSocket API Documentation](./websocket.md) for details.

## SDK Support

Official SDKs available for:
- JavaScript/TypeScript
- Python
- PHP
- Go

## Support

- Documentation: https://docs.mexel.com
- Support Email: <EMAIL>
- Status Page: https://status.mexel.com

For detailed integration examples, see the [Integration Guide](./integration.md).