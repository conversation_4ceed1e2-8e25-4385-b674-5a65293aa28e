# Mexel Testing Guide

## Overview

This comprehensive guide outlines testing practices, tools, and procedures for the Mexel system.

## Testing Stack

### Core Testing Tools
- Jest: Unit and integration testing
- Playwright: End-to-end testing
- SuperTest: API testing
- React Testing Library: Frontend component testing

### Testing Types

1. Unit Tests
   - Business logic
   - Utility functions
   - Data transformations
   - Component rendering

2. Integration Tests
   - API endpoints
   - Database operations
   - Service interactions
   - External integrations

3. End-to-End Tests
   - User workflows
   - System processes
   - Cross-component interactions
   - Browser compatibility

4. Performance Tests
   - Load testing
   - Stress testing
   - Scalability testing
   - Response time benchmarking

## Test Structure

### Directory Organization
```
tests/
├── unit/                 # Unit tests
├── integration/         # Integration tests
├── e2e/                # End-to-end tests
├── performance/        # Performance tests
├── fixtures/          # Test data
└── utils/             # Test utilities
```

### Naming Conventions
- Unit tests: `*.spec.ts`
- Integration tests: `*.test.ts`
- E2E tests: `*.e2e.ts`
- Performance tests: `*.perf.ts`

## Writing Tests

### Unit Test Example
```typescript
describe('TenderService', () => {
  describe('calculateRelevanceScore', () => {
    it('should return high score for relevant keywords', () => {
      const tender = {
        title: 'Chemical Water Treatment',
        description: 'Industrial cleaning solutions needed'
      };
      
      const score = tenderService.calculateRelevanceScore(tender);
      expect(score).toBeGreaterThan(0.8);
    });

    it('should return low score for irrelevant content', () => {
      const tender = {
        title: 'Office Supplies',
        description: 'Paper and stationery items'
      };
      
      const score = tenderService.calculateRelevanceScore(tender);
      expect(score).toBeLessThan(0.3);
    });
  });
});
```

### Integration Test Example
```typescript
describe('TenderAPI', () => {
  it('should create new tender', async () => {
    const tender = {
      title: 'Test Tender',
      value: 50000,
      deadline: '2024-12-31'
    };

    const response = await request(app)
      .post('/api/tenders')
      .send(tender)
      .expect(201);

    expect(response.body).toHaveProperty('id');
    expect(response.body.title).toBe(tender.title);
  });
});
```

### E2E Test Example
```typescript
test('user can view and filter tenders', async ({ page }) => {
  await page.goto('/tenders');
  
  // Filter tenders
  await page.fill('[data-testid="keyword-filter"]', 'chemical');
  await page.click('[data-testid="apply-filter"]');
  
  // Verify filtered results
  const tenders = await page.$$('[data-testid="tender-item"]');
  expect(tenders.length).toBeGreaterThan(0);
  
  // Verify tender details
  await tenders[0].click();
  await expect(page).toHaveURL(/\/tenders\/\d+/);
});
```

## Test Coverage

### Coverage Goals
- Unit Tests: 80% coverage
- Integration Tests: 70% coverage
- E2E Tests: Key user workflows
- Performance Tests: Critical paths

### Running Coverage Reports
```bash
# Unit test coverage
yarn test:unit --coverage

# Integration test coverage
yarn test:integration --coverage

# Combined coverage report
yarn test:coverage
```

## Performance Testing

### Load Testing
```typescript
describe('API Performance', () => {
  it('should handle 100 concurrent requests', async () => {
    const requests = Array(100).fill().map(() =>
      request(app).get('/api/tenders')
    );
    
    const responses = await Promise.all(requests);
    responses.forEach(response => {
      expect(response.status).toBe(200);
      expect(response.timing).toBeLessThan(1000);
    });
  });
});
```

### Stress Testing
```typescript
test('system handles high load', async () => {
  const metrics = await loadTest({
    endpoint: '/api/tenders',
    duration: '5m',
    rampUp: '30s',
    users: 1000
  });
  
  expect(metrics.errorRate).toBeLessThan(0.01);
  expect(metrics.p95ResponseTime).toBeLessThan(2000);
});
```

## Mocking

### API Mocks
```typescript
const mockTenderAPI = {
  getTenders: jest.fn(),
  createTender: jest.fn(),
  updateTender: jest.fn()
};

beforeEach(() => {
  mockTenderAPI.getTenders.mockResolvedValue([
    { id: 1, title: 'Mock Tender' }
  ]);
});
```

### Database Mocks
```typescript
const mockDB = {
  query: jest.fn(),
  transaction: jest.fn()
};

jest.mock('../src/db', () => ({
  __esModule: true,
  default: mockDB
}));
```

## Test Data Management

### Fixtures
```typescript
// fixtures/tenders.ts
export const testTenders = [
  {
    id: 1,
    title: 'Test Tender 1',
    value: 50000,
    deadline: '2024-12-31'
  },
  {
    id: 2,
    title: 'Test Tender 2',
    value: 75000,
    deadline: '2024-11-30'
  }
];
```

### Factory Functions
```typescript
// utils/factories.ts
export const createTestTender = (overrides = {}) => ({
  id: Math.random().toString(36).substr(2, 9),
  title: 'Test Tender',
  value: 50000,
  deadline: '2024-12-31',
  ...overrides
});
```

## CI/CD Integration

### GitHub Actions Workflow
```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Install dependencies
        run: yarn install
      - name: Run tests
        run: |
          yarn test:unit
          yarn test:integration
          yarn test:e2e
      - name: Upload coverage
        uses: codecov/codecov-action@v2
```

## Best Practices

### General Guidelines
1. Write descriptive test names
2. One assertion per test
3. Use beforeEach for setup
4. Clean up after tests
5. Avoid test interdependence

### Code Quality
1. DRY test code
2. Use helper functions
3. Maintain test data
4. Document complex tests
5. Regular test maintenance

### Performance
1. Mock external services
2. Use test doubles
3. Optimize test runs
4. Parallel test execution
5. Regular performance monitoring

## Troubleshooting

### Common Issues
1. Flaky Tests
   - Identify timing issues
   - Add retry mechanisms
   - Improve assertions
   - Log detailed failures

2. Slow Tests
   - Profile test execution
   - Optimize setup/teardown
   - Reduce test data
   - Parallel execution

3. Memory Leaks
   - Monitor memory usage
   - Clean up resources
   - Check async operations
   - Verify teardown

## Running Tests

### Commands
```bash
# Run all tests
yarn test

# Run specific test types
yarn test:unit
yarn test:integration
yarn test:e2e
yarn test:performance

# Watch mode
yarn test:watch

# Update snapshots
yarn test:update

# Debug tests
yarn test:debug
```

## Support

- Documentation: https://docs.mexel.com/testing
- Issues: https://github.com/mexel/issues
- Wiki: https://wiki.mexel.com/testing

For detailed API documentation, see the [API Documentation](../api/README.md).