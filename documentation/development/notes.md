# Development Notes

## Marketing Strategy for Mexel Energy Sustain

### 1. Positioning & Messaging

#### Core Value Proposition:

"Precision chemical treatment powered by AI & IoT — maximize equipment efficiency and lifespan with Mexel."

#### Key Differentiators:

- Film-Forming Amine (FFA) + surfactant tech (Mexel 432)
- Smart dosing with real-time analytics
- Eco-efficient water and energy treatment

## Recent Changes

### May 12, 2025

1. **Removed Unnecessary Mock Data**:

   - Removed the `src/data/mockData.ts` file which was not being used directly in the codebase.
   - The server.ts file has its own mock data definitions that are used as fallbacks when real data is not available.

2. **Fixed TypeScript Errors in DatabaseErrorHandler**:

   - Created a `DatabaseSpecificError` interface that extends `Error` and adds a `code` property.
   - Used type assertions to properly handle the `code` property on error objects.
   - Updated the `DatabaseError` class to properly type the `originalError` property.

3. **Fixed EmailService Implementation**:

   - Updated the EmailService.ts file to work with the latest version of the Brevo API.
   - Added proper type definitions for Brevo API parameters.
   - Updated the constructor to properly initialize the Brevo API client.
   - Implemented the `sendTransactionalEmail` method to use the Brevo API client.
   - Added test mode support to avoid sending real emails during testing.
   - Fixed type issues with the attachment parameter.
   - Improved error handling for API calls.
   - Fixed the `isConfigured` method to properly check if the API key is available.

4. **Fixed Path Alias Issues**:

   - Updated the npm dev script to use tsconfig-paths to properly resolve path aliases.
   - Installed the tsconfig-paths package as a dev dependency.
   - This allows the code to continue using the `@src` path alias without having to update all imports.

5. **Updated .gitignore**:
   - Added database files to .gitignore to prevent them from being committed.

## Known Issues

1. **Database Schema Issues**:

   - See [DATABASE_ISSUES.md](./DATABASE_ISSUES.md) for details on database schema issues and potential solutions.

2. **Test Failures**:
   - There are test failures related to the mock implementations in the test files.
   - These would need to be updated to match the new implementations of the services.

## Development Environment

### Prerequisites

- Node.js 18.x or higher
- npm 9.x or higher
- Python 3.8 or higher (for scraping)
- SQLite 3.x

### Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   yarn install
   ```
3. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```
4. Create a `.env` file based on `.env.example`
5. Run database migrations:
   ```bash
   yarn db:migrate
   ```

### Running the Application

```bash
yarn dev
```

The application will be available at http://localhost:3001.

### Running Tests

```bash
yarn test
```

## Project Structure

- `src/` - Source code
  - `agents/` - Agent implementations
  - `config/` - Configuration files
  - `database/` - Database-related code
  - `interfaces/` - TypeScript interfaces
  - `migrations/` - Database migrations
  - `services/` - Service implementations
  - `types/` - TypeScript types
  - `utils/` - Utility functions
  - `server.ts` - Main server file
- `frontend/` - Frontend code
- `data/` - Database files (gitignored)
- `scripts/` - Helper scripts

## Deployment

TBD

## Future Work

1. Fix database schema issues
2. Update tests to match new implementations
3. Implement proper error handling for database operations
4. Add more comprehensive logging
5. Improve test coverage
