# Mexel Configuration Guide

## Configuration Overview

This guide details all configuration options for the Mexel system, including environment variables, system settings, and integration configurations.

## Environment Variables

### Core Settings
```ini
# Server Configuration
NODE_ENV=development|production
PORT=3001
HOST=localhost

# Security
JWT_SECRET=your_jwt_secret_key
API_KEY=your_api_key
ENCRYPTION_KEY=your_encryption_key

# Database
DB_PATH=./data/mexel.db
DB_BACKUP_PATH=./backups

# AI Integration
DEEPSEEK_API_KEY=your_deepseek_api_key
AI_MODEL=gpt-4|gpt-3.5-turbo

# Email Settings
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_email
SMTP_PASS=your_password
EMAIL_FROM=<EMAIL>
```

### Optional Settings
```ini
# Performance
CACHE_TTL=3600
MAX_CONNECTIONS=100
RATE_LIMIT=100

# Monitoring
SENTRY_DSN=your_sentry_dsn
LOG_LEVEL=info|debug|error

# Integration
ZAPIER_WEBHOOK=your_webhook_url
SLACK_WEBHOOK=your_slack_webhook
```

## System Configuration

### 1. Database Configuration

The SQLite database can be configured in `config/database.json`:

```json
{
  "main": {
    "path": "./data/mexel.db",
    "backup": {
      "enabled": true,
      "schedule": "0 0 * * *",
      "retain": 7
    }
  },
  "performance": {
    "cache": true,
    "journal_mode": "WAL",
    "synchronous": "NORMAL"
  }
}
```

### 2. Tender Monitoring Settings

Configure tender monitoring in `config/tenders.json`:

```json
{
  "sources": {
    "etenders": {
      "enabled": true,
      "url": "https://etenders.gov.za",
      "schedule": "*/30 * * * *"
    },
    "eskom": {
      "enabled": true,
      "url": "https://supplier.eskom.co.za/tenders",
      "schedule": "0 * * * *"
    }
  },
  "filters": {
    "keywords": [
      "chemical",
      "water treatment",
      "industrial cleaning"
    ],
    "excluded_keywords": [
      "construction",
      "vehicles"
    ],
    "min_value": 50000
  },
  "scoring": {
    "keyword_weight": 0.4,
    "value_weight": 0.3,
    "deadline_weight": 0.2,
    "location_weight": 0.1
  }
}
```

### 3. Notification Configuration

Set up notifications in `config/notifications.json`:

```json
{
  "email": {
    "enabled": true,
    "templates": {
      "new_tender": "templates/new_tender.html",
      "tender_update": "templates/tender_update.html",
      "lead_created": "templates/lead_created.html"
    },
    "schedule": {
      "digest": "0 9 * * *",
      "urgent": "immediate"
    }
  },
  "push": {
    "enabled": true,
    "vapid_public_key": "your_vapid_public_key",
    "vapid_private_key": "your_vapid_private_key"
  },
  "slack": {
    "enabled": false,
    "webhook_url": "your_slack_webhook",
    "channel": "#tenders"
  }
}
```

### 4. AI Configuration

Configure AI settings in `config/ai.json`:

```json
{
  "deepseek": {
    "model": "gpt-4",
    "temperature": 0.7,
    "max_tokens": 2000,
    "features": {
      "tender_analysis": true,
      "lead_scoring": true,
      "content_generation": false
    }
  },
  "rules": {
    "tender_relevance": {
      "min_score": 0.6,
      "max_processing_time": 5000
    },
    "lead_scoring": {
      "threshold": 0.7,
      "factors": {
        "company_size": 0.3,
        "industry_match": 0.4,
        "location": 0.3
      }
    }
  }
}
```

### 5. Security Configuration

Set security options in `config/security.json`:

```json
{
  "jwt": {
    "expiration": "24h",
    "refresh_expiration": "7d",
    "algorithm": "HS256"
  },
  "rate_limiting": {
    "window": 60000,
    "max_requests": 100
  },
  "cors": {
    "origins": ["https://app.mexel.com"],
    "methods": ["GET", "POST", "PUT", "DELETE"],
    "allowed_headers": ["Content-Type", "Authorization"]
  },
  "encryption": {
    "algorithm": "aes-256-gcm",
    "key_rotation": "30d"
  }
}
```

## Integration Configuration

### 1. CRM Integration

Configure CRM settings in `config/integrations/crm.json`:

```json
{
  "provider": "hubspot",
  "api_key": "your_hubspot_api_key",
  "pipeline_id": "default",
  "deal_stages": {
    "new_lead": "appointment_scheduled",
    "qualified": "qualified_to_buy",
    "proposal": "presentation_scheduled",
    "won": "closed_won"
  },
  "sync_schedule": "*/15 * * * *"
}
```

### 2. Analytics Integration

Set up analytics in `config/integrations/analytics.json`:

```json
{
  "google_analytics": {
    "enabled": true,
    "tracking_id": "UA-XXXXXXXX-X"
  },
  "custom_metrics": {
    "tender_success_rate": true,
    "lead_conversion_rate": true,
    "response_time": true
  },
  "reporting": {
    "schedule": "0 0 * * *",
    "recipients": ["<EMAIL>"]
  }
}
```

## Configuration Management

### Version Control
- Store configuration templates in version control
- Use environment-specific overrides
- Document all configuration changes

### Security Practices
- Never commit sensitive values
- Use environment variables for secrets
- Rotate keys regularly
- Encrypt sensitive configuration values

### Validation
Run configuration validation:
```bash
yarn config:validate
```

### Backup
Backup configuration:
```bash
yarn config:backup
```

## Troubleshooting

### Common Issues

1. Database Connection
```bash
yarn db:check
```

2. Email Configuration
```bash
yarn email:test
```

3. API Integration
```bash
yarn api:test
```

### Configuration Reload

Reload configuration without restart:
```bash
yarn config:reload
```

### Health Check

Verify configuration health:
```bash
yarn health:check
```

## Monitoring

### Configuration Changes
- Log all configuration changes
- Alert on critical changes
- Track configuration versions

### Performance Impact
- Monitor system performance
- Track configuration-related issues
- Alert on configuration problems

## Support

- Documentation: https://docs.mexel.com/configuration
- Support Email: <EMAIL>
- Status Page: https://status.mexel.com

For detailed setup instructions, see the [Installation Guide](./installation.md).