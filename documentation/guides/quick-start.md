# Quick Start Guide for Mexel

## Prerequisites
- Node.js 16.20.0 or higher
- Yarn package manager
- Git
- SQLite (included in project)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/mexel/mexel.git
cd mexel
```

2. Create environment file:
```bash
cp .env.example .env
```

3. Configure your environment variables in `.env`:
```ini
# Core Settings
NODE_ENV=development
PORT=3001
API_KEY=your_api_key_here

# Database
DB_PATH=./data/mexel.db

# AI Integration
DEEPSEEK_API_KEY=your_deepseek_api_key

# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_email
SMTP_PASS=your_password
```

4. Install dependencies:
```bash
yarn install
cd frontend && yarn install && cd ..
```

5. Initialize the database:
```bash
yarn db:init
```

## Starting the Application

1. Start the backend server:
```bash
yarn start
```

2. In a new terminal, start the frontend:
```bash
cd frontend
yarn start
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001

## Initial Configuration

1. Access the dashboard at http://localhost:3000
2. Log in with default credentials:
   - Username: admin
   - Password: mexel123

3. Change the default password immediately

4. Configure your tender monitoring settings:
   - Set up keyword filters
   - Configure notification preferences
   - Set up email templates

## Running Tests

```bash
# Run backend tests
yarn test

# Run frontend tests
cd frontend && yarn test
```

## Basic Usage

1. Monitor Tenders
   - View active tenders on the dashboard
   - Filter by relevance and deadline
   - Set up email notifications

2. Lead Management
   - Track potential leads
   - Set up automated responses
   - Monitor conversion metrics

3. Analytics
   - View performance metrics
   - Generate reports
   - Track ROI

## Docker Deployment

For containerized deployment:
```bash
docker-compose up -d
```

## Common Issues

1. Database Initialization
   ```bash
   yarn db:reset # Clear and reinitialize database
   ```

2. Port Conflicts
   - Change ports in `.env` file
   - Default ports: 3000 (frontend), 3001 (backend)

3. Dependency Issues
   ```bash
   yarn clean # Remove node_modules
   yarn install # Reinstall dependencies
   ```

## Next Steps

1. Read the [Full Documentation](../README.md)
2. Set up [Monitoring](../maintenance/monitoring.md)
3. Configure [Email Templates](../guides/email-templates.md)
4. Review [Security Best Practices](../guides/security.md)

## Support

- GitHub Issues: [Create an Issue](https://github.com/mexel/issues)
- Email Support: <EMAIL>
- Documentation: [Full Documentation](https://docs.mexel.com)

## Updating

To update to the latest version:
```bash
git pull
yarn install
yarn migrate
```

Remember to check the [Changelog](../CHANGELOG.md) for breaking changes.