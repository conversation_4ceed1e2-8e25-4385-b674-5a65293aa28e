version: '3.9'

services:
  db:
    image: postgres:15
    container_name: mexel_db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: mexel
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d mexel"]
      interval: 5s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend-python
      dockerfile: Dockerfile
    container_name: mexel_backend
    ports:
      - "3001:8000"
    environment:
      - DATABASE_URL=**************************************/mexel
      - PYTHONUNBUFFERED=1
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./backend-python:/app:ro
      - ./backend-python/static:/app/static

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: mexel_frontend
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:3001
      - REACT_APP_WS_URL=ws://localhost:3001/ws
    depends_on:
      - backend

volumes:
  postgres_data:
  node_modules:
