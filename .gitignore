# Dependencies
node_modules/
.pnp/
.pnp.js

# Production build output
dist/
build/
*.tsbuildinfo

# Testing
coverage/

# Environment variables
.env
.env.local
.env.*.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python specific
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.env/
.venv/
pip-log.txt
.pytest_cache/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# TypeScript incremental compilation cache
*.tsbuildinfo

# Frontend build artifacts
frontend/build/
frontend/dist/
frontend/.next/

frontend/node_modules/
frontend/.env
# Project specific
server.log
data/*.db
data/*.db-journal
data/backups/
# npm (disallowed)
package-lock.json
npm-debug.log*
.npm

# Yarn specific
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*
