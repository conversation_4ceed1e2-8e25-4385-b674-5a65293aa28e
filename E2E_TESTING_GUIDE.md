# E2E Testing Guide for Mexel

## Overview

Mexel uses Playwright for Node.js as its end-to-end testing framework. All E2E tests are written in TypeScript and are located in the `frontend/src/tests/e2e` directory.

## Setup

1. Install dependencies:

```bash
cd frontend
yarn install
```

2. Install Playwright browsers:

```bash
yarn test:e2e:install
```

## Running Tests

To run all E2E tests:

```bash
yarn test:e2e
```

To run tests in debug mode:

```bash
yarn test:e2e --debug
```

## Writing Tests

Tests are written using the Playwright test framework. Example:

```typescript
import { test, expect } from '@playwright/test';

test('basic test', async ({ page }) => {
  await page.goto('http://localhost:3000');
  // Add your test assertions here
});
```

## Configuration

The Playwright configuration is located in `frontend/playwright.config.ts`. Key settings:

- Tests are located in `./src/tests`
- Uses Chromium browser
- Automatically starts the development server
- Takes screenshots on test failure
- Generates HTML reports

## CI/CD Integration

E2E tests are run as part of the CI pipeline. See `.github/workflows` for the complete CI configuration.

## Best Practices

1. Keep tests focused and independent
2. Use meaningful test descriptions
3. Clean up test data after each test
4. Use page objects when appropriate
5. Add proper error handling and assertions

## Debugging

For debugging tests:

1. Use `--debug` flag to run tests in debug mode
2. Check screenshots in `test-results` directory for failed tests
3. Review HTML report for detailed test results
