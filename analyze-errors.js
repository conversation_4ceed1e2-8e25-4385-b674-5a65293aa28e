// analyze-errors.js
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Analyzes TypeScript errors and provides a summary of the most common issues
 */
function main() {
  console.log('Analyzing TypeScript and ESLint errors...');

  // Temporary file to store TypeScript error output
  const tempErrorFile = path.join(__dirname, 'ts-errors.log');

  try {
    // Run TypeScript compiler and capture errors
    try {
      execSync(`tsc --noEmit > ${tempErrorFile} 2>&1`, { stdio: 'pipe' });
    } catch (error) {
      // Expected to fail if there are errors
    }

    if (fs.existsSync(tempErrorFile)) {
      const errors = fs.readFileSync(tempErrorFile, 'utf8');
      const errorLines = errors.split('\n').filter(line => line.includes('error TS'));

      // Count occurrences of each error code
      const errorCounts = {};
      const fileErrorCounts = {};
      const errorExamples = {};

      errorLines.forEach(line => {
        // Extract error code
        const errorCodeMatch = line.match(/error TS(\d+)/);
        if (errorCodeMatch) {
          const errorCode = errorCodeMatch[1];
          errorCounts[errorCode] = (errorCounts[errorCode] || 0) + 1;

          // Save example of this error if we don't have one yet
          if (!errorExamples[errorCode]) {
            errorExamples[errorCode] = line.trim();
          }

          // Track which files have the most errors
          const fileMatch = line.match(/^([^(]+)\(/);
          if (fileMatch) {
            const file = fileMatch[1].trim();
            fileErrorCounts[file] = (fileErrorCounts[file] || 0) + 1;
          }
        }
      });

      // Convert counts to sortable arrays
      const sortedErrors = Object.entries(errorCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 15);

      const sortedFiles = Object.entries(fileErrorCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 15);

      // Print summary
      console.log('\n==== TypeScript Error Analysis ====');
      console.log(`\nTotal errors: ${errorLines.length}`);

      console.log('\nMost common error codes:');
      sortedErrors.forEach(([code, count], index) => {
        console.log(`${index + 1}. TS${code}: ${count} occurrences`);
        console.log(`   Example: ${errorExamples[code]}`);

        // Provide tips for common error codes
        console.log(`   Solution: ${getErrorSolution(code)}`);
      });

      console.log('\nFiles with most errors:');
      sortedFiles.forEach(([file, count], index) => {
        console.log(`${index + 1}. ${file}: ${count} errors`);
      });

      console.log('\n==== Error Resolution Strategy ====');
      suggestResolutionStrategy(sortedErrors);

    } else {
      console.log('No TypeScript errors found.');
    }

    // Clean up
    if (fs.existsSync(tempErrorFile)) {
      fs.unlinkSync(tempErrorFile);
    }

  } catch (error) {
    console.error('Error analyzing TypeScript errors:', error);
  }
}

/**
 * Returns common solutions for specific error codes
 */
function getErrorSolution(code) {
  const solutions = {
    '2304': 'Add missing type definitions or import the required module. For "process", add "/// <reference types="node" />" at the top of the file.',
    '2307': 'Make sure the module is installed and properly referenced in tsconfig.json. Check import path case sensitivity.',
    '2339': 'Type doesn\'t have the property you\'re trying to access. Add interface extension or type assertion.',
    '2532': 'Add a null check or use the non-null assertion operator (!) if you\'re sure the value isn\'t null.',
    '2345': 'Type mismatch. Check argument types and function signatures.',
    '2322': 'Type mismatch in assignment. Ensure the types are compatible.',
    '2769': 'Missing properties in object literal. Add the required properties.',
    '1005': 'Check for syntax errors like missing commas, semicolons, or parentheses.',
    '1109': 'Expression expected. Check for syntax errors around the indicated position.',
    '2531': 'Object is possibly null. Add null checks or use the non-null assertion operator.',
    '2571': 'Object is possibly undefined. Add check for undefined or use the non-null assertion operator.',
    '2741': 'Property is missing in type but required in index signature. Add the missing property.',
    '7006': 'Add explicit parameter type instead of using implicit any.',
    '7031': 'Add void return type to function not returning a value.',
    '2554': 'Expected more/different arguments. Check function call signature.',
    '2564': 'Property not initialized in constructor. Add initial value or declare it as optional.',
    '2538': 'Type cannot be used as an index type. Use string, number, or symbol.',
  };

  return solutions[code] || 'No specific solution available for this error code.';
}

/**
 * Suggests a strategy for solving errors based on their frequency
 */
function suggestResolutionStrategy(errors) {
  // Most common error codes
  const mostCommonCodes = errors.map(([code]) => code);

  if (mostCommonCodes.includes('2304')) {
    console.log('1. Focus on missing type definitions (TS2304)');
    console.log('   - Add the missing type declarations to your project');
    console.log('   - Make sure "typeRoots" is properly set in tsconfig.json');
    console.log('   - The custom types directory should be properly linked: ./types');
    console.log('   - Add /// <reference types="node" /> to files using Node.js APIs');
  }

  if (mostCommonCodes.includes('2307')) {
    console.log('\n2. Fix module resolution issues (TS2307)');
    console.log('   - Check that your import statements match the actual file paths');
    console.log('   - Verify "paths" in tsconfig.json for path aliases');
    console.log('   - Make sure all dependencies are properly installed');
  }

  console.log('\n3. General approach:');
  console.log('   - Start with fixing the files that have the most errors');
  console.log('   - Address one type of error at a time, starting with the most common');
  console.log('   - Use // @ts-ignore for complex cases that need more time (temporary)');
  console.log('   - Consider adding more specific type declarations in types/');

  if (mostCommonCodes.includes('2339') || mostCommonCodes.includes('2532') || mostCommonCodes.includes('2345')) {
    console.log('\n4. Type checking issues:');
    console.log('   - Review component props and interfaces');
    console.log('   - Add appropriate type guards where needed');
    console.log('   - Consider using more precise types instead of "any"');
  }

  console.log('\nRemember:');
  console.log('1. The frontend/.env file has TSC_COMPILE_ON_ERROR=true to allow the app to run despite errors');
  console.log('2. The VS Code settings have been updated to optimize TypeScript and ESLint integration');
  console.log('3. You may need to restart VS Code to apply all settings changes');
}

// Run the script
main();
