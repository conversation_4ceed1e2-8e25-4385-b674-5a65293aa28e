#!/usr/bin/env node

/**
 * Fix MUI Chip Icon Type Issues
 *
 * This script specifically targets the Material-UI Chip component's icon prop type issues.
 * The problem is that many components are passing Element | null to the icon prop, but
 * it expects React.ReactElement<any>. This script fixes this by properly wrapping
 * icon generation functions in React.createElement.
 */

const fs = require("fs");
const path = require("path");
const glob = require("glob");

console.log("Starting MUI Chip icon type fixes...");

// Find all component files that use MUI Chip components
const componentFiles = glob.sync("src/components/**/*.{tsx,ts}", {
  cwd: process.cwd(),
});
console.log(`Found ${componentFiles.length} component files to scan...`);

let filesFixed = 0;
let iconsFixed = 0;

componentFiles.forEach((file) => {
  try {
    const filePath = path.resolve(process.cwd(), file);
    const content = fs.readFileSync(filePath, "utf8");

    // Check if the file uses MUI Chip components and has potential icon issues
    if (
      content.includes("Chip") &&
      (content.includes("icon={get") ||
        content.includes("icon={render") ||
        (content.includes("icon={") && content.includes("Icon()")))
    ) {
      let modified = content;
      let hasChanged = false;

      // Pattern to find Chip icon props with function calls
      const iconPattern =
        /icon=\{(get[A-Za-z]+Icon\(\)|render[A-Za-z]+Icon\([^)]*\))\}/g;

      if (iconPattern.test(content)) {
        // Replace with React.createElement to properly type the icon
        modified = content.replace(iconPattern, (match, functionCall) => {
          iconsFixed++;
          return `icon={React.createElement(React.Fragment, null, ${functionCall})}`;
        });

        hasChanged = true;
      }

      // Save changes if modified
      if (hasChanged) {
        fs.writeFileSync(filePath, modified, "utf8");
        filesFixed++;
        console.log(`Fixed MUI Chip icon issues in ${file}`);
      }
    }
  } catch (error) {
    console.error(`Error processing ${file}:`, error);
  }
});

console.log("\n=== MUI Chip Icon Fix Results ===");
console.log(`Files fixed: ${filesFixed}`);
console.log(`Icons fixed: ${iconsFixed}`);
console.log("==================================");
