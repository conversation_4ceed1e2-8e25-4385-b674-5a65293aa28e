const fs = require('fs');
const path = require('path');

// Function to fix Recharts component issues
function fixRechartsIssues(filePath) {
  console.log(`Fixing Recharts issues in ${filePath}`);

  try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');

    // Check if the file imports Recharts components
    const hasRechartsImport = content.includes('from \'recharts\'');

    if (hasRechartsImport && !content.includes('// Recharts wrapper components')) {
      // Extract the Recharts components being imported
      const importMatch = content.match(/import\s+{\s*([^}]*)\s*}\s+from\s+['"]recharts['"];?/);

      if (importMatch && importMatch[1]) {
        const components = importMatch[1].split(',').map(c => c.trim()).filter(c => c);

        // Create wrapper components for each Recharts component
        let wrapperComponents = '\n// Recharts wrapper components\n';

        for (const component of components) {
          // <PERSON><PERSON> renamed imports like "Tooltip as RechartsTooltip"
          let originalName = component;
          let componentName = component;

          if (component.includes(' as ')) {
            const parts = component.split(' as ').map(s => s.trim());
            originalName = parts[0];
            componentName = parts[1];
          }

          wrapperComponents += `const ${componentName}Wrapper = (props) => <${componentName} {...props} />;\n`;
        }

        // Add the wrapper components after the import
        content = content.replace(importMatch[0], importMatch[0] + wrapperComponents);

        // Replace Recharts component usage with wrapper components
        for (const component of components) {
          // Handle renamed imports like "Tooltip as RechartsTooltip"
          let originalName = component;
          let componentName = component;

          if (component.includes(' as ')) {
            const parts = component.split(' as ').map(s => s.trim());
            originalName = parts[0];
            componentName = parts[1];
          }

          // Replace opening tags
          content = content.replace(new RegExp(`<${componentName}\\s`, 'g'), `<${componentName}Wrapper `);

          // Replace self-closing tags
          content = content.replace(new RegExp(`<${componentName}\\s([^>]*)\/>`, 'g'), `<${componentName}Wrapper $1/>`);

          // Replace closing tags
          content = content.replace(new RegExp(`</${componentName}>`, 'g'), `</${componentName}Wrapper>`);
        }

        // Also fix specific components that might be missed
        const commonComponents = ['Bar', 'XAxis', 'YAxis', 'Line', 'Pie', 'RechartsTooltip', 'Legend', 'CartesianGrid', 'Tooltip', 'ResponsiveContainer', 'PolarAngleAxis', 'PolarRadiusAxis', 'Radar'];

        for (const component of commonComponents) {
          if (content.includes(`<${component} `) && !content.includes(`<${component}Wrapper `)) {
            // Add wrapper component if not already added
            if (!content.includes(`const ${component}Wrapper = `)) {
              wrapperComponents += `const ${component}Wrapper = (props) => <${component} {...props} />;\n`;
              content = content.replace(importMatch[0] + '\n// Recharts wrapper components\n', importMatch[0] + '\n// Recharts wrapper components\n' + `const ${component}Wrapper = (props) => <${component} {...props} />;\n`);
            }

            // Replace opening tags
            content = content.replace(new RegExp(`<${component}\\s`, 'g'), `<${component}Wrapper `);

            // Replace self-closing tags
            content = content.replace(new RegExp(`<${component}\\s([^>]*)\/>`, 'g'), `<${component}Wrapper $1/>`);

            // Replace closing tags
            content = content.replace(new RegExp(`</${component}>`, 'g'), `</${component}Wrapper>`);
          }
        }
      }
    }

    // Write the file
    fs.writeFileSync(filePath, content, 'utf8');

    console.log(`Fixed Recharts issues in ${filePath}`);
  } catch (error) {
    console.error(`Error fixing Recharts issues in ${filePath}:`, error);
  }
}

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);

  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);

    if (stats.isDirectory()) {
      processDirectory(filePath);
    } else if (stats.isFile() && (filePath.endsWith('.tsx') || filePath.endsWith('.ts'))) {
      // Check if the file has Recharts imports
      const content = fs.readFileSync(filePath, 'utf8');

      if (content.includes('from \'recharts\'')) {
        fixRechartsIssues(filePath);
      }
    }
  }
}

// Start processing from the src directory
processDirectory('./src');

console.log('Done fixing Recharts issues!');
