<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Mexel Simple Frontend</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f5f5f5;
      }
      .container {
        max-width: 800px;
        margin: 40px auto;
        padding: 20px;
      }
      .card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
      }
      .header {
        margin-bottom: 24px;
      }
      h1 {
        color: #333;
        margin-bottom: 8px;
      }
      .status {
        color: #4caf50;
        font-weight: bold;
      }
      .section {
        margin-top: 24px;
        padding-top: 16px;
        border-top: 1px solid #eee;
      }
      .info {
        background-color: #e3f2fd;
        padding: 16px;
        border-radius: 6px;
        margin-bottom: 20px;
      }
      .error {
        background-color: #ffebee;
        color: #c62828;
        padding: 16px;
        border-radius: 6px;
        margin-bottom: 20px;
      }
      .success {
        background-color: #e8f5e9;
        padding: 16px;
        border-radius: 6px;
        margin-bottom: 20px;
      }
      button {
        background-color: #1976d2;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        margin-right: 8px;
      }
      button:hover {
        background-color: #1565c0;
      }
      pre {
        background-color: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 12px;
        overflow: auto;
        max-height: 300px;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <div class="container">
      <div class="card">
        <div class="header">
          <h1>Mexel Simple Frontend</h1>
          <p class="status">Frontend is running!</p>
        </div>

        <div class="info">
          <h3>Backend Connection</h3>
          <p>This is a minimal frontend implementation that connects to the backend on port 8000</p>
        </div>

        <div class="section">
          <h2>Backend Health Status</h2>
          <button id="checkHealthBtn">Check Health Status</button>
          <div id="healthResult"></div>
        </div>

        <div class="section">
          <h2>API Example Data</h2>
          <button id="fetchDataBtn">Fetch Example Data</button>
          <div id="dataResult"></div>
        </div>

        <div class="section">
          <h3>Instructions</h3>
          <p>
            Make sure the backend server is running (typically on port 8000 inside Docker, mapped
            from host):
          </p>
          <pre>
cd /Users/<USER>/Desktop/Mexel/backend-python
# Ensure your Docker setup exposes the correct port (e.g., 8000)
# The command below is for running locally, not directly applicable when using Docker for the backend.
# python3 run.py --port 8000</pre
          >
        </div>
      </div>
    </div>

    <script>
      // DOM Elements
      const checkHealthBtn = document.getElementById('checkHealthBtn');
      const healthResult = document.getElementById('healthResult');
      const fetchDataBtn = document.getElementById('fetchDataBtn');
      const dataResult = document.getElementById('dataResult');

      // Check health endpoint
      checkHealthBtn.addEventListener('click', async function () {
        healthResult.innerHTML = '<p>Checking backend status...</p>';
        try {
          const response = await fetch('http://localhost:8000/health');
          if (response.ok) {
            const data = await response.json();
            healthResult.innerHTML = `
              <div class="success">
                <h4>Backend Connected!</h4>
                <pre>${JSON.stringify(data, null, 2)}</pre>
              </div>
            `;
          } else {
            healthResult.innerHTML = `
              <div class="error">
                <h4>Backend Error</h4>
                <p>Status: ${response.status}</p>
              </div>
            `;
          }
        } catch (error) {
          healthResult.innerHTML = `
            <div class="error">
              <h4>Connection Failed</h4>
              <p>Error: Cannot connect to backend on port 8000.</p>
              <p>Details: ${error.message}</p>
            </div>
          `;
        }
      });

      // Fetch example data
      fetchDataBtn.addEventListener('click', async function () {
        dataResult.innerHTML = '<p>Fetching data from backend...</p>';
        try {
          const response = await fetch('http://localhost:8000/api/example');
          if (response.ok) {
            const data = await response.json();
            dataResult.innerHTML = `
              <div class="success">
                <h4>Data Retrieved Successfully</h4>
                <pre>${JSON.stringify(data, null, 2)}</pre>
              </div>
            `;
          } else {
            dataResult.innerHTML = `
              <div class="error">
                <h4>API Error</h4>
                <p>Status: ${response.status}</p>
              </div>
            `;
          }
        } catch (error) {
          dataResult.innerHTML = `
            <div class="error">
              <h4>API Request Failed</h4>
              <p>Error: Cannot connect to backend API.</p>
              <p>Details: ${error.message}</p>
            </div>
          `;
        }
      });

      // Check health on page load
      window.addEventListener('load', function () {
        checkHealthBtn.click();
      });
    </script>
  </body>
</html>
