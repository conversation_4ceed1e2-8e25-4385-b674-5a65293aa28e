<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mexel Content API Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2 {
      color: #2c3e50;
    }
    .content-item {
      border: 1px solid #ddd;
      padding: 15px;
      margin-bottom: 15px;
      border-radius: 5px;
    }
    .content-item h3 {
      margin-top: 0;
      color: #3498db;
    }
    .content-item p {
      margin: 5px 0;
    }
    .content-item .meta {
      font-size: 0.9em;
      color: #7f8c8d;
    }
    .content-item .keywords {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin: 10px 0;
    }
    .content-item .keyword {
      background-color: #f0f0f0;
      padding: 3px 8px;
      border-radius: 3px;
      font-size: 0.8em;
    }
    .content-item .status {
      display: inline-block;
      padding: 3px 8px;
      border-radius: 3px;
      font-size: 0.8em;
      font-weight: bold;
    }
    .status-draft {
      background-color: #f0f0f0;
      color: #333;
    }
    .status-review {
      background-color: #3498db;
      color: white;
    }
    .status-approved {
      background-color: #2ecc71;
      color: white;
    }
    .status-published {
      background-color: #9b59b6;
      color: white;
    }
    .status-rejected {
      background-color: #e74c3c;
      color: white;
    }
    .tabs {
      display: flex;
      margin-bottom: 20px;
      border-bottom: 1px solid #ddd;
    }
    .tab {
      padding: 10px 15px;
      cursor: pointer;
      border: 1px solid transparent;
      border-bottom: none;
      margin-right: 5px;
      border-radius: 5px 5px 0 0;
    }
    .tab.active {
      border-color: #ddd;
      background-color: #f9f9f9;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
    button {
      background-color: #3498db;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 5px;
      cursor: pointer;
      margin-bottom: 20px;
    }
    button:hover {
      background-color: #2980b9;
    }
    #loading {
      display: none;
      color: #7f8c8d;
    }
    .preview-content {
      white-space: pre-wrap;
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: 5px;
      border: 1px solid #ddd;
      margin-top: 15px;
      max-height: 300px;
      overflow-y: auto;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input, select, textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    textarea {
      min-height: 100px;
    }
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
    }
    .modal-content {
      background-color: white;
      margin: 50px auto;
      padding: 20px;
      width: 80%;
      max-width: 800px;
      border-radius: 5px;
      max-height: 80vh;
      overflow-y: auto;
    }
    .close {
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <h1>Mexel Content API Test</h1>

  <div class="tabs">
    <div class="tab active" data-tab="content">Content Library</div>
    <div class="tab" data-tab="generate">Generate Content</div>
    <div class="tab" data-tab="schedule">Content Schedule</div>
  </div>

  <div id="content-tab" class="tab-content active">
    <button id="fetchContent">Fetch Content</button>
    <span id="loading-content">Loading...</span>
    <div id="content-list"></div>
  </div>

  <div id="generate-tab" class="tab-content">
    <h2>Generate New Content</h2>
    <form id="generate-form">
      <div class="form-group">
        <label for="content-type">Content Type</label>
        <select id="content-type" required>
          <option value="blog_post">Blog Post</option>
          <option value="linkedin_post">LinkedIn Post</option>
          <option value="infographic">Infographic</option>
          <option value="email_newsletter">Email Newsletter</option>
          <option value="case_study">Case Study</option>
          <option value="technical_article">Technical Article</option>
          <option value="tender_response">Tender Response</option>
        </select>
      </div>

      <div class="form-group">
        <label for="topic">Topic</label>
        <input type="text" id="topic" placeholder="e.g., Benefits of Mexel 432 Film-Forming Amine Technology in Power Plant Cooling Towers">
      </div>

      <div class="form-group">
        <label for="keywords">Keywords (comma separated)</label>
        <input type="text" id="keywords" placeholder="e.g., FFA, Mexel 432, cooling tower, corrosion prevention, energy efficiency, power plant, surfactant technology">
      </div>

      <div class="form-group">
        <label for="target-audience">Target Audience</label>
        <input type="text" id="target-audience" placeholder="e.g., Power plant engineers, cooling tower maintenance managers, mining facility operators, agricultural irrigation specialists">
      </div>

      <div class="form-group">
        <label for="tone">Tone</label>
        <input type="text" id="tone" placeholder="e.g., Professional, educational">
      </div>

      <div class="form-group">
        <label for="length">Length</label>
        <select id="length">
          <option value="short">Short</option>
          <option value="medium" selected>Medium</option>
          <option value="long">Long</option>
        </select>
      </div>

      <div class="form-group">
        <label for="additional-instructions">Additional Instructions</label>
        <textarea id="additional-instructions" placeholder="Any specific instructions for the AI"></textarea>
      </div>

      <div class="form-group">
        <label>
          <input type="checkbox" id="include-image-prompt" checked>
          Include image prompt for Canva
        </label>
      </div>

      <button type="submit">Generate Content</button>
    </form>
  </div>

  <div id="schedule-tab" class="tab-content">
    <h2>Content Schedule</h2>
    <button id="fetchSchedules">Fetch Schedules</button>
    <span id="loading-schedules">Loading...</span>
    <div id="schedule-list"></div>
  </div>

  <div id="preview-modal" class="modal">
    <div class="modal-content">
      <span class="close">&times;</span>
      <h2 id="preview-title"></h2>
      <p id="preview-meta"></p>
      <div id="preview-content" class="preview-content"></div>
      <div id="preview-image-prompt" style="display: none;">
        <h3>Image Prompt</h3>
        <div class="preview-content" id="image-prompt-content"></div>
      </div>
    </div>
  </div>

  <!-- Include the tabs enhancer script -->
  <script src="../../tabs-enhancer.js"></script>

  <script>
    // Tab switching
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', () => {
        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

        tab.classList.add('active');
        document.getElementById(`${tab.dataset.tab}-tab`).classList.add('active');
      });
    });

    // Fetch content
    document.getElementById('fetchContent').addEventListener('click', async () => {
      const loadingEl = document.getElementById('loading-content');
      const contentListEl = document.getElementById('content-list');

      loadingEl.style.display = 'inline';
      contentListEl.innerHTML = '';

      try {
        const response = await fetch('http://localhost:3001/api/content');
        const content = await response.json();

        loadingEl.style.display = 'none';

        if (content.length === 0) {
          contentListEl.innerHTML = '<p>No content found</p>';
          return;
        }

        content.forEach(item => {
          const contentEl = document.createElement('div');
          contentEl.className = 'content-item';

          const createdDate = new Date(item.createdAt).toLocaleDateString();
          const updatedDate = new Date(item.updatedAt).toLocaleDateString();

          let statusClass = '';
          switch (item.status) {
            case 'draft': statusClass = 'status-draft'; break;
            case 'review': statusClass = 'status-review'; break;
            case 'approved': statusClass = 'status-approved'; break;
            case 'published': statusClass = 'status-published'; break;
            case 'rejected': statusClass = 'status-rejected'; break;
          }

          let contentType = '';
          switch (item.type) {
            case 'blog_post': contentType = 'Blog Post'; break;
            case 'linkedin_post': contentType = 'LinkedIn Post'; break;
            case 'infographic': contentType = 'Infographic'; break;
            case 'email_newsletter': contentType = 'Email Newsletter'; break;
            case 'case_study': contentType = 'Case Study'; break;
          }

          contentEl.innerHTML = `
            <h3>${item.title}</h3>
            <p class="meta">Type: ${contentType}</p>
            <p class="meta">Created: ${createdDate} | Updated: ${updatedDate}</p>
            <div class="keywords">
              ${item.keywords.map(keyword => `<span class="keyword">${keyword}</span>`).join('')}
            </div>
            <p><span class="status ${statusClass}">${item.status.toUpperCase()}</span></p>
            ${item.summary ? `<p>${item.summary}</p>` : ''}
            <button class="preview-button" data-id="${item.id}">Preview</button>
          `;

          contentListEl.appendChild(contentEl);
        });

        // Add event listeners to preview buttons
        document.querySelectorAll('.preview-button').forEach(button => {
          button.addEventListener('click', async () => {
            const id = button.dataset.id;
            const item = content.find(c => c.id === id);

            if (item) {
              document.getElementById('preview-title').textContent = item.title;
              document.getElementById('preview-meta').textContent = `Type: ${getContentTypeName(item.type)} | Status: ${item.status.toUpperCase()}`;
              document.getElementById('preview-content').textContent = item.content;

              if (item.imagePrompt) {
                document.getElementById('preview-image-prompt').style.display = 'block';
                document.getElementById('image-prompt-content').textContent = item.imagePrompt;
              } else {
                document.getElementById('preview-image-prompt').style.display = 'none';
              }

              document.getElementById('preview-modal').style.display = 'block';
            }
          });
        });
      } catch (error) {
        loadingEl.style.display = 'none';
        contentListEl.innerHTML = `<p>Error fetching content: ${error.message}</p>`;
        console.error('Error fetching content:', error);
      }
    });

    // Generate content form
    document.getElementById('generate-form').addEventListener('submit', async (e) => {
      e.preventDefault();

      const type = document.getElementById('content-type').value;
      const topic = document.getElementById('topic').value;
      const keywords = document.getElementById('keywords').value.split(',').map(k => k.trim()).filter(k => k);
      const targetAudience = document.getElementById('target-audience').value;
      const tone = document.getElementById('tone').value;
      const length = document.getElementById('length').value;
      const additionalInstructions = document.getElementById('additional-instructions').value;
      const includeImagePrompt = document.getElementById('include-image-prompt').checked;

      const generateTab = document.getElementById('generate-tab');
      generateTab.innerHTML = '<p>Generating content... This may take a minute.</p>';

      try {
        const response = await fetch('http://localhost:3001/api/content/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            type,
            topic,
            keywords,
            targetAudience,
            tone,
            length,
            additionalInstructions,
            includeImagePrompt
          })
        });

        const content = await response.json();

        generateTab.innerHTML = `
          <h2>Content Generated Successfully!</h2>
          <h3>${content.title}</h3>
          <p>Type: ${getContentTypeName(content.type)}</p>
          <p>Status: ${content.status.toUpperCase()}</p>
          <div class="preview-content">${content.content}</div>
          ${content.imagePrompt ? `
            <h3>Image Prompt</h3>
            <div class="preview-content">${content.imagePrompt}</div>
          ` : ''}
          <button id="back-to-form">Generate Another</button>
        `;

        document.getElementById('back-to-form').addEventListener('click', () => {
          location.reload();
        });
      } catch (error) {
        generateTab.innerHTML = `
          <h2>Error Generating Content</h2>
          <p>${error.message}</p>
          <button id="back-to-form">Try Again</button>
        `;

        document.getElementById('back-to-form').addEventListener('click', () => {
          location.reload();
        });

        console.error('Error generating content:', error);
      }
    });

    // Fetch schedules
    document.getElementById('fetchSchedules').addEventListener('click', async () => {
      const loadingEl = document.getElementById('loading-schedules');
      const scheduleListEl = document.getElementById('schedule-list');

      loadingEl.style.display = 'inline';
      scheduleListEl.innerHTML = '';

      try {
        const response = await fetch('http://localhost:3001/api/content/schedules');
        const schedules = await response.json();

        loadingEl.style.display = 'none';

        if (schedules.length === 0) {
          scheduleListEl.innerHTML = '<p>No scheduled content found</p>';
          return;
        }

        schedules.forEach(item => {
          const scheduleEl = document.createElement('div');
          scheduleEl.className = 'content-item';

          const scheduledDate = new Date(item.scheduledDate).toLocaleDateString();
          const createdDate = new Date(item.createdAt).toLocaleDateString();

          let statusClass = '';
          switch (item.status) {
            case 'scheduled': statusClass = 'status-draft'; break;
            case 'generated': statusClass = 'status-review'; break;
            case 'published': statusClass = 'status-published'; break;
            case 'failed': statusClass = 'status-rejected'; break;
          }

          scheduleEl.innerHTML = `
            <h3>${getContentTypeName(item.type)}</h3>
            ${item.topic ? `<p>${item.topic}</p>` : ''}
            <p class="meta">Scheduled: ${scheduledDate} | Created: ${createdDate}</p>
            <div class="keywords">
              ${item.keywords ? item.keywords.map(keyword => `<span class="keyword">${keyword}</span>`).join('') : ''}
            </div>
            <p><span class="status ${statusClass}">${item.status.toUpperCase()}</span></p>
            ${item.error ? `<p style="color: red;">Error: ${item.error}</p>` : ''}
          `;

          scheduleListEl.appendChild(scheduleEl);
        });
      } catch (error) {
        loadingEl.style.display = 'none';
        scheduleListEl.innerHTML = `<p>Error fetching schedules: ${error.message}</p>`;
        console.error('Error fetching schedules:', error);
      }
    });

    // Close modal
    document.querySelector('.close').addEventListener('click', () => {
      document.getElementById('preview-modal').style.display = 'none';
    });

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
      if (e.target === document.getElementById('preview-modal')) {
        document.getElementById('preview-modal').style.display = 'none';
      }
    });

    // Helper function to get content type name
    function getContentTypeName(type) {
      switch (type) {
        case 'blog_post': return 'Blog Post';
        case 'linkedin_post': return 'LinkedIn Post';
        case 'infographic': return 'Infographic';
        case 'email_newsletter': return 'Email Newsletter';
        case 'case_study': return 'Case Study';
        default: return type;
      }
    }
  </script>
</body>
</html>
