#!/bin/bash

# <PERSON><PERSON>t to fix TypeScript language service issues

echo "Starting TypeScript language service repair..."

# Navigate to project directory
cd /Users/<USER>/Desktop/Mexel/frontend

# Stop any running TypeScript services
echo "Stopping TypeScript services..."
pkill -f tsserver || true

# Clear TypeScript cache files
echo "Cleaning TypeScript caches..."
find . -name ".tscache" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".rts2_cache*" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "tsconfig.tsbuildinfo" -type f -delete 2>/dev/null || true

# Reinstall TypeScript
echo "Reinstalling TypeScript..."
yarn install --save-dev typescript@4.9.5

# Run TypeScript compiler once to rebuild project references
echo "Rebuilding TypeScript project..."
yarn tsc --noEmit

echo "TypeScript language service repair completed."
echo "Please restart VS Code with the following extensions disabled:"
echo "- GitHub.copilot-chat"
echo "- VisualStudioExptTeam.vscodeintellicode"
echo "- VisualStudioExptTeam.intellicode-api-usage-examples"
echo ""
echo "You can disable these extensions by:"
echo "1. Opening VS Code Command Palette (Cmd+Shift+P or Ctrl+Shift+P)"
echo "2. Type 'Extensions: Show Installed Extensions'"
echo "3. Find each extension and click the gear icon to disable it"
echo "4. Restart VS Code"
