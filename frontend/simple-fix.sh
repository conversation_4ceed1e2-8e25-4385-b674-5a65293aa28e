#!/bin/bash

# Simple script to fix frontend with minimal dependencies

set -e  # Exit on error

FRONTEND_DIR="/Users/<USER>/Desktop/Mexel/frontend"

echo "🔧 Starting simplified frontend fix..."

# Make sure we're in the frontend directory
cd "$FRONTEND_DIR"

# Backup existing package.json
if [ -f "package.json" ]; then
  echo "Backing up existing package.json..."
  cp package.json package.json.bak
fi

# Set up simplified package.json with minimal dependencies
cat > package.json << 'EOL'
{
  "name": "@mexel/frontend",
  "version": "1.0.0",
  "private": true,
  "dependencies": {
    "@emotion/react": "^11.11.1",
    "@emotion/styled": "^11.11.0",
    "@mui/icons-material": "^5.15.15",
    "@mui/material": "^5.15.15",
    "@mui/system": "^5.15.15",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "5.0.1"
  },
  "devDependencies": {
    "@types/node": "^18.19.14",
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "typescript": "^4.9.5"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  }
}
EOL

# Update yarn configuration to use node-modules nodeLinker
cat > .yarnrc.yml << 'EOL'
nodeLinker: node-modules
enableGlobalCache: true
npmRegistryServer: "https://registry.yarnpkg.com"
npmAlwaysAuth: false
EOL

# Clean up node_modules directory for a fresh install
echo "🧹 Cleaning node_modules directory..."
rm -rf node_modules
rm -rf .yarn/cache

# Install dependencies
echo "📦 Installing dependencies..."
yarn

echo "✅ Frontend fix completed! You can now run 'yarn start' to start the application."
