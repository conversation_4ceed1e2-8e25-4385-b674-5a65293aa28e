const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Function to fix Recharts component issues by using direct components
function fixRechartsDirectUsage(filePath) {
  console.log(`Fixing Recharts direct usage in ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Check if the file has commented out wrapper components
  if (content.includes('// Recharts wrapper components - commented out')) {
    // Find all instances of XAxisWrapper, YAxisWrapper, etc. and replace with direct components
    const wrapperRegex = /<(\w+)Wrapper([^>]*)>/g;
    content = content.replace(wrapperRegex, (match, component, props) => {
      modified = true;
      return `<${component}${props}>`;
    });

    // Find all closing tags
    const closingWrapperRegex = /<\/(\w+)Wrapper>/g;
    content = content.replace(closingWrapperRegex, (match, component) => {
      modified = true;
      return `</${component}>`;
    });

    // Fix RechartsTooltip to Tooltip if needed
    if (content.includes('<RechartsTooltip')) {
      content = content.replace(/<RechartsTooltip([^>]*)>/g, '<Tooltip$1>');
      content = content.replace(/<\/RechartsTooltip>/g, '</Tooltip>');
      modified = true;
    }
  }
  // Check if the file has active wrapper components
  else if (content.includes('// Recharts wrapper components') && !content.includes('// commented out')) {
    // Comment out the wrapper components
    const wrapperComponentsRegex = /(\/\/ Recharts wrapper components\n)(const \w+Wrapper[^;]*;\n)+/g;
    content = content.replace(wrapperComponentsRegex, (match) => {
      modified = true;
      return '// Recharts wrapper components - commented out due to TypeScript errors\n// These components will be replaced with direct usage of Recharts components\n/*\n' + match.replace('// Recharts wrapper components\n', '') + '*/\n';
    });

    // Find all instances of XAxisWrapper, YAxisWrapper, etc. and replace with direct components
    const wrapperRegex = /<(\w+)Wrapper([^>]*)>/g;
    content = content.replace(wrapperRegex, (match, component, props) => {
      modified = true;
      return `<${component}${props}>`;
    });

    // Find all closing tags
    const closingWrapperRegex = /<\/(\w+)Wrapper>/g;
    content = content.replace(closingWrapperRegex, (match, component) => {
      modified = true;
      return `</${component}>`;
    });

    // Fix RechartsTooltip to Tooltip if needed
    if (content.includes('<RechartsTooltip')) {
      content = content.replace(/<RechartsTooltip([^>]*)>/g, '<Tooltip$1>');
      content = content.replace(/<\/RechartsTooltip>/g, '</Tooltip>');
      modified = true;
    }
  }

  // Save the modified file
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed Recharts direct usage in ${filePath}`);
  } else {
    console.log(`No Recharts direct usage to fix in ${filePath}`);
  }
}

// Find all TypeScript files in the src directory
const files = glob.sync('/Users/<USER>/Desktop/Mexel/frontend/src/**/*.{ts,tsx}');

// Fix Recharts direct usage in each file
files.forEach(fixRechartsDirectUsage);

console.log('Done fixing Recharts direct usage!');
