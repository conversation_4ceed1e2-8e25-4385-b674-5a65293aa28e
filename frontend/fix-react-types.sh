#!/bin/bash

# Script to fix common React type errors across the codebase

# Create a backup directory
mkdir -p ./backups

# Function to fix React.FC
fix_react_fc() {
  echo "Fixing React.FC in $1"
  # Backup the file
  cp "$1" "./backups/$(basename "$1").bak"
  # Replace React.FC with FC
  sed -i '' 's/React\.FC/FC/g' "$1"
}

# Function to fix React.ReactNode
fix_react_reactnode() {
  echo "Fixing React.ReactNode in $1"
  # Backup the file
  cp "$1" "./backups/$(basename "$1").bak"
  # Replace React.ReactNode with ReactNode
  sed -i '' 's/React\.ReactNode/ReactNode/g' "$1"
}

# Function to fix React.ChangeEvent
fix_react_changeevent() {
  echo "Fixing React.ChangeEvent in $1"
  # Backup the file
  cp "$1" "./backups/$(basename "$1").bak"
  # Replace React.ChangeEvent with ChangeEvent
  sed -i '' 's/React\.ChangeEvent/ChangeEvent/g' "$1"
}

# Function to fix React.MouseEvent
fix_react_mouseevent() {
  echo "Fixing React.MouseEvent in $1"
  # Backup the file
  cp "$1" "./backups/$(basename "$1").bak"
  # Replace React.MouseEvent with MouseEvent
  sed -i '' 's/React\.MouseEvent/MouseEvent/g' "$1"
}

# Function to fix React.SyntheticEvent
fix_react_syntheticevent() {
  echo "Fixing React.SyntheticEvent in $1"
  # Backup the file
  cp "$1" "./backups/$(basename "$1").bak"
  # Replace React.SyntheticEvent with SyntheticEvent
  sed -i '' 's/React\.SyntheticEvent/SyntheticEvent/g' "$1"
}

# Function to add import for React types
add_react_types_import() {
  echo "Adding React types import to $1"
  # Backup the file
  cp "$1" "./backups/$(basename "$1").bak"
  
  # Determine the relative path to the types/react.ts file
  file_dir=$(dirname "$1")
  rel_path=$(realpath --relative-to="$file_dir" "./src/types")
  
  # Add import statement after the first import
  if grep -q "import React" "$1"; then
    # If there's an import React line, replace it with our imports
    sed -i '' 's/import React.*/import { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from '"'$rel_path\/react'"';/g' "$1"
  else
    # If there's no import React line, add our imports after the first import
    sed -i '' '1s/^/import { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from '"'$rel_path\/react'"';\n/' "$1"
  fi
}

# Find all TypeScript files with React.FC
find ./src -type f -name "*.tsx" -exec grep -l "React\.FC" {} \; | while read -r file; do
  fix_react_fc "$file"
  add_react_types_import "$file"
done

# Find all TypeScript files with React.ReactNode
find ./src -type f -name "*.tsx" -exec grep -l "React\.ReactNode" {} \; | while read -r file; do
  fix_react_reactnode "$file"
  add_react_types_import "$file"
done

# Find all TypeScript files with React.ChangeEvent
find ./src -type f -name "*.tsx" -exec grep -l "React\.ChangeEvent" {} \; | while read -r file; do
  fix_react_changeevent "$file"
  add_react_types_import "$file"
done

# Find all TypeScript files with React.MouseEvent
find ./src -type f -name "*.tsx" -exec grep -l "React\.MouseEvent" {} \; | while read -r file; do
  fix_react_mouseevent "$file"
  add_react_types_import "$file"
done

# Find all TypeScript files with React.SyntheticEvent
find ./src -type f -name "*.tsx" -exec grep -l "React\.SyntheticEvent" {} \; | while read -r file; do
  fix_react_syntheticevent "$file"
  add_react_types_import "$file"
done

echo "Done fixing React types!"
