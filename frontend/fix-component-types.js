const fs = require('fs');
const path = require('path');

// Function to fix React component types
function fixComponentTypes(filePath) {
  console.log(`Fixing component types in ${filePath}`);
  
  try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Fix React.FC to FC
    content = content.replace(/React\.FC/g, 'FC');
    
    // Fix React.ReactNode to ReactNode
    content = content.replace(/React\.ReactNode/g, 'ReactNode');
    
    // Fix React.ChangeEvent to ChangeEvent
    content = content.replace(/React\.ChangeEvent/g, 'ChangeEvent');
    
    // Fix React.MouseEvent to MouseEvent
    content = content.replace(/React\.MouseEvent/g, 'MouseEvent');
    
    // Fix React.SyntheticEvent to SyntheticEvent
    content = content.replace(/React\.SyntheticEvent/g, 'SyntheticEvent');
    
    // Fix React.FormEvent to FormEvent
    content = content.replace(/React\.FormEvent/g, 'FormEvent');
    
    // Fix React.KeyboardEvent to KeyboardEvent
    content = content.replace(/React\.KeyboardEvent/g, 'KeyboardEvent');
    
    // Fix React.FocusEvent to FocusEvent
    content = content.replace(/React\.FocusEvent/g, 'FocusEvent');
    
    // Fix React.CSSProperties to CSSProperties
    content = content.replace(/React\.CSSProperties/g, 'CSSProperties');
    
    // Write the file
    fs.writeFileSync(filePath, content, 'utf8');
    
    console.log(`Fixed component types in ${filePath}`);
  } catch (error) {
    console.error(`Error fixing component types in ${filePath}:`, error);
  }
}

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      processDirectory(filePath);
    } else if (stats.isFile() && (filePath.endsWith('.tsx') || filePath.endsWith('.ts'))) {
      fixComponentTypes(filePath);
    }
  }
}

// Start processing from the src directory
processDirectory('./src');

console.log('Done fixing component types!');
