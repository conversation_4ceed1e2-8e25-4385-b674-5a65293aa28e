// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Define a type for jest global to avoid TypeScript errors
declare global {
  namespace NodeJS {
    interface Global {
      jest: typeof import('jest');
    }
  }

  // Define Jest globals
  const jest: any;
  const describe: Function;
  const it: Function;
  const test: Function;
  const expect: Function;
  const beforeAll: Function;
  const beforeEach: Function;
  const afterAll: Function;
  const afterEach: Function;
}

// Fix missing test environment globals
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock window.fetch
window.fetch = jest.fn();

// Make it a module by adding an export
export {};
