// Type declarations for zustand
declare module 'zustand' {
  export interface StateCreator<T, U = T> {
    (set: (partial: T | Partial<T> | ((state: T) => T | Partial<T>)) => void, get: () => T): U;
  }

  export interface StoreApi<T> {
    setState: (partial: T | Partial<T> | ((state: T) => T | Partial<T>)) => void;
    getState: () => T;
    subscribe: (listener: (state: T, prevState: T) => void) => () => void;
    destroy: () => void;
  }

  export interface UseBoundStore<T> extends StoreApi<T> {
    (): T;
    getState: () => T;
    setState: (partial: T | Partial<T> | ((state: T) => T | Partial<T>)) => void;
    subscribe: (listener: (state: T, prevState: T) => void) => () => void;
  }

  export function create<T>(stateCreator: StateCreator<T>): UseBoundStore<T>;
  export function create<T, U>(stateCreator: StateCreator<T, U>): UseBoundStore<U>;
}
