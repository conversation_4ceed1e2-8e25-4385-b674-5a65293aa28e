import React from "react";
export interface IEmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  metadata?: {
    type?: string;
    industry?: string;
    tone?: string;
    step?: number;
    totalSteps?: number;
    keywords?: string[];
    recipient?: string;
    targetRole?: string;
    createdAt?: string;
    variants?: Array<{
      id: string;
      name: string;
      subject: string;
      body: string;
    }>;
  };
}

export interface IEmailStrategy {
  id: string;
  name: string;
  description?: string;
  execute: (
    lead: any,
    template: IEmailTemplate,
    params?: any
  ) => Promise<IQueuedEmail>;
}

export interface IProductMatch {
  productName: string;
  relevanceScore: number;
  reasoning?: string;
}

export interface IQueuedEmail {
  id?: string;
  recipientEmail: string;
  subject: string;
  body: string;
  templateId?: string;
  campaignId?: string;
  leadId?: string;
  scheduledTime?: Date;
  status: "queued" | "sent" | "failed" | "draft";
  sendAttempts?: number;
  lastAttemptTime?: Date;
  error?: string;
  metadata?: Record<string, any>;
}

export interface IEmailEngagement {
  emailId: string;
  leadId: string;
  opened?: boolean;
  clicked?: boolean;
  replied?: boolean;
  converted?: boolean;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

export interface IEmailMessage {
  to: string | string[];
  from: string;
  subject: string;
  text?: string;
  html: string;
  attachments?: any[];
}

export interface IEmailMetadata {
  [key: string]: any;
}

export interface IEmailSequence {
  id: string;
  name: string;
  steps: IEmailSequenceStep[];
  isActive: boolean;
}

export interface IEmailSequenceStep {
  id: string;
  order: number;
  templateId: string;
  delayDays: number; // Delay in days after the previous step or initial trigger
}

export interface IEmailActivity {
  type: "sent" | "opened" | "clicked" | "replied" | "bounced" | "unsubscribed";
  timestamp: Date;
  emailId?: string;
  campaignId?: string;
  leadId?: string;
  details?: Record<string, any>;
}

export interface IEmailOptions {
  trackingEnabled?: boolean;
  unsubscribeLink?: string;
  headers?: Record<string, string>;
}

export interface IEmailResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

export interface IEmailMetrics {
  totalSent: number;
  totalOpened: number;
  totalClicked: number;
  openRate: number;
  clickThroughRate: number;
  replyRate?: number;
  conversionRate?: number;
  bounceRate?: number;
  unsubscribeRate?: number;
}

export interface IEmailCampaign {
  id: number;
  name: string;
  status: "draft" | "scheduled" | "active" | "completed" | "paused";
  templates: IEmailTemplate[];
  recipients: string[];
  scheduledDate?: string;
  sentDate?: string;
  stats?: {
    sent: number;
    opened: number;
    clicked: number;
    replied: number;
    converted: number;
    openRate: number;
    clickRate: number;
    replyRate: number;
    conversionRate: number;
  };
}

export interface IEmailReport {
  id: number;
  name: string;
  frequency: "daily" | "weekly" | "monthly" | "quarterly";
  recipients: string[];
  lastSent?: string;
  nextScheduled?: string;
  format: "PDF" | "Excel" | "CSV";
  active: boolean;
}

export interface IEmailPreviewProps {
  template: IEmailTemplate;
  onEdit?: (template: IEmailTemplate) => void;
  onSend?: (template: IEmailTemplate) => void;
  onCopy?: (template: IEmailTemplate) => void;
  showControls?: boolean;
  showMetadata?: boolean;
  recipientEmail?: string;
  onRecipientChange?: (email: string) => void;
  previewMode?: "full" | "mobile" | "desktop";
}

export interface IEmailGeneratorProps {
  onGenerate: (prompt: string, recipient: string) => void;
  isGenerating: boolean;
}
