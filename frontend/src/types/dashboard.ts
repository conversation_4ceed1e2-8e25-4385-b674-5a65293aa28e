import React from 'react';
export interface AgentUpdate {
  agentId: string;
  status: string;
  message: string;
  timestamp: string;
  level: "info" | "warning" | "error" | "success";
}

export interface AgentMetrics {
  id: string;
  name: string;
  role: string;
  status: string;
  success_rate: number;
  error_rate: number;
  latency: number;
  last_check_time: string;
  // Additional metrics for specific agent types
  tendersProcessed?: number;
  newOpportunities?: number;
  highPriorityTenders?: number;
  sourcePerformance?: {
    eTenders: number;
    eskom: number;
  };
  emailsGenerated?: number;
  emailsSent?: number;
  queueSize?: number;
  deliveryRate?: number;
  templateUsage?: number;
  metricsCollected?: number;
  reportsGenerated?: number;
  dataPointsAnalyzed?: number;
  reportCategories?: {
    performance: number;
    tender: number;
    email: number;
  };
}

export interface AnalyticsData {
  leads_generated: number;
  website_traffic: number;
  social_engagement: number;
  tender_opportunities: number;
  conversion_rate: number;
  average_tender_score: number;
  top_keywords: string[];
  last_updated: string;
}

export interface TenderOpportunity {
  id?: string;
  title: string;
  description: string;
  source?: string;
  issuer?: string;
  closing_date?: string;
  closingDate?: string | Date;
  relevance_score?: number;
  confidence?: number;
  opportunity_score?: number;
  estimated_value?: number;
  value?: number;
  url?: string;
  status?: string;
  category?: string;
  reference?: string;
  publishDate?: string | Date;
  scrape_date?: string;
}
