// Type declarations for libraries without official type definitions

// Add any missing type declarations here
declare module 'react-markdown' {
  import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';

  export interface ReactMarkdownOptions {
    className?: string;
    source?: string;
    children?: string;
    allowDangerousHtml?: boolean;
    renderers?: {
      [key: string]: React.ComponentType<any>;
    };
    plugins?: any[];
    unwrapDisallowed?: boolean;
    allowNode?: (node: any, index: number, parent: any) => boolean;
    allowedTypes?: string[];
    disallowedTypes?: string[];
    transformLinkUri?: ((uri: string, children?: any, title?: string) => string) | null;
    transformImageUri?: ((uri: string, children?: any, title?: string, alt?: string) => string) | null;
    sourcePos?: boolean;
    rawSourcePos?: boolean;
    skipHtml?: boolean;
    includeNodeIndex?: boolean;
    linkTarget?: string | ((href: string, children: any, title?: string) => string);
  }

  const ReactMarkdown: (ReactMarkdownOptions);

  export default ReactMarkdown;
}

// Add any other missing type declarations as needed
