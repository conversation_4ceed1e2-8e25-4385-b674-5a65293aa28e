// Mock Socket.IO client types until the real package is installed

declare module "socket.io-client" {
  export interface Socket {
    connect(): void;
    disconnect(): void;
    on(event: string, callback: (...args: any[]) => void): Socket;
    once(event: string, callback: (...args: any[]) => void): Socket;
    off(event: string, callback?: (...args: any[]) => void): Socket;
    emit(event: string, ...args: any[]): Socket;
    removeAllListeners(event?: string): Socket;
    connected: boolean;
    disconnected: boolean;
    id?: string;
  }

  export interface SocketOptions {
    autoConnect?: boolean;
    reconnection?: boolean;
    reconnectionDelay?: number;
    reconnectionAttempts?: number;
    timeout?: number;
    transports?: string[];
  }

  export function io(url: string, options?: SocketOptions): Socket;
}

// Also export for direct import
export interface Socket {
  connect(): void;
  disconnect(): void;
  on(event: string, callback: (...args: any[]) => void): Socket;
  once(event: string, callback: (...args: any[]) => void): Socket;
  off(event: string, callback?: (...args: any[]) => void): Socket;
  emit(event: string, ...args: any[]): Socket;
  removeAllListeners(event?: string): Socket;
  connected: boolean;
  disconnected: boolean;
  id?: string;
}

export interface SocketOptions {
  autoConnect?: boolean;
  reconnection?: boolean;
  reconnectionDelay?: number;
  reconnectionAttempts?: number;
  timeout?: number;
  transports?: string[];
}

export function io(url: string, options?: SocketOptions): Socket {
  // Mock implementation - will be replaced when real socket.io-client is available
  const mockSocket: Socket = {
    connect: () => { console.log('Mock socket connect'); return mockSocket; },
    disconnect: () => { console.log('Mock socket disconnect'); return mockSocket; },
    on: (event: string, callback: (...args: any[]) => void) => mockSocket,
    once: (event: string, callback: (...args: any[]) => void) => mockSocket,
    off: (event: string, callback?: (...args: any[]) => void) => mockSocket,
    emit: (event: string, ...args: any[]) => mockSocket,
    removeAllListeners: (event?: string) => mockSocket,
    connected: false,
    disconnected: true,
    id: 'mock-socket-id'
  };
  return mockSocket;
  }

  // Export io as both named and default export
  export default io;
  export { io };
