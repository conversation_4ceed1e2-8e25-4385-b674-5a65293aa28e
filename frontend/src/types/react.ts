// Common React types to be imported across components
import React, {
    ChangeEvent,
    FC,
    FocusEvent,
    FormEvent,
    KeyboardEvent,
    MouseEvent,
    ReactNode,
    Suspense,
    SyntheticEvent,
    useCallback,
    useContext,
    useDebugValue,
    useEffect,
    useImperativeHandle,
    useLayoutEffect,
    useMemo,
    useReducer,
    useRef,
    useState
} from 'react';

// Re-export all the types (except the ones we'll declare below)
export type {
    ChangeEvent, FC, FocusEvent, FormEvent,
    KeyboardEvent, MouseEvent, ReactNode, SyntheticEvent
};

// Also export the React namespace and hooks for cases where they're needed
    export {
        React, Suspense, useCallback, useContext, useDebugValue, useEffect, useImperativeHandle,
        useLayoutEffect, useMemo, useReducer, useRef, useState
    };

// Add Fragment export
export const Fragment = React.Fragment;

// Add lazy export
export const lazy = React.lazy;

// Helper type for useState hook
export type SetState<T> = (value: T | ((prevState: T) => T)) => void;

// Additional React types that might be missing
export type Dispatch<A> = (value: A) => void;
export type SetStateAction<S> = S | ((prevState: S) => S);

// Event types - standalone definitions
export interface DragEvent<T = Element> extends MouseEvent<T> {
  dataTransfer: DataTransfer;
}

export interface ClipboardEvent<T = Element> extends SyntheticEvent<T> {
  clipboardData: DataTransfer;
}

export interface TransitionEvent<T = Element> extends SyntheticEvent<T> {
  propertyName: string;
  elapsedTime: number;
  pseudoElement: string;
}

export interface AnimationEvent<T = Element> extends SyntheticEvent<T> {
  animationName: string;
  elapsedTime: number;
  pseudoElement: string;
}

export interface PointerEvent<T = Element> extends MouseEvent<T> {
  pointerId: number;
  pressure: number;
  tangentialPressure: number;
  tiltX: number;
  tiltY: number;
  twist: number;
  width: number;
  height: number;
  pointerType: 'mouse' | 'pen' | 'touch';
  isPrimary: boolean;
}

export interface TouchEvent<T = Element> extends SyntheticEvent<T> {
  altKey: boolean;
  changedTouches: TouchList;
  ctrlKey: boolean;
  metaKey: boolean;
  shiftKey: boolean;
  targetTouches: TouchList;
  touches: TouchList;
}

export interface WheelEvent<T = Element> extends MouseEvent<T> {
  deltaMode: number;
  deltaX: number;
  deltaY: number;
  deltaZ: number;
}
