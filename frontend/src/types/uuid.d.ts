import React from 'react';
/**
 * Type declaration file for uuid module
 * This is a workaround for missing @types/uuid package
 */

declare module 'uuid' {
  export function v1(): string;
  export function v3(name: string | <PERSON>uffer, namespace: string | <PERSON>uff<PERSON>): string;
  export function v4(): string;
  export function v5(name: string | <PERSON>uff<PERSON>, namespace: string | Buffer): string;
  export function validate(uuid: string): boolean;
  export function parse(uuid: string): Buffer;
  export function stringify(buffer: Buffer): string;
  
  export const v1: {
    (): string;
    DNS: string;
    URL: string;
  };
  
  export const v4: {
    (): string;
    DNS: string;
    URL: string;
  };
}