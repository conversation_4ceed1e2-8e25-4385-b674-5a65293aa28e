
// Basic Node.js type definitions
declare global {
  namespace NodeJS {
    interface Process {
      env: ProcessEnv;
      platform: string;
      version: string;
    }

    interface ProcessEnv {
      [key: string]: string | undefined;
      NODE_ENV?: 'development' | 'production' | 'test';
      CI?: string;
    }

    // Timer types
    type Timeout = ReturnType<typeof setTimeout>;
    type Immediate = ReturnType<typeof setImmediate>;
    type Timer = Timeout | Immediate;
  }

  const process: NodeJS.Process;
}

// This is needed to make this file a module
export { };

