import React from "react";
import * as React from "react";

// Re-export React types
export type FC<P = {}> = React.FC<P>;
export type ReactNode = React.ReactNode;
export type ReactElement = React.ReactElement;
export type ChangeEvent<T = Element> = React.ChangeEvent<T>;
export type MouseEvent<T = Element> = React.MouseEvent<T>;
export type SyntheticEvent<T = Element> = React.SyntheticEvent<T>;
export type FormEvent<T = Element> = React.FormEvent<T>;
export type KeyboardEvent<T = Element> = React.KeyboardEvent<T>;
export type FocusEvent<T = Element> = React.FocusEvent<T>;
export type Ref<T> = React.Ref<T>;
export type RefObject<T> = React.RefObject<T>;
export type CSSProperties = React.CSSProperties;
export type HTMLAttributes<T = Element> = React.HTMLAttributes<T>;
export type InputHTMLAttributes<T = Element> = React.InputHTMLAttributes<T>;
export type ButtonHTMLAttributes<T = Element> = React.ButtonHTMLAttributes<T>;
export type FormHTMLAttributes<T = Element> = React.FormHTMLAttributes<T>;
export type SelectHTMLAttributes<T = Element> = React.SelectHTMLAttributes<T>;
export type TextareaHTMLAttributes<T = Element> =
  React.TextareaHTMLAttributes<T>;
export type AnchorHTMLAttributes<T = Element> = React.AnchorHTMLAttributes<T>;
export type ImgHTMLAttributes<T = Element> = React.ImgHTMLAttributes<T>;
export type DivHTMLAttributes<T = Element> = React.DetailedHTMLProps<
  React.HTMLAttributes<HTMLDivElement>,
  HTMLDivElement
>;
export type SpanHTMLAttributes<T = Element> = React.DetailedHTMLProps<
  React.HTMLAttributes<HTMLSpanElement>,
  HTMLSpanElement
>;
export type LabelHTMLAttributes<T = Element> = React.LabelHTMLAttributes<T>;

// Re-export hooks
export const useState: typeof React.useState;
export const useEffect: typeof React.useEffect;
export const useContext: typeof React.useContext;
export const useReducer: typeof React.useReducer;
export const useCallback: typeof React.useCallback;
export const useMemo: typeof React.useMemo;
export const useRef: typeof React.useRef;
export const useImperativeHandle: typeof React.useImperativeHandle;
export const useLayoutEffect: typeof React.useLayoutEffect;
export const useDebugValue: typeof React.useDebugValue;

// Helper type for useState hook
export type SetState<T> = React.Dispatch<React.SetStateAction<T>>;

// Also export the React namespace for cases where it's needed
export { React };
