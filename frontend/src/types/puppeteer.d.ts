// Type declarations for puppeteer
declare module 'puppeteer' {
  export interface Browser {
    newPage(): Promise<Page>;
    close(): Promise<void>;
  }

  export interface Page {
    goto(url: string, options?: any): Promise<any>;
    screenshot(options?: any): Promise<Buffer>;
    evaluate<T>(pageFunction: () => T): Promise<T>;
    evaluate<T>(pageFunction: (...args: any[]) => T, ...args: any[]): Promise<T>;
    waitForSelector(selector: string, options?: any): Promise<any>;
    click(selector: string): Promise<void>;
    type(selector: string, text: string): Promise<void>;
    close(): Promise<void>;
  }

  export function launch(options?: any): Promise<Browser>;
}
