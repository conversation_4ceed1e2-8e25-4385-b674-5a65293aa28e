// Type declarations for Material-UI icons
declare module '@mui/icons-material' {
  export const AccessTime: any;
  export const Add: any;
  export const ArrowBack: any;
  export const ArrowForward: any;
  export const Check: any;
  export const CheckCircle: any;
  export const Clear: any;
  export const Close: any;
  export const Delete: any;
  export const Edit: any;
  export const Error: any;
  export const ExpandLess: any;
  export const ExpandMore: any;
  export const Home: any;
  export const Info: any;
  export const Menu: any;
  export const MoreVert: any;
  export const Notifications: any;
  export const Person: any;
  export const Refresh: any;
  export const Search: any;
  export const Send: any;
  export const Settings: any;
  export const Star: any;
  export const Warning: any;
  export const Visibility: any;
  export const VisibilityOff: any;
}

declare module '@mui/icons-material/AccessTime' {
  const AccessTime: any;
  export default AccessTime;
}

declare module '@mui/icons-material/Add' {
  const Add: any;
  export default Add;
}

declare module '@mui/icons-material/ArrowBack' {
  const ArrowBack: any;
  export default ArrowBack;
}

declare module '@mui/icons-material/ArrowForward' {
  const ArrowForward: any;
  export default ArrowForward;
}

declare module '@mui/icons-material/Check' {
  const Check: any;
  export default Check;
}

declare module '@mui/icons-material/CheckCircle' {
  const CheckCircle: any;
  export default CheckCircle;
}

declare module '@mui/icons-material/Clear' {
  const Clear: any;
  export default Clear;
}

declare module '@mui/icons-material/Close' {
  const Close: any;
  export default Close;
}

declare module '@mui/icons-material/Delete' {
  const Delete: any;
  export default Delete;
}

declare module '@mui/icons-material/Edit' {
  const Edit: any;
  export default Edit;
}

declare module '@mui/icons-material/Error' {
  const Error: any;
  export default Error;
}

declare module '@mui/icons-material/ExpandLess' {
  const ExpandLess: any;
  export default ExpandLess;
}

declare module '@mui/icons-material/ExpandMore' {
  const ExpandMore: any;
  export default ExpandMore;
}

declare module '@mui/icons-material/Home' {
  const Home: any;
  export default Home;
}

declare module '@mui/icons-material/Info' {
  const Info: any;
  export default Info;
}

declare module '@mui/icons-material/Menu' {
  const Menu: any;
  export default Menu;
}

declare module '@mui/icons-material/MoreVert' {
  const MoreVert: any;
  export default MoreVert;
}

declare module '@mui/icons-material/Notifications' {
  const Notifications: any;
  export default Notifications;
}

declare module '@mui/icons-material/Person' {
  const Person: any;
  export default Person;
}

declare module '@mui/icons-material/Refresh' {
  const Refresh: any;
  export default Refresh;
}

declare module '@mui/icons-material/Search' {
  const Search: any;
  export default Search;
}

declare module '@mui/icons-material/Send' {
  const Send: any;
  export default Send;
}

declare module '@mui/icons-material/Settings' {
  const Settings: any;
  export default Settings;
}

declare module '@mui/icons-material/Star' {
  const Star: any;
  export default Star;
}

declare module '@mui/icons-material/Warning' {
  const Warning: any;
  export default Warning;
}

declare module '@mui/icons-material/Visibility' {
  const Visibility: any;
  export default Visibility;
}

declare module '@mui/icons-material/VisibilityOff' {
  const VisibilityOff: any;
  export default VisibilityOff;
}
