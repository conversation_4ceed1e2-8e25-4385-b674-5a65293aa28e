// Type declarations for Material-UI modules
declare module '@mui/material' {
  export { default as Accordion } from '@mui/material/Accordion';
    export { default as Alert, AlertProps } from '@mui/material/Alert';
    export { default as AppBar } from '@mui/material/AppBar';
    export { default as Autocomplete } from '@mui/material/Autocomplete';
    export { default as Avatar } from '@mui/material/Avatar';
    export { default as Badge } from '@mui/material/Badge';
    export { default as Box } from '@mui/material/Box';
    export { default as Button } from '@mui/material/Button';
    export { default as ButtonGroup } from '@mui/material/ButtonGroup';
    export { default as Card } from '@mui/material/Card';
    export { default as CardActions } from '@mui/material/CardActions';
    export { default as CardContent } from '@mui/material/CardContent';
    export { default as CardHeader } from '@mui/material/CardHeader';
    export { default as Checkbox } from '@mui/material/Checkbox';
    export { default as Chip } from '@mui/material/Chip';
    export { default as CircularProgress } from '@mui/material/CircularProgress';
    export { default as Container } from '@mui/material/Container';
    export { default as CssBaseline } from '@mui/material/CssBaseline';
    export { default as Dialog } from '@mui/material/Dialog';
    export { default as DialogActions } from '@mui/material/DialogActions';
    export { default as DialogContent } from '@mui/material/DialogContent';
    export { default as DialogContentText } from '@mui/material/DialogContentText';
    export { default as DialogTitle } from '@mui/material/DialogTitle';
    export { default as Divider } from '@mui/material/Divider';
    export { default as Drawer } from '@mui/material/Drawer';
    export { default as Fab } from '@mui/material/Fab';
    export { default as FormControl } from '@mui/material/FormControl';
    export { default as FormControlLabel } from '@mui/material/FormControlLabel';
    export { default as FormGroup } from '@mui/material/FormGroup';
    export { default as FormLabel } from '@mui/material/FormLabel';
    export { default as GlobalStyles } from '@mui/material/GlobalStyles';
    export { default as Grid } from '@mui/material/Grid';
    export { default as IconButton } from '@mui/material/IconButton';
    export { default as InputLabel } from '@mui/material/InputLabel';
    export { default as LinearProgress } from '@mui/material/LinearProgress';
    export { default as List } from '@mui/material/List';
    export { default as ListItem } from '@mui/material/ListItem';
    export { default as ListItemButton } from '@mui/material/ListItemButton';
    export { default as ListItemIcon } from '@mui/material/ListItemIcon';
    export { default as ListItemText } from '@mui/material/ListItemText';
    export { default as Menu } from '@mui/material/Menu';
    export { default as MenuItem } from '@mui/material/MenuItem';
    export { default as MenuList } from '@mui/material/MenuList';
    export { default as Modal } from '@mui/material/Modal';
    export { default as Paper } from '@mui/material/Paper';
    export { default as Popover } from '@mui/material/Popover';
    export { default as Radio } from '@mui/material/Radio';
    export { default as RadioGroup } from '@mui/material/RadioGroup';
    export { default as Rating } from '@mui/material/Rating';
    export { default as Select, default as SelectChangeEvent } from '@mui/material/Select';
    export { default as Skeleton } from '@mui/material/Skeleton';
    export { default as Slider } from '@mui/material/Slider';
    export { default as Snackbar } from '@mui/material/Snackbar';
    export { default as Stack } from '@mui/material/Stack';
    export { default as StyledEngineProvider } from '@mui/material/StyledEngineProvider';
    export * from '@mui/material/styles';
    export { default as Switch } from '@mui/material/Switch';
    export { default as Tab } from '@mui/material/Tab';
    export { default as Table } from '@mui/material/Table';
    export { default as TableBody } from '@mui/material/TableBody';
    export { default as TableCell } from '@mui/material/TableCell';
    export { default as TableContainer } from '@mui/material/TableContainer';
    export { default as TableHead } from '@mui/material/TableHead';
    export { default as TablePagination } from '@mui/material/TablePagination';
    export { default as TableRow } from '@mui/material/TableRow';
    export { default as TableSortLabel } from '@mui/material/TableSortLabel';
    export { default as Tabs } from '@mui/material/Tabs';
    export { default as TextField } from '@mui/material/TextField';
    export { default as Toolbar } from '@mui/material/Toolbar';
    export { default as Tooltip } from '@mui/material/Tooltip';
    export { default as Typography } from '@mui/material/Typography';

  // Additional types
  export interface SxProps {
    [key: string]: any;
  }

  export interface Theme {
    [key: string]: any;
  }
}

declare module '@mui/material/styles' {
  export function createTheme(options?: any): any;
  export function useTheme(): any;
  export function styled(component: any): any;
  export const ThemeProvider: any;
}

declare module '@mui/icons-material/*' {
  const Icon: any;
  export default Icon;
}

declare module '@mui/x-date-pickers' {
  export const DatePicker: any;
  export const LocalizationProvider: any;
  export const AdapterDateFns: any;
}

declare module '@mui/x-date-pickers/DatePicker' {
  const DatePicker: any;
  export default DatePicker;
}

declare module '@mui/x-date-pickers/LocalizationProvider' {
  const LocalizationProvider: any;
  export default LocalizationProvider;
}

declare module '@mui/x-date-pickers/AdapterDateFns' {
  const AdapterDateFns: any;
  export default AdapterDateFns;
}
