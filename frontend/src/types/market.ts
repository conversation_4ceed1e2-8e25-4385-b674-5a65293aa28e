import React from 'react';
export interface MarketData {
  id: string;
  name: string;
  category: string;
  currentPrice: number;
  previousPrice: number;
  changePercentage: number;
  volume: number;
  marketCap: number;
  lastUpdated: string;
  trend: 'up' | 'down' | 'stable';
  relatedTenders?: string[];
}

export interface MarketAnalysis {
  sector: string;
  growth: number;
  volatility: number;
  sentiment: 'bullish' | 'bearish' | 'neutral';
  recommendation: string;
  potentialImpact: number;
  lastUpdated: string;
}

export interface MarketInsight {
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  source: string;
  date: string;
  relatedMarkets: string[];
}
