import React from 'react';
/**
 * Tender status enum
 */
export enum TenderStatus {
  NEW = 'NEW',
  PROCESSING = 'PROCESSING',
  REVIEWING = 'REVIEWING',
  SUBMITTED = 'SUBMITTED',
  WON = 'WON',
  LOST = 'LOST',
  CANCELLED = 'CANCELLED'
}

/**
 * Document interface
 */
export interface Document {
  title: string;
  url: string;
}

/**
 * Tender interface
 */
export interface Tender {
  id: string;
  title: string;
  description: string;
  url: string;
  issuer: string;
  status: TenderStatus;
  reference?: string;
  category?: string;
  publishDate?: Date;
  closingDate?: Date;
  value?: number;
  confidence?: number;
  tags?: string[];
  metadata?: Record<string, any>;
  documents?: Document[];
  requirements?: string[];
  source?: string;
}

/**
 * Tender filter options
 */
export interface TenderFilterOptions {
  status?: TenderStatus;
  category?: string;
  issuer?: string;
  source?: string;
  minConfidence?: number;
  search?: string;
  fromDate?: Date;
  toDate?: Date;
}

/**
 * Tender sort options
 */
export interface TenderSortOptions {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

/**
 * Tender pagination options
 */
export interface TenderPaginationOptions {
  page: number;
  limit: number;
}

/**
 * Tender pagination result
 */
export interface TenderPagination {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

/**
 * Tender statistics
 */
export interface TenderStats {
  totalCount: number;
  statusCounts: Record<TenderStatus, number>;
  categoryCounts: Record<string, number>;
  sourceCounts: Record<string, number>;
  averageValue: number;
  upcomingClosingCount: number;
}
