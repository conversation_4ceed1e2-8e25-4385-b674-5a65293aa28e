import React from 'react';
import * as React from 'react';
import { 
  BrowserRouterProps, 
  HashRouterProps, 
  LocationProps, 
  MemoryRouterProps, 
  NavLinkProps, 
  OutletProps, 
  PathRouteProps, 
  RoutesProps, 
  RouteProps 
} from 'react-router-dom';

declare module 'react-router-dom' {
  // Override the RouteProps to fix the element type
  interface RouteProps {
    caseSensitive?: boolean;
    children?: any;
    element?: any | null;
    index?: boolean;
    path?: string;
  }

  // Override the Routes component to fix the return type
  export function Routes(props: RoutesProps): JSX.Element;
  
  // Override the Route component to fix the return type
  export function Route(props: RouteProps): JSX.Element;
}
