import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
declare module 'react-markdown' {
  import { ReactNode, ComponentType, HTMLAttributes } from 'react';

  interface ReactMarkdownProps extends HTMLAttributes<HTMLDivElement> {
    children: string;
    remarkPlugins?: any[];
    rehypePlugins?: any[];
    components?: Record<string, ComponentType<any>>;
    className?: string;
    skipHtml?: boolean;
    sourcePos?: boolean;
    rawSourcePos?: boolean;
    includeElementIndex?: boolean;
    allowedElements?: string[];
    disallowedElements?: string[];
    unwrapDisallowed?: boolean;
    allowElement?: (element: any, index: number, parent: any) => boolean;
    linkTarget?: string | ((href: string, children: any, title?: string) => string);
  }

  const ReactMarkdown: (ReactMarkdownProps);

  export default ReactMarkdown;
}
