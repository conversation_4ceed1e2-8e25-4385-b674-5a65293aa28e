import React, { useEffect, useState } from "react";
import {
  Box,
  Chip,
  Typography,
  Paper,
  Tooltip,
  IconButton,
  Collapse,
} from "@mui/material";
import {
  Check as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import connectionMonitor from "./api/connectionMonitor";

interface ConnectionStatusProps {
  showDetails?: boolean;
  autoMonitor?: boolean;
  monitorInterval?: number;
}

/**
 * ConnectionStatus - A component that displays API and WebSocket connection status
 */
const ConnectionStatus: (ConnectionStatusProps) = ({
  showDetails = false,
  autoMonitor = true,
  monitorInterval = 30000,
}) => {
  const [expanded, setExpanded] = useState(showDetails);
  const [status, setStatus] = React.useState(connectionMonitor.getStatus());

  // Start monitoring on component mount
  useEffect(() => {
    if (autoMonitor) {
      connectionMonitor.startMonitoring(monitorInterval);
    }

    // Subscribe to status updates
    const unsubscribe = connectionMonitor.onStatusChange(
      "status",
      (newStatus) => {
        setStatus(newStatus);
      }
    );

    // Clean up on unmount
    return () => {
      unsubscribe();
      if (autoMonitor) {
        connectionMonitor.stopMonitoring();
      }
    };
  }, [autoMonitor, monitorInterval]);

  // Format timestamp to readable string
  const formatTime = (timestamp: Date | undefined): string => {
    if (!timestamp) return "Never";
    return new Date(timestamp).toLocaleTimeString();
  };

  // Get appropriate icon and color for status
  const getStatusIcon = (status: string | undefined) => {
    switch (status) {
      case "connected":
        return <CheckIcon fontSize="small" style={{ color: "green" }} />;
      case "error":
        return <ErrorIcon fontSize="small" style={{ color: "red" }} />;
      default:
        return <WarningIcon fontSize="small" style={{ color: "orange" }} />;
    }
  };

  // Handle manual refresh
  const handleRefresh = () => {
    connectionMonitor.checkConnections();
  };

  return (
    <Paper
      sx={{
        p: 1,
        border: "1px solid #eee",
        borderLeft: "4px solid",
        borderLeftColor:
          status.api?.status === "connected" &&
          status.websocket?.status === "connected"
            ? "success.main"
            : status.api?.status === "error" ||
              status.websocket?.status === "error"
            ? "error.main"
            : "warning.main",
      }}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Typography variant="subtitle2" sx={{ mr: 1 }}>
            Connection Status:
          </Typography>

          <Tooltip title={`API: ${status.api?.status || "unknown"}`}>
            <Chip
              size="small"
              icon={getStatusIcon(status.api?.status)}
              label="API"
              variant="outlined"
              color={
                status.api?.status === "connected"
                  ? "success"
                  : status.api?.status === "error"
                  ? "error"
                  : "warning"
              }
              sx={{ mr: 1 }}
            />
          </Tooltip>

          <Tooltip
            title={`WebSocket: ${status.websocket?.status || "unknown"}`}
          >
            <Chip
              size="small"
              icon={getStatusIcon(status.websocket?.status)}
              label="WebSocket"
              variant="outlined"
              color={
                status.websocket?.status === "connected"
                  ? "success"
                  : status.websocket?.status === "error"
                  ? "error"
                  : "warning"
              }
            />
          </Tooltip>
        </Box>

        <Box>
          <Tooltip title="Refresh status">
            <IconButton size="small" onClick={handleRefresh}>
              <RefreshIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title={expanded ? "Hide details" : "Show details"}>
            <IconButton
              size="small"
              onClick={() => setExpanded(!expanded)}
              sx={{
                transform: expanded ? "rotate(180deg)" : "rotate(0deg)",
                transition: "transform 0.3s",
              }}
            >
              <ExpandMoreIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Collapse in={expanded}>
        <Box sx={{ mt: 1, p: 1, bgcolor: "rgba(0,0,0,0.03)", borderRadius: 1 }}>
          <Typography variant="caption" display="block">
            Last checked: {formatTime(status.timestamp as Date)}
          </Typography>

          {status.api && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="caption" sx={{ fontWeight: "bold" }}>
                API: {status.api.status}
              </Typography>
              {status.api.details?.latency && (
                <Typography variant="caption" display="block">
                  Latency: {status.api.details.latency}ms
                </Typography>
              )}
              {status.api.details?.message && (
                <Typography variant="caption" display="block" color="error">
                  Error: {status.api.details.message}
                </Typography>
              )}
            </Box>
          )}

          {status.websocket && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="caption" sx={{ fontWeight: "bold" }}>
                WebSocket: {status.websocket.status}
              </Typography>
              {status.websocket.details?.latency && (
                <Typography variant="caption" display="block">
                  Latency: {status.websocket.details.latency}ms
                </Typography>
              )}
              {status.websocket.details?.message && (
                <Typography variant="caption" display="block" color="error">
                  Error: {status.websocket.details.message}
                </Typography>
              )}
            </Box>
          )}
        </Box>
      </Collapse>
    </Paper>
  );
};

export default ConnectionStatus;
