import React, { useState } from "react";
import {
  Box,
  Typography,
  Button,
  Paper,
  Alert,
  CircularProgress,
} from "@mui/material";
import { checkApiHealth, fetchExampleData } from "./api/apiService";

const ApiTester: React.FC = () => {
  const [healthStatus, setHealthStatus] = React.useState("");
  const [healthError, setHealthError] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [testData, setTestData] = React.useState(null);
  const [testError, setTestError] = React.useState("");

  // Function to test backend health endpoint
  const testHealthEndpoint = async () => {
    setIsLoading(true);
    setHealthStatus("");
    setHealthError("");

    try {
      const result = await checkApiHealth();
      if ("error" in result && result.error) {
        setHealthError(
          typeof result.details === "string"
            ? result.details
            : typeof result.message === "string"
            ? result.message
            : "Unknown error occurred"
        );
      } else {
        setHealthStatus(JSON.stringify(result, null, 2));
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      setHealthError(
        errorMessage ||
          "Failed to connect to backend health endpoint. Make sure your backend is running on port 3002."
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Function to test example data endpoint
  const testExampleEndpoint = async () => {
    setIsLoading(true);
    setTestData(null);
    setTestError("");

    try {
      const result = await fetchExampleData();
      if ("error" in result && result.error) {
        setTestError(
          typeof result.details === "string"
            ? result.details
            : typeof result.message === "string"
            ? result.message
            : "Unknown error occurred"
        );
      } else {
        setTestData(result);
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      setTestError(
        errorMessage ||
          "Failed to fetch example data from backend. Make sure your backend has the /api/example endpoint."
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h5" gutterBottom>
        Backend API Connection Test
      </Typography>
      <Alert severity="info" sx={{ mb: 2 }}>
        This component tests the connection to the backend server running on
        port 3002. Make sure the backend server is running before testing.
      </Alert>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Health Check
        </Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={testHealthEndpoint}
          disabled={isLoading}
        >
          Test Health Endpoint
        </Button>

        {isLoading && <CircularProgress sx={{ ml: 2 }} size={24} />}

        {healthStatus && (
          <Box
            sx={{
              mt: 2,
              p: 2,
              bgcolor: "rgba(0, 200, 0, 0.1)",
              borderRadius: 1,
            }}
          >
            <Typography
              variant="body2"
              component="pre"
              sx={{ whiteSpace: "pre-wrap" }}
            >
              {healthStatus}
            </Typography>
          </Box>
        )}

        {healthError && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {healthError}
          </Alert>
        )}
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Example Data Test
        </Typography>
        <Button
          variant="contained"
          color="secondary"
          onClick={testExampleEndpoint}
          disabled={isLoading}
        >
          Test Example Endpoint
        </Button>

        {isLoading && <CircularProgress sx={{ ml: 2 }} size={24} />}

        {testData && (
          <Box
            sx={{
              mt: 2,
              p: 2,
              bgcolor: "rgba(0, 200, 0, 0.1)",
              borderRadius: 1,
            }}
          >
            <Typography
              variant="body2"
              component="pre"
              sx={{ whiteSpace: "pre-wrap" }}
            >
              {JSON.stringify(testData, null, 2)}
            </Typography>
          </Box>
        )}

        {testError && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {testError}
          </Alert>
        )}
      </Paper>
    </Box>
  );
};

export default ApiTester;
