import React, { useState } from 'react';

// Most basic React app possible - no TypeScript, minimal dependencies
const BasicApp = () => {
  const [message, setMessage] = useState("Loading...");
  const [backendStatus, setBackendStatus] = useState("Checking backend...");
  const [testResult, setTestResult] = useState(null);

  // Check backend on button click instead of automatically
  const checkBackend = async () => {
    try {
      const response = await fetch('http://localhost:3002/health');
      if (response.ok) {
        const data = await response.json();
        setBackendStatus(`Connected to backend: ${data.message || 'API is operational'}`);
      } else {
        setBackendStatus(`Error: Backend returned status ${response.status}`);
      }
    } catch (error) {
      setBackendStatus(`Error: Cannot connect to backend at http://localhost:3002`);
    }
  };

  // Test API endpoint
  const testApi = async () => {
    setTestResult("Loading data...");
    try {
      const response = await fetch('http://localhost:3002/api/example');
      if (response.ok) {
        const data = await response.json();
        setTestResult(JSON.stringify(data, null, 2));
      } else {
        setTestResult(`Error fetching data: ${response.status}`);
      }
    } catch (error) {
      setTestResult("Network error: Cannot connect to backend");
    }
  };

  // Set initial message
  React.useEffect(() => {
    setMessage("Frontend is running!");
  }, []);

  return (
    <div style={{ fontFamily: 'Arial, sans-serif', maxWidth: '800px', margin: '40px auto', padding: '20px' }}>
      <h1>Mexel Basic Frontend</h1>
      <p style={{ color: 'green', fontWeight: 'bold' }}>{message}</p>
      
      <div style={{ marginTop: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>
        <h2>Backend Connectivity</h2>
        <p>{backendStatus}</p>
        <button 
          onClick={checkBackend}
          style={{ padding: '8px 16px', background: '#007bff', color: 'white', border: 'none', borderRadius: '4px' }}
        >
          Check Backend
        </button>
      </div>
      
      <div style={{ marginTop: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '5px' }}>
        <h2>API Test</h2>
        <button 
          onClick={testApi}
          style={{ padding: '8px 16px', background: '#28a745', color: 'white', border: 'none', borderRadius: '4px' }}
        >
          Test API
        </button>
        
        {testResult && (
          <pre style={{ 
            marginTop: '10px', 
            padding: '10px', 
            background: '#f8f9fa', 
            border: '1px solid #eee',
            borderRadius: '4px',
            overflow: 'auto'
          }}>
            {testResult}
          </pre>
        )}
      </div>
    </div>
  );
};

export default BasicApp;