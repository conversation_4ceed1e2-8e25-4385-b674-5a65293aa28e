import React from 'react';
import { createRoot } from 'react-dom/client';
import BasicApp from './BasicApp';
import ErrorBoundary from './ErrorBoundary'; // Import the ErrorBoundary
import './index.css';

// Get the root element
const rootElement = document.getElementById('root');

// Make sure the element exists
if (!rootElement) {
  throw new Error('Root element not found');
}

// Create a root
const root = createRoot(rootElement);

// Render the app with BasicApp which is a pure JS component with minimal dependencies
root.render(
  <React.StrictMode>
    <ErrorBoundary
      fallbackRender={(error, errorInfo) => (
        <div>
          <h2>Application Error</h2>
          <p>Sorry, something went wrong. Please try refreshing the page.</p>
          {/* You could add more details here or a button to report the error */}
          <details style={{ whiteSpace: 'pre-wrap' }}>
            {error && error.toString()}
            <br />
            {errorInfo && errorInfo.componentStack}
          </details>
        </div>
      )}
    >
      <BasicApp />
    </ErrorBoundary>
  </React.StrictMode>
);

// Add this to make it a module
export {};
