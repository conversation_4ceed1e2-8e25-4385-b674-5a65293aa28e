import { io, Socket } from "socket.io-client";
import api from "./apiService";

/**
 * WebSocketService - Service for handling WebSocket connections
 */
class WebSocketService {
  private socket: Socket | null = null;
  private connectionStatus: "connected" | "disconnected" | "connecting" =
    "disconnected";
  private messageHandlers: Map<string, Set<(data: any) => void>> = new Map();
  private connectionCallbacks: Set<(status: string) => void> = new Set();
  private debugMode: boolean = false;

  /**
   * Enable or disable debug mode
   */
  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
    console.log(
      `WebSocketService: Debug mode ${enabled ? "enabled" : "disabled"}`
    );
  }

  /**
   * Get current debug mode state
   */
  getDebugMode(): boolean {
    return this.debugMode;
  }

  /**
   * Log message if in debug mode
   */
  private debugLog(...args: any[]): void {
    if (this.debugMode) {
      console.log("[WebSocketService Debug]", ...args);
    }
  }

  /**
   * Connect to WebSocket server
   * @param serverUrl Optional server URL, defaults to "http://localhost:3001"
   * @param options Optional connection options
   */
  connect(serverUrl?: string, options?: any): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (this.socket) {
        this.debugLog("Socket already exists, disconnecting first");
        this.disconnect();
      }

      this.setConnectionStatus("connecting");

      try {
        this.debugLog("Connecting to WebSocket server...");
        console.log("Connecting to WebSocket server...");

        const url = serverUrl || "http://localhost:3001";
        this.debugLog(`Server URL: ${url}`);

        const connectionOptions = {
          transports: ["websocket", "polling"], // Allow fallback to polling if websocket fails
          reconnectionAttempts: 5,
          reconnectionDelay: 1000,
          timeout: 10000,
          ...options, // Override with any provided options
        };
        this.debugLog("Connection options:", connectionOptions);

        this.socket = io(url, connectionOptions);

        // Set up event listeners
        this.socket.on("connect", () => {
          console.log("WebSocket connected!");
          this.setConnectionStatus("connected");
          resolve(true);
        });

        this.socket.on("disconnect", () => {
          console.log("WebSocket disconnected");
          this.setConnectionStatus("disconnected");
        });

        this.socket.on("connect_error", (error) => {
          console.error("WebSocket connection error:", error);
          this.setConnectionStatus("disconnected");
          reject(error);
        });

        // Handle standard server responses
        this.socket.on("welcome", (data) => {
          console.log("Welcome message:", data);
          this.notifyHandlers("welcome", data);
        });

        this.socket.on("response", (data) => {
          console.log("Server response:", data);
          this.notifyHandlers("response", data);
        });

        this.socket.on("echo_response", (data) => {
          console.log("Echo response:", data);
          this.notifyHandlers("echo_response", data);
        });

        this.socket.on("pong", (data) => {
          console.log("Pong response:", data);
          this.notifyHandlers("pong", data);
        });

        // Room events
        this.socket.on("room_joined", (data) => {
          console.log("Room joined:", data);
          this.notifyHandlers("room_joined", data);
        });

        this.socket.on("room_left", (data) => {
          console.log("Room left:", data);
          this.notifyHandlers("room_left", data);
        });

        this.socket.on("user_joined", (data) => {
          console.log("User joined room:", data);
          this.notifyHandlers("user_joined", data);
        });

        this.socket.on("user_left", (data) => {
          console.log("User left room:", data);
          this.notifyHandlers("user_left", data);
        });

        this.socket.on("room_message", (data) => {
          console.log("Room message:", data);
          this.notifyHandlers("room_message", data);
        });
      } catch (error) {
        console.error("Error creating socket connection:", error);
        this.setConnectionStatus("disconnected");
        reject(error);
      }

      // Add timeout to catch if connection takes too long
      const connectionTimeout = setTimeout(() => {
        if (this.connectionStatus === "connecting") {
          console.error("Connection timeout after 10 seconds");
          this.setConnectionStatus("disconnected");
          if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
          }
          reject(new Error("Connection timeout after 10 seconds"));
        }
      }, 10000);

      // Clear timeout if connected
      this.socket?.once("connect", () => {
        clearTimeout(connectionTimeout);
      });
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.setConnectionStatus("disconnected");
    }
  }

  /**
   * Send a message to the server
   */
  sendMessage(message: string): void {
    if (!this.socket || this.connectionStatus !== "connected") {
      console.error("Cannot send message: socket not connected");
      return;
    }

    this.socket.emit("message", message);
  }

  /**
   * Send an echo request
   */
  sendEcho(data: any): void {
    if (!this.socket || this.connectionStatus !== "connected") {
      console.error("Cannot send echo: socket not connected");
      return;
    }

    this.socket.emit("echo", data);
  }

  /**
   * Send a ping request
   */
  sendPing(): void {
    if (!this.socket || this.connectionStatus !== "connected") {
      console.error("Cannot send ping: socket not connected");
      return;
    }

    this.socket.emit("ping");
  }

  /**
   * Join a room
   */
  joinRoom(room: string): void {
    if (!this.socket || this.connectionStatus !== "connected") {
      console.error("Cannot join room: socket not connected");
      return;
    }

    this.socket.emit("join_room", room);
  }

  /**
   * Leave a room
   */
  leaveRoom(room: string): void {
    if (!this.socket || this.connectionStatus !== "connected") {
      console.error("Cannot leave room: socket not connected");
      return;
    }

    this.socket.emit("leave_room", room);
  }

  /**
   * Send a message to a room
   */
  sendRoomMessage(room: string, message: string): void {
    if (!this.socket || this.connectionStatus !== "connected") {
      console.error("Cannot send room message: socket not connected");
      return;
    }

    this.socket.emit("room_message", { room, message });
  }

  /**
   * Get WebSocket server status via REST API
   */
  async getServerStatus(): Promise<any> {
    try {
      const response = await api.get("/api/websocket/status");
      return response.data;
    } catch (error) {
      console.error("Failed to get WebSocket server status:", error);
      throw error;
    }
  }

  /**
   * Get connected clients via REST API
   */
  async getConnectedClients(): Promise<any> {
    try {
      const response = await api.get("/api/websocket/clients");
      return response.data;
    } catch (error) {
      console.error("Failed to get connected clients:", error);
      throw error;
    }
  }

  /**
   * Register an event handler
   */
  onMessage(event: string, callback: (data: any) => void): () => void {
    if (!this.messageHandlers.has(event)) {
      this.messageHandlers.set(event, new Set());
    }

    this.messageHandlers.get(event)!.add(callback);

    // Return unsubscribe function
    return () => {
      const handlers = this.messageHandlers.get(event);
      if (handlers) {
        handlers.delete(callback);
      }
    };
  }

  /**
   * Register a connection status handler
   */
  onConnectionChange(callback: (status: string) => void): () => void {
    this.connectionCallbacks.add(callback);

    // Return unsubscribe function
    return () => {
      this.connectionCallbacks.delete(callback);
    };
  }

  /**
   * Get current connection status
   */
  getConnectionStatus(): string {
    return this.connectionStatus;
  }

  /**
   * Notify message handlers for an event
   */
  private notifyHandlers(event: string, data: any): void {
    const handlers = this.messageHandlers.get(event);
    if (handlers) {
      handlers.forEach((handler) => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in ${event} handler:`, error);
        }
      });
    }
  }

  /**
   * Update connection status and notify callbacks
   */
  private setConnectionStatus(
    status: "connected" | "disconnected" | "connecting"
  ): void {
    this.connectionStatus = status;
    this.connectionCallbacks.forEach((callback) => {
      try {
        callback(status);
      } catch (error) {
        console.error("Error in connection status callback:", error);
      }
    });
  }
}

// Create singleton instance
const webSocketService = new WebSocketService();

export default webSocketService;
