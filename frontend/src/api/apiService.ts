import React from 'react';
import axios from "axios";

// Create an axios instance with default config
const api = axios.create({
  baseURL: "http://localhost:3002",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add a request interceptor to handle authentication tokens if needed
api.interceptors.request.use(
  (config) => {
    // If we have a token stored, we can add it to every request
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors here
    if (error.response) {
      // Server responded with a status code outside the 2xx range
      console.error("API Error:", error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error("API Error: No response received", error.request);
    } else {
      // Something happened in setting up the request
      console.error("API Error:", error.message);
    }
    return Promise.reject(error);
  }
);

// Basic health check to test connectivity
export const checkApiHealth = async () => {
  try {
    console.log("Attempting to connect to health endpoint...");
    const response = await api.get("/health");
    console.log("Health check response:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("Health check failed:", error);
    // Return more detailed error information for debugging
    return {
      error: true,
      message: error.message,
      status: error.response?.status,
      details:
        "Connection to backend health endpoint failed. Make sure the backend server is running on port 3002.",
    };
  }
};

// Example function to fetch data from the backend
export const fetchExampleData = async () => {
  try {
    console.log("Attempting to fetch example data...");
    const response = await api.get("/api/example");
    console.log("Example data response:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch example data:", error);
    // Return more detailed error information for debugging
    return {
      error: true,
      message: error.message,
      status: error.response?.status,
      details:
        "Failed to connect to example data endpoint. Make sure the backend server is running and the /api/example endpoint exists.",
    };
  }
};

export default api;
