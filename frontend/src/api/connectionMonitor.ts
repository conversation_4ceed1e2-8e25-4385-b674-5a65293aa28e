import React from 'react';
// Connection Monitor for Mexel
// This module provides status checking and monitoring for API and WebSocket connections

import api from "./apiService";
import webSocketService from "./webSocketService";

/**
 * ConnectionMonitor class
 * Monitors and reports on the status of API and WebSocket connections
 */
class ConnectionMonitor {
  private checkInterval: number = 30000; // Default check every 30 seconds
  private intervalId: NodeJS.Timeout | null = null;
  private listeners: Map<string, Set<(status: any) => void>> = new Map();
  private lastStatus: {
    api: { status: string; timestamp: Date; details?: any } | null;
    websocket: { status: string; timestamp: Date; details?: any } | null;
  } = {
    api: null,
    websocket: null,
  };

  /**
   * Start monitoring connections
   * @param interval Time between checks in milliseconds
   */
  startMonitoring(interval: number = 30000): void {
    // Clear any existing interval
    this.stopMonitoring();

    // Set new check interval
    this.checkInterval = interval;

    // Perform initial check
    this.checkConnections();

    // Start regular interval checks
    this.intervalId = setInterval(() => {
      this.checkConnections();
    }, this.checkInterval);

    console.log(`Connection monitoring started with interval of ${interval}ms`);
  }

  /**
   * Stop monitoring connections
   */
  stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log("Connection monitoring stopped");
    }
  }

  /**
   * Check API and WebSocket connections
   */
  async checkConnections(): Promise<void> {
    await Promise.all([
      this.checkApiConnection(),
      this.checkWebSocketConnection(),
    ]);

    // Notify status listeners of full status
    this.notifyListeners("status", {
      api: this.lastStatus.api,
      websocket: this.lastStatus.websocket,
      timestamp: new Date(),
    });
  }

  /**
   * Check API connection
   */
  private async checkApiConnection(): Promise<void> {
    try {
      const startTime = Date.now();
      const response = await api.get("/health");
      const latency = Date.now() - startTime;

      this.lastStatus.api = {
        status: "connected",
        timestamp: new Date(),
        details: {
          latency,
          statusCode: response.status,
          data: response.data,
        },
      };

      this.notifyListeners("api", this.lastStatus.api);
    } catch (error: any) {
      this.lastStatus.api = {
        status: "error",
        timestamp: new Date(),
        details: {
          message: error.message,
          error,
        },
      };

      this.notifyListeners("api", this.lastStatus.api);
    }
  }

  /**
   * Check WebSocket connection
   */
  private async checkWebSocketConnection(): Promise<void> {
    try {
      if (webSocketService.getConnectionStatus() === "connected") {
        // If already connected, verify connection works by sending ping
        const startTime = Date.now();

        // Create a promise that resolves when pong is received
        const pingPromise = new Promise((resolve, reject) => {
          const timeoutId = setTimeout(() => {
            unsubscribe();
            reject(new Error("Ping timeout"));
          }, 5000);

          const unsubscribe = webSocketService.onMessage("pong", () => {
            clearTimeout(timeoutId);
            unsubscribe();
            resolve(true);
          });

          webSocketService.sendPing();
        });

        await pingPromise;
        const latency = Date.now() - startTime;

        this.lastStatus.websocket = {
          status: "connected",
          timestamp: new Date(),
          details: {
            latency,
            socketId: "active",
          },
        };
      } else {
        // Not connected, so just report status
        this.lastStatus.websocket = {
          status: "disconnected",
          timestamp: new Date(),
        };
      }

      this.notifyListeners("websocket", this.lastStatus.websocket);
    } catch (error: any) {
      this.lastStatus.websocket = {
        status: "error",
        timestamp: new Date(),
        details: {
          message: error.message,
          error,
        },
      };

      this.notifyListeners("websocket", this.lastStatus.websocket);
    }
  }

  /**
   * Get current connection status
   */
  getStatus(): any {
    return {
      api: this.lastStatus.api,
      websocket: this.lastStatus.websocket,
      timestamp: new Date(),
      isMonitoring: this.intervalId !== null,
    };
  }

  /**
   * Register a listener for connection status updates
   * @param type 'api', 'websocket', or 'status' for all
   * @param callback Function to call when status changes
   * @returns Unsubscribe function
   */
  onStatusChange(
    type: "api" | "websocket" | "status",
    callback: (status: any) => void
  ): () => void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set());
    }

    this.listeners.get(type)!.add(callback);

    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(type);
      if (listeners) {
        listeners.delete(callback);
      }
    };
  }

  /**
   * Notify all listeners of a specific type
   * @param type Type of status update
   * @param status Status data
   */
  private notifyListeners(type: string, status: any): void {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.forEach((listener) => {
        try {
          listener(status);
        } catch (error) {
          console.error(`Error in ${type} status listener:`, error);
        }
      });
    }
  }
}

// Create and export singleton instance
const connectionMonitor = new ConnectionMonitor();
export default connectionMonitor;
