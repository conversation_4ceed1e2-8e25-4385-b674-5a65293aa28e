# API Service Documentation

This directory contains the API service for communicating with the backend server.

## Overview

The API service provides a centralized way to make HTTP requests to the backend API endpoints. It uses Axios for HTTP requests and includes error handling, response interceptors, and request configuration.

## Files

- **apiService.ts**: Main API service configuration and API methods

## Usage

To use the API service in your components:

```typescript
import { checkApiHealth, fetchExampleData } from "./api/apiService";

// Example usage in a component
const MyComponent = () => {
  const handleApiCall = async () => {
    try {
      const healthData = await checkApiHealth();
      console.log(healthData);
    } catch (error) {
      console.error(error);
    }
  };

  // Rest of component...
};
```

## Available API Methods

### `checkApiHealth()`

Checks if the backend API is operational.

- **Endpoint**: GET `/health`
- **Returns**: Health status object with `status`, `message`, and `version`
- **Error Handling**: Returns an error object with detailed information when connection fails

### `fetchExampleData()`

Retrieves example data from the backend.

- **Endpoint**: GET `/api/example`
- **Returns**: Example data object
- **Error Handling**: Returns an error object with detailed information when request fails

## Configuration

The API service is configured to connect to `http://localhost:3001` by default. This can be changed by modifying the `baseURL` in the axios instance configuration.

## Error Handling

The API service includes comprehensive error handling:

1. All API methods return structured error objects when requests fail
2. Error details include:
   - Error message
   - HTTP status code (when available)
   - Detailed description of the error

## Future Enhancements

- Authentication token management
- Request caching
- Request cancellation
- More specialized API endpoints
