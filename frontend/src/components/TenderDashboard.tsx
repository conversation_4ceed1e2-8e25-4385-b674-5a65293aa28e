import React, { useState, useEffect, ChangeEvent } from "react";
import {
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Box,
  CircularProgress,
  Alert,
  TextField,
  InputAdornment,
  IconButton,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Pagination,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import FilterListIcon from "@mui/icons-material/FilterList";
import RefreshIcon from "@mui/icons-material/Refresh";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import BusinessIcon from "@mui/icons-material/Business";
import AttachMoneyIcon from "@mui/icons-material/AttachMoney";
import CloseIcon from "@mui/icons-material/Close";

import { TenderService } from "../services/TenderService";
import {
  Tender,
  TenderStatus,
  TenderFilterOptions,
  TenderPagination,
} from "../types/tender";

const TenderDashboard = () => {
  // State
  const [tenders, setTenders] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);
  const [selectedTender, setSelectedTender] = React.useState(null);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [filterDialogOpen, setFilterDialogOpen] = React.useState(false);
  const [categories, setCategories] = React.useState<any[]>([]);
  const [issuers, setIssuers] = React.useState<any[]>([]);
  const [sources, setSources] = React.useState<any[]>([]);
  const [stats, setStats] = React.useState(null);

  // Pagination state
  const [pagination, setPagination] = React.useState({
    page: 1,
    limit: 10,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  });

  // Filter state
  const [filters, setFilters] = React.useState({
    search: "",
    status: undefined,
    category: undefined,
    issuer: undefined,
    source: undefined,
    fromDate: undefined,
    toDate: undefined,
    minConfidence: undefined,
  });
  const [sortBy, setSortBy] = React.useState("relevanceScore");
  const [sortOrder, setSortOrder] = React.useState("desc");

  // Create service instance
  const tenderService = new TenderService();

  // Fetch tenders and metadata on component mount
  useEffect(() => {
    fetchTenders();
    fetchMetadata();
  }, []);

  // Fetch tenders when filters or pagination changes
  useEffect(() => {
    fetchTenders();
  }, [filters, pagination.page, pagination.limit, sortBy, sortOrder]);

  // Fetch tenders from the API
  const fetchTenders = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await tenderService.getTenders(
        { ...filters, sortBy, sortOrder },
        pagination.page,
        pagination.limit
      );

      setTenders(result.tenders as any);
      setPagination(result.pagination as any);
    } catch (err) {
      setError("Failed to fetch tenders. Please try again later.");
      console.error("Error fetching tenders:", err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch metadata (categories, issuers, sources, stats)
  const fetchMetadata = async () => {
    try {
      const [categoriesData, issuersData, sourcesData, statsData] =
        await Promise.all([
          tenderService.getCategories(),
          tenderService.getIssuers(),
          tenderService.getSources(),
          tenderService.getStats(),
        ]);

      setCategories(categoriesData);
      setIssuers(issuersData);
      setSources(sourcesData);
      setStats(statsData);
    } catch (err) {
      console.error("Error fetching metadata:", err);
    }
  };

  // Refresh tenders
  const refreshTenders = async () => {
    setLoading(true);
    setError(null);

    try {
      await tenderService.refreshTenders();
      await fetchTenders();
      await fetchMetadata();
    } catch (err) {
      setError("Failed to refresh tenders. Please try again later.");
      console.error("Error refreshing tenders:", err);
    } finally {
      setLoading(false);
    }
  };

  // Handle tender click
  const handleTenderClick = (tender: Tender) => {
    setSelectedTender(tender);
    setDialogOpen(true);
  };

  // Handle dialog close
  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedTender(null);
  };

  // Handle filter dialog open
  const handleFilterDialogOpen = () => {
    setFilterDialogOpen(true);
  };

  // Handle filter dialog close
  const handleFilterDialogClose = () => {
    setFilterDialogOpen(false);
  };

  // Handle filter change
  const handleFilterChange = (
    event:
      | SelectChangeEvent<string>
      | React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const name = event.target.name;
    const value = event.target.value;

    if (!name) return; // Guard against undefined name

    setFilters((prev) => ({
      ...prev,
      [name]: value === "all" ? undefined : value,
    }));
  };

  // Handle date filter change
  const handleDateFilterChange = (name: string, value: string) => {
    if (!value) {
      setFilters((prev) => {
        const newFilters = { ...prev };
        delete newFilters[name as keyof TenderFilterOptions];
        return newFilters;
      });
      return;
    }

    setFilters((prev) => ({
      ...prev,
      [name]: new Date(value),
    }));
  };

  // Handle sort change
  const handleSortChange = (event: SelectChangeEvent<string>) => {
    setSortBy(event.target.value as any);
  };

  // Handle sort order change
  const handleSortOrderChange = (event: SelectChangeEvent<string>) => {
    setSortOrder(event.target.value as "asc" | "desc");
  };

  // Handle page change
  const handlePageChange = (_event: any, value: number) => {
    setPagination((prev) => ({
      ...prev,
      page: value,
    }));
  };

  // Format date
  const formatDate = (dateString?: Date) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Format currency
  const formatCurrency = (value?: number) => {
    if (!value) return "N/A";
    return new Intl.NumberFormat("en-ZA", {
      style: "currency",
      currency: "ZAR",
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Calculate days remaining
  const getDaysRemaining = (dateString?: Date) => {
    if (!dateString) return null;

    const closingDate = new Date(dateString);
    const today = new Date();

    // Reset time part for accurate day calculation
    today.setHours(0, 0, 0, 0 as any);
    closingDate.setHours(0, 0, 0, 0 as any);

    const diffTime = closingDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  };

  // Get color for days remaining chip
  const getDaysRemainingColor = (days: number | null) => {
    if (days === null) return "default";
    if (days < 0) return "error";
    if (days <= 7) return "warning";
    return "success";
  };

  // Get text for days remaining chip
  const getDaysRemainingText = (days: number | null) => {
    if (days === null) return "No date";
    if (days < 0) return "Closed";
    if (days === 0) return "Closing today";
    if (days === 1) return "1 day left";
    return `${days} days left`;
  };

  // Load tenders on component mount
  React.useEffect(() => {
    fetchTenders();
  }, []);

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom>
          Tender Opportunities
        </Typography>

        <Box>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={handleFilterDialogOpen}
            sx={{ mr: 1 }}
          >
            Filter
          </Button>
          <Button
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={refreshTenders}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Stats summary */}
      {stats && (
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="div">
                  Total Tenders
                </Typography>
                <Typography variant="h4" component="div">
                  {stats.totalCount}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="div">
                  Average Value
                </Typography>
                <Typography variant="h4" component="div">
                  {formatCurrency(stats.averageValue)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="div">
                  Closing Soon
                </Typography>
                <Typography variant="h4" component="div">
                  {stats.upcomingClosingCount}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="div">
                  New Tenders
                </Typography>
                <Typography variant="h4" component="div">
                  {stats.statusCounts?.NEW || 0}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {loading ? (
        <Box sx={{ display: "flex", justifyContent: "center", my: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      ) : tenders.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          No tenders found matching your criteria.
        </Alert>
      ) : (
        <>
          <Grid container spacing={3}>
            {tenders.map((tender) => (
              <Grid item xs={12} key={tender.id}>
                <Card>
                  <CardContent>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "flex-start",
                        mb: 1,
                      }}
                    >
                      <Typography variant="h6" component="h2" gutterBottom>
                        {tender.title}
                      </Typography>
                      <Chip
                        label={tender.status}
                        color={
                          tender.status === TenderStatus.NEW
                            ? "info"
                            : tender.status === TenderStatus.PROCESSING
                            ? "warning"
                            : tender.status === TenderStatus.REVIEWING
                            ? "secondary"
                            : tender.status === TenderStatus.SUBMITTED
                            ? "primary"
                            : tender.status === TenderStatus.WON
                            ? "success"
                            : tender.status === TenderStatus.LOST
                            ? "error"
                            : "default"
                        }
                        size="small"
                      />
                    </Box>

                    <Typography
                      variant="body2"
                      color="text.secondary"
                      gutterBottom
                    >
                      {tender.description.length > 200
                        ? `${tender.description.substring(0, 200)}...`
                        : tender.description}
                    </Typography>

                    <Box
                      sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 1 }}
                    >
                      <Chip
                        icon={<BusinessIcon />}
                        label={tender.issuer}
                        size="small"
                        variant="outlined"
                      />
                      {tender.category && (
                        <Chip
                          label={tender.category}
                          size="small"
                          variant="outlined"
                        />
                      )}
                      {tender.value && (
                        <Chip
                          icon={<AttachMoneyIcon />}
                          label={formatCurrency(tender.value)}
                          size="small"
                          variant="outlined"
                        />
                      )}
                    </Box>

                    <Divider sx={{ my: 1 }} />

                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                      }}
                    >
                      <Box>
                        {tender.publishDate && (
                          <Typography variant="body2" color="text.secondary">
                            <CalendarTodayIcon
                              fontSize="small"
                              sx={{ mr: 0.5, verticalAlign: "middle" }}
                            />
                            Published: {formatDate(tender.publishDate)}
                          </Typography>
                        )}
                      </Box>

                      <Box>
                        {tender.closingDate && (
                          <Chip
                            label={getDaysRemainingText(
                              getDaysRemaining(tender.closingDate)
                            )}
                            color={getDaysRemainingColor(
                              getDaysRemaining(tender.closingDate)
                            )}
                            size="small"
                          />
                        )}
                      </Box>
                    </Box>
                  </CardContent>

                  <CardActions>
                    <Button
                      size="small"
                      onClick={() => handleTenderClick(tender)}
                    >
                      View Details
                    </Button>
                    <Button
                      size="small"
                      endIcon={<OpenInNewIcon />}
                      href={tender.url}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      View Tender
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <Pagination
                count={pagination.totalPages}
                page={pagination.page}
                onChange={handlePageChange}
                color="primary"
              />
            </Box>
          )}
        </>
      )}

      {/* Tender Details Dialog */}
      {selectedTender && (
        <Dialog
          open={dialogOpen}
          onClose={handleDialogClose}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography variant="h6">{selectedTender.title}</Typography>
              <IconButton onClick={handleDialogClose}>
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent dividers>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="body1" paragraph>
                  {selectedTender.description}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Issuer
                </Typography>
                <Typography variant="body2" gutterBottom>
                  {selectedTender.issuer}
                </Typography>
              </Grid>

              {selectedTender.category && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Category
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    {selectedTender.category}
                  </Typography>
                </Grid>
              )}

              {selectedTender.reference && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Reference
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    {selectedTender.reference}
                  </Typography>
                </Grid>
              )}

              {selectedTender.publishDate && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Published Date
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    {formatDate(selectedTender.publishDate)}
                  </Typography>
                </Grid>
              )}

              {selectedTender.closingDate && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Closing Date
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    {formatDate(selectedTender.closingDate)} (
                    {getDaysRemainingText(
                      getDaysRemaining(selectedTender.closingDate)
                    )}
                    )
                  </Typography>
                </Grid>
              )}

              {selectedTender.value && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Estimated Value
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    {formatCurrency(selectedTender.value)}
                  </Typography>
                </Grid>
              )}

              {selectedTender.confidence !== undefined && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Relevance Score
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    {Math.round(selectedTender.confidence * 100)}%
                  </Typography>
                </Grid>
              )}

              {selectedTender.requirements &&
                selectedTender.requirements.length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Requirements
                    </Typography>
                    <ul>
                      {selectedTender.requirements.map((req, index) => (
                        <li key={index}>
                          <Typography variant="body2">{req}</Typography>
                        </li>
                      ))}
                    </ul>
                  </Grid>
                )}

              {selectedTender.documents &&
                selectedTender.documents.length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Documents
                    </Typography>
                    <ul>
                      {selectedTender.documents.map((doc, index) => (
                        <li key={index}>
                          <Typography variant="body2">
                            <a
                              href={doc.url}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              {doc.title}
                            </a>
                          </Typography>
                        </li>
                      ))}
                    </ul>
                  </Grid>
                )}
            </Grid>
          </DialogContent>
          <DialogActions>
            {selectedTender.url && (
              <Button
                endIcon={<OpenInNewIcon />}
                href={selectedTender.url}
                target="_blank"
                rel="noopener noreferrer"
                color="primary"
              >
                View Original Tender
              </Button>
            )}
            <Button onClick={handleDialogClose} color="primary">
              Close
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {/* Filter Dialog */}
      <Dialog
        open={filterDialogOpen}
        onClose={handleFilterDialogClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography variant="h6">Filter Tenders</Typography>
            <IconButton onClick={handleFilterDialogClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Search"
                name="search"
                value={filters.search || ""}
                onChange={handleFilterChange}
                placeholder="Search in title, description, or issuer"
                InputProps={{
                  startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />,
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={filters.status || "all"}
                  onChange={handleFilterChange}
                  label="Status"
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  {Object.values(TenderStatus).map((status) => (
                    <MenuItem key={status} value={status}>
                      {status}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  name="category"
                  value={filters.category || "all"}
                  onChange={handleFilterChange}
                  label="Category"
                >
                  <MenuItem value="all">All Categories</MenuItem>
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Issuer</InputLabel>
                <Select
                  name="issuer"
                  value={filters.issuer || "all"}
                  onChange={handleFilterChange}
                  label="Issuer"
                >
                  <MenuItem value="all">All Issuers</MenuItem>
                  {issuers.map((issuer) => (
                    <MenuItem key={issuer} value={issuer}>
                      {issuer}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Source</InputLabel>
                <Select
                  name="source"
                  value={filters.source || "all"}
                  onChange={handleFilterChange}
                  label="Source"
                >
                  <MenuItem value="all">All Sources</MenuItem>
                  {sources.map((source) => (
                    <MenuItem key={source} value={source}>
                      {source}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="From Date"
                type="date"
                value={
                  filters.fromDate
                    ? new Date(filters.fromDate).toISOString().split("T")[0]
                    : ""
                }
                onChange={(e) =>
                  handleDateFilterChange("fromDate", e.target.value)
                }
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="To Date"
                type="date"
                value={
                  filters.toDate
                    ? new Date(filters.toDate).toISOString().split("T")[0]
                    : ""
                }
                onChange={(e) =>
                  handleDateFilterChange("toDate", e.target.value)
                }
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Sort By</InputLabel>
                <Select
                  value={sortBy}
                  onChange={handleSortChange}
                  label="Sort By"
                >
                  <MenuItem value="relevanceScore">Relevance</MenuItem>
                  <MenuItem value="closingDate">Closing Date</MenuItem>
                  <MenuItem value="publishDate">Publish Date</MenuItem>
                  <MenuItem value="value">Value</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Sort Order</InputLabel>
                <Select
                  value={sortOrder}
                  onChange={handleSortOrderChange}
                  label="Sort Order"
                >
                  <MenuItem value="asc">Ascending</MenuItem>
                  <MenuItem value="desc">Descending</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setFilters({
                search: "",
                status: undefined,
                category: undefined,
                issuer: undefined,
                source: undefined,
                fromDate: undefined,
                toDate: undefined,
                minConfidence: undefined,
              });
              setSortBy("relevanceScore");
              setSortOrder("desc");
            }}
          >
            Clear Filters
          </Button>
          <Button onClick={handleFilterDialogClose} color="primary">
            Apply Filters
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default TenderDashboard;
