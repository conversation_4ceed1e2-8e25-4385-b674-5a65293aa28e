import { Box, useMediaQuery, useTheme } from '@mui/material';

interface ResponsiveWrapperProps {
  children: any;
  mobileComponent?: any;
}

/**
 * ResponsiveWrapper Component
 *
 * A utility component that renders different content based on screen size
 * If mobileComponent is provided, it will be rendered on mobile devices
 * Otherwise, the children will be rendered with appropriate styling
 */
const ResponsiveWrapper = ({
  children,
  mobileComponent
}: ResponsiveWrapperProps) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  // If a specific mobile component is provided and we're on mobile, render it
  if (isMobile && mobileComponent) {
    return <>{mobileComponent}</>;
  }

  // Otherwise, render the children with responsive styling
  return (
    <Box
      sx={{
        width: '100%',
        padding: isMobile ? 1 : isTablet ? 2 : 3,
        // Add responsive styling based on screen size
        '& .MuiCard-root': {
          mb: isMobile ? 1 : 2
        },
        '& .MuiTypography-h5': {
          fontSize: isMobile ? '1.2rem' : isTablet ? '1.4rem' : '1.5rem'
        },
        '& .MuiTypography-h6': {
          fontSize: isMobile ? '1rem' : isTablet ? '1.1rem' : '1.25rem'
        },
        '& .MuiButton-root': {
          fontSize: isMobile ? '0.8rem' : 'inherit',
          padding: isMobile ? '6px 12px' : 'inherit'
        },
        // Make tables responsive
        '& .MuiTable-root': {
          display: isMobile ? 'block' : 'table',
          overflowX: isMobile ? 'auto' : 'visible',
          '& .MuiTableCell-root': {
            padding: isMobile ? '8px 12px' : '16px'
          }
        },
        // Adjust grid layouts
        '& .MuiGrid-container': {
          '& .MuiGrid-item': {
            padding: isMobile ? 0.5 : isTablet ? 1 : 2
          }
        }
      }}
    >
      {children}
    </Box>
  );
};

export default ResponsiveWrapper;
