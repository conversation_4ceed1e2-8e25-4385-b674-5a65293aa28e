import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import InfoIcon from "@mui/icons-material/Info";
import PauseIcon from "@mui/icons-material/Pause";
import WarningIcon from "@mui/icons-material/Warning";
import { SvgIconProps } from "@mui/material";

type IconType = "success" | "error" | "warning" | "info" | "pause";

interface SafeIconProps {
  type: IconType;
  color?: SvgIconProps["color"];
  fontSize?: SvgIconProps["fontSize"];
}

/**
 * A component that safely renders icons based on a type string
 * This avoids the "Objects are not valid as a React child" error
 */
const SafeIcon = ({ type, color, fontSize  }: SafeIconProps) => {
  switch (type) {
    case "success":
      return <CheckCircleIcon color={color || "success"} fontSize={fontSize} />;
    case "error":
      return <ErrorIcon color={color || "error"} fontSize={fontSize} />;
    case "warning":
      return <WarningIcon color={color || "warning"} fontSize={fontSize} />;
    case "pause":
      return <PauseIcon color={color || "primary"} fontSize={fontSize} />;
    case "info":
    default:
      return <InfoIcon color={color || "info"} fontSize={fontSize} />;
  }
};

export default SafeIcon;
