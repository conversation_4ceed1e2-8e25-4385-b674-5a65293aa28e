import React from 'react';
import { Box } from '@mui/material';

/**
 * Props for the TabPanel component
 */
export interface TabPanelProps {
  children?: any;
  index: number;
  value: number;
}

/**
 * TabPanel component for displaying tab content
 * @param props TabPanelProps
 * @returns JSX.Element
 */
export const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

/**
 * Helper function to generate a11y props for tabs
 * @param index Tab index
 * @returns Object with aria-controls and id properties
 */
export const a11yProps = (index: number) => {
  return {
    id: `tab-${index}`,
    'aria-controls': `tabpanel-${index}`,
  };
}
