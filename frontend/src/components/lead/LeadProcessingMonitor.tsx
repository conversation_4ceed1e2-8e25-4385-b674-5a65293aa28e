import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import DeleteIcon from "@mui/icons-material/Delete";
import ErrorIcon from "@mui/icons-material/Error";
import HourglassEmptyIcon from "@mui/icons-material/HourglassEmpty";
import {
    Alert,
    Box,
    Chip,
    Divider,
    IconButton,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    Paper,
    Tooltip,
    Typography
} from "@mui/material";
import React, { useEffect } from "react";
import { io } from "socket.io-client";

// Define lead processing event interface
interface LeadProcessingEvent {
  status: "started" | "completed" | "error";
  leadId: string;
  companyName: string;
  timestamp?: string;
  result?: {
    analysisSummary?: string;
    sector?: string;
  };
  error?: string;
  additionalInfo?: string;
}

const SOCKET_URL = process.env.REACT_APP_SOCKET_URL || "http://localhost:3001";

const LeadProcessingMonitor = () => {
  const [leadEvents, setLeadEvents] = React.useState<any[]>([]);
  const [connected, setConnected] = React.useState(false);
  const [socket, setSocket] = React.useState(null);
  const [error, setError] = React.useState(null);

  // Set up Socket.IO connection
  useEffect(() => {
    const newSocket = io(SOCKET_URL, {
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    newSocket.on("connect", () => {
      console.log("Connected to Socket.IO server for lead processing");
      setConnected(true);
      setError(null);
    });

    newSocket.on("disconnect", () => {
      console.log("Disconnected from Socket.IO server");
      setConnected(false);
    });

    newSocket.on("connect_error", (err: any) => {
      console.error("Socket.IO connection error:", err);
      setConnected(false);
      setError(
        "Failed to connect to notification server. Please check your network connection."
      );
    });

    // Listen for lead processing events
    newSocket.on("leadProcessing", (data: LeadProcessingEvent) => {
      console.log("Received lead processing event:", data);

      // Add timestamp if not provided
      const eventWithTimestamp = {
        ...data,
        timestamp: data.timestamp || new Date().toISOString(),
      };

      // Update the lead events array
      setLeadEvents((prev) => [eventWithTimestamp, ...prev].slice(0, 50)); // Keep only the last 50 events
    });

    setSocket(newSocket);

    return () => {
      newSocket.disconnect();
    };
  }, []);

  // Clear all notifications
  const clearNotifications = () => {
    setLeadEvents([]);
  };

  // Get appropriate icon for event status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircleIcon color="success" />;
      case "error":
        return <ErrorIcon color="error" />;
      case "started":
      default:
        return <HourglassEmptyIcon color="primary" />;
    }
  };

  // Get appropriate color for event status
  const getStatusColor = (
    status: string
  ): "success" | "error" | "primary" | "default" => {
    switch (status) {
      case "completed":
        return "success";
      case "error":
        return "error";
      case "started":
        return "primary";
      default:
        return "default";
    }
  };

  // Format the timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      return new Date(timestamp).toLocaleString();
    } catch (e) {
      return timestamp;
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 2, mb: 4 }}>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Typography variant="h6" component="h2">
          Lead Processing Monitor
        </Typography>
        <Box>
          <Tooltip title="Connection Status">
            <Chip
              label={connected ? "Connected" : "Disconnected"}
              color={connected ? "success" : "error"}
              size="small"
              sx={{ mr: 1 }}
            />
          </Tooltip>
          <Tooltip title="Clear All Notifications">
            <IconButton onClick={clearNotifications} size="small">
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {!connected && !error && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          Attempting to connect to the notification server...
        </Alert>
      )}

      {leadEvents.length === 0 ? (
        <Alert severity="info">No lead processing events yet.</Alert>
      ) : (
        <List>
          {leadEvents.map((event, index) => (
            <React.Fragment key={`${event.leadId}-${event.status}-${index}`}>
              <ListItem alignItems="flex-start">
                <ListItemIcon>{getStatusIcon(event.status)}</ListItemIcon>
                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center">
                      <Typography variant="subtitle1" component="span">
                        {event.companyName}
                      </Typography>
                      <Chip
                        label={event.status}
                        color={getStatusColor(event.status)}
                        size="small"
                        sx={{ ml: 1 }}
                      />
                    </Box>
                  }
                  secondary={
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Lead ID: {event.leadId} |{" "}
                        {formatTimestamp(event.timestamp || "")}
                      </Typography>

                      {event.status === "completed" && event.result && (
                        <Box mt={1}>
                          <Typography variant="body2">
                            <strong>Sector:</strong> {event.result.sector}
                          </Typography>
                          {event.result.analysisSummary && (
                            <Typography variant="body2">
                              <strong>Analysis:</strong>{" "}
                              {event.result.analysisSummary.substring(0, 200)}
                              {event.result.analysisSummary.length > 200
                                ? "..."
                                : ""}
                            </Typography>
                          )}
                        </Box>
                      )}

                      {event.status === "error" && (
                        <Typography variant="body2" color="error.main">
                          <strong>Error:</strong>{" "}
                          {event.error || "Unknown error occurred"}
                          {event.additionalInfo && (
                            <>
                              <br />
                              {event.additionalInfo}
                            </>
                          )}
                        </Typography>
                      )}
                    </>
                  }
                />
              </ListItem>
              {index < leadEvents.length - 1 && (
                <Divider variant="inset" component="li" />
              )}
            </React.Fragment>
          ))}
        </List>
      )}
    </Paper>
  );
};

export default LeadProcessingMonitor;
