import React from "react";
import { Box, Typography, Container, Grid, Paper } from "@mui/material";
import { LeadProcessingMonitor } from "./index";

const LeadDashboard = () => {
  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Lead Management Dashboard
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <LeadProcessingMonitor />
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper
            sx={{
              p: 2,
              display: "flex",
              flexDirection: "column",
              height: 240,
            }}
          >
            <Typography variant="h6" component="h2" gutterBottom>
              Lead Processing Statistics
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Statistics and charts related to lead processing will be displayed
              here.
            </Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper
            sx={{
              p: 2,
              display: "flex",
              flexDirection: "column",
              height: 240,
            }}
          >
            <Typography variant="h6" component="h2" gutterBottom>
              Lead Sources
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Information about lead sources and acquisition channels will be
              displayed here.
            </Typography>
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" component="h2" gutterBottom>
              Recent Leads
            </Typography>
            <Typography variant="body2" color="text.secondary">
              A table of recently processed leads will be displayed here.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default LeadDashboard;
