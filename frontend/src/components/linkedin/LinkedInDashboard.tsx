import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import { TabPanel, a11yProps } from '../common/TabsNavigation';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Button,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tabs,
  Tab,
  CircularProgress,
  Autocomplete,
  Slider,
  FormControlLabel,
  Switch,
  Tooltip
} from '@mui/material';
import {
  LinkedIn as LinkedInIcon,
  Send as SendIcon,
  Refresh as RefreshIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FileCopy as FileCopyIcon,
  History as HistoryIcon,
  Person as PersonIcon,
  Group as GroupIcon,
  Settings as SettingsIcon,
  Lightbulb as LightbulbIcon,
  Add as AddIcon,
  Search as SearchIcon
} from '@mui/icons-material';

// Sample data
const industries = [
  'Water Treatment',
  'Cooling Systems',
  'Boiler Systems',
  'Industrial Manufacturing',
  'Power Generation',
  'Oil & Gas',
  'Food & Beverage',
  'Pharmaceuticals',
  'Municipal Water',
  'Wastewater Treatment'
];

const jobTitles = [
  'Facility Manager',
  'Plant Manager',
  'Operations Director',
  'Maintenance Manager',
  'Chief Engineer',
  'Sustainability Director',
  'EHS Manager',
  'Technical Director',
  'Procurement Manager',
  'CEO/Owner'
];

const messageTypes = [
  'Connection Request',
  'Initial Outreach',
  'Follow-up',
  'Meeting Request',
  'Content Share',
  'Testimonial Request'
];

const sampleMessages = [
  {
    id: 1,
    type: 'Connection Request',
    recipient: {
      name: 'John Smith',
      title: 'Facility Manager',
      company: 'Industrial Solutions Inc.',
      avatar: ''
    },
    content: "Hi John, I noticed your work in industrial facility management and thought we might benefit from connecting. I'm Zola from Mexel Energy Sustain, where we specialize in innovative water treatment solutions for industrial facilities. I'd love to connect and share insights about energy efficiency in cooling systems.",
    status: 'sent',
    sentDate: '2023-07-15',
    response: 'accepted'
  },
  {
    id: 2,
    type: 'Initial Outreach',
    recipient: {
      name: 'Sarah Johnson',
      title: 'Operations Director',
      company: 'Manufacturing Excellence',
      avatar: ''
    },
    content: "Hi Sarah, thanks for connecting! I noticed Manufacturing Excellence has been focusing on sustainability initiatives. At Mexel, we've developed water treatment technology that reduces energy consumption by up to 15% while extending equipment lifespan. Would you be interested in a case study showing how similar companies have implemented our solutions?",
    status: 'sent',
    sentDate: '2023-07-10',
    response: 'replied'
  },
  {
    id: 3,
    type: 'Follow-up',
    recipient: {
      name: 'Michael Chen',
      title: 'Plant Manager',
      company: 'Global Processing Ltd.',
      avatar: ''
    },
    content: "Hi Michael, I wanted to follow up on our conversation about your cooling system challenges. I've attached that case study I mentioned about how we helped a similar facility reduce their energy costs by 12%. Would you have 15 minutes this week to discuss if our approach might work for your facility?",
    status: 'draft',
    sentDate: '',
    response: ''
  }
];

interface TabPanelProps {
  children?: any;
  index: number;
  value: number;
}

export const LinkedInDashboard = () => {
  const [tabValue, setTabValue] = React.useState(0);
  const [messageType, setMessageType] = React.useState('');
  const [industry, setIndustry] = React.useState('');
  const [jobTitle, setJobTitle] = React.useState('');
  const [recipientName, setRecipientName] = React.useState('');
  const [recipientCompany, setRecipientCompany] = React.useState('');
  const [messageContent, setMessageContent] = React.useState('');
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [messages, setMessages] = React.useState(sampleMessages);
  const [selectedMessage, setSelectedMessage] = React.useState(null);
  const [messageLength, setMessageLength] = React.useState(2); // 1=Short, 2=Medium, 3=Long
  const [formality, setFormality] = React.useState(2); // 1=Casual, 2=Balanced, 3=Formal

  const handleTabChange = (_event: any, newValue: number) => {
    setTabValue(newValue);
  };

  const handleMessageTypeChange = (event: SelectChangeEvent) => {
    setMessageType(event.target.value);
  };

  const handleIndustryChange = (event: SelectChangeEvent) => {
    setIndustry(event.target.value);
  };

  const handleJobTitleChange = (event: SelectChangeEvent) => {
    setJobTitle(event.target.value);
  };

  const handleGenerateMessage = () => {
    if (!messageType || !recipientName) return;

    setIsGenerating(true);

    // Simulate API call to generate message
    setTimeout(() => {
      let generatedContent = '';

      if (messageType === 'Connection Request') {
        generatedContent = `Hi ${recipientName}, I noticed your work in ${industry || 'industrial water treatment'} and thought we might benefit from connecting. I'm Zola from Mexel Energy Sustain, where we specialize in innovative water treatment solutions for industrial facilities. I'd love to connect and share insights about energy efficiency in cooling systems.`;
      } else if (messageType === 'Initial Outreach') {
        generatedContent = `Hi ${recipientName}, thanks for connecting! I noticed ${recipientCompany || 'your company'} has been focusing on sustainability initiatives. At Mexel, we've developed water treatment technology that reduces energy consumption by up to 15% while extending equipment lifespan. Would you be interested in a case study showing how similar companies have implemented our solutions?`;
      } else if (messageType === 'Follow-up') {
        generatedContent = `Hi ${recipientName}, I wanted to follow up on our conversation about your cooling system challenges. I've attached that case study I mentioned about how we helped a similar facility reduce their energy costs by 12%. Would you have 15 minutes this week to discuss if our approach might work for your facility?`;
      } else {
        generatedContent = `Hi ${recipientName}, I'm reaching out from Mexel Energy Sustain regarding innovative water treatment solutions that could benefit ${recipientCompany || 'your company'}. Our technology has helped companies in the ${industry || 'industrial'} sector achieve significant energy savings and equipment lifespan extension. I'd love to share more details if you're interested.`;
      }

      setMessageContent(generatedContent);
      setIsGenerating(false);
    }, 1500);
  };

  const handleSaveMessage = () => {
    const newMessage = {
      id: Date.now(),
      type: messageType,
      recipient: {
        name: recipientName,
        title: jobTitle,
        company: recipientCompany,
        avatar: ''
      },
      content: messageContent,
      status: 'draft',
      sentDate: '',
      response: ''
    };

    setMessages([newMessage, ...messages]);
    setTabValue(0); // Switch to messages tab

    // Reset form
    setMessageType('');
    setIndustry('');
    setJobTitle('');
    setRecipientName('');
    setRecipientCompany('');
    setMessageContent('');
  };

  const handleSelectMessage = (message: any) => {
    setSelectedMessage(message);
    setMessageType(message.type);
    setRecipientName(message.recipient.name);
    setRecipientCompany(message.recipient.company);
    setJobTitle(message.recipient.title);
    setMessageContent(message.content);
    setTabValue(1); // Switch to compose tab
  };

  const handleCopyMessage = (message: any) => {
    navigator.clipboard.writeText(message.content);
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        LinkedIn Message Generation
      </Typography>

      <Paper sx={{ p: 3, mb: 4 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="linkedin dashboard tabs">
            <Tab label="Messages" id="linkedin-tab-0" aria-controls="linkedin-tabpanel-0" />
            <Tab label="Compose" id="linkedin-tab-1" aria-controls="linkedin-tabpanel-1" />
            <Tab label="Analytics" id="linkedin-tab-2" aria-controls="linkedin-tabpanel-2" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">LinkedIn Messages</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {
                setSelectedMessage(null);
                setMessageType('');
                setRecipientName('');
                setRecipientCompany('');
                setJobTitle('');
                setMessageContent('');
                setTabValue(1);
              }}
            >
              New Message
            </Button>
          </Box>

          <List>
            {messages.map((message) => (
              <Paper key={message.id} sx={{ mb: 2 }}>
                <ListItem
                  alignItems="flex-start"
                  secondaryAction={
                    <Box>
                      <Tooltip title="Copy">
                        <IconButton edge="end" aria-label="copy" onClick={() => handleCopyMessage(message)}>
                          <FileCopyIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton edge="end" aria-label="edit" onClick={() => handleSelectMessage(message)}>
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  }
                >
                  <ListItemAvatar>
                    <Avatar>
                      {message.recipient.name.charAt(0)}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {message.recipient.name}
                        <Chip
                          label={message.type}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                        {message.status === 'sent' && (
                          <Chip
                            label={message.response || 'Sent'}
                            size="small"
                            color={
                              message.response === 'accepted' ? 'success' :
                              message.response === 'replied' ? 'info' :
                              'default'
                            }
                          />
                        )}
                        {message.status === 'draft' && (
                          <Chip label="Draft" size="small" color="default" />
                        )}
                      </Box>
                    }
                    secondary={
                      <>
                        <Typography
                          component="span"
                          variant="body2"
                          color="text.primary"
                        >
                          {message.recipient.title} at {message.recipient.company}
                        </Typography>
                        {message.sentDate && (
                          <Typography
                            component="span"
                            variant="body2"
                            sx={{ display: 'block', color: 'text.secondary', fontSize: '0.8rem' }}
                          >
                            Sent: {message.sentDate}
                          </Typography>
                        )}
                        <Typography
                          component="span"
                          variant="body2"
                          sx={{ display: 'block', mt: 1 }}
                        >
                          {message.content.length > 100
                            ? `${message.content.substring(0, 100)}...`
                            : message.content}
                        </Typography>
                      </>
                    }
                  />
                </ListItem>
              </Paper>
            ))}
          </List>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Typography variant="h6" gutterBottom>
                {selectedMessage ? 'Edit Message' : 'Compose New LinkedIn Message'}
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel id="message-type-label">Message Type</InputLabel>
                    <Select
                      labelId="message-type-label"
                      value={messageType}
                      label="Message Type"
                      onChange={handleMessageTypeChange}
                    >
                      {messageTypes.map((type) => (
                        <MenuItem key={type} value={type}>{type}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel id="industry-label">Industry</InputLabel>
                    <Select
                      labelId="industry-label"
                      value={industry}
                      label="Industry"
                      onChange={handleIndustryChange}
                    >
                      {industries.map((ind) => (
                        <MenuItem key={ind} value={ind}>{ind}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Recipient Name"
                    fullWidth
                    value={recipientName}
                    onChange={(e) => setRecipientName(e.target.value)}
                    required
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Recipient Company"
                    fullWidth
                    value={recipientCompany}
                    onChange={(e) => setRecipientCompany(e.target.value)}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel id="job-title-label">Job Title</InputLabel>
                    <Select
                      labelId="job-title-label"
                      value={jobTitle}
                      label="Job Title"
                      onChange={handleJobTitleChange}
                    >
                      {jobTitles.map((title) => (
                        <MenuItem key={title} value={title}>{title}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <FormControl fullWidth>
                      <Typography gutterBottom>Message Length</Typography>
                      <Slider
                        value={messageLength}
                        min={1}
                        max={3}
                        step={1}
                        marks={[
                          { value: 1, label: 'Short' },
                          { value: 2, label: 'Medium' },
                          { value: 3, label: 'Long' }
                        ]}
                        onChange={(_, newValue) => setMessageLength(newValue as number)}
                      />
                    </FormControl>

                    <FormControl fullWidth>
                      <Typography gutterBottom>Formality</Typography>
                      <Slider
                        value={formality}
                        min={1}
                        max={3}
                        step={1}
                        marks={[
                          { value: 1, label: 'Casual' },
                          { value: 2, label: 'Balanced' },
                          { value: 3, label: 'Formal' }
                        ]}
                        onChange={(_, newValue) => setFormality(newValue as number)}
                      />
                    </FormControl>
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    label="Message Content"
                    multiline
                    rows={6}
                    fullWidth
                    value={messageContent}
                    onChange={(e) => setMessageContent(e.target.value)}
                    required
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Button
                      variant="outlined"
                      startIcon={<RefreshIcon />}
                      onClick={() => {
                        setMessageContent('');
                      }}
                    >
                      Clear
                    </Button>

                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Button
                        variant="outlined"
                        startIcon={isGenerating ? <CircularProgress size={20} /> : <LightbulbIcon />}
                        onClick={handleGenerateMessage}
                        disabled={isGenerating || !messageType || !recipientName}
                      >
                        {isGenerating ? 'Generating...' : 'Generate Message'}
                      </Button>

                      <Button
                        variant="contained"
                        startIcon={<SendIcon />}
                        onClick={handleSaveMessage}
                        disabled={!messageContent}
                      >
                        Save Message
                      </Button>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <LightbulbIcon color="warning" sx={{ mr: 1 }} />
                    <Typography variant="h6">LinkedIn Message Tips</Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="body2" paragraph>
                    <strong>1. Keep it concise</strong> - LinkedIn messages have a 300 character limit for connection requests and should be brief for other messages too.
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>2. Personalize</strong> - Reference something specific about the recipient's profile or company.
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>3. Provide value</strong> - Explain how connecting would benefit them, not just you.
                  </Typography>
                  <Typography variant="body2">
                    <strong>4. Clear CTA</strong> - Include a clear, low-commitment call to action.
                  </Typography>
                </CardContent>
              </Card>

              <Card sx={{ mt: 3 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <LinkedInIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="h6">LinkedIn Stats</Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Connection Rate:</Typography>
                    <Typography variant="body2" fontWeight="bold">68%</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Response Rate:</Typography>
                    <Typography variant="body2" fontWeight="bold">42%</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Meeting Conversion:</Typography>
                    <Typography variant="body2" fontWeight="bold">15%</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Messages Sent (30d):</Typography>
                    <Typography variant="body2" fontWeight="bold">87</Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>LinkedIn Analytics</Typography>
          <Typography variant="body1" color="textSecondary">
            Analytics dashboard coming soon. This will include message performance metrics, connection rates, and engagement statistics.
          </Typography>
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default LinkedInDashboard;
