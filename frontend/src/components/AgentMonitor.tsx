import { <PERSON><PERSON>, Container, Grid } from '@mui/material';
import { AgentMetrics, AnalyticsData } from '@shared/types/index';
import axios from 'axios';
import React, { useEffect } from 'react';
import { io, Socket } from 'socket.io-client';
import { TenderOpportunity } from '../types/dashboard';
import { AgentStatusCard } from './AgentStatusCard';
import { AnalyticsOverview } from './AnalyticsOverview';
import { TenderOpportunities } from './TenderOpportunities';

const SOCKET_URL = process.env.REACT_APP_SOCKET_URL || 'http://localhost:3001';
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';

export const AgentMonitor = () => {
  const [connected, setConnected] = React.useState(false);
  const [agents, setAgents] = React.useState<any[]>([]);
  const [analytics, setAnalytics] = React.useState({
    leads_generated: 0,
    website_traffic: 0,
    social_engagement: 0,
    tender_opportunities: 0,
    conversion_rate: 0,
    average_tender_score: 0,
    top_keywords: [],
    last_updated: new Date().toISOString()
  });
  const [opportunities, setOpportunities] = React.useState<any[]>([]);
  const [error, setError] = React.useState(null);

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        type TenderResponse = {
          results?: TenderOpportunity[];
        } | TenderOpportunity[];

        const [agentsRes, analyticsRes, tendersRes] = await Promise.all([
          axios.get<AgentMetrics[]>(`${API_URL}/api/agents`),
          axios.get<AnalyticsData>(`${API_URL}/api/analytics`),
          axios.get<TenderResponse>(`${API_URL}/api/tenders`)
        ]);

        setAgents(agentsRes.data as any);
        setAnalytics(analyticsRes.data as any);

        const tendersData = tendersRes.data;
        if (Array.isArray(tendersData)) {
          setOpportunities(tendersData);
        } else {
          setOpportunities(tendersData.results || []);
        }
      } catch (err) {
        setError('Failed to fetch dashboard data');
        console.error('Error fetching data:', err);
      }
    };

    fetchInitialData();
  }, []);

  useEffect(() => {
    const socket: Socket = io(SOCKET_URL, {
      transports: ['websocket', 'polling'],
      reconnectionDelay: 1000,
      reconnectionAttempts: 5,
      timeout: 20000
    });

    socket.on('connect', () => {
      setConnected(true);
      setError(null);
    });

    socket.on('disconnect', () => {
      setConnected(false);
    });

    socket.on('agentUpdate', (data: AgentMetrics) => {
      setAgents((prev: AgentMetrics[]) => {
        const index = prev.findIndex((a: AgentMetrics) => a.id === data.id);
        if (index === -1) return [...prev, data];
        const newAgents = [...prev];
        newAgents[index] = data;
        return newAgents;
      });
    });

    socket.on('analyticsUpdate', (data: AnalyticsData) => {
      setAnalytics(data);
    });

    socket.on('tenderUpdate', (data: TenderOpportunity[]) => {
      setOpportunities(data);
    });

    return () => {
      socket.disconnect();
    };
  }, []);

  return (
    <Container maxWidth="xl" sx={{ mt: 3, mb: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {!connected && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          Disconnected from server - real-time updates paused
        </Alert>
      )}

      <Grid container spacing={2}>
        <Grid item xs={12}>
          <AnalyticsOverview />
        </Grid>

        <Grid item xs={12}>
          <Grid container spacing={2}>
            {agents.map((agent) => (
              <Grid item xs={12} sm={6} md={4} key={agent.id}>
                <AgentStatusCard agent={agent} />
              </Grid>
            ))}
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <TenderOpportunities opportunities={opportunities} />
        </Grid>
      </Grid>
    </Container>
  );
}
