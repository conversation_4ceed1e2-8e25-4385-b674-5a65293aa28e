import {
  Alert,
  Box,
  Chip,
  CircularProgress,
  Grid,
  List,
  ListItem,
  ListItemText,
  Paper,
  Typography
} from '@mui/material';
import React, { useEffect } from 'react';

// Define types for our data
interface AnalyticsData {
  averageTenderScore: number;
  topKeywords: string[];
  lastUpdated: string;
}

interface TenderOpportunity {
  id: string;
  title: string;
  description: string;
  score: number;
  status: string;
  dueDate: string;
}

// Example component that correctly handles objects and arrays
const AgentMonitorExample = () => {
  // State for our data
  const [analytics, setAnalytics] = React.useState(null);
  const [opportunities, setOpportunities] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);

  // Simulate fetching data
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      try {
        // Mock data
        const mockAnalytics: AnalyticsData = {
          averageTenderScore: 78.5,
          topKeywords: ['water treatment', 'cooling tower', 'boiler'],
          lastUpdated: new Date().toISOString()
        };

        const mockOpportunities: TenderOpportunity[] = [
          {
            id: '1',
            title: 'Water Treatment System Maintenance',
            description: 'Regular maintenance of water treatment systems',
            score: 85,
            status: 'active',
            dueDate: '2023-12-31'
          },
          {
            id: '2',
            title: 'Cooling Tower Chemical Supply',
            description: 'Supply of chemicals for cooling tower treatment',
            score: 92,
            status: 'active',
            dueDate: '2023-11-15'
          }
        ];

        setAnalytics(mockAnalytics);
        setOpportunities(mockOpportunities);
        setLoading(false);
      } catch (err) {
        setError('Failed to load data');
        setLoading(false);
      }
    }, 1000);
  }, []);

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  // CORRECT: Render the component with proper handling of objects and arrays
  return (
    <Box className="agent-monitor-container" sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Agent Performance Dashboard
      </Typography>

      {/* CORRECT: Check if analytics exists before accessing its properties */}
      {analytics && (
        <Paper sx={{ p: 2, mb: 3 }} className="analytics-overview">
          <Typography variant="h6" gutterBottom>
            Analytics Overview
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Average Tender Score
                </Typography>
                <Typography variant="h4" color="primary.main">
                  {analytics.averageTenderScore}%
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={8}>
              <Typography variant="subtitle2" color="text.secondary">
                Top Keywords
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                {/* CORRECT: Map through the array to render each item */}
                {analytics.topKeywords.map((keyword: string, index: number) => (
                  <Chip key={index} label={keyword} size="small" />
                ))}
              </Box>
            </Grid>
          </Grid>
          <Typography variant="caption" display="block" sx={{ mt: 2, textAlign: 'right' }}>
            Last updated: {new Date(analytics.lastUpdated).toLocaleString()}
          </Typography>
        </Paper>
      )}

      <Paper sx={{ p: 2 }} className="tender-opportunities">
        <Typography variant="h6" gutterBottom>
          Tender Opportunities
        </Typography>

        {/* CORRECT: Check if opportunities array has items before rendering */}
        {opportunities.length > 0 ? (
          <List className="opportunities-list">
            {/* CORRECT: Map through the array to render each item */}
            {opportunities.map((opp) => (
              <ListItem key={opp.id} className="opportunity-item" divider>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="subtitle1">{opp.title}</Typography>
                      <Chip
                        label={`Score: ${opp.score}%`}
                        color={opp.score > 80 ? 'success' : 'default'}
                        size="small"
                      />
                    </Box>
                  }
                  secondary={
                    <>
                      <Typography variant="body2">{opp.description}</Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                        <Typography variant="caption">
                          Status: {opp.status.toUpperCase()}
                        </Typography>
                        <Typography variant="caption">
                          Due: {new Date(opp.dueDate).toLocaleDateString()}
                        </Typography>
                      </Box>
                    </>
                  }
                />
              </ListItem>
            ))}
          </List>
        ) : (
          <Alert severity="info">No tender opportunities found.</Alert>
        )}
      </Paper>
    </Box>
  );
};

export default AgentMonitorExample;
