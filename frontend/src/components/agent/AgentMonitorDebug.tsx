import React, { useState, useEffect, ReactElement, FC } from "react";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import WarningIcon from "@mui/icons-material/Warning";
import InfoIcon from "@mui/icons-material/Info";

// Define types for our data
interface AnalyticsData {
  averageTenderScore: number;
  topKeywords: string[];
  lastUpdated: string;
  // Add a field that might cause issues if not handled correctly
  statusIcon?: React.FC;
  // Add a field that might contain a React element
  statusElement?: ReactElement;
}

interface TenderOpportunity {
  id: string;
  title: string;
  description: string;
  score: number;
  status: string;
  dueDate: string;
  // Add a field that might cause issues if not handled correctly
  icon?: React.FC;
}

// Define some component functions that might cause issues if rendered directly
const SuccessIcon = () => <CheckCircleIcon color="success" />;
// These components are used in the examples below
// @ts-ignore - Used in commented code for demonstration purposes
const ErrorIconComponent = () => <ErrorIcon color="error" />;
// @ts-ignore - Used in commented code for demonstration purposes
const WarningIconComponent = () => <WarningIcon color="warning" />;

// Debug component that gradually builds up complexity
const AgentMonitorDebug = () => {
  // Step 1: Start with a simple static component
  // Uncomment state lines one by one as needed
  const [analytics, setAnalytics] = React.useState(null);
  const [opportunities, setOpportunities] = React.useState(
    []
  );
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);
  // Initialize to false to ensure we don't show the error-causing code
  const [showIncorrectRendering, setShowIncorrectRendering] =
    React.useState(false);

  // Add console log to track the value of showIncorrectRendering
  console.log(
    "AgentMonitorDebug: showIncorrectRendering =",
    showIncorrectRendering
  );

  // Create a component function inside the component
  // This component is used in the examples below when showIncorrect is true
  // @ts-ignore - Used in commented code for demonstration purposes
  const InfoIconComponent = () => <InfoIcon color="info" />;

  // Toggle incorrect rendering
  const toggleIncorrectRendering = () => {
    setShowIncorrectRendering(!showIncorrectRendering);
  };

  // Step 2: Add the useEffect hook
  useEffect(() => {
    console.log("AgentMonitorDebug: useEffect called");

    // Simulate API call
    setTimeout(() => {
      try {
        console.log("AgentMonitorDebug: Setting mock data");

        // Mock data with potentially problematic fields
        const mockAnalytics: AnalyticsData = {
          averageTenderScore: 78.5,
          topKeywords: ["water treatment", "cooling tower", "boiler"],
          lastUpdated: new Date().toISOString(),
          // Add a component function - this could cause issues if rendered directly
          statusIcon: SuccessIcon,
          // Add a React element - this should be fine to render directly
          statusElement: <InfoIcon color="info" />,
        };

        const mockOpportunities: TenderOpportunity[] = [
          {
            id: "1",
            title: "Water Treatment System Maintenance",
            description: "Regular maintenance of water treatment systems",
            score: 85,
            status: "active",
            dueDate: "2023-12-31",
            // Add a component function - this could cause issues if rendered directly
            icon: SuccessIcon,
          },
          {
            id: "2",
            title: "Cooling Tower Chemical Supply",
            description: "Supply of chemicals for cooling tower treatment",
            score: 92,
            status: "active",
            dueDate: "2023-11-15",
            // Add a component function - this could cause issues if rendered directly
            icon: WarningIconComponent,
          },
        ];

        setAnalytics(mockAnalytics);
        setOpportunities(mockOpportunities);
        setLoading(false);
      } catch (err: any) {
        console.error("AgentMonitorDebug: Error setting mock data", err);
        setError("Failed to load data");
        setLoading(false);
      }
    }, 1000);
  }, []);

  // Step 3: Add loading and error conditional returns
  if (loading) {
    return (
      <div className="loading-container">
        <p>Loading...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <p>Error: {error}</p>
      </div>
    );
  }

  // Step 4: Start with a simple static JSX structure
  return (
    <div className="agent-monitor-container">
      <h1>Agent Performance Dashboard</h1>

      {/* Add a button to toggle incorrect rendering */}
      <div style={{ marginBottom: "20px" }}>
        <button
          onClick={toggleIncorrectRendering}
          style={{
            padding: "10px 20px",
            backgroundColor: showIncorrectRendering ? "red" : "green",
            color: "white",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer",
          }}
        >
          {showIncorrectRendering
            ? "Hide Incorrect Rendering (Error Mode)"
            : "Show Incorrect Rendering (Error Mode)"}
        </button>
      </div>

      {/* This will cause an error when showIncorrectRendering is true */}
      {showIncorrectRendering && analytics && (
        <div
          style={{
            padding: "10px",
            backgroundColor: "#ffdddd",
            marginBottom: "20px",
          }}
        >
          <h3>Error Example:</h3>
          <p>
            This would cause an "Objects are not valid as a React child" error
            if uncommented:
          </p>
          {/* This line causes the error - we're commenting it out so the app can render */}
          {/* <p>Status Icon (wrong): {analytics.statusIcon}</p> */}

          <p>
            <strong>WRONG WAY (causes error):</strong>{" "}
            <code>{`<p>Status Icon (wrong): {analytics.statusIcon}</p>`}</code>
          </p>
          <p>
            <strong>RIGHT WAY:</strong>{" "}
            <code>{`<p>Status Icon (correct): {analytics.statusIcon && <analytics.statusIcon />}</p>`}</code>
          </p>
        </div>
      )}

      {/* Step 5: Add analytics section with conditional rendering */}
      <section className="analytics-overview">
        <h2>Analytics Overview</h2>
        {analytics && (
          <div className="stat-card">
            <p>Average Tender Score:</p>
            <h3>{analytics.averageTenderScore}%</h3>

            {/* Step 6: Add more analytics data gradually */}
            <p>Top Keywords:</p>
            <ul>
              {analytics.topKeywords.map((keyword, index) => (
                <li key={index}>{keyword}</li>
              ))}
            </ul>

            <p>Last Updated:</p>
            <p>{new Date(analytics.lastUpdated).toLocaleString()}</p>

            {/* CORRECT: Render the React element directly */}
            <p>Status Element (correct): {analytics.statusElement}</p>

            {/* CORRECT: Render the component function using JSX syntax */}
            <p>
              Status Icon (correct):{" "}
              {analytics.statusIcon && <analytics.statusIcon />}
            </p>

            {/* WRONG: This will cause an error - uncomment to test */}
            {/* <p>Status Icon (wrong): {analytics.statusIcon}</p> */}
          </div>
        )}
      </section>

      {/* Step 7: Add opportunities section with conditional rendering */}
      <section className="tender-opportunities">
        <h2>Tender Opportunities</h2>
        {opportunities.length > 0 ? (
          <ul className="opportunities-list">
            {opportunities.map((opp) => (
              <li key={opp.id} className="opportunity-item">
                <h3>{opp.title}</h3>
                <p>{opp.description}</p>
                <p>Score: {opp.score}%</p>
                <p>Status: {opp.status}</p>
                <p>Due Date: {new Date(opp.dueDate).toLocaleDateString()}</p>

                {/* CORRECT: Render the component function using JSX syntax */}
                <p>Icon (correct): {opp.icon && <opp.icon />}</p>

                {/* WRONG: This will cause an error - uncomment to test */}
                {/* <p>Icon (wrong): {opp.icon}</p> */}
              </li>
            ))}
          </ul>
        ) : (
          <p>No tender opportunities found.</p>
        )}
      </section>
    </div>
  );
};

export default AgentMonitorDebug;
