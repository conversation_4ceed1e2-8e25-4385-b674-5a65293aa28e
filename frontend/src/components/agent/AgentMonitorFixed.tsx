import {
    Al<PERSON>,
    Box,
    Chip,
    CircularProgress,
    Grid,
    List,
    ListItem,
    ListItemText,
    Paper,
    Typography
} from '@mui/material';
import React, { useEffect } from 'react';

// Define types for our data
interface AnalyticsData {
  averageTenderScore: number;
  totalTendersMonitored: number;
  topKeywords: string[];
  lastUpdated: string;
  scoreTrend?: { date: string; score: number }[];
}

interface TenderOpportunity {
  id: string;
  title: string;
  description: string;
  score: number;
  deadline: string;
  source: string;
}

// Example component that correctly handles objects and arrays
const AgentMonitorFixed = () => {
  // State for our data
  const [analytics, setAnalytics] = React.useState(null);
  const [opportunities, setOpportunities] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);

  // Simulate fetching data
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      try {
        // Mock data
        const mockAnalytics: AnalyticsData = {
          averageTenderScore: 78.5,
          totalTendersMonitored: 42,
          topKeywords: ['water treatment', 'cooling tower', 'boiler'],
          lastUpdated: new Date().toISOString(),
          scoreTrend: [
            { date: '2023-01-01', score: 75 },
            { date: '2023-02-01', score: 78 },
            { date: '2023-03-01', score: 76 },
            { date: '2023-04-01', score: 79 },
            { date: '2023-05-01', score: 82 },
          ]
        };

        const mockOpportunities: TenderOpportunity[] = [
          {
            id: '1',
            title: 'Water Treatment System Maintenance',
            description: 'Regular maintenance of water treatment systems',
            score: 85,
            deadline: '2023-12-31',
            source: 'eTenders'
          },
          {
            id: '2',
            title: 'Cooling Tower Chemical Supply',
            description: 'Supply of chemicals for cooling tower treatment',
            score: 92,
            deadline: '2023-11-15',
            source: 'Eskom'
          }
        ];

        setAnalytics(mockAnalytics);
        setOpportunities(mockOpportunities);
        setLoading(false);
      } catch (err) {
        setError('Failed to load data');
        setLoading(false);
      }
    }, 1000);
  }, []);

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  // CORRECT: Render the component with proper handling of objects and arrays
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Agent Performance Dashboard
      </Typography>

      {/* CORRECT: Check if analytics exists before accessing its properties */}
      {analytics && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Analytics Overview
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Average Tender Score
                </Typography>
                <Typography variant="h4" color="primary.main">
                  {analytics.averageTenderScore}%
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Total Tenders Monitored
                </Typography>
                <Typography variant="h4" color="primary.main">
                  {analytics.totalTendersMonitored}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="subtitle2" color="text.secondary">
                Top Keywords
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                {/* CORRECT: Map through the array to render each item */}
                {analytics.topKeywords.map((keyword: string, index: number) => (
                  <Chip key={index} label={keyword} size="small" />
                ))}
              </Box>
            </Grid>
          </Grid>
          <Typography variant="caption" display="block" sx={{ mt: 2, textAlign: 'right' }}>
            Last updated: {new Date(analytics.lastUpdated).toLocaleString()}
          </Typography>
        </Paper>
      )}

      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Tender Opportunities
        </Typography>

        {/* CORRECT: Check if opportunities array has items before rendering */}
        {opportunities.length > 0 ? (
          <List>
            {/* CORRECT: Map through the array to render each item */}
            {opportunities.map((opp) => (
              <ListItem key={opp.id} divider>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="subtitle1">{opp.title}</Typography>
                      <Chip
                        label={`Score: ${opp.score}%`}
                        color={opp.score > 80 ? 'success' : 'default'}
                        size="small"
                      />
                    </Box>
                  }
                  secondary={
                    <>
                      <Typography variant="body2">{opp.description}</Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                        <Typography variant="caption">
                          Source: {opp.source}
                        </Typography>
                        <Typography variant="caption">
                          Deadline: {new Date(opp.deadline).toLocaleDateString()}
                        </Typography>
                      </Box>
                    </>
                  }
                />
              </ListItem>
            ))}
          </List>
        ) : (
          <Alert severity="info">No tender opportunities found.</Alert>
        )}
      </Paper>
    </Box>
  );
};

export default AgentMonitorFixed;
