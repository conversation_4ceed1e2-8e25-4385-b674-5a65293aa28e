import React from 'react';
import { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Button,
  AlertProps,
} from "@mui/material";
import axios from "axios";
import LeadProcessingMonitor from "../lead/LeadProcessingMonitor";

// Define agent types
interface Agent {
  id: string;
  name: string;
  type: string;
  status: string;
  lastActive: string;
  successRate: number;
  errorRate: number;
}

// Define tab panel props
interface TabPanelProps {
  children?: any;
  index: number;
  value: number;
}

// Tab Panel component
const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`agent-tabpanel-${index}`}
      aria-labelledby={`agent-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

// Main Agent Dashboard component
const AgentDashboard = () => {
  // State for agents data
  const [agents, setAgents] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);
  const [activeTab, setActiveTab] = React.useState(0);

  // Fetch agents data
  useEffect(() => {
    const fetchAgents = async () => {
      try {
        setLoading(true);
        // For now, use mock data
        const mockAgents: Agent[] = [
          {
            id: "1",
            name: "TenderMonitor",
            type: "Monitor",
            status: "Running",
            lastActive: new Date().toISOString(),
            successRate: 95.2,
            errorRate: 4.8,
          },
          {
            id: "2",
            name: "OutreachEmail",
            type: "Email",
            status: "Idle",
            lastActive: new Date().toISOString(),
            successRate: 88.7,
            errorRate: 11.3,
          },
          {
            id: "3",
            name: "Analytics",
            type: "Analytics",
            status: "Running",
            lastActive: new Date().toISOString(),
            successRate: 92.5,
            errorRate: 7.5,
          },
          {
            id: "4",
            name: "Coordinator",
            type: "Coordinator",
            status: "Running",
            lastActive: new Date().toISOString(),
            successRate: 97.8,
            errorRate: 2.2,
          },
        ];

        setAgents(mockAgents);
        setLoading(false);
      } catch (err) {
        console.error("Error fetching agents:", err);
        setError("Failed to load agents data. Please try again later.");
        setLoading(false);
      }
    };

    fetchAgents();
  }, []);

  // Handle tab change
  const handleTabChange = (event: any, newValue: number) => {
    setActiveTab(newValue);
  };

  // Get status color
  const getStatusColor = (
    status: string
  ): "success" | "error" | "warning" | "default" => {
    switch (status.toLowerCase()) {
      case "running":
        return "success";
      case "error":
        return "error";
      case "idle":
        return "warning";
      default:
        return "default";
    }
  };

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ width: "100%" }}>
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          aria-label="agent dashboard tabs"
        >
          <Tab label="Agent Status" />
          <Tab label="Real-Time Updates" />
          <Tab label="Lead Processing" />
          <Tab label="Performance" />
        </Tabs>
      </Box>

      {/* Agent Status Tab */}
      <TabPanel value={activeTab} index={0}>
        <Typography variant="h6" gutterBottom>
          Agent Status Overview
        </Typography>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Last Active</TableCell>
                <TableCell align="right">Success Rate</TableCell>
                <TableCell align="right">Error Rate</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {agents.map((agent) => (
                <TableRow key={agent.id}>
                  <TableCell>{agent.name}</TableCell>
                  <TableCell>{agent.type}</TableCell>
                  <TableCell>
                    <Chip
                      label={agent.status}
                      color={getStatusColor(agent.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(agent.lastActive).toLocaleTimeString()}
                  </TableCell>
                  <TableCell align="right">
                    {agent.successRate.toFixed(1)}%
                  </TableCell>
                  <TableCell align="right">
                    {agent.errorRate.toFixed(1)}%
                  </TableCell>
                  <TableCell>
                    <Button size="small" variant="outlined">
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Real-Time Updates Tab */}
      <TabPanel value={activeTab} index={1}>
        <Typography variant="h6" gutterBottom>
          Real-Time Agent Updates
        </Typography>
        <Alert severity="info" icon={false}>
          Real-time updates will appear here as agents perform their tasks.
        </Alert>
      </TabPanel>

      {/* Lead Processing Tab */}
      <TabPanel value={activeTab} index={2}>
        <Typography variant="h6" gutterBottom>
          Lead Processing Monitor
        </Typography>
        <LeadProcessingMonitor />
      </TabPanel>

      {/* Performance Tab */}
      <TabPanel value={activeTab} index={3}>
        <Typography variant="h6" gutterBottom>
          Agent Performance Metrics
        </Typography>
        <Alert severity="info">
          Performance charts and metrics will be displayed here.
        </Alert>
      </TabPanel>
    </Box>
  );
};

export default AgentDashboard;
