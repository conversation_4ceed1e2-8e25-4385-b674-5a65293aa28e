import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  Typography, 
  Grid, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  Paper,
  Chip,
  Box,
  Tabs,
  Tab,
  Alert,
  TextField,
  IconButton,
  InputAdornment,
  CircularProgress
} from '@mui/material';
import { ArrowUpward, ArrowDownward, Remove, Search, Refresh } from '@mui/icons-material';
import { MarketData, MarketAnalysis, MarketInsight } from '../types/market';

interface MarketToolProps {
  showRelatedTenders?: boolean;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`market-tabpanel-${index}`}
      aria-labelledby={`market-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export const MarketTool: React.FC<MarketToolProps> = ({ showRelatedTenders = true }) => {
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [marketAnalyses, setMarketAnalyses] = useState<MarketAnalysis[]>([]);
  const [marketInsights, setMarketInsights] = useState<MarketInsight[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [apiLimitReached, setApiLimitReached] = useState<boolean>(false);
  const [tabValue, setTabValue] = useState(0);
  const [symbolSearch, setSymbolSearch] = useState<string>('');
  const [defaultSymbols] = useState<string[]>(['MSFT', 'AAPL', 'GOOGL', 'AMZN']);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSymbolSearch = (event: React.FormEvent) => {
    event.preventDefault();
    if (symbolSearch.trim()) {
      fetchData([symbolSearch.trim().toUpperCase()]);
    }
  };

  const handleRefresh = () => {
    fetchData(defaultSymbols);
  };

  // Function to provide mock data when API limit is reached
  const provideMockData = (symbols: string[]) => {
    // Create mock market data
    const mockMarketData: MarketData[] = symbols.map((symbol) => {
      const isUp = Math.random() > 0.4;
      const changePercent = isUp ? 
        (Math.random() * 5).toFixed(2) : 
        (-Math.random() * 5).toFixed(2);
      
      return {
        id: symbol,
        name: symbol,
        category: 'Technology',
        currentPrice: 100 + Math.random() * 500,
        previousPrice: 100 + Math.random() * 500,
        changePercentage: parseFloat(changePercent),
        volume: Math.floor(Math.random() * 10000000),
        marketCap: Math.floor(Math.random() * **********),
        lastUpdated: new Date().toISOString(),
        trend: isUp ? 'up' : (parseFloat(changePercent) === 0 ? 'stable' : 'down'),
        relatedTenders: []
      };
    });

    // Create mock analyses
    const sectors = ['Technology', 'Healthcare', 'Energy', 'Finance', 'Consumer Goods'];
    const mockAnalyses: MarketAnalysis[] = sectors.map(sector => {
      const growth = (Math.random() * 10 - 5).toFixed(1);
      
      let sentiment: 'bullish' | 'bearish' | 'neutral';
      if (parseFloat(growth) > 0) sentiment = 'bullish';
      else if (parseFloat(growth) < 0) sentiment = 'bearish';
      else sentiment = 'neutral';
      
      return {
        sector,
        growth: parseFloat(growth),
        volatility: Math.random() * 5,
        sentiment,
        recommendation: `Mock analysis for ${sector}`,
        potentialImpact: Math.random() * 10,
        lastUpdated: new Date().toISOString()
      };
    });

    return { marketData: mockMarketData, analyses: mockAnalyses };
  };

  const fetchData = async (symbols: string[] = defaultSymbols) => {
    try {
      setLoading(true);
      setError(null);
      setApiLimitReached(false);
      
      // Fetch quotes for all symbols
      const quotePromises = symbols.map(symbol => 
        fetch(`http://localhost:3001/api/market/quote/${symbol}`)
          .then(res => {
            if (!res.ok) throw new Error(`Failed to fetch quote for ${symbol}`);
            return res.json();
          })
      );
      
      // Fetch sector performance for analysis
      const sectorPromise = fetch('http://localhost:3001/api/market/sectors')
        .then(res => {
          if (!res.ok) throw new Error('Failed to fetch sector performance');
          return res.json();
        });
      
      // Wait for all data to be fetched
      const quotesData = await Promise.all(quotePromises);
      const sectorsData = await sectorPromise;

      // Check for API limit errors
      const hasApiLimitError = quotesData.some(data => 
        data.Note && data.Note.includes('API call frequency')
      );

      if (hasApiLimitError) {
        setApiLimitReached(true);
        // Use mock data for demo purposes
        const mockResult = provideMockData(symbols);
        setMarketData(mockResult.marketData);
        setMarketAnalyses(mockResult.analyses);
      } else {
        // Transform quotes data to our MarketData format
        const transformedData: MarketData[] = quotesData.map((response, index) => {
          const symbol = symbols[index];
          const quote = response['Global Quote'] || {};
          const changeValue = parseFloat(quote['09. change'] || '0');
          
          // Ensure trend is one of the required literal types
          let trend: 'up' | 'down' | 'stable';
          if (changeValue > 0) trend = 'up';
          else if (changeValue < 0) trend = 'down';
          else trend = 'stable';
          
          return {
            id: symbol,
            name: symbol,
            category: 'Technology',
            currentPrice: parseFloat(quote['05. price']) || 0,
            previousPrice: parseFloat(quote['08. previous close']) || 0,
            changePercentage: parseFloat((quote['10. change percent'] || '0%').replace('%', '')) || 0,
            volume: parseInt(quote['06. volume'] || '0'),
            marketCap: 0, // Not available from this API
            lastUpdated: quote['07. latest trading day'] || new Date().toISOString(),
            trend,
            relatedTenders: []
          };
        });
        
        // Transform sectors data to our MarketAnalysis format
        const sectorPerformance = sectorsData['Rank A: Real-Time Performance'] || {};
        const transformedAnalyses: MarketAnalysis[] = Object.entries(sectorPerformance).map(([sector, performance]) => {
          const perfValue = parseFloat((performance as string).replace('%', '')) || 0;
          
          // Ensure sentiment is one of the required literal types
          let sentiment: 'bullish' | 'bearish' | 'neutral';
          if (perfValue > 0) sentiment = 'bullish';
          else if (perfValue < 0) sentiment = 'bearish';
          else sentiment = 'neutral';
          
          return {
            sector,
            growth: perfValue,
            volatility: Math.abs(perfValue) / 2, // Simplified estimation
            sentiment,
            recommendation: perfValue > 1 ? 'Consider investment opportunities' :
                          perfValue < -1 ? 'Monitor closely for stabilization' : 
                          'Maintain current positions',
            potentialImpact: Math.min(10, Math.abs(perfValue) * 2),
            lastUpdated: new Date().toISOString()
          };
        });

        setMarketData(transformedData);
        setMarketAnalyses(transformedAnalyses);
      }
      
      // Create some insights based on the data
      const insights: MarketInsight[] = [
        {
          title: 'Market Trend Analysis',
          description: 'Technology stocks are showing strong momentum with cloud services leading the growth.',
          impact: 'high',
          source: 'Market Analysis Tool',
          date: new Date().toISOString(),
          relatedMarkets: ['MSFT', 'AMZN', 'GOOGL']
        },
        {
          title: 'Sector Rotation Alert',
          description: 'Investors are moving from growth to value stocks indicating a potential shift in market sentiment.',
          impact: 'medium',
          source: 'Financial Trends',
          date: new Date().toISOString(),
          relatedMarkets: ['AAPL', 'MSFT']
        }
      ];
      
      setMarketInsights(insights);
    } catch (err) {
      console.error('Error fetching market data:', err);
      setError('Failed to load market data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const renderTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <ArrowUpward color="success" fontSize="small" />;
      case 'down':
        return <ArrowDownward color="error" fontSize="small" />;
      default:
        return <Remove color="action" fontSize="small" />;
    }
  };

  const renderSentimentChip = (sentiment: 'bullish' | 'bearish' | 'neutral') => {
    switch (sentiment) {
      case 'bullish':
        return <Chip size="small" label="Bullish" color="success" />;
      case 'bearish':
        return <Chip size="small" label="Bearish" color="error" />;
      default:
        return <Chip size="small" label="Neutral" color="default" />;
    }
  };

  const renderImpactChip = (impact: 'high' | 'medium' | 'low') => {
    switch (impact) {
      case 'high':
        return <Chip size="small" label="High Impact" color="error" />;
      case 'medium':
        return <Chip size="small" label="Medium Impact" color="warning" />;
      default:
        return <Chip size="small" label="Low Impact" color="info" />;
    }
  };

  if (loading) {
    return (
      <Card sx={{ m: 1, minHeight: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <CardContent>
          <CircularProgress size={40} />
          <Typography sx={{ mt: 2 }}>Loading market data...</Typography>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card sx={{ m: 1, minHeight: 200, bgcolor: '#ffebee' }}>
        <CardContent>
          <Typography color="error" variant="h6">Error</Typography>
          <Typography>{error}</Typography>
          <Box sx={{ mt: 2 }}>
            <IconButton color="primary" onClick={handleRefresh}>
              <Refresh /> Retry
            </IconButton>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ m: 1 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Market Intelligence</Typography>
          
          <Box component="form" onSubmit={handleSymbolSearch} sx={{ display: 'flex', alignItems: 'center' }}>
            <TextField
              size="small"
              placeholder="Stock Symbol"
              value={symbolSearch}
              onChange={(e) => setSymbolSearch(e.target.value)}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton type="submit" edge="end">
                      <Search />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <IconButton onClick={handleRefresh} sx={{ ml: 1 }}>
              <Refresh />
            </IconButton>
          </Box>
        </Box>
        
        {apiLimitReached && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            API call limit reached. Showing demo data instead.
          </Alert>
        )}
        
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="market tool tabs">
            <Tab label="Market Data" id="market-tab-0" aria-controls="market-tabpanel-0" />
            <Tab label="Analyses" id="market-tab-1" aria-controls="market-tabpanel-1" />
            <Tab label="Insights" id="market-tab-2" aria-controls="market-tabpanel-2" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <TableContainer component={Paper}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Market</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell align="right">Price</TableCell>
                  <TableCell align="right">Change %</TableCell>
                  <TableCell align="right">Trend</TableCell>
                  {showRelatedTenders && <TableCell>Related Tenders</TableCell>}
                </TableRow>
              </TableHead>
              <TableBody>
                {marketData.map((market) => (
                  <TableRow key={market.id}>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        {market.name}
                      </Typography>
                    </TableCell>
                    <TableCell>{market.category}</TableCell>
                    <TableCell align="right">${market.currentPrice.toLocaleString()}</TableCell>
                    <TableCell 
                      align="right"
                      sx={{ 
                        color: market.changePercentage > 0 ? 'success.main' : 
                               market.changePercentage < 0 ? 'error.main' : 'text.primary'
                      }}
                    >
                      {market.changePercentage > 0 ? '+' : ''}{market.changePercentage.toFixed(2)}%
                    </TableCell>
                    <TableCell align="right">{renderTrendIcon(market.trend)}</TableCell>
                    {showRelatedTenders && (
                      <TableCell>
                        {market.relatedTenders && market.relatedTenders.length > 0 ? (
                          <Typography variant="body2" color="primary">
                            {market.relatedTenders.length} related
                          </Typography>
                        ) : (
                          <Typography variant="body2" color="text.secondary">None</Typography>
                        )}
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={2}>
            {marketAnalyses.map((analysis) => (
              <Grid item xs={12} sm={6} md={4} key={analysis.sector}>
                <Paper 
                  elevation={1} 
                  sx={{ 
                    p: 2,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column'
                  }}
                >
                  <Typography variant="subtitle1" gutterBottom>{analysis.sector}</Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Growth:</Typography>
                    <Typography 
                      variant="body2"
                      sx={{ 
                        color: analysis.growth > 0 ? 'success.main' : 
                               analysis.growth < 0 ? 'error.main' : 'text.primary'
                      }}
                    >
                      {analysis.growth > 0 ? '+' : ''}{analysis.growth.toFixed(1)}%
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Volatility:</Typography>
                    <Typography variant="body2">{analysis.volatility.toFixed(1)}%</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="body2">Sentiment:</Typography>
                    {renderSentimentChip(analysis.sentiment)}
                  </Box>
                  <Typography variant="body2" sx={{ mb: 2 }}>{analysis.recommendation}</Typography>
                  <Box sx={{ mt: 'auto' }}>
                    <Typography variant="caption" color="text.secondary">
                      Last updated: {new Date(analysis.lastUpdated).toLocaleDateString()}
                    </Typography>
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={2}>
            {marketInsights.map((insight) => (
              <Grid item xs={12} key={insight.title}>
                <Paper elevation={1} sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle1">{insight.title}</Typography>
                    {renderImpactChip(insight.impact)}
                  </Box>
                  <Typography variant="body2" paragraph>{insight.description}</Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="caption" color="text.secondary">
                      Source: {insight.source}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(insight.date).toLocaleDateString()}
                    </Typography>
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </TabPanel>
      </CardContent>
    </Card>
  );
};
