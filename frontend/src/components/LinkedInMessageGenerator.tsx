import {
    Alert,
    Box,
    <PERSON>ton,
    Card,
    CardContent,
    Chip,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Divider,
    FormControl,
    Grid,
    IconButton,
    InputLabel,
    MenuItem,
    Paper,
    Select,
    SelectChangeEvent,
    TextField,
    Tooltip,
    Typography,
} from "@mui/material";
import React, { KeyboardEvent } from "react";

import AddIcon from "@mui/icons-material/Add";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import DeleteIcon from "@mui/icons-material/Delete";
import ImageIcon from "@mui/icons-material/Image";
import LinkedInIcon from "@mui/icons-material/LinkedIn";
import SaveIcon from "@mui/icons-material/Save";


// Define interfaces for LinkedIn message generation
interface LinkedInMessageTemplate {
  id: string;
  title: string;
  content: string;
  imagePrompt?: string;
  tags: string[];
  tone: string;
  targetAudience: string;
  type: "post" | "message" | "comment";
  createdAt: Date;
}

interface GenerationOptions {
  topic: string;
  keywords: string[];
  tone: string;
  targetAudience: string;
  type: "post" | "message" | "comment";
  includeImagePrompt: boolean;
  length: "short" | "medium" | "long";
}

// Add these type definitions near the top of the file, after the interfaces
type MessageType = "post" | "message" | "comment";
type ContentLength = "short" | "medium" | "long";

/**
 * LinkedIn Message Generator Component
 *
 * Allows users to generate LinkedIn content with AI assistance
 */
export const LinkedInMessageGenerator = () => {
  // State for form inputs
  const [topic, setTopic] = React.useState("");
  const [keywords, setKeywords] = React.useState<any[]>([]);
  const [currentKeyword, setCurrentKeyword] = React.useState("");
  const [tone, setTone] = React.useState("professional");
  const [targetAudience, setTargetAudience] = React.useState(
    "industry professionals"
  );
  const [messageType, setMessageType] =
    React.useState("post");
  const [contentLength, setContentLength] =
    React.useState("medium");
  const [includeImagePrompt, setIncludeImagePrompt] =
    React.useState(true);

  // State for generated content
  const [generatedContent, setGeneratedContent] = React.useState(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState(null);

  // State for saved templates
  const [savedTemplates, setSavedTemplates] = React.useState<any[]>([]);
  const [showTemplatesDialog, setShowTemplatesDialog] = React.useState(false);
  const [selectedTemplate, setSelectedTemplate] = React.useState(null);

  // Handle form submission
  const handleGenerate = async () => {
    if (!topic) {
      setError("Please enter a topic");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // In a real implementation, this would call an API
      // For now, we'll simulate with a timeout and mock data
      setTimeout(() => {
        const options: GenerationOptions = {
          topic,
          keywords,
          tone,
          targetAudience,
          type: messageType as "post" | "message" | "comment",
          includeImagePrompt,
          length: contentLength as "short" | "medium" | "long",
        };

        const content = generateMockLinkedInContent(options);
        setGeneratedContent(content);
        setLoading(false);
      }, 2000);
    } catch (err) {
      setError("Failed to generate content. Please try again.");
      setLoading(false);
    }
  };

  // Handle adding keywords
  const handleAddKeyword = () => {
    if (currentKeyword && !keywords.includes(currentKeyword)) {
      setKeywords([...keywords, currentKeyword] as any);
      setCurrentKeyword("");
    }
  };

  // Handle removing keywords
  const handleRemoveKeyword = (keywordToRemove: string) => {
    setKeywords(keywords.filter((keyword) => keyword !== keywordToRemove));
  };

  // Handle keypress in keyword input
  const handleKeywordKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddKeyword();
    }
  };

  // Handle saving a template
  const handleSaveTemplate = () => {
    if (generatedContent) {
      const newTemplate = {
        ...generatedContent,
        id: `template-${Date.now()}`,
        createdAt: new Date(),
      };
      setSavedTemplates([...savedTemplates, newTemplate] as any);
    }
  };

  // Handle loading a template
  const handleLoadTemplate = (template: LinkedInMessageTemplate) => {
    setGeneratedContent(template);
    setShowTemplatesDialog(false);
  };

  // Handle deleting a template
  const handleDeleteTemplate = (templateId: string) => {
    setSavedTemplates(
      savedTemplates.filter((template) => template.id !== templateId)
    );
  };

  // Handle copying content to clipboard
  const handleCopyContent = () => {
    if (generatedContent) {
      navigator.clipboard.writeText(generatedContent.content);
    }
  };

  return (
    <Box sx={{ width: "100%" }}>
      <Typography variant="h5" component="h1" gutterBottom>
        LinkedIn Content Generator
      </Typography>

      <Grid container spacing={3}>
        {/* Form Section */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Generate New Content
              </Typography>

              <TextField
                label="Topic"
                fullWidth
                margin="normal"
                value={topic}
                onChange={(e) => setTopic(e.target.value as any)}
                placeholder="e.g., Benefits of Film-Forming Amines in Water Treatment"
              />

              <Box sx={{ display: "flex", alignItems: "center", mt: 2, mb: 1 }}>
                <TextField
                  label="Keywords"
                  value={currentKeyword}
                  onChange={(e) => setCurrentKeyword(e.target.value as any)}
                  onKeyPress={handleKeywordKeyPress}
                  placeholder="Add keywords and press Enter"
                  sx={{ flexGrow: 1, mr: 1 }}
                />
                <Button
                  variant="outlined"
                  onClick={handleAddKeyword}
                  disabled={!currentKeyword}
                >
                  <AddIcon />
                </Button>
              </Box>

              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 2 }}>
                {keywords.map((keyword, index) => (
                  <Chip
                    key={index}
                    label={keyword}
                    onDelete={() => handleRemoveKeyword(keyword)}
                  />
                ))}
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Tone</InputLabel>
                    <Select
                      value={tone}
                      label="Tone"
                      onChange={(e) => setTone(e.target.value as any)}
                    >
                      <MenuItem value="professional">Professional</MenuItem>
                      <MenuItem value="conversational">Conversational</MenuItem>
                      <MenuItem value="educational">Educational</MenuItem>
                      <MenuItem value="persuasive">Persuasive</MenuItem>
                      <MenuItem value="technical">Technical</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Target Audience</InputLabel>
                    <Select
                      value={targetAudience}
                      label="Target Audience"
                      onChange={(e) => setTargetAudience(e.target.value as any)}
                    >
                      <MenuItem value="industry professionals">
                        Industry Professionals
                      </MenuItem>
                      <MenuItem value="technical decision makers">
                        Technical Decision Makers
                      </MenuItem>
                      <MenuItem value="plant managers">Plant Managers</MenuItem>
                      <MenuItem value="executives">Executives</MenuItem>
                      <MenuItem value="engineers">Engineers</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Content Type</InputLabel>
                    <Select<MessageType>
                      value={messageType}
                      label="Content Type"
                      onChange={(e: SelectChangeEvent<MessageType>) =>
                        setMessageType(e.target.value as MessageType)
                      }
                    >
                      <MenuItem value="post">LinkedIn Post</MenuItem>
                      <MenuItem value="message">Direct Message</MenuItem>
                      <MenuItem value="comment">Comment</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Length</InputLabel>
                    <Select<ContentLength>
                      value={contentLength}
                      label="Length"
                      onChange={(e: SelectChangeEvent<ContentLength>) =>
                        setContentLength(e.target.value as ContentLength)
                      }
                    >
                      <MenuItem value="short">Short</MenuItem>
                      <MenuItem value="medium">Medium</MenuItem>
                      <MenuItem value="long">Long</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              <Box sx={{ display: "flex", alignItems: "center", mt: 2 }}>
                <Typography variant="body2" sx={{ mr: 2 }}>
                  Include Image Prompt:
                </Typography>
                <Button
                  variant={includeImagePrompt ? "contained" : "outlined"}
                  color={includeImagePrompt ? "primary" : "inherit"}
                  onClick={() =>
                    setIncludeImagePrompt(!includeImagePrompt as any)
                  }
                  startIcon={<ImageIcon />}
                  size="small"
                >
                  {includeImagePrompt ? "Yes" : "No"}
                </Button>
              </Box>

              <Box
                sx={{ mt: 3, display: "flex", justifyContent: "space-between" }}
              >
                <Button
                  variant="outlined"
                  onClick={() => setShowTemplatesDialog(true)}
                  startIcon={<SaveIcon />}
                >
                  Saved Templates
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleGenerate}
                  startIcon={
                    loading ? (
                      <CircularProgress size={20} color="inherit" />
                    ) : (
                      <LinkedInIcon />
                    )
                  }
                  disabled={loading || !topic}
                >
                  {loading ? "Generating..." : "Generate Content"}
                </Button>
              </Box>

              {error && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {error}
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Preview Section */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mb: 2,
                }}
              >
                <Typography variant="h6">Content Preview</Typography>
                {generatedContent && (
                  <Box>
                    <Tooltip title="Copy to clipboard">
                      <IconButton onClick={handleCopyContent} size="small">
                        <ContentCopyIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Save as template">
                      <IconButton onClick={handleSaveTemplate} size="small">
                        <SaveIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                )}
              </Box>

              {generatedContent ? (
                <>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 3,
                      backgroundColor: "#f5f7f9",
                      borderRadius: 2,
                      minHeight: "200px",
                      mb: 2,
                    }}
                  >
                    <Typography variant="subtitle1" gutterBottom>
                      {generatedContent.title}
                    </Typography>
                    <Typography variant="body1" sx={{ whiteSpace: "pre-line" }}>
                      {generatedContent.content}
                    </Typography>

                    <Box
                      sx={{
                        display: "flex",
                        flexWrap: "wrap",
                        gap: 0.5,
                        mt: 2,
                      }}
                    >
                      {generatedContent.tags.map(
                        (tag: string, index: number) => (
                          <Chip
                            key={index}
                            label={`#${tag.replace(/\s+/g, "")}`}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        )
                      )}
                    </Box>
                  </Paper>

                  {generatedContent.imagePrompt && (
                    <>
                      <Divider sx={{ my: 2 }} />
                      <Typography variant="subtitle2" gutterBottom>
                        Image Prompt for Canva
                      </Typography>
                      <Paper sx={{ p: 2, backgroundColor: "#f0f0f0" }}>
                        <Typography variant="body2">
                          {generatedContent.imagePrompt}
                        </Typography>
                      </Paper>
                      <Button
                        startIcon={<ImageIcon />}
                        variant="outlined"
                        size="small"
                        sx={{ mt: 1 }}
                        onClick={() =>
                          window.open(
                            "https://www.canva.com/create/ai-image-generator/",
                            "_blank"
                          )
                        }
                      >
                        Open Canva AI Image Generator
                      </Button>
                    </>
                  )}
                </>
              ) : (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "300px",
                    backgroundColor: "#f5f7f9",
                    borderRadius: 2,
                  }}
                >
                  <LinkedInIcon
                    sx={{ fontSize: 60, color: "#0077B5", opacity: 0.5, mb: 2 }}
                  />
                  <Typography variant="body1" color="textSecondary">
                    Fill out the form and click "Generate Content" to create
                    LinkedIn content
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Saved Templates Dialog */}
      <Dialog
        open={showTemplatesDialog}
        onClose={() => setShowTemplatesDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Saved Templates</DialogTitle>
        <DialogContent dividers>
          {savedTemplates.length === 0 ? (
            <Typography variant="body1" align="center" sx={{ py: 4 }}>
              No saved templates yet. Generate and save content to see it here.
            </Typography>
          ) : (
            <Grid container spacing={2}>
              {savedTemplates.map((template) => (
                <Grid item xs={12} key={template.id}>
                  <Paper sx={{ p: 2, position: "relative" }}>
                    <Typography variant="subtitle1" gutterBottom>
                      {template.title}
                    </Typography>
                    <Typography variant="body2" noWrap>
                      {template.content.substring(0, 100)}...
                    </Typography>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        mt: 1,
                      }}
                    >
                      <Typography variant="caption" color="textSecondary">
                        {new Date(template.createdAt).toLocaleDateString()}
                      </Typography>
                      <Box>
                        <Button
                          size="small"
                          onClick={() => handleLoadTemplate(template)}
                        >
                          Load
                        </Button>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteTemplate(template.id)}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowTemplatesDialog(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

// Mock data generator function
function generateMockLinkedInContent(
  options: GenerationOptions
): LinkedInMessageTemplate {
  const { topic, keywords, tone, targetAudience, type, includeImagePrompt } =
    options;

  // Generate a mock title based on the topic
  const title = `${topic} - ${targetAudience} ${
    type === "post" ? "Post" : type === "message" ? "Message" : "Comment"
  }`;

  // Generate mock content based on the options
  let content = "";

  if (type === "post") {
    content = `🔍 Interesting insights about ${topic}!\n\nAs water treatment professionals, we're always looking for ways to improve efficiency and reduce costs. ${
      tone === "technical"
        ? "Our recent analysis shows that"
        : "I wanted to share that"
    } implementing proper water treatment protocols can reduce energy consumption by up to 15%.\n\nHere are 3 key benefits:\n\n1️⃣ Lower maintenance costs\n2️⃣ Extended equipment lifespan\n3️⃣ Reduced energy consumption\n\nWhat's your experience with modern water treatment solutions? Have you seen similar results?\n\n#WaterTreatment ${keywords
      .map((k) => "#" + k.replace(/\s+/g, ""))
      .join(" ")}`;
  } else if (type === "message") {
    content = `Hi there,\n\nI noticed your interest in ${topic} and thought I'd reach out. At Mexel Energy Sustain, we specialize in innovative water treatment solutions that address the exact challenges you mentioned in your recent post.\n\nWould you be open to a brief conversation about how our Film-Forming Amine technology might help with your specific needs?\n\nLooking forward to connecting,\n[Your Name]\nMexel Energy Sustain`;
  } else {
    content = `Great post! I completely agree that ${topic} is crucial for operational efficiency. In my experience working with various industrial facilities, the right water treatment approach can make a significant difference. Have you looked into Film-Forming Amine technology? It's been a game-changer for many of our clients, especially when dealing with cooling systems.`;
  }

  // Generate mock image prompt if requested
  let imagePrompt: string | undefined = undefined;
  if (includeImagePrompt) {
    imagePrompt = `Create a professional LinkedIn post image related to ${topic}. Show industrial water treatment equipment with clear, clean water flowing. Include visual elements representing ${keywords.join(
      ", "
    )}. Use a blue and green color scheme to represent water and sustainability. Add the text "${topic}" prominently. Style should be corporate and professional.`;
  }

  return {
    id: `linkedin-${Date.now()}`,
    title,
    content,
    imagePrompt,
    tags: [...keywords, "WaterTreatment", "Sustainability"],
    tone,
    targetAudience,
    type,
    createdAt: new Date(),
  };
}

export default LinkedInMessageGenerator;
