import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import { TabPanel, a11yProps } from './common/TabsNavigation';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  Divider
} from '@mui/material';
import EmailPreview from './EmailPreview';
import { IEmailTemplate } from '../types/email';
import sampleEmailTemplates from '../data/sampleEmailTemplates';

interface EmailDashboardProps {
  title?: string;
}

export const EmailDashboard = ({ title = "Email Templates"  }: EmailDashboardProps) => {
  const [tabValue, setTabValue] = React.useState(0);
  const [selectedTemplate, setSelectedTemplate] = React.useState(sampleEmailTemplates[0]);
  const [recipient, setRecipient] = React.useState('');
  const [templateCategory, setTemplateCategory] = React.useState('all');

  const handleTabChange = (_event: any, newValue: number) => {
    setTabValue(newValue);
  };

  const handleTemplateSelect = (template: IEmailTemplate) => {
    setSelectedTemplate(template);
    setTabValue(1); // Switch to preview tab
  };

  const handleCategoryChange = (event: SelectChangeEvent) => {
    setTemplateCategory(event.target.value);
  };

  const handleRecipientChange = (email: string) => {
    setRecipient(email);
  };

  const handleSendEmail = (template: IEmailTemplate) => {
    console.log('Sending email:', template, 'to:', recipient);
    // In a real app, this would call an API to send the email
    alert(`Email would be sent to ${recipient}`);
  };

  const handleEditEmail = (template: IEmailTemplate) => {
    console.log('Editing email:', template);
    // In a real app, this would open an editor
  };

  // Filter templates by category
  const filteredTemplates = templateCategory === 'all'
    ? sampleEmailTemplates
    : sampleEmailTemplates.filter(template =>
        template.metadata?.type === templateCategory
      );

  return (
    <Paper sx={{ p: 3, mb: 4 }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5" component="h2">
            {title}
          </Typography>

          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel id="template-category-label">Category</InputLabel>
            <Select
              labelId="template-category-label"
              id="template-category"
              value={templateCategory}
              label="Category"
              onChange={handleCategoryChange}
            >
              <MenuItem value="all">All Templates</MenuItem>
              <MenuItem value="outreach">Outreach</MenuItem>
              <MenuItem value="follow-up">Follow-up</MenuItem>
              <MenuItem value="tender">Tender Response</MenuItem>
            </Select>
          </FormControl>
        </Box>

        <Tabs value={tabValue} onChange={handleTabChange} aria-label="email dashboard tabs">
          <Tab label="Template Library" {...a11yProps(0)} />
          <Tab label="Email Preview" {...a11yProps(1)} />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          {filteredTemplates.map((template) => (
            <Grid item xs={12} md={6} key={template.id}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" component="h3" gutterBottom>
                    {template.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Subject: {template.subject}
                  </Typography>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1, mb: 2 }}>
                    {template.metadata?.type && (
                      <Chip
                        label={`Type: ${template.metadata.type}`}
                        size="small"
                        variant="outlined"
                        color="primary"
                      />
                    )}
                    {template.metadata?.industry && (
                      <Chip
                        label={`Industry: ${template.metadata.industry}`}
                        size="small"
                        variant="outlined"
                      />
                    )}
                    {template.metadata?.tone && (
                      <Chip
                        label={`Tone: ${template.metadata.tone}`}
                        size="small"
                        variant="outlined"
                      />
                    )}
                  </Box>

                  <Divider sx={{ my: 1 }} />

                  <Typography variant="body2" color="text.secondary" sx={{
                    height: '80px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: '-webkit-box',
                    WebkitLineClamp: 4,
                    WebkitBoxOrient: 'vertical'
                  }}>
                    {template.body.replace(/<[^>]*>?/gm, ' ')}
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button size="small" onClick={() => handleTemplateSelect(template)}>
                    Preview
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <EmailPreview
          template={selectedTemplate}
          onEdit={handleEditEmail}
          onSend={handleSendEmail}
          onCopy={() => console.log('Copied to clipboard')}
          showControls={true}
          showMetadata={true}
          recipientEmail={recipient}
          onRecipientChange={handleRecipientChange}
          previewMode="full"
        />
      </TabPanel>
    </Paper>
  );
};

export default EmailDashboard;
