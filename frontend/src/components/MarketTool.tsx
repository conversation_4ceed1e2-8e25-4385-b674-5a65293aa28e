import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import RemoveIcon from "@mui/icons-material/Remove";
import {
    Box,
    Card,
    CardContent,
    Chip,
    CircularProgress,
    Grid,
    Paper,
    Tab,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Tabs,
    Typography,
} from "@mui/material";
import React from "react";
import { MarketAnalysis, MarketData, MarketInsight } from "../types/market";
import { TabPanel, a11yProps } from "./common/TabsNavigation";

interface MarketToolProps {
  showRelatedTenders?: boolean;
}

export const MarketTool = ({ showRelatedTenders = true }: MarketToolProps) => {
  const [marketData, setMarketData] = React.useState<any[]>([]);
  const [marketAnalyses, setMarketAnalyses] = React.useState(
    []
  );
  const [marketInsights, setMarketInsights] = React.useState(
    []
  );
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);
  const [tabValue, setTabValue] = React.useState(0);

  const handleTabChange = (event: any, newValue: number) => {
    setTabValue(newValue);
  };

  React.useEffect(() => {
    const fetchMarketData = async () => {
      try {
        setLoading(true);
        const symbols = ["MSFT", "AAPL", "GOOGL", "AMZN"];

        // Fetch market data for each symbol
        const quotesPromises = symbols.map((symbol) =>
          fetch(`http://localhost:3001/api/market/quote/${symbol}`)
            .then((res) => {
              if (!res.ok)
                throw new Error(`Failed to fetch data for ${symbol}`);
              return res.json();
            })
            .then((data) => ({
              symbol,
              data,
            }))
        );

        // Fetch sector performance
        const sectorsPromise = fetch(
          "http://localhost:3001/api/market/sectors"
        ).then((res) => {
          if (!res.ok) throw new Error("Failed to fetch sector data");
          return res.json();
        });

        // Wait for all data
        const [quotesResponses, sectorsData] = await Promise.all([
          Promise.all(quotesPromises),
          sectorsPromise,
        ]);

        // Transform quotes data
        const transformedMarketData: MarketData[] = quotesResponses.map(
          ({ symbol, data }) => {
            const quote = data["Global Quote"] || {};
            const change = parseFloat(quote["09. change"] || "0");
            return {
              id: symbol,
              name: symbol,
              category: "Technology",
              currentPrice: parseFloat(quote["05. price"]) || 0,
              previousPrice: parseFloat(quote["08. previous close"]) || 0,
              changePercentage: parseFloat(
                quote["10. change percent"]?.replace("%", "") || "0"
              ),
              volume: parseInt(quote["06. volume"] || "0"),
              marketCap: 0,
              lastUpdated:
                quote["07. latest trading day"] || new Date().toISOString(),
              trend: change > 0 ? "up" : change < 0 ? "down" : "stable",
              relatedTenders: [],
            };
          }
        );

        // Transform sectors data
        const transformedAnalyses: MarketAnalysis[] = Object.entries(
          sectorsData["Rank A: Real-Time Performance"] || {}
        ).map(([sector, performance]) => {
          const value = parseFloat((performance as string).replace("%", ""));
          return {
            sector,
            growth: value,
            volatility: Math.abs(value / 2),
            sentiment:
              value > 0 ? "bullish" : value < 0 ? "bearish" : "neutral",
            recommendation: `Market ${
              value > 0
                ? "shows strength"
                : value < 0
                ? "shows weakness"
                : "is stable"
            }`,
            potentialImpact: Math.min(10, Math.abs(value)),
            lastUpdated: new Date().toISOString(),
          };
        });

        // Sample insights based on the data
        const insights: MarketInsight[] = [
          {
            title: "Market Movement Analysis",
            description:
              "Technology sector showing significant movement based on latest data.",
            impact: "high",
            source: "Alpha Vantage Analysis",
            date: new Date().toISOString(),
            relatedMarkets: symbols,
          },
        ];

        setMarketData(transformedMarketData);
        setMarketAnalyses(transformedAnalyses);
        setMarketInsights(insights);
        setError(null);
      } catch (err) {
        console.error("Error fetching market data:", err);
        setError("Failed to load market data");
      } finally {
        setLoading(false);
      }
    };

    fetchMarketData();

    // Refresh data every 5 minutes
    const intervalId = setInterval(fetchMarketData, 5 * 60 * 1000 as any);

    return () => clearInterval(intervalId);
  }, []);

  const renderTrendIcon = (trend: "up" | "down" | "stable") => {
    switch (trend) {
      case "up":
        return <ArrowUpwardIcon color="success" fontSize="small" />;
      case "down":
        return <ArrowDownwardIcon color="error" fontSize="small" />;
      default:
        return <RemoveIcon color="action" fontSize="small" />;
    }
  };

  if (loading) {
    return (
      <Card
        sx={{
          m: 1,
          minHeight: 200,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <CardContent>
          <CircularProgress />
          <Typography sx={{ mt: 2 }}>Loading market data...</Typography>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card sx={{ m: 1, minHeight: 200, bgcolor: "#ffebee" }}>
        <CardContent>
          <Typography color="error" variant="h6">
            Error
          </Typography>
          <Typography>{error}</Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ m: 1 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Market Intelligence
        </Typography>

        <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 2 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="market tool tabs"
          >
            <Tab label="Market Data" {...a11yProps(0)} />
            <Tab label="Analysis" {...a11yProps(1)} />
            <Tab label="Insights" {...a11yProps(2)} />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <TableContainer component={Paper}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Symbol</TableCell>
                  <TableCell>Price</TableCell>
                  <TableCell align="right">Change %</TableCell>
                  <TableCell align="right">Volume</TableCell>
                  <TableCell align="right">Trend</TableCell>
                  {showRelatedTenders && <TableCell>Related Tenders</TableCell>}
                </TableRow>
              </TableHead>
              <TableBody>
                {marketData.map((market: MarketData) => (
                  <TableRow key={market.id}>
                    <TableCell>{market.name}</TableCell>
                    <TableCell>
                      ${market.currentPrice.toLocaleString()}
                    </TableCell>
                    <TableCell
                      align="right"
                      sx={{
                        color:
                          market.changePercentage > 0
                            ? "success.main"
                            : market.changePercentage < 0
                            ? "error.main"
                            : "text.primary",
                      }}
                    >
                      {market.changePercentage > 0 ? "+" : ""}
                      {market.changePercentage.toFixed(2)}%
                    </TableCell>
                    <TableCell align="right">
                      {market.volume.toLocaleString()}
                    </TableCell>
                    <TableCell align="right">
                      {renderTrendIcon(market.trend)}
                    </TableCell>
                    {showRelatedTenders && (
                      <TableCell>
                        {market.relatedTenders &&
                        market.relatedTenders.length > 0 ? (
                          <Chip
                            size="small"
                            label={`${market.relatedTenders.length} related`}
                            color="primary"
                          />
                        ) : (
                          <Chip size="small" label="None" variant="outlined" />
                        )}
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={2}>
            {marketAnalyses.map((analysis: MarketAnalysis) => (
              <Grid item xs={12} md={6} key={analysis.sector}>
                <Paper elevation={1} sx={{ p: 2 }}>
                  <Typography variant="subtitle1">{analysis.sector}</Typography>
                  <Box sx={{ my: 1 }}>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        mb: 1,
                      }}
                    >
                      <Typography variant="body2">Growth:</Typography>
                      <Typography
                        variant="body2"
                        color={
                          analysis.growth > 0 ? "success.main" : "error.main"
                        }
                      >
                        {analysis.growth > 0 ? "+" : ""}
                        {analysis.growth.toFixed(2)}%
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        mb: 1,
                      }}
                    >
                      <Typography variant="body2">Volatility:</Typography>
                      <Typography variant="body2">
                        {analysis.volatility.toFixed(2)}%
                      </Typography>
                    </Box>
                  </Box>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mb: 1 }}
                  >
                    {analysis.recommendation}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Last updated:{" "}
                    {new Date(analysis.lastUpdated).toLocaleString()}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={2}>
            {marketInsights.map((insight: MarketInsight, index: number) => (
              <Grid item xs={12} key={index}>
                <Paper elevation={1} sx={{ p: 2 }}>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      mb: 1,
                    }}
                  >
                    <Typography variant="subtitle1">{insight.title}</Typography>
                    <Chip
                      size="small"
                      label={`${insight.impact.toUpperCase()} IMPACT`}
                      color={
                        insight.impact === "high"
                          ? "error"
                          : insight.impact === "medium"
                          ? "warning"
                          : "info"
                      }
                    />
                  </Box>
                  <Typography variant="body2" paragraph>
                    {insight.description}
                  </Typography>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <Typography variant="caption" color="text.secondary">
                      Source: {insight.source}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(insight.date).toLocaleString()}
                    </Typography>
                  </Box>
                  {insight.relatedMarkets && (
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        Related Markets: {insight.relatedMarkets.join(", ")}
                      </Typography>
                    </Box>
                  )}
                </Paper>
              </Grid>
            ))}
          </Grid>
        </TabPanel>
      </CardContent>
    </Card>
  );
}
