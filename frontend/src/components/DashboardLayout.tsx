import { Box, Container, Paper, Typography } from "@mui/material";
import React from "react";

interface DashboardLayoutProps {
  title: string;
  children: React.ReactNode; // Changed from any to React.ReactNode
}

/**
 * A simplified dashboard layout component for the minimal frontend
 */
const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  title,
  children,
}) => {
  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {title}
        </Typography>
        <Box sx={{ mt: 2 }}>{children}</Box>
      </Paper>
    </Container>
  );
};

export default DashboardLayout;
