import React, { FC } from "react";
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import { TenderOpportunity } from "../types/dashboard";

interface TenderOpportunitiesProps {
  opportunities: TenderOpportunity[];
}

export const TenderOpportunities = ({ opportunities,
 }: TenderOpportunitiesProps) => {
  return (
    <Card sx={{ m: 1 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Latest Tender Opportunities
        </Typography>

        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Title</TableCell>
                <TableCell>Source</TableCell>
                <TableCell>Closing Date</TableCell>
                <TableCell>Est. Value</TableCell>
                <TableCell>Score</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {opportunities.map(
                (opportunity: TenderOpportunity, index: number) => (
                  <TableRow
                    key={index}
                    sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                  >
                    <TableCell>
                      <Typography
                        variant="body2"
                        sx={{
                          maxWidth: 300,
                          whiteSpace: "nowrap",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                        }}
                      >
                        {opportunity.title}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      {opportunity.source || opportunity.issuer || "Unknown"}
                    </TableCell>
                    <TableCell>
                      {opportunity.closing_date
                        ? new Date(
                            opportunity.closing_date
                          ).toLocaleDateString()
                        : opportunity.closingDate
                        ? new Date(opportunity.closingDate).toLocaleDateString()
                        : "N/A"}
                    </TableCell>
                    <TableCell>
                      {opportunity.estimated_value
                        ? `R${opportunity.estimated_value.toLocaleString()}`
                        : opportunity.value
                        ? `R${opportunity.value.toLocaleString()}`
                        : "N/A"}
                    </TableCell>
                    <TableCell>
                      {opportunity.relevance_score !== undefined
                        ? opportunity.relevance_score.toFixed(1)
                        : opportunity.confidence !== undefined
                        ? opportunity.confidence.toFixed(1)
                        : opportunity.opportunity_score !== undefined
                        ? opportunity.opportunity_score.toFixed(1)
                        : "N/A"}
                    </TableCell>
                  </TableRow>
                )
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );
}
