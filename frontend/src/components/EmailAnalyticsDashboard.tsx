import {
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    CircularProgress,
    FormControl,
    Grid,
    InputLabel,
    MenuItem,
    Paper,
    Select,
    SelectChangeEvent,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography
} from "@mui/material";
import axios from "axios";
import { format } from "date-fns";
import React, { useEffect } from "react";

// Define interfaces
interface IndustryAnalyticsData {
  sent: number;
  opened: number;
  clicked: number;
  // Add any other properties that might be in the industry data
}

interface IEmailAnalytics {
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  replied: number;
  bounced: number;
  unsubscribed: number;
  openRate: number;
  clickRate: number;
  replyRate: number;
  bounceRate: number;
  unsubscribeRate: number;
  byIndustry: Record<string, IndustryAnalyticsData>;
  bySegment: Record<string, any>;
  byTemplate: Record<string, any>;
}

interface ICampaign {
  id: string;
  name: string;
  status: string;
  sentDate: string;
  analytics: {
    sent: number;
    opened: number;
    clicked: number;
    openRate: number;
    clickRate: number;
  };
}

interface ILead {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  company: string;
  status: string;
  lastEngagement?: {
    type: string;
    timestamp: string;
  };
}

// Colors for charts
const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Email Analytics Dashboard Component
const EmailAnalyticsDashboard = () => {
  // State
  const [analytics, setAnalytics] = React.useState(
    null
  );
  const [campaigns, setCampaigns] = React.useState<any[]>([]);
  const [recentLeads, setRecentLeads] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [period, setPeriod] = React.useState(format(new Date(), "yyyy-MM"));
  const [error, setError] = React.useState(null);

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch email analytics
        const analyticsResponse = await axios.get(
          `/api/email/analytics?period=${period}`
        );
        setAnalytics(analyticsResponse.data as IEmailAnalytics);

        // Fetch campaigns
        const campaignsResponse = await axios.get("/api/email/campaigns");
        setCampaigns(campaignsResponse.data as ICampaign[]);

        // Fetch recent leads with engagement
        const leadsResponse = await axios.get(
          "/api/crm/leads/recent-engagement"
        );
        setRecentLeads(leadsResponse.data as ILead[]);

        setLoading(false);
      } catch (err) {
        console.error("Error fetching data:", err);
        setError("Failed to load data. Please try again later.");
        setLoading(false);

        // For demo purposes, set mock data
        setMockData();
      }
    };

    fetchData();
  }, [period]);

  // Set mock data for demonstration
  const setMockData = () => {
    // Mock analytics data
    setAnalytics({
      sent: 1250,
      delivered: 1200,
      opened: 720,
      clicked: 360,
      replied: 180,
      bounced: 50,
      unsubscribed: 25,
      openRate: 0.6,
      clickRate: 0.5,
      replyRate: 0.25,
      bounceRate: 0.04,
      unsubscribeRate: 0.02,
      byIndustry: {
        Manufacturing: { sent: 400, opened: 240, clicked: 120 },
        Energy: { sent: 300, opened: 210, clicked: 105 },
        Technology: { sent: 250, opened: 150, clicked: 75 },
        Healthcare: { sent: 200, opened: 100, clicked: 50 },
        Other: { sent: 100, opened: 20, clicked: 10 },
      },
      bySegment: {
        "High Priority": { sent: 500, opened: 400, clicked: 250 },
        "Medium Priority": { sent: 400, opened: 240, clicked: 80 },
        "Low Priority": { sent: 300, opened: 80, clicked: 30 },
      },
      byTemplate: {
        "Product Introduction": { sent: 400, opened: 280, clicked: 140 },
        "Follow-up": { sent: 350, opened: 210, clicked: 105 },
        "Tender Response": { sent: 300, opened: 150, clicked: 75 },
        Newsletter: { sent: 200, opened: 80, clicked: 40 },
      },
    });

    // Mock campaigns data
    setCampaigns([
      {
        id: "campaign_1",
        name: "Q2 Product Introduction",
        status: "completed",
        sentDate: "2023-04-15T10:00:00Z",
        analytics: {
          sent: 500,
          opened: 300,
          clicked: 150,
          openRate: 0.6,
          clickRate: 0.5,
        },
      },
      {
        id: "campaign_2",
        name: "Water Treatment Solutions",
        status: "completed",
        sentDate: "2023-05-01T10:00:00Z",
        analytics: {
          sent: 350,
          opened: 210,
          clicked: 105,
          openRate: 0.6,
          clickRate: 0.5,
        },
      },
      {
        id: "campaign_3",
        name: "Energy Efficiency Webinar",
        status: "scheduled",
        sentDate: "2023-06-01T10:00:00Z",
        analytics: {
          sent: 0,
          opened: 0,
          clicked: 0,
          openRate: 0,
          clickRate: 0,
        },
      },
    ]);

    // Mock leads data
    setRecentLeads([
      {
        id: "lead_1",
        email: "<EMAIL>",
        firstName: "John",
        lastName: "Doe",
        company: "Acme Corp",
        status: "ENGAGED",
        lastEngagement: {
          type: "click",
          timestamp: "2023-05-10T14:30:00Z",
        },
      },
      {
        id: "lead_2",
        email: "<EMAIL>",
        firstName: "Jane",
        lastName: "Smith",
        company: "Tech Solutions",
        status: "EMAIL_SEQUENCE_STARTED",
        lastEngagement: {
          type: "open",
          timestamp: "2023-05-09T10:15:00Z",
        },
      },
      {
        id: "lead_3",
        email: "<EMAIL>",
        firstName: "Bob",
        lastName: "Johnson",
        company: "Energy Systems",
        status: "ENGAGED",
        lastEngagement: {
          type: "reply",
          timestamp: "2023-05-08T16:45:00Z",
        },
      },
      {
        id: "lead_4",
        email: "<EMAIL>",
        firstName: "Alice",
        lastName: "Williams",
        company: "Global Manufacturing",
        status: "QUALIFIED",
        lastEngagement: {
          type: "click",
          timestamp: "2023-05-07T09:20:00Z",
        },
      },
      {
        id: "lead_5",
        email: "<EMAIL>",
        firstName: "Charlie",
        lastName: "Brown",
        company: "Water Solutions",
        status: "CONTACTED",
        lastEngagement: {
          type: "open",
          timestamp: "2023-05-06T11:10:00Z",
        },
      },
    ]);
  };

  // Handle period change
  const handlePeriodChange = (event: SelectChangeEvent) => {
    setPeriod(event.target.value as any);
  };

  // Prepare data for charts
  const prepareEngagementData = () => {
    if (!analytics) return [];

    return [
      { name: "Sent", value: analytics.sent },
      { name: "Opened", value: analytics.opened },
      { name: "Clicked", value: analytics.clicked },
      { name: "Replied", value: analytics.replied },
    ];
  };

  const prepareIndustryData = () => {
    if (!analytics || !analytics.byIndustry) return [];

    return Object.entries(analytics.byIndustry).map(
      ([industry, data]: [string, IndustryAnalyticsData]) => ({
        industry,
        sent: data.sent,
        opened: data.opened,
        clicked: data.clicked,
      })
    );
  };

  // Render loading state
  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="80vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="80vh"
      >
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={3}
      >
        <Typography variant="h4" component="h1">
          Email Analytics Dashboard
        </Typography>

        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel id="period-select-label">Period</InputLabel>
          <Select
            labelId="period-select-label"
            id="period-select"
            value={period}
            label="Period"
            onChange={handlePeriodChange}
          >
            <MenuItem value="2023-01">January 2023</MenuItem>
            <MenuItem value="2023-02">February 2023</MenuItem>
            <MenuItem value="2023-03">March 2023</MenuItem>
            <MenuItem value="2023-04">April 2023</MenuItem>
            <MenuItem value="2023-05">May 2023</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Open Rate
              </Typography>
              <Typography variant="h4" component="div">
                {analytics
                  ? `${(analytics.openRate * 100).toFixed(1)}%`
                  : "N/A"}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {analytics
                  ? `${analytics.opened} / ${analytics.delivered}`
                  : ""}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Click Rate
              </Typography>
              <Typography variant="h4" component="div">
                {analytics
                  ? `${(analytics.clickRate * 100).toFixed(1)}%`
                  : "N/A"}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {analytics ? `${analytics.clicked} / ${analytics.opened}` : ""}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Reply Rate
              </Typography>
              <Typography variant="h4" component="div">
                {analytics
                  ? `${(analytics.replyRate * 100).toFixed(1)}%`
                  : "N/A"}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {analytics
                  ? `${analytics.replied} / ${analytics.delivered}`
                  : ""}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Bounce Rate
              </Typography>
              <Typography variant="h4" component="div">
                {analytics
                  ? `${(analytics.bounceRate * 100).toFixed(1)}%`
                  : "N/A"}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {analytics ? `${analytics.bounced} / ${analytics.sent}` : ""}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Email Engagement Overview
              </Typography>
              <Box height={300}>
                <Box
                  sx={{
                    height: 300,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    border: "1px dashed #ccc",
                    borderRadius: 1,
                  }}
                >
                  <Typography>Chart data loaded successfully.</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Engagement by Industry
              </Typography>
              <Box height={300}>
                <Box
                  sx={{
                    height: 300,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    border: "1px dashed #ccc",
                    borderRadius: 1,
                  }}
                >
                  <Typography>Chart data loaded successfully.</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Campaigns */}
      <Typography variant="h5" gutterBottom>
        Recent Campaigns
      </Typography>
      <TableContainer component={Paper} sx={{ mb: 4 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Campaign Name</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Sent Date</TableCell>
              <TableCell>Sent</TableCell>
              <TableCell>Opened</TableCell>
              <TableCell>Clicked</TableCell>
              <TableCell>Open Rate</TableCell>
              <TableCell>Click Rate</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {campaigns.map((campaign) => (
              <TableRow key={campaign.id}>
                <TableCell>{campaign.name}</TableCell>
                <TableCell>
                  <Chip
                    label={campaign.status}
                    color={
                      campaign.status === "completed" ? "success" : "primary"
                    }
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {campaign.sentDate
                    ? new Date(campaign.sentDate).toLocaleDateString()
                    : "Scheduled"}
                </TableCell>
                <TableCell>{campaign.analytics.sent}</TableCell>
                <TableCell>{campaign.analytics.opened}</TableCell>
                <TableCell>{campaign.analytics.clicked}</TableCell>
                <TableCell>{`${(campaign.analytics.openRate * 100).toFixed(
                  1
                )}%`}</TableCell>
                <TableCell>{`${(campaign.analytics.clickRate * 100).toFixed(
                  1
                )}%`}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Recent Lead Engagements */}
      <Typography variant="h5" gutterBottom>
        Recent Lead Engagements
      </Typography>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Lead Name</TableCell>
              <TableCell>Company</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Last Engagement</TableCell>
              <TableCell>Engagement Time</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {recentLeads.map((lead) => (
              <TableRow key={lead.id}>
                <TableCell>{`${lead.firstName} ${lead.lastName}`}</TableCell>
                <TableCell>{lead.company}</TableCell>
                <TableCell>
                  <Chip
                    label={lead.status}
                    color={
                      lead.status === "ENGAGED" || lead.status === "QUALIFIED"
                        ? "success"
                        : lead.status === "EMAIL_SEQUENCE_STARTED"
                        ? "primary"
                        : "default"
                    }
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {lead.lastEngagement ? (
                    <Chip
                      label={lead.lastEngagement.type}
                      color={
                        lead.lastEngagement.type === "reply"
                          ? "success"
                          : lead.lastEngagement.type === "click"
                          ? "primary"
                          : "default"
                      }
                      size="small"
                    />
                  ) : (
                    "None"
                  )}
                </TableCell>
                <TableCell>
                  {lead.lastEngagement
                    ? new Date(lead.lastEngagement.timestamp).toLocaleString()
                    : "N/A"}
                </TableCell>
                <TableCell>
                  <Button size="small" variant="outlined">
                    View Details
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default EmailAnalyticsDashboard;
