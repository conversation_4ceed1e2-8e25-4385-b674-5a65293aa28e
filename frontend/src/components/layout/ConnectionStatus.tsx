import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import WifiIcon from '@mui/icons-material/Wifi';
import WifiOffIcon from '@mui/icons-material/WifiOff';
import { Box, IconButton, Tooltip, Typography } from '@mui/material';
import React, { useCallback, useEffect } from 'react';

interface ConnectionState {
  apiConnected: boolean;
  websocketConnected: boolean;
  lastApiCheck: Date | null;
  lastWsCheck: Date | null;
}

const ConnectionStatus: React.FC = () => {
  const [connectionState, setConnectionState] = React.useState<ConnectionState>({
    apiConnected: false,
    websocketConnected: false,
    lastApiCheck: null,
    lastWsCheck: null,
  });

  const checkApiHealth = useCallback(async (): Promise<void> => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/health');
      setConnectionState(prev => ({
        ...prev,
        apiConnected: response.ok,
        lastApiCheck: new Date(),
      }));
    } catch (error) {
      setConnectionState(prev => ({
        ...prev,
        apiConnected: false,
        lastApiCheck: new Date(),
      }));
    }
  }, []);

  const checkWebSocketHealth = useCallback((): void => {
    // TODO: Implement actual WebSocket check
    // For now, we'll simulate a check
    const connected = Math.random() > 0.1; // 90% success rate
    setConnectionState(prev => ({
      ...prev,
      websocketConnected: connected,
      lastWsCheck: new Date(),
    }));
  }, []);

  useEffect(() => {
    // Initial checks
    checkApiHealth();
    checkWebSocketHealth();

    // Set up intervals for health checks
    const apiInterval = setInterval(checkApiHealth, 30000); // Every 30 seconds
    const wsInterval = setInterval(checkWebSocketHealth, 10000); // Every 10 seconds

    return () => {
      clearInterval(apiInterval);
      clearInterval(wsInterval);
    };
  }, [checkApiHealth, checkWebSocketHealth]);

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
      <Tooltip title={`API: ${connectionState.apiConnected ? 'Connected' : 'Disconnected'}`}>
        <span>
          <IconButton
            color={connectionState.apiConnected ? 'success' : 'error'}
            size="small"
            onClick={checkApiHealth}
          >
            {connectionState.apiConnected ? <CheckCircleOutlineIcon /> : <ErrorOutlineIcon />}
            <Typography
              variant="caption"
              sx={{
                ml: 0.5,
                display: { xs: 'none', sm: 'inline' },
              }}
            >
              API
            </Typography>
          </IconButton>
        </span>
      </Tooltip>
      <Tooltip
        title={`WebSocket: ${connectionState.websocketConnected ? 'Connected' : 'Disconnected'}`}
      >
        <span>
          <IconButton
            color={connectionState.websocketConnected ? 'success' : 'error'}
            size="small"
            onClick={checkWebSocketHealth}
          >
            {connectionState.websocketConnected ? <WifiIcon /> : <WifiOffIcon />}
            <Typography
              variant="caption"
              sx={{
                ml: 0.5,
                display: { xs: 'none', sm: 'inline' },
              }}
            >
              WS
            </Typography>
          </IconButton>
        </span>
      </Tooltip>
    </Box>
  );
};

export default ConnectionStatus;
