import { AppBar, Box, Toolbar, Typography, useTheme } from "@mui/material";
import React from "react";
import ConnectionStatus from "./ConnectionStatus"; // Changed import
import Navigation from "./Navigation"; // Changed import

interface MainLayoutProps {
  children: React.ReactNode; // Changed from any to React.ReactNode
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const theme = useTheme();

  return (
    <Box sx={{ display: "flex", minHeight: "100vh" }}>
      <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            Mexel Lead Discovery System
          </Typography>
          <ConnectionStatus />
        </Toolbar>
      </AppBar>
      <Navigation />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          marginTop: "64px", // Height of AppBar
          backgroundColor: theme.palette.background.default,
        }}
      >
        {children}
      </Box>
    </Box>
  );
};
