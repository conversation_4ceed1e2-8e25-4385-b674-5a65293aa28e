import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import { TabPanel, a11yProps } from '../common/TabsNavigation';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Button,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  IconButton,
  Tooltip,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Switch,
  FormControlLabel
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import {
  Email as EmailIcon,
  Send as SendIcon,
  Schedule as ScheduleIcon,
  GetApp as DownloadIcon,
  Refresh as RefreshIcon,
  Settings as <PERSON>tingsIcon,
  DateRange as DateRangeIcon,
  <PERSON>lter<PERSON>ist as FilterListIcon,
  <PERSON><PERSON>hart as PieChartIcon,
  <PERSON><PERSON>hart as Bar<PERSON>hartIcon,
  Timeline as TimelineIcon,
  Person as PersonIcon,
  Group as GroupIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';
// Recharts wrapper components - commented out due to TypeScript errors
// These components will be replaced with direct usage of Recharts components
/*
const BarWrapper = (props) => <Bar {...props} />;
const XAxisWrapper = (props) => <XAxis {...props} />;
const YAxisWrapper = (props) => <YAxis {...props} />;
const LineWrapper = (props) => <Line {...props} />;
const PieWrapper = (props) => <Pie {...props} />;
const TooltipWrapper = (props) => <Tooltip {...props} />;
const LegendWrapper = (props) => <Legend {...props} />;
*/

// Sample data
const emailPerformanceData = [
  { month: 'Jan', sent: 120, opened: 78, clicked: 45, replied: 22 },
  { month: 'Feb', sent: 150, opened: 92, clicked: 53, replied: 28 },
  { month: 'Mar', sent: 180, opened: 115, clicked: 68, replied: 35 },
  { month: 'Apr', sent: 210, opened: 142, clicked: 82, replied: 41 },
  { month: 'May', sent: 240, opened: 168, clicked: 96, replied: 48 },
  { month: 'Jun', sent: 270, opened: 189, clicked: 108, replied: 54 },
  { month: 'Jul', sent: 300, opened: 210, clicked: 120, replied: 60 },
];

const campaignData = [
  {
    id: 1,
    name: 'Q2 Water Treatment Solutions',
    status: 'completed',
    sentDate: '2023-04-15',
    recipients: 150,
    opened: 98,
    clicked: 56,
    replied: 32,
    converted: 8,
    openRate: 65.3,
    clickRate: 37.3,
    replyRate: 21.3,
    conversionRate: 5.3
  },
  {
    id: 2,
    name: 'Cooling Tower Efficiency Webinar',
    status: 'completed',
    sentDate: '2023-05-10',
    recipients: 200,
    opened: 142,
    clicked: 78,
    replied: 45,
    converted: 12,
    openRate: 71.0,
    clickRate: 39.0,
    replyRate: 22.5,
    conversionRate: 6.0
  },
  {
    id: 3,
    name: 'Film-Forming Amine Technology Introduction',
    status: 'active',
    sentDate: '2023-06-20',
    recipients: 180,
    opened: 126,
    clicked: 72,
    replied: 38,
    converted: 9,
    openRate: 70.0,
    clickRate: 40.0,
    replyRate: 21.1,
    conversionRate: 5.0
  },
  {
    id: 4,
    name: 'Energy Savings Case Studies',
    status: 'scheduled',
    sentDate: '2023-08-05',
    recipients: 220,
    opened: 0,
    clicked: 0,
    replied: 0,
    converted: 0,
    openRate: 0,
    clickRate: 0,
    replyRate: 0,
    conversionRate: 0
  }
];

const recipientSegmentData = [
  { name: 'Facility Managers', value: 35 },
  { name: 'Operations Directors', value: 25 },
  { name: 'Plant Managers', value: 20 },
  { name: 'Maintenance Managers', value: 15 },
  { name: 'Other', value: 5 },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

const scheduledReports = [
  {
    id: 1,
    name: 'Weekly Email Performance Summary',
    frequency: 'weekly',
    recipients: ['<EMAIL>'],
    lastSent: '2023-07-14',
    nextScheduled: '2023-07-21',
    format: 'PDF',
    active: true
  },
  {
    id: 2,
    name: 'Monthly Campaign Analysis',
    frequency: 'monthly',
    recipients: ['<EMAIL>', '<EMAIL>'],
    lastSent: '2023-06-30',
    nextScheduled: '2023-07-31',
    format: 'PDF',
    active: true
  },
  {
    id: 3,
    name: 'Quarterly Conversion Report',
    frequency: 'quarterly',
    recipients: ['<EMAIL>', '<EMAIL>'],
    lastSent: '2023-06-30',
    nextScheduled: '2023-09-30',
    format: 'Excel',
    active: true
  }
];

export const EmailReports = () => {
  const [tabValue, setTabValue] = React.useState(0);
  const [isLoading, setIsLoading] = React.useState(true);
  const [dateRange, setDateRange] = React.useState('6m');
  const [selectedCampaign, setSelectedCampaign] = React.useState('all');
  const [openNewReportDialog, setOpenNewReportDialog] = React.useState(false);
  const [isGeneratingReport, setIsGeneratingReport] = React.useState(false);
  const [startDate, setStartDate] = React.useState(null);
  const [endDate, setEndDate] = React.useState(null);

  React.useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleTabChange = (_event: any, newValue: number) => {
    setTabValue(newValue);
  };

  const handleDateRangeChange = (event: SelectChangeEvent) => {
    setDateRange(event.target.value);
  };

  const handleCampaignChange = (event: SelectChangeEvent) => {
    setSelectedCampaign(event.target.value);
  };

  const handleOpenNewReportDialog = () => {
    setOpenNewReportDialog(true);
  };

  const handleCloseNewReportDialog = () => {
    setOpenNewReportDialog(false);
  };

  const handleGenerateReport = () => {
    if (!startDate || !endDate) return;

    setIsGeneratingReport(true);

    // Simulate report generation
    setTimeout(() => {
      setIsGeneratingReport(false);
      handleCloseNewReportDialog();
      // In a real app, this would trigger a download or show a success message
      alert('Report generated successfully!');
    }, 2000);
  };

  const calculateTotals = () => {
    const totals = {
      sent: 0,
      opened: 0,
      clicked: 0,
      replied: 0,
      converted: 0,
      openRate: 0,
      clickRate: 0,
      replyRate: 0,
      conversionRate: 0
    };

    const filteredCampaigns = selectedCampaign === 'all'
      ? campaignData.filter(c => c.status !== 'scheduled')
      : campaignData.filter(c => c.id.toString() === selectedCampaign && c.status !== 'scheduled');

    if (filteredCampaigns.length === 0) return totals;

    filteredCampaigns.forEach(campaign => {
      totals.sent += campaign.recipients;
      totals.opened += campaign.opened;
      totals.clicked += campaign.clicked;
      totals.replied += campaign.replied;
      totals.converted += campaign.converted;
    });

    totals.openRate = (totals.opened / totals.sent) * 100;
    totals.clickRate = (totals.clicked / totals.sent) * 100;
    totals.replyRate = (totals.replied / totals.sent) * 100;
    totals.conversionRate = (totals.converted / totals.sent) * 100;

    return totals;
  };

  const totals = calculateTotals();

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box>
        <Typography variant="h4" gutterBottom>
          Email Reports
        </Typography>

        {isLoading ? (
          <LinearProgress sx={{ mb: 4 }} />
        ) : (
          <>
            <Paper sx={{ p: 3, mb: 4 }}>
              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Tabs value={tabValue} onChange={handleTabChange} aria-label="email reports tabs">
                    <Tab label="Performance Overview" id="report-tab-0" aria-controls="report-tabpanel-0" />
                    <Tab label="Campaign Analysis" id="report-tab-1" aria-controls="report-tabpanel-1" />
                    <Tab label="Scheduled Reports" id="report-tab-2" aria-controls="report-tabpanel-2" />
                  </Tabs>

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Button
                      variant="outlined"
                      startIcon={<DownloadIcon />}
                      onClick={handleOpenNewReportDialog}
                    >
                      Generate Report
                    </Button>
                  </Box>
                </Box>
              </Box>

              <TabPanel value={tabValue} index={0}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6">Email Performance Metrics</Typography>
                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel id="date-range-label">Date Range</InputLabel>
                    <Select
                      labelId="date-range-label"
                      id="date-range"
                      value={dateRange}
                      label="Date Range"
                      onChange={handleDateRangeChange}
                      startAdornment={<DateRangeIcon fontSize="small" sx={{ mr: 1, opacity: 0.5 }} />}
                    >
                      <MenuItem value="1m">Last Month</MenuItem>
                      <MenuItem value="3m">Last 3 Months</MenuItem>
                      <MenuItem value="6m">Last 6 Months</MenuItem>
                      <MenuItem value="1y">Last Year</MenuItem>
                    </Select>
                  </FormControl>
                </Box>

                <Grid container spacing={3} sx={{ mb: 4 }}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Card>
                      <CardContent>
                        <Typography variant="subtitle2" color="textSecondary">Emails Sent</Typography>
                        <Typography variant="h4">{totals.sent}</Typography>
                        <Box sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
                          <SendIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                          <Typography variant="body2">
                            {dateRange === '1m' ? 'Last month' :
                             dateRange === '3m' ? 'Last 3 months' :
                             dateRange === '6m' ? 'Last 6 months' : 'Last year'}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Card>
                      <CardContent>
                        <Typography variant="subtitle2" color="textSecondary">Open Rate</Typography>
                        <Typography variant="h4">{totals.openRate.toFixed(1)}%</Typography>
                        <Box sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
                          <VisibilityIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                          <Typography variant="body2">
                            {totals.opened} opens
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Card>
                      <CardContent>
                        <Typography variant="subtitle2" color="textSecondary">Click Rate</Typography>
                        <Typography variant="h4">{totals.clickRate.toFixed(1)}%</Typography>
                        <Box sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
                          <CheckCircleIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                          <Typography variant="body2">
                            {totals.clicked} clicks
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Card>
                      <CardContent>
                        <Typography variant="subtitle2" color="textSecondary">Conversion Rate</Typography>
                        <Typography variant="h4">{totals.conversionRate.toFixed(1)}%</Typography>
                        <Box sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
                          <PersonIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                          <Typography variant="body2">
                            {totals.converted} conversions
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={8}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="h6" gutterBottom>Email Metrics Over Time</Typography>
                      <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
                    </Paper>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Paper sx={{ p: 2, height: '100%' }}>
                      <Typography variant="h6" gutterBottom>Recipient Segments</Typography>
                      <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              </TabPanel>

              <TabPanel value={tabValue} index={1}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6">Campaign Performance</Typography>
                  <FormControl size="small" sx={{ minWidth: 200 }}>
                    <InputLabel id="campaign-label">Campaign</InputLabel>
                    <Select
                      labelId="campaign-label"
                      id="campaign"
                      value={selectedCampaign}
                      label="Campaign"
                      onChange={handleCampaignChange}
                    >
                      <MenuItem value="all">All Campaigns</MenuItem>
                      {campaignData.map((campaign) => (
                        <MenuItem key={campaign.id} value={campaign.id.toString()}>
                          {campaign.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>

                <TableContainer component={Paper} sx={{ mb: 4 }}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Campaign Name</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Sent Date</TableCell>
                        <TableCell align="right">Recipients</TableCell>
                        <TableCell align="right">Open Rate</TableCell>
                        <TableCell align="right">Click Rate</TableCell>
                        <TableCell align="right">Reply Rate</TableCell>
                        <TableCell align="right">Conversion Rate</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {(selectedCampaign === 'all' ? campaignData : campaignData.filter(c => c.id.toString() === selectedCampaign)).map((campaign) => (
                        <TableRow key={campaign.id}>
                          <TableCell component="th" scope="row">
                            {campaign.name}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={campaign.status}
                              color={
                                campaign.status === 'completed' ? 'success' :
                                campaign.status === 'active' ? 'primary' :
                                'default'
                              }
                              size="small"
                            />
                          </TableCell>
                          <TableCell>{campaign.sentDate}</TableCell>
                          <TableCell align="right">{campaign.recipients}</TableCell>
                          <TableCell align="right">{campaign.openRate.toFixed(1)}%</TableCell>
                          <TableCell align="right">{campaign.clickRate.toFixed(1)}%</TableCell>
                          <TableCell align="right">{campaign.replyRate.toFixed(1)}%</TableCell>
                          <TableCell align="right">{campaign.conversionRate.toFixed(1)}%</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {selectedCampaign !== 'all' && (
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="h6" gutterBottom>Campaign Performance Breakdown</Typography>
                        <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
                      </Paper>
                    </Grid>
                  </Grid>
                )}
              </TabPanel>

              <TabPanel value={tabValue} index={2}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6">Scheduled Reports</Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => alert('This would open a dialog to schedule a new recurring report')}
                  >
                    Schedule New Report
                  </Button>
                </Box>

                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Report Name</TableCell>
                        <TableCell>Frequency</TableCell>
                        <TableCell>Recipients</TableCell>
                        <TableCell>Last Sent</TableCell>
                        <TableCell>Next Scheduled</TableCell>
                        <TableCell>Format</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {scheduledReports.map((report) => (
                        <TableRow key={report.id}>
                          <TableCell component="th" scope="row">
                            {report.name}
                          </TableCell>
                          <TableCell>{report.frequency}</TableCell>
                          <TableCell>{report.recipients.join(', ')}</TableCell>
                          <TableCell>{report.lastSent}</TableCell>
                          <TableCell>{report.nextScheduled}</TableCell>
                          <TableCell>{report.format}</TableCell>
                          <TableCell>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={report.active}
                                  onChange={() => {}}
                                  size="small"
                                />
                              }
                              label={report.active ? 'Active' : 'Inactive'}
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Tooltip title="Edit">
                              <IconButton size="small">
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete">
                              <IconButton size="small">
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Send Now">
                              <IconButton size="small">
                                <SendIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </TabPanel>
            </Paper>

            {/* Generate Report Dialog */}
            <Dialog open={openNewReportDialog} onClose={handleCloseNewReportDialog} maxWidth="sm" fullWidth>
              <DialogTitle>Generate Email Report</DialogTitle>
              <DialogContent>
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel id="report-type-label">Report Type</InputLabel>
                      <Select
                        labelId="report-type-label"
                        defaultValue="performance"
                        label="Report Type"
                      >
                        <MenuItem value="performance">Performance Overview</MenuItem>
                        <MenuItem value="campaign">Campaign Analysis</MenuItem>
                        <MenuItem value="detailed">Detailed Analytics</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <DatePicker
                      label="Start Date"
                      value={startDate}
                      onChange={(newValue) => setStartDate(newValue)}
                      slotProps={{ textField: { fullWidth: true } }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <DatePicker
                      label="End Date"
                      value={endDate}
                      onChange={(newValue) => setEndDate(newValue)}
                      slotProps={{ textField: { fullWidth: true } }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel id="format-label">Format</InputLabel>
                      <Select
                        labelId="format-label"
                        defaultValue="pdf"
                        label="Format"
                      >
                        <MenuItem value="pdf">PDF</MenuItem>
                        <MenuItem value="excel">Excel</MenuItem>
                        <MenuItem value="csv">CSV</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      label="Email Recipients (optional)"
                      fullWidth
                      placeholder="Enter email addresses separated by commas"
                      helperText="Leave blank to download directly"
                    />
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={handleCloseNewReportDialog}>Cancel</Button>
                <Button
                  onClick={handleGenerateReport}
                  variant="contained"
                  disabled={isGeneratingReport || !startDate || !endDate}
                  startIcon={isGeneratingReport ? <CircularProgress size={20} /> : <DownloadIcon />}
                >
                  {isGeneratingReport ? 'Generating...' : 'Generate Report'}
                </Button>
              </DialogActions>
            </Dialog>
          </>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default EmailReports;
