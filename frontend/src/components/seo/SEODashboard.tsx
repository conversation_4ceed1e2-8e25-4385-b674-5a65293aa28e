import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import { TabPanel, a11yProps } from '../common/TabsNavigation';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Button,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Tooltip,
  Tab,
  Tabs,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Search as SearchIcon,
  Link as LinkIcon,
  Language as LanguageIcon,
  Speed as SpeedIcon,
  DeviceHub as DeviceHubIcon,
  BarChart as BarChartIcon,
  FilterList as FilterListIcon,
  DateRange as DateRangeIcon,
  GetApp as GetAppIcon,
  Refresh as RefreshIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon
} from '@mui/icons-material';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON>ltip as RechartsTooltip, 
  Legend, 
  ResponsiveContainer,
  BarChart,
  Bar
} from 'recharts';
// Recharts wrapper components - commented out due to TypeScript errors
// These components will be replaced with direct usage of Recharts components
/*
const BarWrapper = (props) => <Bar {...props} />;
const XAxisWrapper = (props) => <XAxis {...props} />;
const YAxisWrapper = (props) => <YAxis {...props} />;
const LineWrapper = (props) => <Line {...props} />;
const PieWrapper = (props) => <Pie {...props} />;
const TooltipWrapper = (props) => <Tooltip {...props} />;
const LegendWrapper = (props) => <Legend {...props} />;
*/

// Sample SEO data
const trafficData = [
  { date: '2023-01', organic: 1200, direct: 800, referral: 400, social: 300 },
  { date: '2023-02', organic: 1300, direct: 850, referral: 450, social: 350 },
  { date: '2023-03', organic: 1400, direct: 900, referral: 500, social: 400 },
  { date: '2023-04', organic: 1600, direct: 950, referral: 550, social: 450 },
  { date: '2023-05', organic: 1800, direct: 1000, referral: 600, social: 500 },
  { date: '2023-06', organic: 2000, direct: 1050, referral: 650, social: 550 },
  { date: '2023-07', organic: 2200, direct: 1100, referral: 700, social: 600 },
];

const keywordData = [
  { keyword: 'water treatment chemicals', position: 3, change: 2, volume: 1200, difficulty: 65 },
  { keyword: 'industrial cooling tower treatment', position: 5, change: -1, volume: 880, difficulty: 58 },
  { keyword: 'boiler water treatment solutions', position: 7, change: 3, volume: 720, difficulty: 62 },
  { keyword: 'film forming amine technology', position: 2, change: 5, volume: 320, difficulty: 45 },
  { keyword: 'corrosion prevention industrial', position: 9, change: 0, volume: 590, difficulty: 70 },
  { keyword: 'energy efficient water treatment', position: 12, change: -2, volume: 480, difficulty: 68 },
  { keyword: 'mexel 432 water treatment', position: 1, change: 0, volume: 90, difficulty: 30 },
];

const pagePerformanceData = [
  { page: '/products/mexel-432', traffic: 850, position: 3.2, conversions: 32 },
  { page: '/solutions/cooling-towers', traffic: 720, position: 4.5, conversions: 28 },
  { page: '/solutions/boiler-treatment', traffic: 680, position: 5.1, conversions: 25 },
  { page: '/case-studies/energy-savings', traffic: 520, position: 6.8, conversions: 18 },
  { page: '/about-us', traffic: 450, position: 8.2, conversions: 5 },
];

const competitorData = [
  { competitor: 'ChemTreat', keywords: 245, topKeywords: 42, traffic: 15800 },
  { competitor: 'Nalco Water', keywords: 312, topKeywords: 68, traffic: 22400 },
  { competitor: 'Kurita', keywords: 198, topKeywords: 35, traffic: 12600 },
  { competitor: 'Solenis', keywords: 223, topKeywords: 39, traffic: 14200 },
];

export const SEODashboard = () => {
  const [tabValue, setTabValue] = React.useState(0);
  const [isLoading, setIsLoading] = React.useState(true);
  const [dateRange, setDateRange] = React.useState('6m');
  const [keywordSort, setKeywordSort] = React.useState('position');

  React.useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleTabChange = (_event: any, newValue: number) => {
    setTabValue(newValue);
  };

  const handleDateRangeChange = (event: SelectChangeEvent) => {
    setDateRange(event.target.value);
  };

  const handleKeywordSortChange = (event: SelectChangeEvent) => {
    setKeywordSort(event.target.value);
  };

  // Sort keywords based on selected sort option
  const sortedKeywords = [...keywordData].sort((a, b) => {
    switch (keywordSort) {
      case 'position':
        return a.position - b.position;
      case 'volume':
        return b.volume - a.volume;
      case 'change':
        return b.change - a.change;
      case 'difficulty':
        return a.difficulty - b.difficulty;
      default:
        return a.position - b.position;
    }
  });

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        SEO Insights Dashboard
      </Typography>

      {isLoading ? (
        <LinearProgress sx={{ mb: 4 }} />
      ) : (
        <>
          {/* KPI Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Paper elevation={2} sx={{ p: 2, height: '100%' }}>
                <Typography variant="subtitle2" color="textSecondary">Organic Traffic</Typography>
                <Typography variant="h4">2,200</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <TrendingUpIcon color="success" fontSize="small" />
                  <Typography variant="body2" color="success.main" sx={{ ml: 0.5 }}>
                    +10% vs last month
                  </Typography>
                </Box>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper elevation={2} sx={{ p: 2, height: '100%' }}>
                <Typography variant="subtitle2" color="textSecondary">Avg. Position</Typography>
                <Typography variant="h4">4.8</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <TrendingUpIcon color="success" fontSize="small" />
                  <Typography variant="body2" color="success.main" sx={{ ml: 0.5 }}>
                    +0.3 vs last month
                  </Typography>
                </Box>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper elevation={2} sx={{ p: 2, height: '100%' }}>
                <Typography variant="subtitle2" color="textSecondary">Indexed Pages</Typography>
                <Typography variant="h4">128</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <TrendingUpIcon color="success" fontSize="small" />
                  <Typography variant="body2" color="success.main" sx={{ ml: 0.5 }}>
                    +5 new pages
                  </Typography>
                </Box>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper elevation={2} sx={{ p: 2, height: '100%' }}>
                <Typography variant="subtitle2" color="textSecondary">Backlinks</Typography>
                <Typography variant="h4">342</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <TrendingUpIcon color="success" fontSize="small" />
                  <Typography variant="body2" color="success.main" sx={{ ml: 0.5 }}>
                    +12 new links
                  </Typography>
                </Box>
              </Paper>
            </Grid>
          </Grid>

          {/* Tabs and Filters */}
          <Paper sx={{ p: 2, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Tabs value={tabValue} onChange={handleTabChange} aria-label="seo dashboard tabs">
                <Tab label="Overview" {...a11yProps(0)} />
                <Tab label="Keywords" {...a11yProps(1)} />
                <Tab label="Pages" {...a11yProps(2)} />
                <Tab label="Competitors" {...a11yProps(3)} />
              </Tabs>
              
              <Box sx={{ display: 'flex', gap: 2 }}>
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel id="date-range-label">Date Range</InputLabel>
                  <Select
                    labelId="date-range-label"
                    id="date-range"
                    value={dateRange}
                    label="Date Range"
                    onChange={handleDateRangeChange}
                    startAdornment={<DateRangeIcon fontSize="small" sx={{ mr: 1, opacity: 0.5 }} />}
                  >
                    <MenuItem value="1m">Last Month</MenuItem>
                    <MenuItem value="3m">Last 3 Months</MenuItem>
                    <MenuItem value="6m">Last 6 Months</MenuItem>
                    <MenuItem value="1y">Last Year</MenuItem>
                  </Select>
                </FormControl>
                
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  size="small"
                >
                  Refresh
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<GetAppIcon />}
                  size="small"
                >
                  Export
                </Button>
              </Box>
            </Box>

            {/* Tab Panels */}
            <TabPanel value={tabValue} index={0}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>Traffic Overview</Typography>
                  <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>Top Performing Keywords</Typography>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Keyword</TableCell>
                          <TableCell align="right">Position</TableCell>
                          <TableCell align="right">Change</TableCell>
                          <TableCell align="right">Volume</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {keywordData.slice(0, 5).map((row) => (
                          <TableRow key={row.keyword}>
                            <TableCell component="th" scope="row">
                              {row.keyword}
                            </TableCell>
                            <TableCell align="right">{row.position}</TableCell>
                            <TableCell align="right">
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                                {row.change > 0 ? (
                                  <ArrowUpwardIcon fontSize="small" color="success" />
                                ) : row.change < 0 ? (
                                  <ArrowDownwardIcon fontSize="small" color="error" />
                                ) : null}
                                {Math.abs(row.change)}
                              </Box>
                            </TableCell>
                            <TableCell align="right">{row.volume}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>Top Pages</Typography>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Page</TableCell>
                          <TableCell align="right">Traffic</TableCell>
                          <TableCell align="right">Avg. Position</TableCell>
                          <TableCell align="right">Conversions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {pagePerformanceData.slice(0, 5).map((row) => (
                          <TableRow key={row.page}>
                            <TableCell component="th" scope="row">
                              {row.page}
                            </TableCell>
                            <TableCell align="right">{row.traffic}</TableCell>
                            <TableCell align="right">{row.position.toFixed(1)}</TableCell>
                            <TableCell align="right">{row.conversions}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            </TabPanel>
            
            <TabPanel value={tabValue} index={1}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">Keyword Rankings</Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <TextField
                    size="small"
                    placeholder="Search keywords..."
                    InputProps={{
                      startAdornment: <SearchIcon fontSize="small" sx={{ mr: 1, opacity: 0.5 }} />,
                    }}
                    sx={{ width: 200 }}
                  />
                  
                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel id="keyword-sort-label">Sort By</InputLabel>
                    <Select
                      labelId="keyword-sort-label"
                      id="keyword-sort"
                      value={keywordSort}
                      label="Sort By"
                      onChange={handleKeywordSortChange}
                    >
                      <MenuItem value="position">Position</MenuItem>
                      <MenuItem value="volume">Search Volume</MenuItem>
                      <MenuItem value="change">Position Change</MenuItem>
                      <MenuItem value="difficulty">Difficulty</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </Box>
              
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Keyword</TableCell>
                      <TableCell align="right">Position</TableCell>
                      <TableCell align="right">Change</TableCell>
                      <TableCell align="right">Volume</TableCell>
                      <TableCell align="right">Difficulty</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {sortedKeywords.map((row) => (
                      <TableRow key={row.keyword}>
                        <TableCell component="th" scope="row">
                          {row.keyword}
                        </TableCell>
                        <TableCell align="right">
                          <Chip 
                            label={row.position} 
                            size="small"
                            color={
                              row.position <= 3 ? 'success' :
                              row.position <= 10 ? 'primary' :
                              'default'
                            }
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                            {row.change > 0 ? (
                              <ArrowUpwardIcon fontSize="small" color="success" />
                            ) : row.change < 0 ? (
                              <ArrowDownwardIcon fontSize="small" color="error" />
                            ) : null}
                            {Math.abs(row.change)}
                          </Box>
                        </TableCell>
                        <TableCell align="right">{row.volume}</TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                            <LinearProgress
                              variant="determinate"
                              value={row.difficulty}
                              sx={{ 
                                width: 100, 
                                mr: 1,
                                height: 8,
                                borderRadius: 5,
                                backgroundColor: '#e0e0e0',
                                '& .MuiLinearProgress-bar': {
                                  borderRadius: 5,
                                  backgroundColor: 
                                    row.difficulty < 40 ? '#4caf50' :
                                    row.difficulty < 70 ? '#ff9800' :
                                    '#f44336'
                                }
                              }}
                            />
                            {row.difficulty}
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>
            
            <TabPanel value={tabValue} index={2}>
              <Typography variant="h6" gutterBottom>Page Performance</Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Page URL</TableCell>
                      <TableCell align="right">Organic Traffic</TableCell>
                      <TableCell align="right">Avg. Position</TableCell>
                      <TableCell align="right">Conversions</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {pagePerformanceData.map((row) => (
                      <TableRow key={row.page}>
                        <TableCell component="th" scope="row">
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <LinkIcon fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                            {row.page}
                          </Box>
                        </TableCell>
                        <TableCell align="right">{row.traffic}</TableCell>
                        <TableCell align="right">{row.position.toFixed(1)}</TableCell>
                        <TableCell align="right">{row.conversions}</TableCell>
                        <TableCell align="right">
                          <Button size="small">Analyze</Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>
            
            <TabPanel value={tabValue} index={3}>
              <Typography variant="h6" gutterBottom>Competitor Analysis</Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Competitor</TableCell>
                          <TableCell align="right">Keywords</TableCell>
                          <TableCell align="right">Top 10 Keywords</TableCell>
                          <TableCell align="right">Est. Traffic</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {competitorData.map((row) => (
                          <TableRow key={row.competitor}>
                            <TableCell component="th" scope="row">
                              {row.competitor}
                            </TableCell>
                            <TableCell align="right">{row.keywords}</TableCell>
                            <TableCell align="right">{row.topKeywords}</TableCell>
                            <TableCell align="right">{row.traffic.toLocaleString()}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom>Competitor Keyword Overlap</Typography>
                  <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
                </Grid>
              </Grid>
            </TabPanel>
          </Paper>
        </>
      )}
    </Box>
  );
};

export default SEODashboard;
