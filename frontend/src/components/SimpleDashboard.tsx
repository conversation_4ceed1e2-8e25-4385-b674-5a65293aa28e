import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  List,
  ListItem,
  ListItemText,
  Divider,
  Button
} from '@mui/material';

/**
 * A simple dashboard component that displays the email templates and other features
 */
const SimpleDashboard = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Mexel Dashboard
      </Typography>

      <Typography variant="subtitle1" color="text.secondary" paragraph>
        Welcome to the Mexel Dashboard. Here you can manage your email templates, monitor campaigns, and track performance.
      </Typography>

      <Grid container spacing={3} sx={{ mt: 2 }}>
        {/* Email Templates Section */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Email Templates by Role
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <List>
              <ListItem>
                <ListItemText
                  primary="Manager Templates"
                  secondary="Templates for facility managers and decision makers"
                />
                <Button size="small" variant="outlined">View</Button>
              </ListItem>
              <Divider />

              <ListItem>
                <ListItemText
                  primary="Engineer Templates"
                  secondary="Templates for technical professionals and engineers"
                />
                <Button size="small" variant="outlined">View</Button>
              </ListItem>
              <Divider />

              <ListItem>
                <ListItemText
                  primary="Buyer Templates"
                  secondary="Templates for procurement specialists and buyers"
                />
                <Button size="small" variant="outlined">View</Button>
              </ListItem>
              <Divider />

              <ListItem>
                <ListItemText
                  primary="Technician Templates"
                  secondary="Templates for maintenance technicians and operators"
                />
                <Button size="small" variant="outlined">View</Button>
              </ListItem>
            </List>
          </Paper>
        </Grid>

        {/* Sector Performance */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Sector Performance
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <List>
              <ListItem>
                <ListItemText
                  primary="Power Plants"
                  secondary="45 leads, 28 responses, 12 conversions"
                />
              </ListItem>
              <Divider />

              <ListItem>
                <ListItemText
                  primary="HVAC Systems"
                  secondary="32 leads, 18 responses, 8 conversions"
                />
              </ListItem>
              <Divider />

              <ListItem>
                <ListItemText
                  primary="Mining Operations"
                  secondary="28 leads, 15 responses, 6 conversions"
                />
              </ListItem>
              <Divider />

              <ListItem>
                <ListItemText
                  primary="Oil & Gas"
                  secondary="37 leads, 20 responses, 9 conversions"
                />
              </ListItem>
              <Divider />

              <ListItem>
                <ListItemText
                  primary="Agriculture"
                  secondary="19 leads, 12 responses, 5 conversions"
                />
              </ListItem>
              <Divider />

              <ListItem>
                <ListItemText
                  primary="Cooling Towers"
                  secondary="41 leads, 25 responses, 11 conversions"
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>

        {/* Email Campaign Stats */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="Email Campaigns" />
            <CardContent>
              <Typography variant="h3" align="center" color="primary">
                1,250
              </Typography>
              <Typography variant="subtitle1" align="center" color="text.secondary">
                Emails Sent
              </Typography>

              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">
                  Open Rate: <strong>58%</strong>
                </Typography>
                <Typography variant="body2">
                  Click Rate: <strong>32%</strong>
                </Typography>
                <Typography variant="body2">
                  Response Rate: <strong>15%</strong>
                </Typography>
                <Typography variant="body2">
                  Conversion Rate: <strong>8%</strong>
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* LinkedIn Stats */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="LinkedIn Outreach" />
            <CardContent>
              <Typography variant="h3" align="center" color="primary">
                450
              </Typography>
              <Typography variant="subtitle1" align="center" color="text.secondary">
                Connection Requests
              </Typography>

              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">
                  Acceptance Rate: <strong>65%</strong>
                </Typography>
                <Typography variant="body2">
                  Response Rate: <strong>42%</strong>
                </Typography>
                <Typography variant="body2">
                  Meeting Rate: <strong>18%</strong>
                </Typography>
                <Typography variant="body2">
                  Conversion Rate: <strong>7%</strong>
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Tender Stats */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="Tender Opportunities" />
            <CardContent>
              <Typography variant="h3" align="center" color="primary">
                28
              </Typography>
              <Typography variant="subtitle1" align="center" color="text.secondary">
                Active Tenders
              </Typography>

              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">
                  High Priority: <strong>12</strong>
                </Typography>
                <Typography variant="body2">
                  Medium Priority: <strong>10</strong>
                </Typography>
                <Typography variant="body2">
                  Low Priority: <strong>6</strong>
                </Typography>
                <Typography variant="body2">
                  Total Value: <strong>R 45.2M</strong>
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SimpleDashboard;
