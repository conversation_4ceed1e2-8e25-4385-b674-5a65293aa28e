import React, { useEffect, useMemo, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { CircularProgress, Box, Typography } from "@mui/material";
import { AuthService } from "../services/AuthService";
import {
  useAuthErrorStore,
  AuthErrorType,
  withAuthErrorHandling,
  shouldRedirectToLogin,
} from "../services/authErrorHandling";

interface ProtectedRouteProps {
  children: any;
  requiredRoles?: string[];
}

const ProtectedRoute = ({ children, requiredRoles }: ProtectedRouteProps) => {
  const [isAuthenticated, setIsAuthenticated] = React.useState(null);
  const [hasRequiredRole, setHasRequiredRole] = React.useState(null);
  const location = useLocation();
  const authService = useMemo(() => new AuthService(), []);
  const { setError } = useAuthErrorStore();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if user is logged in
        const isLoggedIn = authService.isLoggedIn();

        if (!isLoggedIn) {
          setIsAuthenticated(false);
          setError(AuthErrorType.SESSION_EXPIRED);
          return;
        }

        // Verify token is still valid by getting current user using our error handling wrapper
        await withAuthErrorHandling(
          async () => {
            const user = await authService.getCurrentUser();

            if (!user) {
              setIsAuthenticated(false);
              setError(AuthErrorType.UNAUTHORIZED);
              return null;
            }

            setIsAuthenticated(true);

            // Check if user has required role
            if (requiredRoles && requiredRoles.length > 0) {
              const hasRole = requiredRoles.includes(user.role);
              setHasRequiredRole(hasRole);

              if (!hasRole) {
                setError(AuthErrorType.FORBIDDEN);
              }
            } else {
              setHasRequiredRole(true);
            }

            return user;
          },
          (errorType) => {
            // This callback handles any auth errors from the API call
            setIsAuthenticated(false);
          }
        );
      } catch (error) {
        console.error("Authentication check failed:", error);
        setIsAuthenticated(false);
      }
    };

    checkAuth();
  }, [authService, requiredRoles, location, setError]);

  // Show loading while checking authentication
  if (isAuthenticated === null || hasRequiredRole === null) {
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          height: "100vh",
        }}
      >
        <CircularProgress />
        <Typography variant="body1" sx={{ mt: 2 }}>
          Verifying authentication...
        </Typography>
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Redirect to unauthorized page if doesn't have required role
  if (!hasRequiredRole) {
    return <Navigate to="/unauthorized" state={{ from: location }} replace />;
  }

  // Render children if authenticated and has required role
  return <>{children}</>;
};

export default ProtectedRoute;
