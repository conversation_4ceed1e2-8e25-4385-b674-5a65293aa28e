import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Divider,
  Button,
  Chip,
  Paper,
  IconButton,
  Tooltip,
  Grid,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  Snackbar,
  Alert,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Badge,
  useTheme,
  Collapse,
  Tabs,
  Tab
} from '@mui/material';
import {
  ContentCopy as ContentCopyIcon,
  Edit as EditIcon,
  Send as SendIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  OpenInNew as OpenInNewIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Preview as PreviewIcon,
  Save as SaveIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
  PersonAdd as PersonAddIcon,
  FormatColorText as FormatColorTextIcon
} from '@mui/icons-material';
import { IEmailTemplate } from '../types/email';

interface EmailPreviewProps {
  template: IEmailTemplate;
  onEdit?: (template: IEmailTemplate) => void;
  onSend?: (template: IEmailTemplate) => void;
  onCopy?: (template: IEmailTemplate) => void;
  onSave?: (template: IEmailTemplate) => void;
  onDelete?: (template: IEmailTemplate) => void;
  onSchedule?: (template: IEmailTemplate, date: Date) => void;
  showControls?: boolean;
  showMetadata?: boolean;
  isLoading?: boolean;
  previewMode?: 'compact' | 'full' | 'mobile';
  recipientEmail?: string;
  onRecipientChange?: (email: string) => void;
  status?: 'draft' | 'scheduled' | 'sent' | 'error';
  lastSent?: Date;
  stats?: {
    opens?: number;
    clicks?: number;
    replies?: number;
  };
}

/**
 * Email Preview Component
 *
 * Displays a preview of an email template with optional controls for editing, sending, and copying.
 * Enhanced with additional functionality for scheduling, saving, and viewing analytics.
 */
export const EmailPreview = ({ template,
  onEdit,
  onSend,
  onCopy,
  onSave,
  onDelete,
  onSchedule,
  showControls = true,
  showMetadata = true,
  isLoading = false,
  previewMode = 'full',
  recipientEmail = '',
  onRecipientChange,
  status = 'draft',
  lastSent,
  stats
 }: EmailPreviewProps) => {
  const theme = useTheme();
  const [showRawHtml, setShowRawHtml] = React.useState(false);
  const [selectedVariant, setSelectedVariant] = React.useState('default');
  const [showDetails, setShowDetails] = React.useState(false);
  const [snackbarOpen, setSnackbarOpen] = React.useState(false);
  const [snackbarMessage, setSnackbarMessage] = React.useState('');
  const [snackbarSeverity, setSnackbarSeverity] = React.useState('success');
  const [sendDialogOpen, setSendDialogOpen] = React.useState(false);
  const [scheduleDialogOpen, setScheduleDialogOpen] = React.useState(false);
  const [recipient, setRecipient] = React.useState(recipientEmail);
  const [scheduleDate, setScheduleDate] = React.useState(new Date(Date.now() + 24 * 60 * 60 * 1000)); // Tomorrow
  const [previewTab, setPreviewTab] = React.useState(0); // 0 = desktop, 1 = mobile

  // Update recipient if prop changes
  React.useEffect(() => {
    setRecipient(recipientEmail);
  }, [recipientEmail]);

  // Handle variant selection if template has A/B test variants
  const handleVariantChange = (event: SelectChangeEvent) => {
    setSelectedVariant(event.target.value);
  };

  // Get variants if they exist in metadata
  const variants = template.metadata?.variants || [];
  const hasVariants = variants.length > 0;

  // Get the current template content based on selected variant
  const currentTemplate = selectedVariant === 'default'
    ? template
    : variants.find((v: any) => v.id === selectedVariant) || template;

  // Handle copy to clipboard
  const handleCopy = () => {
    navigator.clipboard.writeText(currentTemplate.body);
    if (onCopy) onCopy(currentTemplate);
    showSnackbar('Email content copied to clipboard', 'success');
  };

  // Handle send dialog
  const handleSendClick = () => {
    setSendDialogOpen(true);
  };

  const handleSendConfirm = () => {
    if (onSend) {
      onSend(currentTemplate);
      showSnackbar('Email sent successfully', 'success');
    }
    setSendDialogOpen(false);
  };

  // Handle schedule dialog
  const handleScheduleClick = () => {
    setScheduleDialogOpen(true);
  };

  const handleScheduleConfirm = () => {
    if (onSchedule) {
      onSchedule(currentTemplate, scheduleDate);
      showSnackbar(`Email scheduled for ${scheduleDate.toLocaleString()}`, 'success');
    }
    setScheduleDialogOpen(false);
  };

  // Handle recipient change
  const handleRecipientChange = (e: any) => {
    setRecipient(e.target.value);
    if (onRecipientChange) {
      onRecipientChange(e.target.value);
    }
  };

  // Handle save
  const handleSave = () => {
    if (onSave) {
      onSave(currentTemplate);
      showSnackbar('Template saved successfully', 'success');
    }
  };

  // Handle delete
  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this template?')) {
      if (onDelete) {
        onDelete(currentTemplate);
        showSnackbar('Template deleted', 'info');
      }
    }
  };

  // Show snackbar message
  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Handle preview tab change
  const handlePreviewTabChange = (_event: any, newValue: number) => {
    setPreviewTab(newValue);
  };

  // Get status color and icon
  const getStatusColor = () => {
    switch (status) {
      case 'sent': return 'success';
      case 'scheduled': return 'info';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'sent': return <CheckCircleIcon fontSize="small" color="success" />;
      case 'scheduled': return <ScheduleIcon fontSize="small" color="info" />;
      case 'error': return <ErrorIcon fontSize="small" color="error" />;
      default: return null;
    }
  };

  // Determine if we should show compact view
  const isCompact = previewMode === 'compact';

  return (
    <Box sx={{
      width: '100%',
      mb: 3,
      opacity: isLoading ? 0.7 : 1,
      position: 'relative'
    }}>
      {isLoading && (
        <Box sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 10
        }}>
          <CircularProgress />
        </Box>
      )}

      {/* Email Header */}
      <Card
        elevation={2}
        sx={{
          mb: 0.5,
          borderRadius: '8px 8px 0 0',
          borderLeft: status !== 'draft' ? `4px solid ${theme.palette[getStatusColor()].main}` : undefined
        }}
      >
        <CardContent sx={{ pb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="h6" component="div">
                {template.name || 'Email Preview'}
              </Typography>
              {getStatusIcon() && (
                <Tooltip title={`Status: ${status}`}>
                  <Box sx={{ ml: 1 }}>
                    {getStatusIcon()}
                  </Box>
                </Tooltip>
              )}
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {hasVariants && (
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel id="variant-select-label">Variant</InputLabel>
                  <Select
                    labelId="variant-select-label"
                    id="variant-select"
                    value={selectedVariant}
                    label="Variant"
                    onChange={handleVariantChange}
                  >
                    <MenuItem value="default">Default</MenuItem>
                    {variants.map((variant: any) => (
                      <MenuItem key={variant.id} value={variant.id}>
                        {variant.name || `Variant ${variant.id}`}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}

              {previewMode === 'full' && (
                <Tabs
                  value={previewTab}
                  onChange={handlePreviewTabChange}
                  sx={{ minHeight: 0, height: 36 }}
                >
                  <Tab
                    label="Desktop"
                    sx={{ minHeight: 0, py: 1 }}
                  />
                  <Tab
                    label="Mobile"
                    sx={{ minHeight: 0, py: 1 }}
                  />
                </Tabs>
              )}

              <Tooltip title={showRawHtml ? "Show Rendered HTML" : "Show Raw HTML"}>
                <IconButton
                  size="small"
                  onClick={() => setShowRawHtml(!showRawHtml)}
                  color={showRawHtml ? "primary" : "default"}
                >
                  {showRawHtml ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </IconButton>
              </Tooltip>

              <Tooltip title={showDetails ? "Hide Details" : "Show Details"}>
                <IconButton
                  size="small"
                  onClick={() => setShowDetails(!showDetails)}
                  color={showDetails ? "primary" : "default"}
                >
                  {showDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          <Typography variant="subtitle1" color="text.primary" gutterBottom>
            Subject: {currentTemplate.subject}
          </Typography>

          {/* Recipient field */}
          {(onRecipientChange || recipient) && (
            <Box sx={{ mt: 1, mb: 2 }}>
              <TextField
                label="Recipient"
                value={recipient}
                onChange={handleRecipientChange}
                size="small"
                fullWidth={isCompact}
                sx={{ maxWidth: isCompact ? '100%' : 400 }}
                placeholder="Enter recipient email"
                InputProps={{
                  startAdornment: <PersonAddIcon fontSize="small" sx={{ mr: 1, opacity: 0.6 }} />,
                }}
              />
            </Box>
          )}

          {/* Metadata chips */}
          {showMetadata && template.metadata && (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
              {template.metadata.type && (
                <Chip
                  label={`Type: ${template.metadata.type}`}
                  size="small"
                  variant="outlined"
                  color="primary"
                />
              )}
              {template.metadata.step && (
                <Chip
                  label={`Step: ${template.metadata.step}/${template.metadata.totalSteps || '?'}`}
                  size="small"
                  variant="outlined"
                />
              )}
              {template.metadata.tone && (
                <Chip
                  label={`Tone: ${template.metadata.tone}`}
                  size="small"
                  variant="outlined"
                />
              )}
              {status === 'sent' && lastSent && (
                <Chip
                  label={`Sent: ${new Date(lastSent).toLocaleString()}`}
                  size="small"
                  variant="outlined"
                  color="success"
                  icon={<CheckCircleIcon />}
                />
              )}
              {status === 'scheduled' && (
                <Chip
                  label={`Scheduled`}
                  size="small"
                  variant="outlined"
                  color="info"
                  icon={<ScheduleIcon />}
                />
              )}
            </Box>
          )}

          {/* Expanded details section */}
          <Collapse in={showDetails}>
            <Box sx={{ mt: 2 }}>
              <Divider sx={{ mb: 2 }} />

              {/* Stats section */}
              {stats && (
                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={4}>
                    <Paper sx={{ p: 1, textAlign: 'center', bgcolor: 'background.default' }}>
                      <Typography variant="h6" color="primary">{stats.opens || 0}</Typography>
                      <Typography variant="body2" color="text.secondary">Opens</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={4}>
                    <Paper sx={{ p: 1, textAlign: 'center', bgcolor: 'background.default' }}>
                      <Typography variant="h6" color="primary">{stats.clicks || 0}</Typography>
                      <Typography variant="body2" color="text.secondary">Clicks</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={4}>
                    <Paper sx={{ p: 1, textAlign: 'center', bgcolor: 'background.default' }}>
                      <Typography variant="h6" color="primary">{stats.replies || 0}</Typography>
                      <Typography variant="body2" color="text.secondary">Replies</Typography>
                    </Paper>
                  </Grid>
                </Grid>
              )}

              {/* Additional metadata */}
              {template.metadata && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>Additional Information</Typography>
                  <Grid container spacing={1}>
                    {Object.entries(template.metadata).map(([key, value]) => {
                      // Skip already displayed metadata or complex objects
                      if (['type', 'step', 'totalSteps', 'tone', 'variants'].includes(key) ||
                          typeof value === 'object') {
                        return null;
                      }
                      return (
                        <Grid item xs={6} key={key}>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                            {key.charAt(0).toUpperCase() + key.slice(1)}:
                          </Typography>
                          <Typography variant="body2">{String(value)}</Typography>
                        </Grid>
                      );
                    })}
                  </Grid>
                </Box>
              )}
            </Box>
          </Collapse>
        </CardContent>
      </Card>

      {/* Email Body */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          minHeight: '200px',
          backgroundColor: '#fff',
          fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
          fontSize: '14px',
          lineHeight: 1.5,
          maxWidth: previewTab === 1 ? 375 : '100%', // Mobile width if mobile tab selected
          mx: previewTab === 1 ? 'auto' : 0, // Center if mobile view
          border: previewTab === 1 ? '10px solid #333' : 'none', // Phone frame if mobile view
          borderRadius: previewTab === 1 ? '20px' : '0 0 8px 8px',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {previewTab === 1 && (
          <Box sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '20px',
            bgcolor: '#333',
            borderTopLeftRadius: '10px',
            borderTopRightRadius: '10px',
            zIndex: 1
          }} />
        )}

        <Box sx={{
          height: previewTab === 1 ? '500px' : 'auto',
          overflowY: previewTab === 1 ? 'auto' : 'visible',
          mt: previewTab === 1 ? '20px' : 0
        }}>
          {showRawHtml ? (
            <Box
              component="pre"
              sx={{
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                p: 2,
                backgroundColor: '#f5f5f5',
                borderRadius: 1,
                fontSize: '12px',
                fontFamily: 'monospace',
                overflowX: 'auto'
              }}
            >
              {currentTemplate.body}
            </Box>
          ) : (
            <Box
              dangerouslySetInnerHTML={{ __html: currentTemplate.body }}
              sx={{
                '& a': { color: '#1976d2', textDecoration: 'underline' },
                '& img': { maxWidth: '100%', height: 'auto' }
              }}
            />
          )}
        </Box>
      </Paper>

      {/* Controls */}
      {showControls && (
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          mt: 1,
          gap: 1,
          flexWrap: 'wrap'
        }}>
          {/* Left side buttons */}
          <Box sx={{ display: 'flex', gap: 1 }}>
            {onDelete && (
              <Button
                startIcon={<DeleteIcon />}
                size="small"
                onClick={handleDelete}
                variant="outlined"
                color="error"
              >
                Delete
              </Button>
            )}
          </Box>

          {/* Right side buttons */}
          <Box sx={{ display: 'flex', gap: 1 }}>
            {onCopy && (
              <Button
                startIcon={<ContentCopyIcon />}
                size="small"
                onClick={handleCopy}
                variant="outlined"
              >
                Copy
              </Button>
            )}
            {onSave && (
              <Button
                startIcon={<SaveIcon />}
                size="small"
                onClick={handleSave}
                variant="outlined"
              >
                Save
              </Button>
            )}
            {onEdit && (
              <Button
                startIcon={<EditIcon />}
                size="small"
                onClick={() => onEdit(currentTemplate)}
                variant="outlined"
              >
                Edit
              </Button>
            )}
            {onSchedule && (
              <Button
                startIcon={<ScheduleIcon />}
                size="small"
                onClick={handleScheduleClick}
                variant="outlined"
                color="info"
              >
                Schedule
              </Button>
            )}
            {onSend && (
              <Button
                startIcon={<SendIcon />}
                size="small"
                onClick={handleSendClick}
                variant="contained"
                color="primary"
                disabled={!recipient}
              >
                Send
              </Button>
            )}
          </Box>
        </Box>
      )}

      {/* Send Dialog */}
      <Dialog open={sendDialogOpen} onClose={() => setSendDialogOpen(false)}>
        <DialogTitle>Send Email</DialogTitle>
        <DialogContent>
          <Box sx={{ minWidth: 400, pt: 1 }}>
            <TextField
              label="Recipient"
              value={recipient}
              onChange={handleRecipientChange}
              fullWidth
              margin="normal"
              required
              error={!recipient}
              helperText={!recipient ? "Recipient email is required" : ""}
            />
            <Typography variant="subtitle1" sx={{ mt: 2 }}>
              Email Details:
            </Typography>
            <Typography variant="body2">
              <strong>Subject:</strong> {currentTemplate.subject}
            </Typography>
            {template.metadata?.type && (
              <Typography variant="body2">
                <strong>Type:</strong> {template.metadata.type}
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSendDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleSendConfirm}
            variant="contained"
            color="primary"
            disabled={!recipient}
          >
            Send Now
          </Button>
        </DialogActions>
      </Dialog>

      {/* Schedule Dialog */}
      <Dialog open={scheduleDialogOpen} onClose={() => setScheduleDialogOpen(false)}>
        <DialogTitle>Schedule Email</DialogTitle>
        <DialogContent>
          <Box sx={{ minWidth: 400, pt: 1 }}>
            <TextField
              label="Recipient"
              value={recipient}
              onChange={handleRecipientChange}
              fullWidth
              margin="normal"
              required
              error={!recipient}
              helperText={!recipient ? "Recipient email is required" : ""}
            />

            <TextField
              label="Schedule Date"
              type="datetime-local"
              value={scheduleDate.toISOString().slice(0, 16)}
              onChange={(e) => {
                const date = new Date(e.target.value);
                setScheduleDate(date);
              }}
              fullWidth
              margin="normal"
              InputLabelProps={{
                shrink: true,
              }}
            />

            <Typography variant="subtitle1" sx={{ mt: 2 }}>
              Email Details:
            </Typography>
            <Typography variant="body2">
              <strong>Subject:</strong> {currentTemplate.subject}
            </Typography>
            {template.metadata?.type && (
              <Typography variant="body2">
                <strong>Type:</strong> {template.metadata.type}
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setScheduleDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleScheduleConfirm}
            variant="contained"
            color="primary"
            disabled={!recipient}
          >
            Schedule
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default EmailPreview;
