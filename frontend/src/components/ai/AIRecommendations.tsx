import CancelIcon from "@mui/icons-material/Cancel";
import EmailIcon from "@mui/icons-material/Email";
import LightbulbIcon from "@mui/icons-material/Lightbulb";
import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    CircularProgress,
    Divider,
    FormControl,
    FormControlLabel,
    Grid,
    InputLabel,
    LinearProgress,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    MenuItem,
    Paper,
    Select,
    SelectChangeEvent,
    Switch,
    Tab,
    Tabs,
    Typography
} from '@mui/material';
import React from 'react';
import { TabPanel, a11yProps } from '../common/TabsNavigation';

// Define types for recommendations
interface Recommendation {
  id: number;
  title: string;
  description: string;
  category: string;
  confidence: number;
  impact: string;
  effort: string;
  status: string;
  createdAt: string;
  implemented: boolean;
  implementedDate?: string;
  results?: string;
}

// Sample data
const recommendationCategories = [
  'All',
  'Email Campaigns',
  'LinkedIn Outreach',
  'SEO Optimization',
  'Content Strategy',
  'Lead Generation',
  'Tender Opportunities'
];

const sampleRecommendations = [
  {
    id: 1,
    title: 'Increase email frequency to high-engagement leads',
    description: 'Analysis of your email campaign data shows that leads who opened your last 3 emails have a 68% higher conversion rate. Consider increasing email frequency to this segment with more targeted content about energy efficiency benefits.',
    category: 'Email Campaigns',
    confidence: 92,
    impact: 'high',
    effort: 'medium',
    status: 'new',
    createdAt: '2023-07-10',
    implemented: false
  },
  {
    id: 2,
    title: 'Optimize website for "industrial cooling efficiency" keywords',
    description: 'This keyword cluster has shown increasing search volume (+18% in the last quarter) with moderate competition. Adding dedicated content targeting these terms could improve your organic traffic for high-intent searches.',
    category: 'SEO Optimization',
    confidence: 87,
    impact: 'medium',
    effort: 'high',
    status: 'new',
    createdAt: '2023-07-12',
    implemented: false
  },
  {
    id: 3,
    title: 'Target facility managers in manufacturing sector with LinkedIn campaign',
    description: 'Your connection acceptance rate is 78% higher with facility managers in manufacturing compared to other industries. A targeted campaign highlighting case studies from this sector could yield better results.',
    category: 'LinkedIn Outreach',
    confidence: 85,
    impact: 'high',
    effort: 'medium',
    status: 'implemented',
    createdAt: '2023-07-05',
    implemented: true,
    implementedDate: '2023-07-08',
    results: 'Increased connection rate by 23% and meeting bookings by 15%'
  },
  {
    id: 4,
    title: 'Create comparison content: Mexel vs traditional chemicals',
    description: 'Competitive analysis shows that prospects are frequently searching for comparisons between different water treatment approaches. Creating detailed comparison content could address these questions directly and position Mexel favorably.',
    category: 'Content Strategy',
    confidence: 79,
    impact: 'medium',
    effort: 'medium',
    status: 'new',
    createdAt: '2023-07-15',
    implemented: false
  },
  {
    id: 5,
    title: 'Focus on municipal water tenders in Q3',
    description: 'Historical data shows a 32% increase in municipal water treatment tenders in Q3. Allocating more resources to monitoring and responding to these opportunities could yield higher success rates.',
    category: 'Tender Opportunities',
    confidence: 83,
    impact: 'high',
    effort: 'high',
    status: 'new',
    createdAt: '2023-07-14',
    implemented: false
  },
  {
    id: 6,
    title: 'Segment leads by cooling system type for personalized outreach',
    description: 'Analysis of your CRM data reveals distinct needs based on cooling system types. Creating segmented email sequences for each system type could improve engagement rates.',
    category: 'Lead Generation',
    confidence: 88,
    impact: 'medium',
    effort: 'medium',
    status: 'implemented',
    createdAt: '2023-06-28',
    implemented: true,
    implementedDate: '2023-07-03',
    results: 'Email open rates increased by 15% and click-through rates by 22%'
  }
];

export const AIRecommendations = () => {
  const [tabValue, setTabValue] = React.useState(0);
  const [isLoading, setIsLoading] = React.useState(true);
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const [selectedCategory, setSelectedCategory] = React.useState('All');
  const [showImplemented, setShowImplemented] = React.useState(false);
  const [recommendations, setRecommendations] = React.useState(sampleRecommendations);
  const [sortBy, setSortBy] = React.useState('confidence');

  React.useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleTabChange = (_event: any, newValue: number) => {
    setTabValue(newValue);
  };

  const handleCategoryChange = (event: SelectChangeEvent) => {
    setSelectedCategory(event.target.value);
  };

  const handleSortChange = (event: SelectChangeEvent) => {
    setSortBy(event.target.value);
  };

  const handleRefresh = () => {
    setIsRefreshing(true);

    // Simulate refreshing recommendations
    setTimeout(() => {
      setIsRefreshing(false);
    }, 2000);
  };

  const handleImplement = (id: number) => {
    const today = new Date().toISOString().split('T')[0];
    setRecommendations(recommendations.map(rec => {
      if (rec.id === id) {
        const updatedRec = {
          ...rec,
          status: 'implemented',
          implemented: true,
          implementedDate: today,
        };
        // Add results property if it doesn't exist
        if (!('results' in rec)) {
          return {
            ...updatedRec,
            results: 'Results pending...'
          };
        }
        return updatedRec;
      }
      return rec;
    }));
  };

  const handleDismiss = (id: number) => {
    setRecommendations(recommendations.map(rec =>
      rec.id === id ? {
        ...rec,
        status: 'dismissed',
        implemented: false
      } : rec
    ));
  };

  // Filter and sort recommendations
  const filteredRecommendations = recommendations.filter(rec => {
    const matchesCategory = selectedCategory === 'All' || rec.category === selectedCategory;
    const matchesImplementationStatus = showImplemented ? true : !rec.implemented;
    return matchesCategory && matchesImplementationStatus;
  }).sort((a, b) => {
    if (sortBy === 'confidence') {
      return b.confidence - a.confidence;
    } else if (sortBy === 'impact') {
      const impactValue = { high: 3, medium: 2, low: 1 };
      return impactValue[b.impact as keyof typeof impactValue] - impactValue[a.impact as keyof typeof impactValue];
    } else if (sortBy === 'date') {
      // Convert dates to timestamps for comparison
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return dateB - dateA;
    }
    return 0;
  });

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        AI Recommendations
      </Typography>

      {isLoading ? (
        <LinearProgress sx={{ mb: 4 }} />
      ) : (
        <>
          <Paper sx={{ p: 3, mb: 4 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Tabs value={tabValue} onChange={handleTabChange} aria-label="ai recommendations tabs">
                  <Tab label="Recommendations" {...a11yProps(0)} />
                  <Tab label="Insights" {...a11yProps(1)} />
                  <Tab label="Settings" {...a11yProps(2)} />
                </Tabs>

                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    variant="outlined"
                    startIcon={isRefreshing ? <CircularProgress size={20} /> : <RefreshIcon />}
                    onClick={handleRefresh}
                    disabled={isRefreshing}
                  >
                    {isRefreshing ? 'Refreshing...' : 'Refresh'}
                  </Button>
                </Box>
              </Box>
            </Box>

            <TabPanel value={tabValue} index={0}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">AI-Generated Recommendations</Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel id="category-label">Category</InputLabel>
                    <Select
                      labelId="category-label"
                      id="category"
                      value={selectedCategory}
                      label="Category"
                      onChange={handleCategoryChange}
                    >
                      {Array.isArray(recommendationCategories) && recommendationCategories.map((category) => (
                        <MenuItem key={category} value={category}>{category}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel id="sort-label">Sort By</InputLabel>
                    <Select
                      labelId="sort-label"
                      id="sort"
                      value={sortBy}
                      label="Sort By"
                      onChange={handleSortChange}
                    >
                      <MenuItem value="confidence">Confidence</MenuItem>
                      <MenuItem value="impact">Impact</MenuItem>
                      <MenuItem value="date">Date</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={showImplemented}
                        onChange={(e) => setShowImplemented(e.target.checked)}
                      />
                    }
                    label="Show Implemented"
                  />
                </Box>
              </Box>

              {filteredRecommendations.length === 0 ? (
                <Typography variant="body1" color="textSecondary" align="center" sx={{ py: 4 }}>
                  No recommendations found matching your criteria.
                </Typography>
              ) : (
                filteredRecommendations.map((recommendation) => (
                  <Accordion key={recommendation.id} sx={{ mb: 2 }}>
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      aria-controls={`recommendation-${recommendation.id}-content`}
                      id={`recommendation-${recommendation.id}-header`}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                        <ListItemIcon>
                          <LightbulbIcon color={recommendation.implemented ? 'success' : 'warning'} />
                        </ListItemIcon>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="subtitle1">{recommendation.title}</Typography>
                          <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                            <Chip
                              label={recommendation.category}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                            <Chip
                              label={`${recommendation.confidence}% Confidence`}
                              size="small"
                              color={
                                recommendation.confidence >= 90 ? 'success' :
                                recommendation.confidence >= 80 ? 'primary' :
                                'default'
                              }
                            />
                            <Chip
                              label={`${recommendation.impact.charAt(0).toUpperCase() + recommendation.impact.slice(1)} Impact`}
                              size="small"
                              color={
                                recommendation.impact === 'high' ? 'error' :
                                recommendation.impact === 'medium' ? 'warning' :
                                'default'
                              }
                              variant="outlined"
                            />
                          </Box>
                        </Box>
                        {recommendation.implemented && (
                          <Chip
                            label="Implemented"
                            size="small"
                            color="success"
                            icon={<CheckCircleIcon />}
                          />
                        )}
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Typography paragraph>{recommendation.description}</Typography>

                      {recommendation.implemented ? (
                        <Box sx={{ mt: 2, p: 2, bgcolor: 'success.light', borderRadius: 1 }}>
                          <Typography variant="subtitle2">Implementation Results:</Typography>
                          <Typography variant="body2">{recommendation.results || 'Results pending...'}</Typography>
                          <Typography variant="body2" sx={{ mt: 1 }}>
                            Implemented on: {recommendation.implementedDate}
                          </Typography>
                        </Box>
                      ) : (
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                          <Button
                            variant="outlined"
                            color="error"
                            startIcon={<CancelIcon />}
                            onClick={() => handleDismiss(recommendation.id)}
                            sx={{ mr: 2 }}
                          >
                            Dismiss
                          </Button>
                          <Button
                            variant="contained"
                            color="success"
                            startIcon={<CheckCircleIcon />}
                            onClick={() => handleImplement(recommendation.id)}
                          >
                            Implement
                          </Button>
                        </Box>
                      )}
                    </AccordionDetails>
                  </Accordion>
                ))
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <Typography variant="h6" gutterBottom>AI Insights</Typography>
              <Typography variant="body1" paragraph>
                Based on analysis of your marketing data, here are key insights that can help improve your strategy:
              </Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Email Campaign Insights</Typography>
                      <List>
                        <ListItem>
                          <ListItemIcon><EmailIcon color="primary" /></ListItemIcon>
                          <ListItemText
                            primary="Tuesday and Thursday have 23% higher open rates"
                            secondary="Consider scheduling important emails on these days"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><EmailIcon color="primary" /></ListItemIcon>
                          <ListItemText
                            primary="Subject lines with numbers perform 18% better"
                            secondary="e.g., '5 Ways to Reduce Energy Costs'"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><EmailIcon color="primary" /></ListItemIcon>
                          <ListItemText
                            primary="Follow-up emails sent 3-4 days after initial contact have highest response rate"
                            secondary="Adjust your follow-up sequence timing"
                          />
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>SEO & Content Insights</Typography>
                      <List>
                        <ListItem>
                          <ListItemIcon><SearchIcon color="primary" /></ListItemIcon>
                          <ListItemText
                            primary="Case studies generate 2.5x more leads than general content"
                            secondary="Prioritize creating industry-specific case studies"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><SearchIcon color="primary" /></ListItemIcon>
                          <ListItemText
                            primary="'Energy efficiency' related keywords have lower competition but good conversion"
                            secondary="Opportunity to capture this traffic with targeted content"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><SearchIcon color="primary" /></ListItemIcon>
                          <ListItemText
                            primary="Technical content performs better with diagrams and visuals"
                            secondary="Add more visual elements to technical pages"
                          />
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Lead Generation Patterns</Typography>
                      <Typography variant="body2" paragraph>
                        Analysis of your lead generation data shows clear patterns in the types of companies most likely to convert:
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={4}>
                          <Box sx={{ p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                            <Typography variant="subtitle1" gutterBottom>Company Size</Typography>
                            <Typography variant="body2" paragraph>
                              Mid-sized companies (50-200 employees) have a 32% higher conversion rate than other segments.
                            </Typography>
                            <Typography variant="body2">
                              <strong>Recommendation:</strong> Focus outreach efforts on this segment for better ROI.
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={4}>
                          <Box sx={{ p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                            <Typography variant="subtitle1" gutterBottom>Industry Vertical</Typography>
                            <Typography variant="body2" paragraph>
                              Manufacturing and food processing industries show highest interest in energy efficiency solutions.
                            </Typography>
                            <Typography variant="body2">
                              <strong>Recommendation:</strong> Create industry-specific landing pages for these verticals.
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={4}>
                          <Box sx={{ p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                            <Typography variant="subtitle1" gutterBottom>Decision Maker Role</Typography>
                            <Typography variant="body2" paragraph>
                              Facility Managers and Operations Directors have 2.1x higher engagement than other roles.
                            </Typography>
                            <Typography variant="body2">
                              <strong>Recommendation:</strong> Tailor messaging to address the specific concerns of these roles.
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <Typography variant="h6" gutterBottom>AI Recommendation Settings</Typography>
              <Card>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>Recommendation Preferences</Typography>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={<Switch defaultChecked />}
                        label="Email Campaign Recommendations"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={<Switch defaultChecked />}
                        label="SEO Optimization Recommendations"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={<Switch defaultChecked />}
                        label="LinkedIn Outreach Recommendations"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={<Switch defaultChecked />}
                        label="Content Strategy Recommendations"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={<Switch defaultChecked />}
                        label="Lead Generation Recommendations"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={<Switch defaultChecked />}
                        label="Tender Opportunity Recommendations"
                      />
                    </Grid>
                  </Grid>

                  <Divider sx={{ my: 3 }} />

                  <Typography variant="subtitle1" gutterBottom>Notification Settings</Typography>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={<Switch defaultChecked />}
                        label="Email Notifications for New Recommendations"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={<Switch defaultChecked />}
                        label="Dashboard Notifications"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel id="frequency-label">Recommendation Frequency</InputLabel>
                        <Select
                          labelId="frequency-label"
                          defaultValue="weekly"
                          label="Recommendation Frequency"
                        >
                          <MenuItem value="daily">Daily</MenuItem>
                          <MenuItem value="weekly">Weekly</MenuItem>
                          <MenuItem value="monthly">Monthly</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel id="threshold-label">Confidence Threshold</InputLabel>
                        <Select
                          labelId="threshold-label"
                          defaultValue="75"
                          label="Confidence Threshold"
                        >
                          <MenuItem value="60">60% and above</MenuItem>
                          <MenuItem value="75">75% and above</MenuItem>
                          <MenuItem value="90">90% and above</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>

                  <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                    <Button variant="contained">Save Settings</Button>
                  </Box>
                </CardContent>
              </Card>
            </TabPanel>
          </Paper>
        </>
      )}
    </Box>
  );
};

export default AIRecommendations;
