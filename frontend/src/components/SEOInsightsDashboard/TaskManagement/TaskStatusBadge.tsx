import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';

import { Chip } from '@mui/material';
import {
  PlayArrow as InProgressIcon,
  CheckCircle as CompletedIcon,
  Block as BlockedIcon,
  Visibility as ReviewIcon,
  RadioButtonUnchecked as TodoIcon
} from '@mui/icons-material';
import { TaskStatus, TaskStatusBadgeProps } from './types';

/**
 * Task Status Badge Component
 * 
 * Displays a badge with the task status
 */
const TaskStatusBadge = ({ status  }: TaskStatusBadgeProps) => {
  // Get icon for status
  const getStatusIcon = () => {
    switch (status) {
      case TaskStatus.TODO:
        return <TodoIcon fontSize="small" />;
      case TaskStatus.IN_PROGRESS:
        return <InProgressIcon fontSize="small" />;
      case TaskStatus.REVIEW:
        return <ReviewIcon fontSize="small" />;
      case TaskStatus.COMPLETED:
        return <CompletedIcon fontSize="small" />;
      case TaskStatus.BLOCKED:
        return <BlockedIcon fontSize="small" />;
      default:
        return null;
    }
  };
  
  // Get color for status
  const getStatusColor = () => {
    switch (status) {
      case TaskStatus.TODO:
        return 'default';
      case TaskStatus.IN_PROGRESS:
        return 'primary';
      case TaskStatus.REVIEW:
        return 'secondary';
      case TaskStatus.COMPLETED:
        return 'success';
      case TaskStatus.BLOCKED:
        return 'error';
      default:
        return 'default';
    }
  };
  
  // Get label for status
  const getStatusLabel = () => {
    switch (status) {
      case TaskStatus.TODO:
        return 'To Do';
      case TaskStatus.IN_PROGRESS:
        return 'In Progress';
      case TaskStatus.REVIEW:
        return 'In Review';
      case TaskStatus.COMPLETED:
        return 'Completed';
      case TaskStatus.BLOCKED:
        return 'Blocked';
      default:
        return '';
    }
  };
  
  // Use type assertion to fix the Chip component error
  return (
    <Chip 
      icon={getStatusIcon()} 
      label={getStatusLabel()} 
      size="small" 
      color={getStatusColor() as "primary" | "secondary" | "success" | "error" | "default"}
    />
  );
};

export default TaskStatusBadge;
