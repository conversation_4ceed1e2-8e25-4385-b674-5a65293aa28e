import React from "react";

import {
    <PERSON>,
    Chip,
    Paper,
    Stack,
    Typography
} from "@mui/material";
import { DropResult } from "react-beautiful-dnd";
import {
    DragDropContextWrapper,
    DraggableWrapper,
    DroppableWrapper,
} from "./DndWrappers";
import TaskCard from "./TaskCard";
import {
    Task,
    TaskBoardProps,
    TaskCategory,
    TaskGroupByOption,
    TaskPriority,
    TaskStatus,
} from "./types";

/**
 * Task Board Component
 *
 * Displays tasks in a kanban board layout with drag and drop functionality
 */
const TaskBoard = ({
  tasks,
  onTaskSelect,
  onTaskStatusChange,
  filter,
  groupBy,
}: TaskBoardProps) => {
  // Filter tasks based on filter criteria
  const filteredTasks = tasks.filter((task) => {
    // Filter by category
    if (!filter.categories.includes(task.category)) {
      return false;
    }

    // Filter by priority
    if (!filter.priorities.includes(task.priority)) {
      return false;
    }

    // Filter by status
    if (!filter.statuses.includes(task.status)) {
      return false;
    }

    // Filter by assignee
    if (
      filter.assignees.length > 0 &&
      (!task.assignee || !filter.assignees.includes(task.assignee))
    ) {
      return false;
    }

    // Filter by tags
    if (
      filter.tags.length > 0 &&
      (!task.tags || !task.tags.some((tag) => filter.tags.includes(tag)))
    ) {
      return false;
    }

    // Filter by search term
    if (filter.searchTerm) {
      const searchTerm = filter.searchTerm.toLowerCase();
      return (
        task.title.toLowerCase().includes(searchTerm) ||
        task.description.toLowerCase().includes(searchTerm) ||
        (task.assignee && task.assignee.toLowerCase().includes(searchTerm)) ||
        (task.tags &&
          task.tags.some((tag) => tag.toLowerCase().includes(searchTerm)))
      );
    }

    return true;
  });

  // Group tasks based on groupBy option
  const groupTasks = () => {
    const groups: Record<string, Task[]> = {};

    switch (groupBy) {
      case TaskGroupByOption.STATUS:
        // Initialize groups for all statuses
        Object.values(TaskStatus).forEach((status) => {
          groups[status] = [];
        });

        // Group tasks by status
        filteredTasks.forEach((task) => {
          groups[task.status].push(task);
        });
        break;

      case TaskGroupByOption.CATEGORY:
        // Initialize groups for all categories
        Object.values(TaskCategory).forEach((category) => {
          groups[category] = [];
        });

        // Group tasks by category
        filteredTasks.forEach((task) => {
          groups[task.category].push(task);
        });
        break;

      case TaskGroupByOption.PRIORITY:
        // Initialize groups for all priorities
        Object.values(TaskPriority).forEach((priority) => {
          groups[priority] = [];
        });

        // Group tasks by priority
        filteredTasks.forEach((task) => {
          groups[task.priority].push(task);
        });
        break;

      case TaskGroupByOption.ASSIGNEE:
        // Group tasks by assignee
        filteredTasks.forEach((task) => {
          const assignee = task.assignee || "Unassigned";
          if (!groups[assignee]) {
            groups[assignee] = [];
          }
          groups[assignee].push(task);
        });
        break;

      case TaskGroupByOption.DUE_DATE:
        // Group tasks by due date
        filteredTasks.forEach((task) => {
          let dueDate = "No Due Date";

          if (task.dueDate) {
            const date = new Date(task.dueDate);
            dueDate = date.toLocaleDateString();
          }

          if (!groups[dueDate]) {
            groups[dueDate] = [];
          }

          groups[dueDate].push(task);
        });
        break;

      default:
        // Default to status grouping
        Object.values(TaskStatus).forEach((status) => {
          groups[status] = [];
        });

        filteredTasks.forEach((task) => {
          groups[task.status].push(task);
        });
        break;
    }

    return groups;
  };

  // Get group title
  const getGroupTitle = (groupKey: string): string => {
    switch (groupBy) {
      case TaskGroupByOption.STATUS:
        return groupKey
          .replace("_", " ")
          .replace(/\b\w/g, (l) => l.toUpperCase());

      case TaskGroupByOption.CATEGORY:
        return groupKey
          .replace("_", " ")
          .replace(/\b\w/g, (l) => l.toUpperCase());

      case TaskGroupByOption.PRIORITY:
        return groupKey
          .replace("_", " ")
          .replace(/\b\w/g, (l) => l.toUpperCase());

      case TaskGroupByOption.ASSIGNEE:
        return groupKey;

      case TaskGroupByOption.DUE_DATE:
        return groupKey;

      default:
        return groupKey;
    }
  };

  // Get group color
  const getGroupColor = (groupKey: string): string => {
    switch (groupBy) {
      case TaskGroupByOption.STATUS:
        switch (groupKey) {
          case TaskStatus.TODO:
            return "#f5f5f5";
          case TaskStatus.IN_PROGRESS:
            return "#e3f2fd";
          case TaskStatus.REVIEW:
            return "#fff8e1";
          case TaskStatus.COMPLETED:
            return "#e8f5e9";
          case TaskStatus.BLOCKED:
            return "#ffebee";
          default:
            return "#f5f5f5";
        }

      case TaskGroupByOption.PRIORITY:
        switch (groupKey) {
          case TaskPriority.HIGH:
            return "#ffebee";
          case TaskPriority.MEDIUM:
            return "#fff8e1";
          case TaskPriority.LOW:
            return "#e8f5e9";
          default:
            return "#f5f5f5";
        }

      case TaskGroupByOption.CATEGORY:
        switch (groupKey) {
          case TaskCategory.CONTENT:
            return "#e3f2fd";
          case TaskCategory.TECHNICAL:
            return "#e8f5e9";
          case TaskCategory.KEYWORD:
            return "#fff8e1";
          case TaskCategory.BACKLINK:
            return "#f3e5f5";
          case TaskCategory.ANALYTICS:
            return "#e0f7fa";
          case TaskCategory.SOCIAL:
            return "#e8eaf6";
          case TaskCategory.OTHER:
            return "#f5f5f5";
          default:
            return "#f5f5f5";
        }

      default:
        return "#f5f5f5";
    }
  };

  // Handle drag end
  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result;

    // If there's no destination or the item was dropped in the same place
    if (
      !destination ||
      (destination.droppableId === source.droppableId &&
        destination.index === source.index)
    ) {
      return;
    }

    // If grouping by status, update task status
    if (groupBy === TaskGroupByOption.STATUS) {
      const newStatus = destination.droppableId as TaskStatus;
      onTaskStatusChange(draggableId, newStatus);
    }
  };

  // Group tasks
  const groupedTasks = groupTasks();

  return (
    <DragDropContextWrapper onDragEnd={handleDragEnd}>
      <Box sx={{ overflowX: "auto", pb: 2 }}>
        <Box
          sx={{
            display: "flex",
            gap: 2,
            minWidth: Object.keys(groupedTasks).length * 300,
          }}
        >
          {Object.entries(groupedTasks).map(([groupKey, groupTasks]) => (
            <Box
              key={groupKey}
              sx={{
                width: 300,
                flexShrink: 0,
              }}
            >
              <Paper
                sx={{
                  height: "100%",
                  bgcolor: getGroupColor(groupKey),
                }}
              >
                <Box
                  sx={{ p: 2, borderBottom: "1px solid rgba(0, 0, 0, 0.12)" }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <Typography variant="subtitle1" fontWeight="bold">
                      {getGroupTitle(groupKey)}
                    </Typography>
                    <Chip
                      label={groupTasks.length}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  </Box>
                </Box>

                <DroppableWrapper droppableId={groupKey}>
                  {(provided) => (
                    <Box
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      sx={{
                        height: "calc(100vh - 350px)",
                        overflowY: "auto",
                        p: 1,
                      }}
                    >
                      <Stack spacing={2}>
                        {/* @ts-ignore */}
                        <React.Fragment>
                          {groupTasks.map((task, index) => (
                            <DraggableWrapper
                              draggableId={task.id}
                              index={index}
                              isDragDisabled={
                                groupBy !== TaskGroupByOption.STATUS
                              }
                            >
                              {(provided, snapshot) => (
                                <div
                                  key={task.id}
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                >
                                  <TaskCard
                                    task={task}
                                    onClick={() => onTaskSelect(task)}
                                    onStatusChange={(status) =>
                                      onTaskStatusChange(task.id, status)
                                    }
                                  />
                                </div>
                              )}
                            </DraggableWrapper>
                          ))}
                          {provided.placeholder}
                        </React.Fragment>
                      </Stack>
                    </Box>
                  )}
                </DroppableWrapper>
              </Paper>
            </Box>
          ))}
        </Box>
      </Box>
    </DragDropContextWrapper>
  );
};

export default TaskBoard;
