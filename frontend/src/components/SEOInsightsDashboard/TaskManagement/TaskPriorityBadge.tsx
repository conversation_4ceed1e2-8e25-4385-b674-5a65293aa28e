import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';

import { Chip } from '@mui/material';
import {
  PriorityHigh as HighPriorityIcon,
  LowPriority as LowPriorityIcon,
  MoreHoriz as MediumPriorityIcon
} from '@mui/icons-material';
import { TaskPriority, TaskPriorityBadgeProps } from './types';

/**
 * Task Priority Badge Component
 * 
 * Displays a badge with the task priority
 */
const TaskPriorityBadge = ({ priority  }: TaskPriorityBadgeProps) => {
  // Get icon for priority
  const getPriorityIcon = () => {
    switch (priority) {
      case TaskPriority.HIGH:
        return <HighPriorityIcon fontSize="small" />;
      case TaskPriority.MEDIUM:
        return <MediumPriorityIcon fontSize="small" />;
      case TaskPriority.LOW:
        return <LowPriorityIcon fontSize="small" />;
      default:
        return null;
    }
  };
  
  // Get color for priority
  const getPriorityColor = () => {
    switch (priority) {
      case TaskPriority.HIGH:
        return 'error';
      case TaskPriority.MEDIUM:
        return 'warning';
      case TaskPriority.LOW:
        return 'info';
      default:
        return 'default';
    }
  };
  
  // Get label for priority
  const getPriorityLabel = () => {
    switch (priority) {
      case TaskPriority.HIGH:
        return 'High';
      case TaskPriority.MEDIUM:
        return 'Medium';
      case TaskPriority.LOW:
        return 'Low';
      default:
        return '';
    }
  };
  
  // Use type assertion to fix the Chip component error
  return (
    <Chip 
      icon={getPriorityIcon()} 
      label={getPriorityLabel()} 
      size="small" 
      color={getPriorityColor() as "error" | "warning" | "info" | "default"}
      variant="outlined"
    />
  );
};

export default TaskPriorityBadge;
