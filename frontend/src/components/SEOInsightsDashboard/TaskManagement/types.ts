import React from 'react';
/**
 * Task Category
 * Defines the different categories of SEO tasks
 */
export enum TaskCategory {
  CONTENT = 'content',
  TECHNICAL = 'technical',
  KEYWORD = 'keyword',
  BACKLINK = 'backlink',
  ANALYTICS = 'analytics',
  SOCIAL = 'social',
  OTHER = 'other'
}

/**
 * Task Priority
 * Defines the priority levels for tasks
 */
export enum TaskPriority {
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low'
}

/**
 * Task Status
 * Defines the status of a task
 */
export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  REVIEW = 'review',
  COMPLETED = 'completed',
  BLOCKED = 'blocked'
}

/**
 * Task Interface
 * Defines the structure of a task
 */
export interface Task {
  id: string;
  title: string;
  description: string;
  category: TaskCategory;
  priority: TaskPriority;
  status: TaskStatus;
  dueDate?: string;
  assignee?: string;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  estimatedHours?: number;
  actualHours?: number;
  parentTaskId?: string;
  subtasks?: string[];
  attachments?: TaskAttachment[];
  comments?: TaskComment[];
  relatedUrls?: string[];
  metrics?: TaskMetric[];
}

/**
 * Task Attachment Interface
 * Defines the structure of a task attachment
 */
export interface TaskAttachment {
  id: string;
  name: string;
  url: string;
  type: string;
  size: number;
  uploadedAt: string;
  uploadedBy: string;
}

/**
 * Task Comment Interface
 * Defines the structure of a task comment
 */
export interface TaskComment {
  id: string;
  content: string;
  author: string;
  createdAt: string;
  updatedAt?: string;
}

/**
 * Task Metric Interface
 * Defines the structure of a task metric
 */
export interface TaskMetric {
  name: string;
  value: number;
  unit?: string;
  date: string;
}

/**
 * Task Filter Interface
 * Defines the filter options for tasks
 */
export interface TaskFilter {
  categories: TaskCategory[];
  priorities: TaskPriority[];
  statuses: TaskStatus[];
  assignees: string[];
  tags: string[];
  searchTerm: string;
  dueDateRange?: {
    start: string;
    end: string;
  };
}

/**
 * Task Sort Option
 * Defines the options for sorting tasks
 */
export enum TaskSortOption {
  DUE_DATE_ASC = 'due_date_asc',
  DUE_DATE_DESC = 'due_date_desc',
  PRIORITY_HIGH_TO_LOW = 'priority_high_to_low',
  PRIORITY_LOW_TO_HIGH = 'priority_low_to_high',
  STATUS = 'status',
  CATEGORY = 'category',
  CREATED_DATE_ASC = 'created_date_asc',
  CREATED_DATE_DESC = 'created_date_desc'
}

/**
 * Task View Mode
 * Defines the different view modes for tasks
 */
export enum TaskViewMode {
  LIST = 'list',
  BOARD = 'board',
  CALENDAR = 'calendar',
  GANTT = 'gantt'
}

/**
 * Task Group By Option
 * Defines the options for grouping tasks
 */
export enum TaskGroupByOption {
  NONE = 'none',
  STATUS = 'status',
  CATEGORY = 'category',
  PRIORITY = 'priority',
  ASSIGNEE = 'assignee',
  DUE_DATE = 'due_date'
}

/**
 * Task Management Props
 * Props for the TaskManagement component
 */
export interface TaskManagementProps {
  tasks: Task[];
  onTaskCreate: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onTaskUpdate: (taskId: string, updates: Partial<Task>) => void;
  onTaskDelete: (taskId: string) => void;
  onTaskStatusChange: (taskId: string, status: TaskStatus) => void;
  onTaskAssign: (taskId: string, assignee: string) => void;
  isLoading: boolean;
}

/**
 * Task List Props
 * Props for the TaskList component
 */
export interface TaskListProps {
  tasks: Task[];
  onTaskSelect: (task: Task) => void;
  onTaskStatusChange: (taskId: string, status: TaskStatus) => void;
  filter: TaskFilter;
  sortOption: TaskSortOption;
  groupBy: TaskGroupByOption;
}

/**
 * Task Board Props
 * Props for the TaskBoard component
 */
export interface TaskBoardProps {
  tasks: Task[];
  onTaskSelect: (task: Task) => void;
  onTaskStatusChange: (taskId: string, status: TaskStatus) => void;
  filter: TaskFilter;
  groupBy: TaskGroupByOption;
}

/**
 * Task Calendar Props
 * Props for the TaskCalendar component
 */
export interface TaskCalendarProps {
  tasks: Task[];
  onTaskSelect: (task: Task) => void;
  onTaskStatusChange: (taskId: string, status: TaskStatus) => void;
  filter: TaskFilter;
}

/**
 * Task Detail Props
 * Props for the TaskDetail component
 */
export interface TaskDetailProps {
  task: Task;
  onTaskUpdate: (taskId: string, updates: Partial<Task>) => void;
  onTaskDelete: (taskId: string) => void;
  onTaskStatusChange: (taskId: string, status: TaskStatus) => void;
  onTaskAssign: (taskId: string, assignee: string) => void;
  onClose: () => void;
}

/**
 * Task Form Props
 * Props for the TaskForm component
 */
export interface TaskFormProps {
  task?: Task;
  onSubmit: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
}

/**
 * Task Filter Panel Props
 * Props for the TaskFilterPanel component
 */
export interface TaskFilterPanelProps {
  filter: TaskFilter;
  onFilterChange: (filter: TaskFilter) => void;
  sortOption: TaskSortOption;
  onSortOptionChange: (option: TaskSortOption) => void;
  groupBy: TaskGroupByOption;
  onGroupByChange: (option: TaskGroupByOption) => void;
  viewMode: TaskViewMode;
  onViewModeChange: (mode: TaskViewMode) => void;
  onReset: () => void;
}

/**
 * Task Card Props
 * Props for the TaskCard component
 */
export interface TaskCardProps {
  task: Task;
  onClick: () => void;
  onStatusChange: (status: TaskStatus) => void;
}

/**
 * Task Status Badge Props
 * Props for the TaskStatusBadge component
 */
export interface TaskStatusBadgeProps {
  status: TaskStatus;
}

/**
 * Task Priority Badge Props
 * Props for the TaskPriorityBadge component
 */
export interface TaskPriorityBadgeProps {
  priority: TaskPriority;
}

/**
 * Task Category Badge Props
 * Props for the TaskCategoryBadge component
 */
export interface TaskCategoryBadgeProps {
  category: TaskCategory;
}

/**
 * Task Comment Form Props
 * Props for the TaskCommentForm component
 */
export interface TaskCommentFormProps {
  taskId: string;
  onSubmit: (comment: Omit<TaskComment, 'id' | 'createdAt'>) => void;
}

/**
 * Task Comment List Props
 * Props for the TaskCommentList component
 */
export interface TaskCommentListProps {
  comments: TaskComment[];
}

/**
 * Task Metrics Chart Props
 * Props for the TaskMetricsChart component
 */
export interface TaskMetricsChartProps {
  metrics: TaskMetric[];
}

/**
 * Task Attachment List Props
 * Props for the TaskAttachmentList component
 */
export interface TaskAttachmentListProps {
  attachments: TaskAttachment[];
  onDelete: (attachmentId: string) => void;
}

/**
 * Task Attachment Upload Props
 * Props for the TaskAttachmentUpload component
 */
export interface TaskAttachmentUploadProps {
  taskId: string;
  onUpload: (attachment: Omit<TaskAttachment, 'id' | 'uploadedAt'>) => void;
}
