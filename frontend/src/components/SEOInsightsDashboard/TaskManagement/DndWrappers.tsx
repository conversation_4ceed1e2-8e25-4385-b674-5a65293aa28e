import React from 'react';
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
  DroppableProvided,
  DroppableStateSnapshot,
  DraggableProvided,
  DraggableStateSnapshot
} from 'react-beautiful-dnd';

// Wrapper for DragDropContext
export const DragDropContextWrapper = ({
  onDragEnd,
  children
}: {
  onDragEnd: (result: DropResult) => void;
  children: any
}) => {
  // @ts-ignore - Ignoring the refs property issue
  return React.createElement(DragDropContext, { onDragEnd }, children);
};

// Wrapper for Droppable
export const DroppableWrapper = ({
  droppableId,
  children
}: {
  droppableId: string;
  children: (provided: DroppableProvided, snapshot: DroppableStateSnapshot) => React.ReactElement
}) => {
  // @ts-ignore - Ignoring the refs property issue
  return React.createElement(Droppable, { droppableId }, children);
};

// Wrapper for Draggable
export const DraggableWrapper = ({
  draggableId,
  index,
  isDragDisabled = false,
  children
}: {
  draggableId: string;
  index: number;
  isDragDisabled?: boolean;
  children: (provided: DraggableProvided, snapshot: DraggableStateSnapshot) => React.ReactElement
}) => {
  // @ts-ignore - Ignoring the refs property issue
  return React.createElement(Draggable, { draggableId, index, isDragDisabled }, children);
}
