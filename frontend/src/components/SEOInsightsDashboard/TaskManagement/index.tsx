import AddIcon from '@mui/icons-material/Add';
import CalendarIcon from '@mui/icons-material/CalendarMonth';
import FilterListIcon from '@mui/icons-material/FilterList';
import BoardIcon from '@mui/icons-material/ViewKanban';
import ListIcon from '@mui/icons-material/ViewList';
import {
    Alert,
    Box,
    Button,
    CircularProgress,
    Grid,
    Paper,
    Snackbar,
    Tab,
    Tabs,
    Typography
} from '@mui/material';
import React from 'react';
import TaskBoard from './TaskBoard';
import TaskCalendar from './TaskCalendar';
import TaskDetail from './TaskDetail';
import TaskFilterPanel from './TaskFilterPanel';
import TaskForm from './TaskForm';
import TaskList from './TaskList';
import {
    Task,
    TaskCategory,
    TaskFilter,
    TaskGroupByOption,
    TaskManagementProps,
    TaskPriority,
    TaskSortOption,
    TaskStatus,
    TaskViewMode
} from './types';

/**
 * Task Management Component
 *
 * Manages SEO tasks with different view modes and filtering options
 */
const TaskManagement = ({ tasks,
  onTaskCreate,
  onTaskUpdate,
  onTaskDelete,
  onTaskStatusChange,
  onTaskAssign,
  isLoading
 }: TaskManagementProps) => {
  // State for selected task
  const [selectedTask, setSelectedTask] = React.useState(null);

  // State for task form
  const [isTaskFormOpen, setIsTaskFormOpen] = React.useState(false);
  const [taskToEdit, setTaskToEdit] = React.useState(undefined);

  // State for filter panel visibility
  const [isFilterPanelOpen, setIsFilterPanelOpen] = React.useState(false);

  // State for view mode
  const [viewMode, setViewMode] = React.useState(TaskViewMode.BOARD);

  // State for filter options
  const [filter, setFilter] = React.useState({
    categories: Object.values(TaskCategory),
    priorities: Object.values(TaskPriority),
    statuses: Object.values(TaskStatus),
    assignees: [],
    tags: [],
    searchTerm: ''
  });

  // State for sort option
  const [sortOption, setSortOption] = React.useState(
    TaskSortOption.DUE_DATE_ASC
  );

  // State for group by option
  const [groupBy, setGroupBy] = React.useState(
    TaskGroupByOption.STATUS
  );

  // State for notification
  const [notification, setNotification] = React.useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Handle task selection
  const handleSelectTask = (task: Task) => {
    setSelectedTask(task);
  };

  // Handle task detail close
  const handleCloseDetail = () => {
    setSelectedTask(null);
  };

  // Handle create task button click
  const handleCreateTaskClick = () => {
    setTaskToEdit(undefined);
    setIsTaskFormOpen(true);
  };

  // Handle edit task button click
  const handleEditTaskClick = (task: Task) => {
    setTaskToEdit(task);
    setIsTaskFormOpen(true);
  };

  // Handle task form submit
  const handleTaskFormSubmit = (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (taskToEdit) {
      // Update existing task
      onTaskUpdate(taskToEdit.id, task);
      setNotification({
        open: true,
        message: 'Task updated successfully',
        severity: 'success'
      });
    } else {
      // Create new task
      onTaskCreate(task);
      setNotification({
        open: true,
        message: 'Task created successfully',
        severity: 'success'
      });
    }

    setIsTaskFormOpen(false);
  };

  // Handle task form cancel
  const handleTaskFormCancel = () => {
    setIsTaskFormOpen(false);
  };

  // Handle filter change
  const handleFilterChange = (newFilter: TaskFilter) => {
    setFilter(newFilter);
  };

  // Handle sort option change
  const handleSortOptionChange = (option: TaskSortOption) => {
    setSortOption(option);
  };

  // Handle group by option change
  const handleGroupByChange = (option: TaskGroupByOption) => {
    setGroupBy(option);
  };

  // Handle view mode change
  const handleViewModeChange = (mode: TaskViewMode) => {
    setViewMode(mode);
  };

  // Handle filter panel toggle
  const handleToggleFilterPanel = () => {
    setIsFilterPanelOpen(!isFilterPanelOpen);
  };

  // Handle filter reset
  const handleResetFilter = () => {
    setFilter({
      categories: Object.values(TaskCategory),
      priorities: Object.values(TaskPriority),
      statuses: Object.values(TaskStatus),
      assignees: [],
      tags: [],
      searchTerm: ''
    });
    setSortOption(TaskSortOption.DUE_DATE_ASC);
    setGroupBy(TaskGroupByOption.STATUS);
  };

  // Handle notification close
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };

  // Calculate task statistics
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter(t => t.status === TaskStatus.COMPLETED).length;
  const inProgressTasks = tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length;
  const todoTasks = tasks.filter(t => t.status === TaskStatus.TODO).length;
  const reviewTasks = tasks.filter(t => t.status === TaskStatus.REVIEW).length;
  const blockedTasks = tasks.filter(t => t.status === TaskStatus.BLOCKED).length;

  // Get all unique assignees
  const allAssignees = Array.from(new Set(tasks.filter(t => t.assignee).map(t => t.assignee!)));

  // Get all unique tags
  const allTags = Array.from(new Set(tasks.flatMap(t => t.tags || [])));

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          SEO Task Management
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={handleToggleFilterPanel}
            sx={{ mr: 1 }}
          >
            Filter
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateTaskClick}
          >
            Create Task
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={4} sm={2}>
          <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
            <Typography variant="h4" color="primary.main">
              {totalTasks}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total Tasks
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={4} sm={2}>
          <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
            <Typography variant="h4" color="success.main">
              {completedTasks}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Completed
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={4} sm={2}>
          <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
            <Typography variant="h4" color="info.main">
              {inProgressTasks}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              In Progress
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={4} sm={2}>
          <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
            <Typography variant="h4" color="warning.main">
              {todoTasks}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              To Do
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={4} sm={2}>
          <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
            <Typography variant="h4" color="secondary.main">
              {reviewTasks}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              In Review
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={4} sm={2}>
          <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
            <Typography variant="h4" color="error.main">
              {blockedTasks}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Blocked
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* View Mode Tabs */}
      <Box sx={{ mb: 3 }}>
        <Tabs
          value={viewMode}
          onChange={(_e, value) => handleViewModeChange(value)}
          aria-label="task view mode"
        >
          <Tab
            icon={<ListIcon />}
            label="List"
            value={TaskViewMode.LIST}
            iconPosition="start"
          />
          <Tab
            icon={<BoardIcon />}
            label="Board"
            value={TaskViewMode.BOARD}
            iconPosition="start"
          />
          <Tab
            icon={<CalendarIcon />}
            label="Calendar"
            value={TaskViewMode.CALENDAR}
            iconPosition="start"
          />
        </Tabs>
      </Box>

      {/* Filter Panel */}
      {isFilterPanelOpen && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <TaskFilterPanel
            filter={filter}
            onFilterChange={handleFilterChange}
            sortOption={sortOption}
            onSortOptionChange={handleSortOptionChange}
            groupBy={groupBy}
            onGroupByChange={handleGroupByChange}
            viewMode={viewMode}
            onViewModeChange={handleViewModeChange}
            onReset={handleResetFilter}
          />
        </Paper>
      )}

      {/* Loading State */}
      {isLoading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Empty State */}
      {!isLoading && tasks.length === 0 && (
        <Alert severity="info" sx={{ my: 2 }}>
          No tasks available. Click "Create Task" to add your first SEO task.
        </Alert>
      )}

      {/* Content */}
      {!isLoading && tasks.length > 0 && (
        <Grid container spacing={3}>
          {/* Task Views */}
          <Grid item xs={12} md={selectedTask ? 7 : 12}>
            {viewMode === TaskViewMode.LIST && (
              <TaskList
                tasks={tasks}
                onTaskSelect={handleSelectTask}
                onTaskStatusChange={onTaskStatusChange}
                filter={filter}
                sortOption={sortOption}
                groupBy={groupBy}
              />
            )}

            {viewMode === TaskViewMode.BOARD && (
              <TaskBoard
                tasks={tasks}
                onTaskSelect={handleSelectTask}
                onTaskStatusChange={onTaskStatusChange}
                filter={filter}
                groupBy={groupBy}
              />
            )}

            {viewMode === TaskViewMode.CALENDAR && (
              <TaskCalendar
                tasks={tasks}
                onTaskSelect={handleSelectTask}
                onTaskStatusChange={onTaskStatusChange}
                filter={filter}
              />
            )}
          </Grid>

          {/* Task Detail */}
          {selectedTask && (
            <Grid item xs={12} md={5}>
              <TaskDetail
                task={selectedTask}
                onTaskUpdate={onTaskUpdate}
                onTaskDelete={onTaskDelete}
                onTaskStatusChange={onTaskStatusChange}
                onTaskAssign={onTaskAssign}
                onClose={handleCloseDetail}
              />
            </Grid>
          )}
        </Grid>
      )}

      {/* Task Form Dialog */}
      {isTaskFormOpen && (
        <TaskForm
          task={taskToEdit}
          onSubmit={handleTaskFormSubmit}
          onCancel={handleTaskFormCancel}
        />
      )}

      {/* Notification */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          variant="filled"
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TaskManagement;
