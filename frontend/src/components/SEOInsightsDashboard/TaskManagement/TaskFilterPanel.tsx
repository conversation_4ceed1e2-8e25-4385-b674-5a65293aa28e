import SearchIcon from '@mui/icons-material/Search';
import {
    Box,
    Button,
    ButtonGroup // Use ButtonGroup as fallback for ToggleButtonGroup
    ,
    Checkbox,
    FormControl,
    Grid,
    InputLabel,
    ListItemText,
    MenuItem,
    Select,
    SelectChangeEvent,
    TextField,
    Typography
} from '@mui/material';
// Remove all missing icon imports and use available ones or placeholders
// import RestartAltIcon from '@mui/icons-material/RestartAlt'; // Not available
// import ViewListIcon from '@mui/icons-material/ViewList'; // Not available
// import ViewKanbanIcon from '@mui/icons-material/ViewKanban'; // Not available
// import CalendarMonthIcon from '@mui/icons-material/CalendarMonth'; // Not available
// import TimelineIcon from '@mui/icons-material/Timeline'; // Not available
import {
    TaskCategory,
    TaskFilterPanelProps,
    TaskGroupByOption,
    TaskPriority,
    TaskSortOption,
    TaskStatus,
    TaskViewMode
} from './types';

/**
 * Task Filter Panel Component
 *
 * Provides controls for filtering and sorting tasks
 */
const TaskFilterPanel = ({ filter,
  onFilterChange,
  sortOption,
  onSortOptionChange,
  groupBy,
  onGroupByChange,
  viewMode,
  onViewModeChange,
  onReset
 }: TaskFilterPanelProps) => {
  // Handle category filter change
  const handleCategoryChange = (event: SelectChangeEvent<TaskCategory[]>) => {
    const value = event.target.value as TaskCategory[];
    onFilterChange({
      ...filter,
      categories: value
    });
  };

  // Handle priority filter change
  const handlePriorityChange = (event: SelectChangeEvent<TaskPriority[]>) => {
    const value = event.target.value as TaskPriority[];
    onFilterChange({
      ...filter,
      priorities: value
    });
  };

  // Handle status filter change
  const handleStatusChange = (event: SelectChangeEvent<TaskStatus[]>) => {
    const value = event.target.value as TaskStatus[];
    onFilterChange({
      ...filter,
      statuses: value
    });
  };

  // Handle assignee filter change
  const handleAssigneeChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value as string[];
    onFilterChange({
      ...filter,
      assignees: value
    });
  };

  // Handle tag filter change
  const handleTagChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value as string[];
    onFilterChange({
      ...filter,
      tags: value
    });
  };

  // Handle search term change
  const handleSearchChange = (event: any) => {
    onFilterChange({
      ...filter,
      searchTerm: event.target.value
    });
  };

  // Handle sort option change
  const handleSortOptionChange = (event: SelectChangeEvent) => {
    onSortOptionChange(event.target.value as TaskSortOption);
  };

  // Handle group by option change
  const handleGroupByChange = (event: SelectChangeEvent) => {
    onGroupByChange(event.target.value as TaskGroupByOption);
  };

  // Handle view mode change
  const handleViewModeChange = (_event: any, newMode: TaskViewMode) => {
    if (newMode !== null) {
      onViewModeChange(newMode);
    }
  };

  // Mock assignees (in a real app, this would come from a user service)
  const assignees = [
    'John Doe',
    'Jane Smith',
    'Bob Johnson',
    'Alice Williams',
    'David Brown'
  ];

  // Common SEO tags
  const commonTags = [
    'SEO',
    'Content',
    'Technical',
    'Keyword',
    'Backlink',
    'Analytics',
    'Social',
    'On-Page',
    'Off-Page',
    'Local SEO',
    'Mobile',
    'Performance',
    'Schema',
    'Meta Tags',
    'Link Building'
  ];

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Filter & Sort Tasks</Typography>
        <Button
          startIcon={<SearchIcon />} // Use SearchIcon as a placeholder
          onClick={onReset}
          size="small"
        >
          Reset
        </Button>
      </Box>

      <Grid container spacing={2}>
        {/* Search */}
        <Grid item xs={12}>
          <TextField
            fullWidth
            placeholder="Search tasks..."
            value={filter.searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <Box sx={{ display: 'flex', alignItems: 'center', pr: 1 }}>
                  <SearchIcon />
                </Box>
              )
            }}
          />
        </Grid>

        {/* View Mode */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
            <ButtonGroup
              value={viewMode}
              exclusive
              onChange={handleViewModeChange}
              aria-label="view mode"
            >
              <Button value={TaskViewMode.LIST} aria-label="list view">
                <SearchIcon /> {/* Placeholder for ListIcon */}
              </Button>
              <Button value={TaskViewMode.BOARD} aria-label="board view">
                <SearchIcon /> {/* Placeholder for BoardIcon */}
              </Button>
              <Button value={TaskViewMode.CALENDAR} aria-label="calendar view">
                <SearchIcon /> {/* Placeholder for CalendarIcon */}
              </Button>
            </ButtonGroup>
          </Box>
        </Grid>

        {/* Category Filter */}
        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth size="small">
            <InputLabel id="category-filter-label">Categories</InputLabel>
            <Select
              labelId="category-filter-label"
              multiple
              value={filter.categories}
              onChange={handleCategoryChange}
              renderValue={(selected) => selected.length === Object.values(TaskCategory).length
                ? 'All Categories'
                : `${selected.length} Categories`}
            >
              {Object.values(TaskCategory).map((category) => (
                <MenuItem key={category} value={category}>
                  <Checkbox checked={filter.categories.includes(category)} />
                  <ListItemText primary={category.charAt(0).toUpperCase() + category.slice(1)} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Priority Filter */}
        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth size="small">
            <InputLabel id="priority-filter-label">Priorities</InputLabel>
            <Select
              labelId="priority-filter-label"
              multiple
              value={filter.priorities}
              onChange={handlePriorityChange}
              renderValue={(selected) => selected.length === Object.values(TaskPriority).length
                ? 'All Priorities'
                : `${selected.length} Priorities`}
            >
              {Object.values(TaskPriority).map((priority) => (
                <MenuItem key={priority} value={priority}>
                  <Checkbox checked={filter.priorities.includes(priority)} />
                  <ListItemText primary={priority.charAt(0).toUpperCase() + priority.slice(1)} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Status Filter */}
        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth size="small">
            <InputLabel id="status-filter-label">Statuses</InputLabel>
            <Select
              labelId="status-filter-label"
              multiple
              value={filter.statuses}
              onChange={handleStatusChange}
              renderValue={(selected) => selected.length === Object.values(TaskStatus).length
                ? 'All Statuses'
                : `${selected.length} Statuses`}
            >
              {Object.values(TaskStatus).map((status) => (
                <MenuItem key={status} value={status}>
                  <Checkbox checked={filter.statuses.includes(status)} />
                  <ListItemText primary={status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Assignee Filter */}
        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth size="small">
            <InputLabel id="assignee-filter-label">Assignees</InputLabel>
            <Select
              labelId="assignee-filter-label"
              multiple
              value={filter.assignees}
              onChange={handleAssigneeChange}
              renderValue={(selected) => selected.length === 0
                ? 'All Assignees'
                : `${selected.length} Assignees`}
            >
              <MenuItem value="Unassigned">
                <Checkbox checked={filter.assignees.includes('Unassigned')} />
                <ListItemText primary="Unassigned" />
              </MenuItem>
              {assignees.map((assignee) => (
                <MenuItem key={assignee} value={assignee}>
                  <Checkbox checked={filter.assignees.includes(assignee)} />
                  <ListItemText primary={assignee} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Tag Filter */}
        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth size="small">
            <InputLabel id="tag-filter-label">Tags</InputLabel>
            <Select
              labelId="tag-filter-label"
              multiple
              value={filter.tags}
              onChange={handleTagChange}
              renderValue={(selected) => selected.length === 0
                ? 'All Tags'
                : `${selected.length} Tags`}
            >
              {commonTags.map((tag) => (
                <MenuItem key={tag} value={tag}>
                  <Checkbox checked={filter.tags.includes(tag)} />
                  <ListItemText primary={tag} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Sort Option */}
        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth size="small">
            <InputLabel id="sort-option-label">Sort By</InputLabel>
            <Select
              labelId="sort-option-label"
              value={sortOption}
              onChange={handleSortOptionChange}
              label="Sort By"
            >
              <MenuItem value={TaskSortOption.DUE_DATE_ASC}>
                Due Date: Earliest First
              </MenuItem>
              <MenuItem value={TaskSortOption.DUE_DATE_DESC}>
                Due Date: Latest First
              </MenuItem>
              <MenuItem value={TaskSortOption.PRIORITY_HIGH_TO_LOW}>
                Priority: High to Low
              </MenuItem>
              <MenuItem value={TaskSortOption.PRIORITY_LOW_TO_HIGH}>
                Priority: Low to High
              </MenuItem>
              <MenuItem value={TaskSortOption.STATUS}>
                Status
              </MenuItem>
              <MenuItem value={TaskSortOption.CATEGORY}>
                Category
              </MenuItem>
              <MenuItem value={TaskSortOption.CREATED_DATE_ASC}>
                Created: Oldest First
              </MenuItem>
              <MenuItem value={TaskSortOption.CREATED_DATE_DESC}>
                Created: Newest First
              </MenuItem>
            </Select>
          </FormControl>
        </Grid>

        {/* Group By Option */}
        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth size="small">
            <InputLabel id="group-by-label">Group By</InputLabel>
            <Select
              labelId="group-by-label"
              value={groupBy}
              onChange={handleGroupByChange}
              label="Group By"
            >
              <MenuItem value={TaskGroupByOption.NONE}>
                No Grouping
              </MenuItem>
              <MenuItem value={TaskGroupByOption.STATUS}>
                Status
              </MenuItem>
              <MenuItem value={TaskGroupByOption.CATEGORY}>
                Category
              </MenuItem>
              <MenuItem value={TaskGroupByOption.PRIORITY}>
                Priority
              </MenuItem>
              <MenuItem value={TaskGroupByOption.ASSIGNEE}>
                Assignee
              </MenuItem>
              <MenuItem value={TaskGroupByOption.DUE_DATE}>
                Due Date
              </MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TaskFilterPanel;
