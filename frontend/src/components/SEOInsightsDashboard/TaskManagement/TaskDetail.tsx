import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import PlayArrow from '@mui/icons-material/PlayArrow';
import { 
  Box, 
  Typography, 
  Paper, 
  Divider,
  Button,
  IconButton,
  TextField,
  Grid,
  Chip,
  Avatar,
  Link,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Tooltip
} from '@mui/material';
import {
  Close as CloseIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as StartIcon,
  Pause as PauseIcon,
  CheckCircle as CompleteIcon,
  Block as BlockIcon,
  Visibility as ReviewIcon,
  Person as PersonIcon,
  AccessTime as TimeIcon,
  Link as LinkIcon,
  Comment as CommentIcon,
  Send as SendIcon,
  AttachFile as AttachmentIcon,
  OpenInNew as OpenInNewIcon
} from '@mui/icons-material';
import { 
  Task, 
  TaskStatus, 
  TaskDetailProps,
  TaskComment
} from './types';
import TaskStatusBadge from './TaskStatusBadge';
import TaskPriorityBadge from './TaskPriorityBadge';
import TaskCategoryBadge from './TaskCategoryBadge';

/**
 * Task Detail Component
 * 
 * Displays detailed information about a task
 */
const TaskDetail = ({ task, 
  onTaskUpdate, 
  onTaskDelete, 
  onTaskStatusChange, 
  onTaskAssign, 
  onClose 
 }: TaskDetailProps) => {
  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  
  // State for status menu
  const [statusAnchorEl, setStatusAnchorEl] = React.useState(null);
  const statusMenuOpen = Boolean(statusAnchorEl);
  
  // State for assignee menu
  const [assigneeAnchorEl, setAssigneeAnchorEl] = React.useState(null);
  const assigneeMenuOpen = Boolean(assigneeAnchorEl);
  
  // State for comment input
  const [commentInput, setCommentInput] = React.useState('');
  
  // Mock assignees (in a real app, this would come from a user service)
  const assignees = [
    'John Doe',
    'Jane Smith',
    'Bob Johnson',
    'Alice Williams',
    'David Brown'
  ];
  
  // Handle status menu open
  const handleStatusMenuOpen = (event: any) => {
    setStatusAnchorEl(event.currentTarget);
  };
  
  // Handle status menu close
  const handleStatusMenuClose = () => {
    setStatusAnchorEl(null);
  };
  
  // Handle assignee menu open
  const handleAssigneeMenuOpen = (event: any) => {
    setAssigneeAnchorEl(event.currentTarget);
  };
  
  // Handle assignee menu close
  const handleAssigneeMenuClose = () => {
    setAssigneeAnchorEl(null);
  };
  
  // Handle status change
  const handleStatusChange = (status: TaskStatus) => {
    onTaskStatusChange(task.id, status);
    handleStatusMenuClose();
  };
  
  // Handle assignee change
  const handleAssigneeChange = (assignee: string) => {
    onTaskAssign(task.id, assignee);
    handleAssigneeMenuClose();
  };
  
  // Handle delete dialog open
  const handleDeleteDialogOpen = () => {
    setDeleteDialogOpen(true);
  };
  
  // Handle delete dialog close
  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
  };
  
  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    onTaskDelete(task.id);
    handleDeleteDialogClose();
    onClose();
  };
  
  // Handle comment input change
  const handleCommentInputChange = (event: any) => {
    setCommentInput(event.target.value);
  };
  
  // Handle comment submit
  const handleCommentSubmit = () => {
    if (!commentInput.trim()) return;
    
    const newComment: Omit<TaskComment, 'id' | 'createdAt'> = {
      content: commentInput,
      author: 'Current User' // In a real app, this would be the current user
    };
    
    const comments = task.comments || [];
    
    onTaskUpdate(task.id, {
      comments: [...comments, {
        id: `comment-${Date.now()}`,
        ...newComment,
        createdAt: new Date().toISOString()
      }]
    });
    
    setCommentInput('');
  };
  
  // Format date
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  // Format time ago
  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffMins > 0) {
      return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };
  
  // Get assignee initials
  const getAssigneeInitials = (name: string): string => {
    const nameParts = name.split(' ');
    if (nameParts.length === 1) {
      return nameParts[0].charAt(0).toUpperCase();
    } else {
      return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
    }
  };
  
  // Get assignee avatar color
  const getAssigneeAvatarColor = (name: string): string => {
    const colors = [
      '#f44336', '#e91e63', '#9c27b0', '#673ab7', '#3f51b5',
      '#2196f3', '#03a9f4', '#00bcd4', '#009688', '#4caf50',
      '#8bc34a', '#cddc39', '#ffeb3b', '#ffc107', '#ff9800',
      '#ff5722', '#795548', '#607d8b'
    ];
    
    // Simple hash function to get consistent color for the same name
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  };
  
  return (
    <Paper sx={{ p: 3, height: '100%', position: 'relative', display: 'flex', flexDirection: 'column' }}>
      {/* Close Button */}
      <IconButton 
        sx={{ position: 'absolute', top: 8, right: 8 }}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>
      
      {/* Header */}
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <TaskCategoryBadge category={task.category} />
          <Box sx={{ ml: 'auto', display: 'flex' }}>
            <Tooltip title="Edit Task">
              <IconButton size="small" color="primary" sx={{ mr: 1 }}>
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete Task">
              <IconButton size="small" color="error" onClick={handleDeleteDialogOpen}>
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        <Typography variant="h5" gutterBottom>
          {task.title}
        </Typography>
      </Box>
      
      {/* Task Metadata */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={6} sm={3}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Status
          </Typography>
          <Button
            variant="outlined"
            size="small"
            onClick={handleStatusMenuOpen}
            startIcon={<TaskStatusBadge status={task.status} />}
            sx={{ textTransform: 'none' }}
          >
            {task.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Button>
          <Menu
            anchorEl={statusAnchorEl}
            open={statusMenuOpen}
            onClose={handleStatusMenuClose}
          >
            <MenuItem onClick={() => handleStatusChange(TaskStatus.TODO)}>
              <ListItemIcon>
                <StartIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>To Do</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => handleStatusChange(TaskStatus.IN_PROGRESS)}>
              <ListItemIcon>
                <PlayArrow fontSize="small" />
              </ListItemIcon>
              <ListItemText>In Progress</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => handleStatusChange(TaskStatus.REVIEW)}>
              <ListItemIcon>
                <ReviewIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>In Review</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => handleStatusChange(TaskStatus.COMPLETED)}>
              <ListItemIcon>
                <CompleteIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Completed</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => handleStatusChange(TaskStatus.BLOCKED)}>
              <ListItemIcon>
                <BlockIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Blocked</ListItemText>
            </MenuItem>
          </Menu>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Priority
          </Typography>
          <Box>
            <TaskPriorityBadge priority={task.priority} />
          </Box>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Assignee
          </Typography>
          <Button
            variant="outlined"
            size="small"
            onClick={handleAssigneeMenuOpen}
            startIcon={
              task.assignee ? (
                <Avatar 
                  sx={{ 
                    width: 24, 
                    height: 24, 
                    fontSize: '0.75rem',
                    bgcolor: getAssigneeAvatarColor(task.assignee)
                  }}
                >
                  {getAssigneeInitials(task.assignee)}
                </Avatar>
              ) : (
                <PersonIcon fontSize="small" />
              )
            }
            sx={{ textTransform: 'none' }}
          >
            {task.assignee || 'Unassigned'}
          </Button>
          <Menu
            anchorEl={assigneeAnchorEl}
            open={assigneeMenuOpen}
            onClose={handleAssigneeMenuClose}
          >
            {assignees.map(assignee => (
              <MenuItem key={assignee} onClick={() => handleAssigneeChange(assignee)}>
                <ListItemIcon>
                  <Avatar 
                    sx={{ 
                      width: 24, 
                      height: 24, 
                      fontSize: '0.75rem',
                      bgcolor: getAssigneeAvatarColor(assignee)
                    }}
                  >
                    {getAssigneeInitials(assignee)}
                  </Avatar>
                </ListItemIcon>
                <ListItemText>{assignee}</ListItemText>
              </MenuItem>
            ))}
          </Menu>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Due Date
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <TimeIcon fontSize="small" sx={{ mr: 0.5 }} />
            <Typography variant="body2">
              {task.dueDate ? new Date(task.dueDate).toLocaleDateString() : 'No due date'}
            </Typography>
          </Box>
        </Grid>
      </Grid>
      
      {/* Description */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Description
        </Typography>
        <Typography variant="body2">
          {task.description}
        </Typography>
      </Box>
      
      {/* Tags */}
      {task.tags && task.tags.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Tags
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {task.tags.map(tag => (
              <Chip 
                key={tag} 
                label={tag} 
                size="small" 
                color="primary" 
                variant="outlined" 
              />
            ))}
          </Box>
        </Box>
      )}
      
      {/* Related URLs */}
      {task.relatedUrls && task.relatedUrls.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Related URLs
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            {task.relatedUrls.map((url, index) => (
              <Link 
                key={index} 
                href={url} 
                target="_blank" 
                rel="noopener noreferrer"
                sx={{ display: 'flex', alignItems: 'center' }}
              >
                <LinkIcon fontSize="small" sx={{ mr: 0.5 }} />
                {url}
                <OpenInNewIcon fontSize="small" sx={{ ml: 0.5 }} />
              </Link>
            ))}
          </Box>
        </Box>
      )}
      
      <Divider sx={{ my: 2 }} />
      
      {/* Comments */}
      <Box sx={{ mb: 2, flexGrow: 1, overflow: 'auto' }}>
        <Typography variant="subtitle1" gutterBottom>
          Comments
        </Typography>
        
        {(!task.comments || task.comments.length === 0) && (
          <Typography variant="body2" color="text.secondary">
            No comments yet.
          </Typography>
        )}
        
        {task.comments && task.comments.length > 0 && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {task.comments.map(comment => (
              <Box key={comment.id} sx={{ display: 'flex', gap: 1 }}>
                <Avatar 
                  sx={{ 
                    width: 32, 
                    height: 32, 
                    fontSize: '0.875rem',
                    bgcolor: getAssigneeAvatarColor(comment.author)
                  }}
                >
                  {getAssigneeInitials(comment.author)}
                </Avatar>
                <Box sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="subtitle2">
                      {comment.author}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formatTimeAgo(comment.createdAt)}
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    {comment.content}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>
        )}
      </Box>
      
      {/* Add Comment */}
      <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-start' }}>
        <Avatar 
          sx={{ 
            width: 32, 
            height: 32, 
            fontSize: '0.875rem',
            bgcolor: 'primary.main'
          }}
        >
          CU
        </Avatar>
        <TextField
          fullWidth
          placeholder="Add a comment..."
          multiline
          rows={2}
          value={commentInput}
          onChange={handleCommentInputChange}
          variant="outlined"
          size="small"
        />
        <IconButton 
          color="primary" 
          onClick={handleCommentSubmit}
          disabled={!commentInput.trim()}
        >
          <SendIcon />
        </IconButton>
      </Box>
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteDialogClose}
      >
        <DialogTitle>Delete Task</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this task? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">Delete</Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default TaskDetail;
