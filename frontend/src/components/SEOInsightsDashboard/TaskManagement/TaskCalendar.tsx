import React from 'react';
import { FC, <PERSON>actNode, ChangeEvent, MouseEvent } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ChevronLeft as PrevIcon,
  ChevronRight as NextIcon,
  Event as EventIcon
} from '@mui/icons-material';
import { 
  Task, 
  TaskStatus, 
  TaskCalendarProps 
} from './types';
import TaskStatusBadge from './TaskStatusBadge';
import TaskPriorityBadge from './TaskPriorityBadge';
import TaskCategoryBadge from './TaskCategoryBadge';

/**
 * Task Calendar Component
 * 
 * Displays tasks in a calendar view
 */
const TaskCalendar = ({ tasks, 
  onTaskSelect, 
  onTaskStatusChange, 
  filter 
 }: TaskCalendarProps) => {
  // State for current month and year
  const [currentDate, setCurrentDate] = React.useState(new Date());
  
  // Filter tasks based on filter criteria
  const filteredTasks = tasks.filter(task => {
    // Filter by category
    if (!filter.categories.includes(task.category)) {
      return false;
    }
    
    // Filter by priority
    if (!filter.priorities.includes(task.priority)) {
      return false;
    }
    
    // Filter by status
    if (!filter.statuses.includes(task.status)) {
      return false;
    }
    
    // Filter by assignee
    if (filter.assignees.length > 0 && (!task.assignee || !filter.assignees.includes(task.assignee))) {
      return false;
    }
    
    // Filter by tags
    if (filter.tags.length > 0 && (!task.tags || !task.tags.some(tag => filter.tags.includes(tag)))) {
      return false;
    }
    
    // Filter by search term
    if (filter.searchTerm) {
      const searchTerm = filter.searchTerm.toLowerCase();
      return (
        task.title.toLowerCase().includes(searchTerm) ||
        task.description.toLowerCase().includes(searchTerm) ||
        (task.assignee && task.assignee.toLowerCase().includes(searchTerm)) ||
        (task.tags && task.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
      );
    }
    
    return true;
  });
  
  // Get days in month
  const getDaysInMonth = (year: number, month: number): number => {
    return new Date(year, month + 1, 0).getDate();
  };
  
  // Get first day of month
  const getFirstDayOfMonth = (year: number, month: number): number => {
    return new Date(year, month, 1).getDay();
  };
  
  // Get month name
  const getMonthName = (month: number): string => {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return monthNames[month];
  };
  
  // Get tasks for a specific day
  const getTasksForDay = (day: number): Task[] => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    return filteredTasks.filter(task => {
      if (!task.dueDate) return false;
      
      const dueDate = new Date(task.dueDate);
      return (
        dueDate.getFullYear() === year &&
        dueDate.getMonth() === month &&
        dueDate.getDate() === day
      );
    });
  };
  
  // Handle previous month
  const handlePrevMonth = () => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(prev.getMonth() - 1);
      return newDate;
    });
  };
  
  // Handle next month
  const handleNextMonth = () => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(prev.getMonth() + 1);
      return newDate;
    });
  };
  
  // Calendar data
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const daysInMonth = getDaysInMonth(year, month);
  const firstDayOfMonth = getFirstDayOfMonth(year, month);
  const monthName = getMonthName(month);
  
  // Generate calendar days
  const calendarDays = [];
  
  // Add empty cells for days before the first day of the month
  for (let i = 0; i < firstDayOfMonth; i++) {
    calendarDays.push(null);
  }
  
  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push(day);
  }
  
  // Day names
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  
  // Check if a day is today
  const isToday = (day: number): boolean => {
    const today = new Date();
    return (
      today.getFullYear() === year &&
      today.getMonth() === month &&
      today.getDate() === day
    );
  };
  
  return (
    <Box>
      {/* Calendar Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <IconButton onClick={handlePrevMonth}>
          <PrevIcon />
        </IconButton>
        <Typography variant="h6">
          {monthName} {year}
        </Typography>
        <IconButton onClick={handleNextMonth}>
          <NextIcon />
        </IconButton>
      </Box>
      
      {/* Calendar Grid */}
      <Grid container spacing={1}>
        {/* Day Names */}
        {dayNames.map((dayName, index) => (
          <Grid item xs={12/7} key={index}>
            <Typography 
              variant="subtitle2" 
              align="center"
              sx={{ fontWeight: 'bold', mb: 1 }}
            >
              {dayName}
            </Typography>
          </Grid>
        ))}
        
        {/* Calendar Days */}
        {calendarDays.map((day, index) => (
          <Grid item xs={12/7} key={index}>
            {day !== null ? (
              <Paper 
                sx={{ 
                  p: 1, 
                  height: 120, 
                  bgcolor: isToday(day) ? 'primary.light' : 'background.paper',
                  overflow: 'auto'
                }}
              >
                <Typography 
                  variant="body2" 
                  sx={{ 
                    fontWeight: isToday(day) ? 'bold' : 'normal',
                    mb: 1
                  }}
                >
                  {day}
                </Typography>
                
                {/* Tasks for this day */}
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                  {getTasksForDay(day).map(task => (
                    <Paper 
                      key={task.id}
                      sx={{ 
                        p: 0.5, 
                        cursor: 'pointer',
                        borderLeft: '3px solid',
                        borderColor: task.status === TaskStatus.COMPLETED 
                          ? 'success.main' 
                          : task.status === TaskStatus.BLOCKED 
                            ? 'error.main' 
                            : 'primary.main'
                      }}
                      onClick={() => onTaskSelect(task)}
                    >
                      <Tooltip title={task.title}>
                        <Typography 
                          variant="caption" 
                          sx={{ 
                            display: 'block',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis'
                          }}
                        >
                          {task.title}
                        </Typography>
                      </Tooltip>
                    </Paper>
                  ))}
                </Box>
              </Paper>
            ) : (
              <Box sx={{ height: 120 }} />
            )}
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default TaskCalendar;
