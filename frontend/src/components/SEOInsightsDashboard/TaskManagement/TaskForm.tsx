import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import { 
  <PERSON>, 
  <PERSON>po<PERSON>, 
  TextField, 
  <PERSON>ton, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Chip,
  OutlinedInput,
  Autocomplete,
  Grid
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { 
  Task, 
  TaskCategory, 
  TaskPriority, 
  TaskStatus, 
  TaskFormProps 
} from './types';

/**
 * Task Form Component
 * 
 * Form for creating or editing a task
 */
const TaskForm = ({ task, 
  onSubmit, 
  onCancel 
 }: TaskFormProps) => {
  // Form state
  const [title, setTitle] = React.useState(task?.title || '');
  const [description, setDescription] = React.useState(task?.description || '');
  const [category, setCategory] = React.useState(task?.category || TaskCategory.CONTENT);
  const [priority, setPriority] = React.useState(task?.priority || TaskPriority.MEDIUM);
  const [status, setStatus] = React.useState(task?.status || TaskStatus.TODO);
  const [dueDate, setDueDate] = React.useState(task?.dueDate ? new Date(task.dueDate) : null);
  const [assignee, setAssignee] = React.useState(task?.assignee);
  const [tags, setTags] = React.useState(task?.tags || []);
  const [relatedUrls, setRelatedUrls] = React.useState(task?.relatedUrls || []);
  const [estimatedHours, setEstimatedHours] = React.useState(task?.estimatedHours);
  
  // Validation state
  const [titleError, setTitleError] = React.useState('');
  
  // Mock assignees (in a real app, this would come from a user service)
  const assignees = [
    'John Doe',
    'Jane Smith',
    'Bob Johnson',
    'Alice Williams',
    'David Brown'
  ];
  
  // Common SEO tags
  const commonTags = [
    'SEO',
    'Content',
    'Technical',
    'Keyword',
    'Backlink',
    'Analytics',
    'Social',
    'On-Page',
    'Off-Page',
    'Local SEO',
    'Mobile',
    'Performance',
    'Schema',
    'Meta Tags',
    'Link Building'
  ];
  
  // Handle title change
  const handleTitleChange = (event: any) => {
    const value = event.target.value;
    setTitle(value);
    
    // Validate title
    if (!value.trim()) {
      setTitleError('Title is required');
    } else {
      setTitleError('');
    }
  };
  
  // Handle description change
  const handleDescriptionChange = (event: any) => {
    setDescription(event.target.value);
  };
  
  // Handle category change
  const handleCategoryChange = (event: SelectChangeEvent<TaskCategory>) => {
    setCategory(event.target.value as TaskCategory);
  };
  
  // Handle priority change
  const handlePriorityChange = (event: SelectChangeEvent<TaskPriority>) => {
    setPriority(event.target.value as TaskPriority);
  };
  
  // Handle status change
  const handleStatusChange = (event: SelectChangeEvent<TaskStatus>) => {
    setStatus(event.target.value as TaskStatus);
  };
  
  // Handle due date change
  const handleDueDateChange = (date: Date | null) => {
    setDueDate(date);
  };
  
  // Handle assignee change
  const handleAssigneeChange = (event: SelectChangeEvent<string>) => {
    setAssignee(event.target.value);
  };
  
  // Handle tags change
  const handleTagsChange = (_event: any, newValue: string[]) => {
    setTags(newValue);
  };
  
  // Handle related URLs change
  const handleRelatedUrlsChange = (_event: any, newValue: string[]) => {
    setRelatedUrls(newValue);
  };
  
  // Handle estimated hours change
  const handleEstimatedHoursChange = (event: any) => {
    const value = event.target.value;
    setEstimatedHours(value ? Number(value) : undefined);
  };
  
  // Handle form submit
  const handleSubmit = () => {
    // Validate form
    if (!title.trim()) {
      setTitleError('Title is required');
      return;
    }
    
    // Prepare task data
    const taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'> = {
      title,
      description,
      category,
      priority,
      status,
      dueDate: dueDate ? dueDate.toISOString() : undefined,
      assignee,
      tags,
      relatedUrls,
      estimatedHours
    };
    
    // Submit form
    onSubmit(taskData);
  };
  
  return (
    <Dialog 
      open={true} 
      onClose={onCancel}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        {task ? 'Edit Task' : 'Create New Task'}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <Grid container spacing={2}>
            {/* Title */}
            <Grid item xs={12}>
              <TextField
                label="Title"
                value={title}
                onChange={handleTitleChange}
                fullWidth
                required
                error={!!titleError}
                helperText={titleError}
              />
            </Grid>
            
            {/* Description */}
            <Grid item xs={12}>
              <TextField
                label="Description"
                value={description}
                onChange={handleDescriptionChange}
                fullWidth
                multiline
                rows={4}
              />
            </Grid>
            
            {/* Category */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="category-label">Category</InputLabel>
                <Select
                  labelId="category-label"
                  value={category}
                  onChange={handleCategoryChange}
                  label="Category"
                >
                  {Object.values(TaskCategory).map((cat) => (
                    <MenuItem key={cat} value={cat}>
                      {cat.charAt(0).toUpperCase() + cat.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            {/* Priority */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="priority-label">Priority</InputLabel>
                <Select
                  labelId="priority-label"
                  value={priority}
                  onChange={handlePriorityChange}
                  label="Priority"
                >
                  {Object.values(TaskPriority).map((pri) => (
                    <MenuItem key={pri} value={pri}>
                      {pri.charAt(0).toUpperCase() + pri.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            {/* Status */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="status-label">Status</InputLabel>
                <Select
                  labelId="status-label"
                  value={status}
                  onChange={handleStatusChange}
                  label="Status"
                >
                  {Object.values(TaskStatus).map((stat) => (
                    <MenuItem key={stat} value={stat}>
                      {stat.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            {/* Due Date */}
            <Grid item xs={12} sm={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Due Date"
                  value={dueDate}
                  onChange={handleDueDateChange}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </LocalizationProvider>
            </Grid>
            
            {/* Assignee */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="assignee-label">Assignee</InputLabel>
                <Select
                  labelId="assignee-label"
                  value={assignee || ''}
                  onChange={handleAssigneeChange}
                  label="Assignee"
                >
                  <MenuItem value="">
                    <em>Unassigned</em>
                  </MenuItem>
                  {assignees.map((assignee) => (
                    <MenuItem key={assignee} value={assignee}>
                      {assignee}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            {/* Estimated Hours */}
            <Grid item xs={12} sm={6}>
              <TextField
                label="Estimated Hours"
                type="number"
                value={estimatedHours || ''}
                onChange={handleEstimatedHoursChange}
                fullWidth
                inputProps={{ min: 0, step: 0.5 }}
              />
            </Grid>
            
            {/* Tags */}
            <Grid item xs={12}>
              <Autocomplete
                multiple
                freeSolo
                options={commonTags}
                value={tags}
                onChange={handleTagsChange}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip 
                      label={option} 
                      {...getTagProps({ index })} 
                      key={index}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Tags"
                    placeholder="Add tags"
                  />
                )}
              />
            </Grid>
            
            {/* Related URLs */}
            <Grid item xs={12}>
              <Autocomplete
                multiple
                freeSolo
                options={[]}
                value={relatedUrls}
                onChange={handleRelatedUrlsChange}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip 
                      label={option} 
                      {...getTagProps({ index })} 
                      key={index}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Related URLs"
                    placeholder="Add URLs"
                  />
                )}
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onCancel}>Cancel</Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained" 
          color="primary"
          disabled={!title.trim()}
        >
          {task ? 'Update Task' : 'Create Task'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TaskForm;
