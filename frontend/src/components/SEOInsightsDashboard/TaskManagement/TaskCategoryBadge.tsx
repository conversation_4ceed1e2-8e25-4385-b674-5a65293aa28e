import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';

import { Chip } from '@mui/material';
import {
  Article as ContentIcon,
  Code as TechnicalIcon,
  Search as KeywordIcon,
  Link as BacklinkIcon,
  <PERSON><PERSON>hart as AnalyticsIcon,
  Share as SocialIcon,
  MoreH<PERSON>z as OtherIcon
} from '@mui/icons-material';
import { TaskCategory, TaskCategoryBadgeProps } from './types';

/**
 * Task Category Badge Component
 * 
 * Displays a badge with the task category
 */
const TaskCategoryBadge = ({ category  }: TaskCategoryBadgeProps) => {
  // Get icon for category
  const getCategoryIcon = () => {
    switch (category) {
      case TaskCategory.CONTENT:
        return <ContentIcon fontSize="small" />;
      case TaskCategory.TECHNICAL:
        return <TechnicalIcon fontSize="small" />;
      case TaskCategory.KEYWORD:
        return <KeywordIcon fontSize="small" />;
      case TaskCategory.BACKLINK:
        return <BacklinkIcon fontSize="small" />;
      case TaskCategory.ANALYTICS:
        return <AnalyticsIcon fontSize="small" />;
      case TaskCategory.SOCIAL:
        return <SocialIcon fontSize="small" />;
      case TaskCategory.OTHER:
        return <OtherIcon fontSize="small" />;
      default:
        return null;
    }
  };
  
  // Get color for category
  const getCategoryColor = () => {
    switch (category) {
      case TaskCategory.CONTENT:
        return 'primary';
      case TaskCategory.TECHNICAL:
        return 'success';
      case TaskCategory.KEYWORD:
        return 'warning';
      case TaskCategory.BACKLINK:
        return 'secondary';
      case TaskCategory.ANALYTICS:
        return 'info';
      case TaskCategory.SOCIAL:
        return 'error';
      case TaskCategory.OTHER:
        return 'default';
      default:
        return 'default';
    }
  };
  
  // Get label for category
  const getCategoryLabel = () => {
    switch (category) {
      case TaskCategory.CONTENT:
        return 'Content';
      case TaskCategory.TECHNICAL:
        return 'Technical';
      case TaskCategory.KEYWORD:
        return 'Keyword';
      case TaskCategory.BACKLINK:
        return 'Backlink';
      case TaskCategory.ANALYTICS:
        return 'Analytics';
      case TaskCategory.SOCIAL:
        return 'Social';
      case TaskCategory.OTHER:
        return 'Other';
      default:
        return '';
    }
  };
  
  // Use type assertion to fix the Chip component error
  return (
    <Chip 
      icon={getCategoryIcon()} 
      label={getCategoryLabel()} 
      size="small" 
      color={getCategoryColor() as "primary" | "success" | "warning" | "secondary" | "info" | "error" | "default"}
    />
  );
};

export default TaskCategoryBadge;
