import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import <PERSON>Arrow from '@mui/icons-material/PlayArrow';
import {
  Box,
  Typography,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Tooltip
} from '@mui/material';
import {
  MoreVert as MoreIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  PlayArrow as StartIcon,
  Pause as PauseIcon,
  CheckCircle as CompleteIcon,
  Block as BlockIcon,
  Visibility as ReviewIcon
} from '@mui/icons-material';
import {
  Task,
  TaskStatus,
  TaskCardProps
} from './types';
import TaskStatusBadge from './TaskStatusBadge';
import TaskPriorityBadge from './TaskPriorityBadge';
import TaskCategoryBadge from './TaskCategoryBadge';

/**
 * Task Card Component
 *
 * Displays a task in a card format
 */
const TaskCard = ({ task,
  onClick,
  onStatusChange
 }: TaskCardProps) => {
  // State for menu
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);

  // Handle menu open
  const handleMenuOpen = (event: any) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  // Handle menu close
  const handleMenuClose = (event?: any) => {
    if (event) {
      event.stopPropagation();
    }
    setAnchorEl(null);
  };

  // Handle status change
  const handleStatusChange = (status: TaskStatus) => (event: any) => {
    event.stopPropagation();
    onStatusChange(status);
    handleMenuClose();
  };

  // Format date
  const formatDate = (dateString?: string): string => {
    if (!dateString) return '';

    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const isToday = date.toDateString() === today.toDateString();
    const isTomorrow = date.toDateString() === tomorrow.toDateString();

    if (isToday) {
      return 'Today';
    } else if (isTomorrow) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString();
    }
  };

  // Check if task is overdue
  const isOverdue = (): boolean => {
    if (!task.dueDate) return false;

    const dueDate = new Date(task.dueDate);
    const today = new Date();

    return dueDate < today && task.status !== TaskStatus.COMPLETED;
  };

  // Get assignee initials
  const getAssigneeInitials = (): string => {
    if (!task.assignee) return '';

    const nameParts = task.assignee.split(' ');
    if (nameParts.length === 1) {
      return nameParts[0].charAt(0).toUpperCase();
    } else {
      return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
    }
  };

  // Get assignee avatar color
  const getAssigneeAvatarColor = (): string => {
    if (!task.assignee) return '#bdbdbd';

    const colors = [
      '#f44336', '#e91e63', '#9c27b0', '#673ab7', '#3f51b5',
      '#2196f3', '#03a9f4', '#00bcd4', '#009688', '#4caf50',
      '#8bc34a', '#cddc39', '#ffeb3b', '#ffc107', '#ff9800',
      '#ff5722', '#795548', '#607d8b'
    ];

    // Simple hash function to get consistent color for the same name
    let hash = 0;
    for (let i = 0; i < task.assignee.length; i++) {
      hash = task.assignee.charCodeAt(i) + ((hash << 5) - hash);
    }

    return colors[Math.abs(hash) % colors.length];
  };

  return (
    <Paper
      sx={{
        p: 2,
        cursor: 'pointer',
        transition: 'all 0.2s',
        '&:hover': {
          boxShadow: 3,
          transform: 'translateY(-2px)'
        },
        borderLeft: '4px solid',
        borderColor: task.status === TaskStatus.COMPLETED
          ? 'success.main'
          : task.status === TaskStatus.BLOCKED
            ? 'error.main'
            : 'primary.main'
      }}
      onClick={onClick}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
        <TaskCategoryBadge category={task.category} />
        <Box>
          <IconButton
            size="small"
            onClick={handleMenuOpen}
            sx={{ ml: 1 }}
          >
            <MoreIcon fontSize="small" />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleMenuClose}
            onClick={(e) => e.stopPropagation()}
          >
            <MenuItem onClick={handleStatusChange(TaskStatus.TODO)}>
              <ListItemIcon>
                <StartIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Mark as To Do</ListItemText>
            </MenuItem>
            <MenuItem onClick={handleStatusChange(TaskStatus.IN_PROGRESS)}>
              <ListItemIcon>
                <PlayArrow fontSize="small" />
              </ListItemIcon>
              <ListItemText>Start Working</ListItemText>
            </MenuItem>
            <MenuItem onClick={handleStatusChange(TaskStatus.REVIEW)}>
              <ListItemIcon>
                <ReviewIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Ready for Review</ListItemText>
            </MenuItem>
            <MenuItem onClick={handleStatusChange(TaskStatus.COMPLETED)}>
              <ListItemIcon>
                <CompleteIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Mark as Completed</ListItemText>
            </MenuItem>
            <MenuItem onClick={handleStatusChange(TaskStatus.BLOCKED)}>
              <ListItemIcon>
                <BlockIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Mark as Blocked</ListItemText>
            </MenuItem>
          </Menu>
        </Box>
      </Box>

      <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'medium' }}>
        {task.title}
      </Typography>

      <Typography
        variant="body2"
        color="text.secondary"
        sx={{
          mb: 2,
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis'
        }}
      >
        {task.description}
      </Typography>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <TaskStatusBadge status={task.status} />
        <TaskPriorityBadge priority={task.priority} />
      </Box>

      {task.tags && task.tags.length > 0 && (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
          {task.tags.map(tag => (
            <Chip
              key={tag}
              label={tag}
              size="small"
              variant="outlined"
              sx={{ height: 20 }}
            />
          ))}
        </Box>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        {task.dueDate && (
          <Tooltip title={`Due: ${new Date(task.dueDate).toLocaleDateString()}`}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <TimeIcon
                fontSize="small"
                color={isOverdue() ? 'error' : 'action'}
                sx={{ mr: 0.5 }}
              />
              <Typography
                variant="caption"
                color={isOverdue() ? 'error.main' : 'text.secondary'}
              >
                {isOverdue() ? 'Overdue' : formatDate(task.dueDate)}
              </Typography>
            </Box>
          </Tooltip>
        )}

        {task.assignee && (
          <Tooltip title={`Assigned to: ${task.assignee}`}>
            <Avatar
              sx={{
                width: 24,
                height: 24,
                fontSize: '0.75rem',
                bgcolor: getAssigneeAvatarColor()
              }}
            >
              {getAssigneeInitials()}
            </Avatar>
          </Tooltip>
        )}
      </Box>
    </Paper>
  );
};

export default TaskCard;
