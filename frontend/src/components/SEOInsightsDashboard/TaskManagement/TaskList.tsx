import AssignmentIcon from "@mui/icons-material/Assignment";
import EditIcon from "@mui/icons-material/Edit";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import {
    Box,
    Chip,
    IconButton,
    MenuItem,
    Paper,
    Select,
    SelectChangeEvent,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Tooltip,
    Typography
} from "@mui/material";
import React, { useEffect } from "react";
import TaskCategoryBadge from "./TaskCategoryBadge";
import TaskPriorityBadge from "./TaskPriorityBadge";
import TaskStatusBadge from "./TaskStatusBadge";
import {
    Task,
    TaskCategory,
    TaskGroupByOption,
    TaskListProps,
    TaskPriority,
    TaskSortOption,
    TaskStatus,
} from "./types";

/**
 * Task List Component
 *
 * Displays tasks in a table format with sorting and grouping
 */
const TaskList: React.FC<TaskListProps> = ({
  tasks,
  onTaskSelect,
  onTaskStatusChange,
  filter,
  sortOption,
  groupBy,
}) => {
  // State for expanded groups
  const [expandedGroups, setExpandedGroups] = React.useState<Record<string, boolean>>({});

  // Filter tasks based on filter criteria
  const filteredTasks = tasks.filter((task) => {
    // Filter by category
    if (!filter.categories.includes(task.category)) {
      return false;
    }

    // Filter by priority
    if (!filter.priorities.includes(task.priority)) {
      return false;
    }

    // Filter by status
    if (!filter.statuses.includes(task.status)) {
      return false;
    }

    // Filter by assignee
    if (
      filter.assignees.length > 0 &&
      (!task.assignee || !filter.assignees.includes(task.assignee))
    ) {
      return false;
    }

    // Filter by tags
    if (
      filter.tags.length > 0 &&
      (!task.tags || !task.tags.some((tag) => filter.tags.includes(tag)))
    ) {
      return false;
    }

    // Filter by search term
    if (filter.searchTerm) {
      const searchTerm = filter.searchTerm.toLowerCase();
      return (
        task.title.toLowerCase().includes(searchTerm) ||
        task.description.toLowerCase().includes(searchTerm) ||
        (task.assignee && task.assignee.toLowerCase().includes(searchTerm)) ||
        (task.tags &&
          task.tags.some((tag) => tag.toLowerCase().includes(searchTerm)))
      );
    }

    return true;
  });

  // Sort tasks based on sort option
  const sortedTasks = [...filteredTasks].sort((a, b) => {
    switch (sortOption) {
      case TaskSortOption.DUE_DATE_ASC:
        if (!a.dueDate && !b.dueDate) return 0;
        if (!a.dueDate) return 1;
        if (!b.dueDate) return -1;
        return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();

      case TaskSortOption.DUE_DATE_DESC:
        if (!a.dueDate && !b.dueDate) return 0;
        if (!a.dueDate) return 1;
        if (!b.dueDate) return -1;
        return new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime();

      case TaskSortOption.PRIORITY_HIGH_TO_LOW:
        return getPriorityValue(b.priority) - getPriorityValue(a.priority);

      case TaskSortOption.PRIORITY_LOW_TO_HIGH:
        return getPriorityValue(a.priority) - getPriorityValue(b.priority);

      case TaskSortOption.STATUS:
        return getStatusValue(a.status) - getStatusValue(b.status);

      case TaskSortOption.CATEGORY:
        return a.category.localeCompare(b.category);

      case TaskSortOption.CREATED_DATE_ASC:
        return (
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );

      case TaskSortOption.CREATED_DATE_DESC:
        return (
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );

      default:
        return 0;
    }
  });

  // Helper function to get priority value for sorting
  function getPriorityValue(priority: TaskPriority): number {
    switch (priority) {
      case TaskPriority.HIGH:
        return 3;
      case TaskPriority.MEDIUM:
        return 2;
      case TaskPriority.LOW:
        return 1;
      default:
        return 0;
    }
  }

  // Helper function to get status value for sorting
  function getStatusValue(status: TaskStatus): number {
    switch (status) {
      case TaskStatus.TODO:
        return 1;
      case TaskStatus.IN_PROGRESS:
        return 2;
      case TaskStatus.REVIEW:
        return 3;
      case TaskStatus.COMPLETED:
        return 4;
      case TaskStatus.BLOCKED:
        return 5;
      default:
        return 0;
    }
  }

  // Group tasks based on groupBy option
  const groupTasks = () => {
    const groups: Record<string, Task[]> = {};

    if (groupBy === TaskGroupByOption.NONE) {
      groups["All Tasks"] = sortedTasks;
      return groups;
    }

    switch (groupBy) {
      case TaskGroupByOption.STATUS:
        // Initialize groups for all statuses
        Object.values(TaskStatus).forEach((status) => {
          groups[status] = [];
        });

        // Group tasks by status
        sortedTasks.forEach((task) => {
          groups[task.status].push(task);
        });
        break;

      case TaskGroupByOption.CATEGORY:
        // Initialize groups for all categories
        Object.values(TaskCategory).forEach((category) => {
          groups[category] = [];
        });

        // Group tasks by category
        sortedTasks.forEach((task) => {
          groups[task.category].push(task);
        });
        break;

      case TaskGroupByOption.PRIORITY:
        // Initialize groups for all priorities
        Object.values(TaskPriority).forEach((priority) => {
          groups[priority] = [];
        });

        // Group tasks by priority
        sortedTasks.forEach((task) => {
          groups[task.priority].push(task);
        });
        break;

      case TaskGroupByOption.ASSIGNEE:
        // Group tasks by assignee
        sortedTasks.forEach((task) => {
          const assignee = task.assignee || "Unassigned";
          if (!groups[assignee]) {
            groups[assignee] = [];
          }
          groups[assignee].push(task);
        });
        break;

      case TaskGroupByOption.DUE_DATE:
        // Group tasks by due date
        sortedTasks.forEach((task) => {
          let dueDate = "No Due Date";

          if (task.dueDate) {
            const date = new Date(task.dueDate);
            dueDate = date.toLocaleDateString();
          }

          if (!groups[dueDate]) {
            groups[dueDate] = [];
          }

          groups[dueDate].push(task);
        });
        break;

      default:
        groups["All Tasks"] = sortedTasks;
        break;
    }

    // Remove empty groups
    Object.keys(groups).forEach((key) => {
      if (groups[key].length === 0) {
        delete groups[key];
      }
    });

    return groups;
  };

  // Get group title
  const getGroupTitle = (groupKey: string): string => {
    switch (groupBy) {
      case TaskGroupByOption.STATUS:
        return groupKey
          .replace("_", " ")
          .replace(/\b\w/g, (l) => l.toUpperCase());

      case TaskGroupByOption.CATEGORY:
        return groupKey
          .replace("_", " ")
          .replace(/\b\w/g, (l) => l.toUpperCase());

      case TaskGroupByOption.PRIORITY:
        return groupKey
          .replace("_", " ")
          .replace(/\b\w/g, (l) => l.toUpperCase());

      case TaskGroupByOption.ASSIGNEE:
        return groupKey;

      case TaskGroupByOption.DUE_DATE:
        return groupKey;

      default:
        return groupKey;
    }
  };

  // Toggle group expansion
  const toggleGroupExpansion = (groupKey: string): void => {
    setExpandedGroups((prev) => ({
      ...prev,
      [groupKey]: !prev[groupKey],
    }));
  };

  // Handle status change
  const handleStatusChange = (
    taskId: string,
    event: SelectChangeEvent<string>
  ): void => {
    onTaskStatusChange(taskId, event.target.value as TaskStatus);
  };

  // Format date
  const formatDate = (dateString?: string): string => {
    if (!dateString) return "N/A";

    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Group tasks
  const groupedTasks = groupTasks();

  // Initialize expanded groups if not already set
  useEffect(() => {
    const newExpandedGroups: Record<string, boolean> = {};

    Object.keys(groupedTasks).forEach((groupKey) => {
      if (expandedGroups[groupKey] === undefined) {
        newExpandedGroups[groupKey] = true;
      }
    });

    if (Object.keys(newExpandedGroups).length > 0) {
      setExpandedGroups((prev) => ({
        ...prev,
        ...newExpandedGroups,
      }));
    }
  }, [groupedTasks]);

  return (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            {groupBy !== TaskGroupByOption.NONE && (
              <TableCell width={50}></TableCell>
            )}
            <TableCell>Title</TableCell>
            {groupBy !== TaskGroupByOption.STATUS && (
              <TableCell>Status</TableCell>
            )}
            {groupBy !== TaskGroupByOption.PRIORITY && (
              <TableCell>Priority</TableCell>
            )}
            {groupBy !== TaskGroupByOption.CATEGORY && (
              <TableCell>Category</TableCell>
            )}
            {groupBy !== TaskGroupByOption.ASSIGNEE && (
              <TableCell>Assignee</TableCell>
            )}
            {groupBy !== TaskGroupByOption.DUE_DATE && (
              <TableCell>Due Date</TableCell>
            )}
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {Object.entries(groupedTasks).map(([groupKey, groupTasks]) => (
            <React.Fragment key={groupKey}>
              {/* Group Header Row */}
              {groupBy !== TaskGroupByOption.NONE && (
                <TableRow
                  sx={{
                    bgcolor: "action.hover",
                    "& > *": { borderBottom: "unset" },
                  }}
                >
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => toggleGroupExpansion(groupKey)}
                    >
                      {expandedGroups[groupKey] ? (
                        <KeyboardArrowUpIcon />
                      ) : (
                        <KeyboardArrowDownIcon />
                      )}
                    </IconButton>
                  </TableCell>
                  <TableCell colSpan={6}>
                    <Typography variant="subtitle1" fontWeight="bold">
                      {getGroupTitle(groupKey)} ({groupTasks.length})
                    </Typography>
                  </TableCell>
                </TableRow>
              )}

              {/* Group Tasks */}
              {(groupBy === TaskGroupByOption.NONE ||
                expandedGroups[groupKey]) &&
                groupTasks.map((task) => (
                  <TableRow
                    key={task.id}
                    hover
                    onClick={() => onTaskSelect(task)}
                    sx={{ cursor: "pointer" }}
                  >
                    {groupBy !== TaskGroupByOption.NONE && (
                      <TableCell></TableCell>
                    )}
                    <TableCell>
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <AssignmentIcon sx={{ mr: 1, color: "primary.main" }} />
                        <Box>
                          <Typography variant="body2">{task.title}</Typography>{" "}
                          {task.tags && task.tags.length > 0 && (
                            <Box
                              sx={{
                                display: "flex",
                                flexWrap: "wrap",
                                gap: 0.5,
                                mt: 0.5,
                              }}
                            >
                              {task.tags.map((tag: string, index: number) => (
                                <Chip
                                  key={tag}
                                  label={tag}
                                  size="small"
                                  variant="outlined"
                                  sx={{ height: 20 }}
                                />
                              ))}
                            </Box>
                          )}
                        </Box>
                      </Box>
                    </TableCell>
                    {groupBy !== TaskGroupByOption.STATUS && (
                      <TableCell>
                        <Select
                          value={task.status}
                          onChange={(e) => handleStatusChange(task.id, e)}
                          size="small"
                          onClick={(e) => e.stopPropagation()}
                          sx={{ minWidth: 120 }}
                        >
                          {Object.values(TaskStatus).map((status) => (
                            <MenuItem key={status} value={status}>
                              <TaskStatusBadge status={status} />
                            </MenuItem>
                          ))}
                        </Select>
                      </TableCell>
                    )}
                    {groupBy !== TaskGroupByOption.PRIORITY && (
                      <TableCell>
                        <TaskPriorityBadge priority={task.priority} />
                      </TableCell>
                    )}
                    {groupBy !== TaskGroupByOption.CATEGORY && (
                      <TableCell>
                        <TaskCategoryBadge category={task.category} />
                      </TableCell>
                    )}
                    {groupBy !== TaskGroupByOption.ASSIGNEE && (
                      <TableCell>{task.assignee || "Unassigned"}</TableCell>
                    )}
                    {groupBy !== TaskGroupByOption.DUE_DATE && (
                      <TableCell>{formatDate(task.dueDate)}</TableCell>
                    )}
                    <TableCell>
                      <Box sx={{ display: "flex" }}>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={(e) => {
                              e.stopPropagation();
                              onTaskSelect(task);
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default TaskList;
