import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import DownloadIcon from '@mui/icons-material/Download';
import TimelineIcon from '@mui/icons-material/Timeline';
import {
    Alert,
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    FormControl,
    Grid,
    InputLabel,
    MenuItem,
    Select,
    SelectChangeEvent,
    Tab,
    Tabs,
    Typography
} from '@mui/material';
import React from 'react';
// Recharts wrapper components - commented out due to TypeScript errors
// These components will be replaced with direct usage of Recharts components
/*
const LineChartWrapper = (props: any) => <LineChart {...props} />;
const LineWrapper = (props: any) => <Line {...props} />;
const XAxisWrapper = (props: any) => <XAxis {...props} />;
const YAxisWrapper = (props: any) => <YAxis {...props} />;
const CartesianGridWrapper = (props: any) => <CartesianGrid {...props} />;
const TooltipWrapper = (props: any) => <Tooltip {...props} />;
const LegendWrapper = (props: any) => <Legend {...props} />;
const ResponsiveContainerWrapper = (props: any) => <ResponsiveContainer {...props} />;
const BarChartWrapper = (props: any) => <BarChart {...props} />;
const BarWrapper = (props: any) => <Bar {...props} />;
const CellWrapper = (props: any) => <Cell {...props} />;
const PieChartWrapper = (props: any) => <PieChart {...props} />;
const PieWrapper = (props: any) => <Pie {...props} />;
const RadarWrapper = (props: any) => <Radar {...props} />;
const RadarChartWrapper = (props: any) => <RadarChart {...props} />;
const PolarGridWrapper = (props: any) => <PolarGrid {...props} />;
const PolarAngleAxisWrapper = (props: any) => <PolarAngleAxis {...props} />;
const PolarRadiusAxisWrapper = (props: any) => <PolarRadiusAxis {...props} />;
*/

import { HistoricalMetrics, SEOInsightsData } from '../types';
import KeywordTrendsTab from './KeywordTrendsTab';
import MetricsComparisonTab from './MetricsComparisonTab';
import PagePerformanceTab from './PagePerformanceTab';

interface HistoricalAnalysisProps {
  data: SEOInsightsData;
}

/**
 * Historical Analysis Component
 *
 * Displays historical SEO data and allows comparison between different time periods
 */
const HistoricalAnalysis = ({ data }: HistoricalAnalysisProps) => {
  const [tabValue, setTabValue] = React.useState(0);
  const [selectedPeriod1, setSelectedPeriod1] = React.useState('');
  const [selectedPeriod2, setSelectedPeriod2] = React.useState('');

  // Check if historical data is available
  const hasHistoricalData = data.historicalData &&
                           data.historicalData.availablePeriods &&
                           data.historicalData.availablePeriods.length > 0;

  // Set default periods on component mount
  React.useEffect(() => {
    if (hasHistoricalData) {
      const periods = data.historicalData!.availablePeriods;

      // Set most recent period as period 1
      setSelectedPeriod1(periods[periods.length - 1].id);

      // Set previous period as period 2 (if available)
      if (periods.length > 1) {
        setSelectedPeriod2(periods[periods.length - 2].id);
      }
    }
  }, [data.historicalData]);

  // Handle tab change
  const handleTabChange = (_event: any, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle period 1 change
  const handlePeriod1Change = (event: SelectChangeEvent) => {
    const newPeriod = event.target.value;
    setSelectedPeriod1(newPeriod);

    // If both periods are the same, change period 2
    if (newPeriod === selectedPeriod2 && hasHistoricalData) {
      const periods = data.historicalData!.availablePeriods;
      const currentIndex = periods.findIndex(p => p.id === newPeriod);

      // Set period 2 to the previous period if available, otherwise the next period
      if (currentIndex > 0) {
        setSelectedPeriod2(periods[currentIndex - 1].id);
      } else if (currentIndex < periods.length - 1) {
        setSelectedPeriod2(periods[currentIndex + 1].id);
      }
    }
  };

  // Handle period 2 change
  const handlePeriod2Change = (event: SelectChangeEvent) => {
    const newPeriod = event.target.value;
    setSelectedPeriod2(newPeriod);

    // If both periods are the same, change period 1
    if (newPeriod === selectedPeriod1 && hasHistoricalData) {
      const periods = data.historicalData!.availablePeriods;
      const currentIndex = periods.findIndex(p => p.id === newPeriod);

      // Set period 1 to the next period if available, otherwise the previous period
      if (currentIndex < periods.length - 1) {
        setSelectedPeriod1(periods[currentIndex + 1].id);
      } else if (currentIndex > 0) {
        setSelectedPeriod1(periods[currentIndex - 1].id);
      }
    }
  };

  // Get period label by ID
  const getPeriodLabel = (periodId: string): string => {
    if (!hasHistoricalData) return '';

    const period = data.historicalData!.availablePeriods.find(p => p.id === periodId);
    return period ? period.label : '';
  };

  // Get metrics for a specific period
  const getMetricsForPeriod = (periodId: string): HistoricalMetrics | null => {
    if (!hasHistoricalData) return null;

    const metricPoint = data.historicalData!.metrics.find(m => m.period === periodId);
    return metricPoint ? metricPoint.data : null;
  };

  // If no historical data is available, show a message
  if (!hasHistoricalData) {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        No historical data is available for analysis. Historical data is needed to compare performance over time.
      </Alert>
    );
  }

  // Get metrics for selected periods
  const period1Metrics = getMetricsForPeriod(selectedPeriod1);
  const period2Metrics = getMetricsForPeriod(selectedPeriod2);

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          <TimelineIcon sx={{ verticalAlign: 'middle', mr: 1 }} />
          Historical Performance Analysis
        </Typography>
        <Button
          variant="outlined"
          size="small"
          startIcon={<DownloadIcon />}
          onClick={() => console.log('Download report')}
        >
          Export Report
        </Button>
      </Box>

      {/* Period Selection */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <CompareArrowsIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6">Compare Time Periods</Typography>
          </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} md={5}>
              <FormControl fullWidth>
                <InputLabel id="period-1-label">Period 1</InputLabel>
                <Select
                  labelId="period-1-label"
                  value={selectedPeriod1}
                  label="Period 1"
                  onChange={handlePeriod1Change}
                >
                  {data.historicalData!.availablePeriods.map((period) => (
                    <MenuItem key={`p1-${period.id}`} value={period.id}>
                      {period.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={2} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <Chip
                label="vs"
                color="primary"
                sx={{
                  width: 40,
                  height: 40,
                  fontSize: '1.2rem',
                  fontWeight: 'bold'
                }}
              />
            </Grid>

            <Grid item xs={12} md={5}>
              <FormControl fullWidth>
                <InputLabel id="period-2-label">Period 2</InputLabel>
                <Select
                  labelId="period-2-label"
                  value={selectedPeriod2}
                  label="Period 2"
                  onChange={handlePeriod2Change}
                >
                  {data.historicalData!.availablePeriods.map((period) => (
                    <MenuItem key={`p2-${period.id}`} value={period.id}>
                      {period.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tabs Navigation */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="historical analysis tabs"
        >
          <Tab label="Metrics Comparison" />
          <Tab label="Keyword Trends" />
          <Tab label="Page Performance" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      <Box role="tabpanel" hidden={tabValue !== 0} id="tabpanel-metrics">
        {tabValue === 0 && period1Metrics && period2Metrics && (
          <MetricsComparisonTab
            period1={{
              id: selectedPeriod1,
              label: getPeriodLabel(selectedPeriod1),
              metrics: period1Metrics
            }}
            period2={{
              id: selectedPeriod2,
              label: getPeriodLabel(selectedPeriod2),
              metrics: period2Metrics
            }}
          />
        )}
      </Box>

      <Box role="tabpanel" hidden={tabValue !== 1} id="tabpanel-keywords">
        {tabValue === 1 && data.historicalData?.keywordTrends && (
          <KeywordTrendsTab
            keywordTrends={data.historicalData.keywordTrends}
            periods={data.historicalData.availablePeriods}
            selectedPeriods={[selectedPeriod1, selectedPeriod2]}
          />
        )}
      </Box>

      <Box role="tabpanel" hidden={tabValue !== 2} id="tabpanel-pages">
        {tabValue === 2 && data.historicalData?.pageTrends && (
          <PagePerformanceTab
            pageTrends={data.historicalData.pageTrends}
            periods={data.historicalData.availablePeriods}
            selectedPeriods={[selectedPeriod1, selectedPeriod2]}
          />
        )}
      </Box>
    </Box>
  );
};

export default HistoricalAnalysis;
