import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  InputAdornment,
  IconButton
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
// Recharts wrapper components - commented out due to TypeScript errors
// These components will be replaced with direct usage of Recharts components
/*
const BarWrapper = (props: any) => <Bar {...props} />;
const XAxisWrapper = (props: any) => <XAxis {...props} />;
const YAxisWrapper = (props: any) => <YAxis {...props} />;
const LineWrapper = (props: any) => <Line {...props} />;
const PieWrapper = (props: any) => <Pie {...props} />;
const TooltipWrapper = (props: any) => <Tooltip {...props} />;
const LegendWrapper = (props: any) => <Legend {...props} />;
*/

import { HistoricalDataPeriod } from '../types';

interface KeywordTrend {
  keyword: string;
  positions: {
    period: string;
    position: number;
  }[];
}

interface KeywordTrendsTabProps {
  keywordTrends: KeywordTrend[];
  periods: HistoricalDataPeriod[];
  selectedPeriods: string[];
}

/**
 * Keyword Trends Tab
 *
 * Displays trends for keyword positions over time
 */
const KeywordTrendsTab = ({ keywordTrends, periods, selectedPeriods  }: KeywordTrendsTabProps) => {
  const [selectedKeyword, setSelectedKeyword] = React.useState(keywordTrends[0]?.keyword || '');
  const [searchTerm, setSearchTerm] = React.useState('');

  // Handle keyword selection change
  const handleKeywordChange = (event: SelectChangeEvent) => {
    setSelectedKeyword(event.target.value);
  };

  // Handle search term change
  const handleSearchChange = (event: any) => {
    setSearchTerm(event.target.value);
  };

  // Filter keywords based on search term
  const filteredKeywords = keywordTrends.filter(trend =>
    trend.keyword.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get trend icon based on position change
  const getTrendIcon = (startPosition: number, endPosition: number) => {
    const change = startPosition - endPosition; // Positive change means improvement (lower position number is better)

    if (change > 1) {
      return <TrendingUpIcon color="success" />;
    } else if (change < -1) {
      return <TrendingDownIcon color="error" />;
    } else {
      return <TrendingFlatIcon color="action" />;
    }
  };

  // Get position color based on position value
  const getPositionColor = (position: number) => {
    if (position <= 3) return 'success';
    if (position <= 10) return 'primary';
    if (position <= 20) return 'info';
    return 'default';
  };

  // Prepare data for the selected keyword's trend chart
  const prepareChartData = (keyword: string) => {
    const trend = keywordTrends.find(t => t.keyword === keyword);
    if (!trend) return [];

    return periods.map(period => {
      const positionData = trend.positions.find(p => p.period === period.id);
      return {
        period: period.label,
        position: positionData ? positionData.position : null
      };
    });
  };

  // Get position for a specific keyword and period
  const getPosition = (keyword: string, periodId: string): number | null => {
    const trend = keywordTrends.find(t => t.keyword === keyword);
    if (!trend) return null;

    const positionData = trend.positions.find(p => p.period === periodId);
    return positionData ? positionData.position : null;
  };

  // Calculate position change between two periods
  const calculatePositionChange = (keyword: string, period1Id: string, period2Id: string): number => {
    const position1 = getPosition(keyword, period1Id);
    const position2 = getPosition(keyword, period2Id);

    if (position1 === null || position2 === null) return 0;
    return position2 - position1; // Negative change means improvement
  };

  // Selected keyword chart data
  const chartData = prepareChartData(selectedKeyword);

  return (
    <Box>
      {/* Keyword Search and Selection */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Search Keywords"
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={() => setSearchTerm('')}
                      >
                        <FilterListIcon />
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel id="keyword-select-label">Selected Keyword</InputLabel>
                <Select
                  labelId="keyword-select-label"
                  value={selectedKeyword}
                  label="Selected Keyword"
                  onChange={handleKeywordChange}
                >
                  {filteredKeywords.map((trend) => (
                    <MenuItem key={trend.keyword} value={trend.keyword}>
                      {trend.keyword}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Selected Keyword Trend Chart */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Position Trend for "{selectedKeyword}"
          </Typography>
          <Box sx={{ height: 400 }}>
            <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
          </Box>
          <Typography variant="caption" color="text.secondary">
            Note: Lower position numbers are better (position 1 is the top of search results).
          </Typography>
        </CardContent>
      </Card>

      {/* Keywords Comparison Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Keyword Position Comparison
          </Typography>
          <TableContainer component={Paper} sx={{ maxHeight: 440 }}>
            <Table stickyHeader size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Keyword</TableCell>
                  <TableCell align="center">{periods.find(p => p.id === selectedPeriods[0])?.label || 'Current'}</TableCell>
                  <TableCell align="center">{periods.find(p => p.id === selectedPeriods[1])?.label || 'Previous'}</TableCell>
                  <TableCell align="center">Change</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredKeywords.map((trend) => {
                  const position1 = getPosition(trend.keyword, selectedPeriods[0]);
                  const position2 = getPosition(trend.keyword, selectedPeriods[1]);
                  const change = position2 !== null && position1 !== null ? position2 - position1 : 0;

                  return (
                    <TableRow
                      key={trend.keyword}
                      hover
                      selected={trend.keyword === selectedKeyword}
                      onClick={() => setSelectedKeyword(trend.keyword)}
                      sx={{ cursor: 'pointer' }}
                    >
                      <TableCell component="th" scope="row">
                        {trend.keyword}
                      </TableCell>
                      <TableCell align="center">
                        {position1 !== null ? (
                          <Chip
                            label={position1}
                            size="small"
                            color={getPositionColor(position1)}
                            variant={position1 <= 10 ? 'filled' : 'outlined'}
                          />
                        ) : (
                          'N/A'
                        )}
                      </TableCell>
                      <TableCell align="center">
                        {position2 !== null ? (
                          <Chip
                            label={position2}
                            size="small"
                            color={getPositionColor(position2)}
                            variant={position2 <= 10 ? 'filled' : 'outlined'}
                          />
                        ) : (
                          'N/A'
                        )}
                      </TableCell>
                      <TableCell align="center">
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          {position1 !== null && position2 !== null && (
                            <>
                              {getTrendIcon(position2, position1)}
                              <Typography
                                variant="body2"
                                color={change < 0 ? 'error.main' : change > 0 ? 'success.main' : 'text.secondary'}
                                sx={{ ml: 0.5 }}
                              >
                                {change === 0 ? 'No change' : change < 0 ? change : `+${change}`}
                              </Typography>
                            </>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default KeywordTrendsTab;
