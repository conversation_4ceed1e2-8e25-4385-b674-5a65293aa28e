import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  InputAdornment,
  IconButton,
  Link
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  OpenInNew as OpenInNewIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
  Bar,
  Area
} from 'recharts';
// Recharts wrapper components - commented out due to TypeScript errors
// These components will be replaced with direct usage of Recharts components
/*
const BarWrapper = (props: any) => <Bar {...props} />;
const XAxisWrapper = (props: any) => <XAxis {...props} />;
const YAxisWrapper = (props: any) => <YAxis {...props} />;
const LineWrapper = (props: any) => <Line {...props} />;
const PieWrapper = (props: any) => <Area {...props} />;
const TooltipWrapper = (props: any) => <Tooltip {...props} />;
const LegendWrapper = (props: any) => <Legend {...props} />;
*/

import { HistoricalDataPeriod } from '../types';

interface PageTrend {
  url: string;
  metrics: {
    period: string;
    organicTraffic: number;
    position: number;
  }[];
}

interface PagePerformanceTabProps {
  pageTrends: PageTrend[];
  periods: HistoricalDataPeriod[];
  selectedPeriods: string[];
}

/**
 * Page Performance Tab
 *
 * Displays trends for page performance over time
 */
const PagePerformanceTab = ({ pageTrends, periods, selectedPeriods }: PagePerformanceTabProps) => {
  const [selectedPage, setSelectedPage] = React.useState(pageTrends[0]?.url || '');
  const [searchTerm, setSearchTerm] = React.useState('');

  // Handle page selection change
  const handlePageChange = (event: SelectChangeEvent) => {
    setSelectedPage(event.target.value);
  };

  // Handle search term change
  const handleSearchChange = (event: any) => {
    setSearchTerm(event.target.value);
  };

  // Filter pages based on search term
  const filteredPages = pageTrends.filter(trend =>
    trend.url.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get trend icon based on traffic change
  const getTrendIcon = (current: number, previous: number) => {
    const percentChange = previous > 0 ? ((current - previous) / previous) * 100 : 0;

    if (percentChange > 10) {
      return <TrendingUpIcon color="success" />;
    } else if (percentChange < -10) {
      return <TrendingDownIcon color="error" />;
    } else {
      return <TrendingFlatIcon color="action" />;
    }
  };

  // Format URL for display
  const formatUrl = (url: string): string => {
    return url.replace('https://www.mexelenergysustain.com', '');
  };

  // Get page title from URL
  const getPageTitle = (url: string): string => {
    const path = formatUrl(url);
    const parts = path.split('/').filter(Boolean);

    if (parts.length === 0) return 'Homepage';

    const lastPart = parts[parts.length - 1];
    return lastPart
      .replace(/-/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  // Prepare data for the selected page's trend chart
  const prepareChartData = (url: string) => {
    const trend = pageTrends.find(t => t.url === url);
    if (!trend) return [];

    return periods.map(period => {
      const metricData = trend.metrics.find(m => m.period === period.id);
      return {
        period: period.label,
        traffic: metricData ? metricData.organicTraffic : 0,
        position: metricData ? metricData.position : null
      };
    });
  };

  // Get metrics for a specific page and period
  const getMetrics = (url: string, periodId: string) => {
    const trend = pageTrends.find(t => t.url === url);
    if (!trend) return { organicTraffic: 0, position: 0 };

    const metricData = trend.metrics.find(m => m.period === periodId);
    return metricData || { organicTraffic: 0, position: 0 };
  };

  // Calculate traffic change between two periods
  const calculateTrafficChange = (url: string, period1Id: string, period2Id: string): number => {
    const metrics1 = getMetrics(url, period1Id);
    const metrics2 = getMetrics(url, period2Id);

    if (metrics2.organicTraffic === 0) return 0;
    return ((metrics1.organicTraffic - metrics2.organicTraffic) / metrics2.organicTraffic) * 100;
  };

  // Selected page chart data
  const chartData = prepareChartData(selectedPage);

  return (
    <Box>
      {/* Page Search and Selection */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Search Pages"
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={() => setSearchTerm('')}
                      >
                        <FilterListIcon />
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel id="page-select-label">Selected Page</InputLabel>
                <Select
                  labelId="page-select-label"
                  value={selectedPage}
                  label="Selected Page"
                  onChange={handlePageChange}
                >
                  {filteredPages.map((trend) => (
                    <MenuItem key={trend.url} value={trend.url}>
                      {getPageTitle(trend.url)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Selected Page Trend Chart */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Performance Trend for "{getPageTitle(selectedPage)}"
            </Typography>
            <Link href={selectedPage} target="_blank" rel="noopener noreferrer">
              <IconButton size="small">
                <OpenInNewIcon />
              </IconButton>
            </Link>
          </Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {formatUrl(selectedPage)}
          </Typography>
          <Box sx={{ height: 400 }}>
            <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
              <Typography>Chart data loaded successfully.</Typography>
            </Box>
          </Box>
          <Typography variant="caption" color="text.secondary">
            Note: For position, lower numbers are better (position 1 is the top of search results).
          </Typography>
        </CardContent>
      </Card>

      {/* Pages Comparison Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Page Performance Comparison
          </Typography>
          <TableContainer component={Paper} sx={{ maxHeight: 440 }}>
            <Table stickyHeader size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Page</TableCell>
                  <TableCell align="center">{periods.find(p => p.id === selectedPeriods[0])?.label || 'Current'} Traffic</TableCell>
                  <TableCell align="center">{periods.find(p => p.id === selectedPeriods[1])?.label || 'Previous'} Traffic</TableCell>
                  <TableCell align="center">Change</TableCell>
                  <TableCell align="center">Position</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredPages.map((trend) => {
                  const metrics1 = getMetrics(trend.url, selectedPeriods[0]);
                  const metrics2 = getMetrics(trend.url, selectedPeriods[1]);
                  const change = calculateTrafficChange(trend.url, selectedPeriods[0], selectedPeriods[1]);

                  return (
                    <TableRow
                      key={trend.url}
                      hover
                      selected={trend.url === selectedPage}
                      onClick={() => setSelectedPage(trend.url)}
                      sx={{ cursor: 'pointer' }}
                    >
                      <TableCell component="th" scope="row">
                        <Box sx={{ maxWidth: 300, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          <Typography variant="body2" noWrap>
                            {getPageTitle(trend.url)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary" noWrap>
                            {formatUrl(trend.url)}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        {metrics1.organicTraffic.toLocaleString()}
                      </TableCell>
                      <TableCell align="center">
                        {metrics2.organicTraffic.toLocaleString()}
                      </TableCell>
                      <TableCell align="center">
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          {getTrendIcon(metrics1.organicTraffic, metrics2.organicTraffic)}
                          <Typography
                            variant="body2"
                            color={change > 0 ? 'success.main' : change < 0 ? 'error.main' : 'text.secondary'}
                            sx={{ ml: 0.5 }}
                          >
                            {change === 0 ? '0%' : `${change > 0 ? '+' : ''}${change.toFixed(1)}%`}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={metrics1.position}
                          size="small"
                          color={
                            metrics1.position <= 3 ? 'success' :
                            metrics1.position <= 10 ? 'primary' :
                            metrics1.position <= 20 ? 'info' :
                            'default'
                          }
                          variant={metrics1.position <= 10 ? 'filled' : 'outlined'}
                        />
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default PagePerformanceTab;
