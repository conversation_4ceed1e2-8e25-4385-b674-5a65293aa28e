import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';

import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Divider,
  Paper,
  LinearProgress
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon
} from '@mui/icons-material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
// Recharts wrapper components - commented out due to TypeScript errors
// These components will be replaced with direct usage of Recharts components
/*
const BarWrapper = (props: any) => <Bar {...props} />;
const XAxisWrapper = (props: any) => <XAxis {...props} />;
const YAxisWrapper = (props: any) => <YAxis {...props} />;
const LineWrapper = (props: any) => <Line {...props} />;
const PieWrapper = (props: any) => <Pie {...props} />;
const TooltipWrapper = (props: any) => <Tooltip {...props} />;
const LegendWrapper = (props: any) => <Legend {...props} />;
*/

import { HistoricalMetrics } from '../types';

interface PeriodData {
  id: string;
  label: string;
  metrics: HistoricalMetrics;
}

interface MetricsComparisonTabProps {
  period1: PeriodData;
  period2: PeriodData;
}

/**
 * Metrics Comparison Tab
 *
 * Displays a comparison of key metrics between two time periods
 */
const MetricsComparisonTab = ({ period1, period2  }: MetricsComparisonTabProps) => {
  // Calculate percentage change between two values
  const calculateChange = (current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  };

  // Get trend icon based on change percentage
  const getTrendIcon = (change: number) => {
    if (change > 1) {
      return <TrendingUpIcon color="success" />;
    } else if (change < -1) {
      return <TrendingDownIcon color="error" />;
    } else {
      return <TrendingFlatIcon color="action" />;
    }
  };

  // Format percentage change
  const formatChange = (change: number): string => {
    const sign = change > 0 ? '+' : '';
    return `${sign}${change.toFixed(1)}%`;
  };

  // Prepare data for key metrics comparison chart
  const keyMetricsData = [
    {
      name: 'Domain Authority',
      [period1.label]: period1.metrics.domainAuthority,
      [period2.label]: period2.metrics.domainAuthority
    },
    {
      name: 'Organic Traffic',
      [period1.label]: period1.metrics.organicTraffic / 1000, // Scale down for better visualization
      [period2.label]: period2.metrics.organicTraffic / 1000
    },
    {
      name: 'Organic Keywords',
      [period1.label]: period1.metrics.organicKeywords,
      [period2.label]: period2.metrics.organicKeywords
    },
    {
      name: 'Backlinks',
      [period1.label]: period1.metrics.backlinks / 100, // Scale down for better visualization
      [period2.label]: period2.metrics.backlinks / 100
    },
    {
      name: 'Avg. Position',
      [period1.label]: period1.metrics.averagePosition,
      [period2.label]: period2.metrics.averagePosition
    }
  ];

  // Prepare data for keyword position distribution comparison
  const prepareKeywordDistributionData = (metrics: HistoricalMetrics) => {
    return metrics.keywordPositionDistribution.map(item => ({
      name: item.position,
      value: item.count
    }));
  };

  // Prepare data for radar chart
  const radarData = [
    {
      subject: 'Domain Authority',
      [period1.label]: period1.metrics.domainAuthority,
      [period2.label]: period2.metrics.domainAuthority,
      fullMark: 100
    },
    {
      subject: 'Traffic',
      [period1.label]: period1.metrics.organicTraffic / 100,
      [period2.label]: period2.metrics.organicTraffic / 100,
      fullMark: 100
    },
    {
      subject: 'Keywords',
      [period1.label]: period1.metrics.organicKeywords / 2,
      [period2.label]: period2.metrics.organicKeywords / 2,
      fullMark: 100
    },
    {
      subject: 'CTR',
      [period1.label]: period1.metrics.clickThroughRate * 10,
      [period2.label]: period2.metrics.clickThroughRate * 10,
      fullMark: 100
    },
    {
      subject: 'Top Positions',
      [period1.label]: period1.metrics.topPositionCount * 2,
      [period2.label]: period2.metrics.topPositionCount * 2,
      fullMark: 100
    }
  ];

  // Colors for pie charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <Box>
      {/* Key Metrics Summary */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Organic Traffic</Typography>
              <Box sx={{ display: 'flex', alignItems: 'baseline' }}>
                <Typography variant="h4">{period1.metrics.organicTraffic.toLocaleString()}</Typography>
                <Box sx={{ ml: 2, display: 'flex', alignItems: 'center' }}>
                  {getTrendIcon(calculateChange(period1.metrics.organicTraffic, period2.metrics.organicTraffic))}
                  <Typography
                    variant="body2"
                    color={calculateChange(period1.metrics.organicTraffic, period2.metrics.organicTraffic) > 0 ? 'success.main' : 'error.main'}
                    sx={{ ml: 0.5 }}
                  >
                    {formatChange(calculateChange(period1.metrics.organicTraffic, period2.metrics.organicTraffic))}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                vs. {period2.metrics.organicTraffic.toLocaleString()} in {period2.label}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Organic Keywords</Typography>
              <Box sx={{ display: 'flex', alignItems: 'baseline' }}>
                <Typography variant="h4">{period1.metrics.organicKeywords.toLocaleString()}</Typography>
                <Box sx={{ ml: 2, display: 'flex', alignItems: 'center' }}>
                  {getTrendIcon(calculateChange(period1.metrics.organicKeywords, period2.metrics.organicKeywords))}
                  <Typography
                    variant="body2"
                    color={calculateChange(period1.metrics.organicKeywords, period2.metrics.organicKeywords) > 0 ? 'success.main' : 'error.main'}
                    sx={{ ml: 0.5 }}
                  >
                    {formatChange(calculateChange(period1.metrics.organicKeywords, period2.metrics.organicKeywords))}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                vs. {period2.metrics.organicKeywords.toLocaleString()} in {period2.label}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Average Position</Typography>
              <Box sx={{ display: 'flex', alignItems: 'baseline' }}>
                <Typography variant="h4">{period1.metrics.averagePosition.toFixed(1)}</Typography>
                <Box sx={{ ml: 2, display: 'flex', alignItems: 'center' }}>
                  {getTrendIcon(-calculateChange(period1.metrics.averagePosition, period2.metrics.averagePosition))}
                  <Typography
                    variant="body2"
                    color={calculateChange(period1.metrics.averagePosition, period2.metrics.averagePosition) < 0 ? 'success.main' : 'error.main'}
                    sx={{ ml: 0.5 }}
                  >
                    {formatChange(-calculateChange(period1.metrics.averagePosition, period2.metrics.averagePosition))}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                vs. {period2.metrics.averagePosition.toFixed(1)} in {period2.label}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Key Metrics Comparison Chart */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Key Metrics Comparison</Typography>
          <Box sx={{ height: 400 }}>
            <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
          </Box>
          <Typography variant="caption" color="text.secondary">
            Note: Some values are scaled for better visualization. Organic Traffic is shown in thousands, Backlinks in hundreds.
          </Typography>
        </CardContent>
      </Card>

      {/* Keyword Position Distribution Comparison */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>Keyword Distribution - {period1.label}</Typography>
              <Box sx={{ height: 300 }}>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>Keyword Distribution - {period2.label}</Typography>
              <Box sx={{ height: 300 }}>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Radar Chart Comparison */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>Performance Radar</Typography>
          <Box sx={{ height: 400 }}>
            <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default MetricsComparisonTab;
