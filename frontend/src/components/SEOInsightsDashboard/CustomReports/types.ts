import React from 'react';
import { SEOKeyword, SEOPageMetrics, CompetitorData, TechnicalSEOIssue } from '../types';

/**
 * Report Widget Type
 * Defines the different types of widgets that can be added to a custom report
 */
export enum ReportWidgetType {
  METRICS_SUMMARY = 'metrics_summary',
  TRAFFIC_TREND = 'traffic_trend',
  KEYWORD_POSITIONS = 'keyword_positions',
  TOP_KEYWORDS = 'top_keywords',
  TOP_PAGES = 'top_pages',
  TECHNICAL_ISSUES = 'technical_issues',
  COMPETITOR_COMPARISON = 'competitor_comparison',
  HISTORICAL_COMPARISON = 'historical_comparison',
  CONTENT_OPTIMIZATION = 'content_optimization',
  CUSTOM_CHART = 'custom_chart'
}

/**
 * Report Widget Size
 * Defines the different sizes a widget can have in the report grid
 */
export enum ReportWidgetSize {
  SMALL = 'small',     // 1x1 grid
  MEDIUM = 'medium',   // 1x2 grid
  LARGE = 'large',     // 2x2 grid
  FULL = 'full'        // Full width
}

/**
 * Report Widget Position
 * Defines the position of a widget in the report grid
 */
export interface ReportWidgetPosition {
  x: number;
  y: number;
  w: number;
  h: number;
}

/**
 * Report Widget Filter
 * Defines filters that can be applied to widget data
 */
export interface ReportWidgetFilter {
  field: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'between' | 'in';
  value: any;
}

/**
 * Report Widget Base
 * Base interface for all report widgets
 */
export interface ReportWidgetBase {
  id: string;
  type: ReportWidgetType;
  title: string;
  description?: string;
  size: ReportWidgetSize;
  position: ReportWidgetPosition;
  filters?: ReportWidgetFilter[];
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  comparisonDateRange?: {
    startDate: string;
    endDate: string;
  };
  showComparison?: boolean;
}

/**
 * Metrics Summary Widget
 * Shows key metrics in a summary view
 */
export interface MetricsSummaryWidget extends ReportWidgetBase {
  type: ReportWidgetType.METRICS_SUMMARY;
  metrics: Array<'domainAuthority' | 'organicTraffic' | 'organicKeywords' | 'backlinks' | 'averagePosition'>;
}

/**
 * Traffic Trend Widget
 * Shows traffic trends over time
 */
export interface TrafficTrendWidget extends ReportWidgetBase {
  type: ReportWidgetType.TRAFFIC_TREND;
  trafficTypes: Array<'organic' | 'direct' | 'referral' | 'social' | 'paid'>;
  interval: 'day' | 'week' | 'month';
}

/**
 * Keyword Positions Widget
 * Shows keyword position distribution
 */
export interface KeywordPositionsWidget extends ReportWidgetBase {
  type: ReportWidgetType.KEYWORD_POSITIONS;
  chartType: 'pie' | 'bar' | 'donut';
}

/**
 * Top Keywords Widget
 * Shows top keywords by various metrics
 */
export interface TopKeywordsWidget extends ReportWidgetBase {
  type: ReportWidgetType.TOP_KEYWORDS;
  sortBy: 'position' | 'traffic' | 'volume' | 'change';
  limit: number;
  keywords?: SEOKeyword[];
}

/**
 * Top Pages Widget
 * Shows top pages by various metrics
 */
export interface TopPagesWidget extends ReportWidgetBase {
  type: ReportWidgetType.TOP_PAGES;
  sortBy: 'traffic' | 'keywords' | 'conversions' | 'pageSpeed';
  limit: number;
  pages?: SEOPageMetrics[];
}

/**
 * Technical Issues Widget
 * Shows technical SEO issues
 */
export interface TechnicalIssuesWidget extends ReportWidgetBase {
  type: ReportWidgetType.TECHNICAL_ISSUES;
  issueTypes: Array<'error' | 'warning' | 'info'>;
  limit: number;
  issues?: TechnicalSEOIssue[];
}

/**
 * Competitor Comparison Widget
 * Shows comparison with competitors
 */
export interface CompetitorComparisonWidget extends ReportWidgetBase {
  type: ReportWidgetType.COMPETITOR_COMPARISON;
  competitors: string[];
  metrics: Array<'domainAuthority' | 'organicTraffic' | 'organicKeywords' | 'backlinks'>;
  chartType: 'bar' | 'radar';
  competitorData?: CompetitorData[];
}

/**
 * Historical Comparison Widget
 * Shows comparison between two time periods
 */
export interface HistoricalComparisonWidget extends ReportWidgetBase {
  type: ReportWidgetType.HISTORICAL_COMPARISON;
  metrics: Array<'domainAuthority' | 'organicTraffic' | 'organicKeywords' | 'backlinks' | 'averagePosition'>;
  period1: string;
  period2: string;
}

/**
 * Content Optimization Widget
 * Shows content optimization suggestions
 */
export interface ContentOptimizationWidget extends ReportWidgetBase {
  type: ReportWidgetType.CONTENT_OPTIMIZATION;
  urls: string[];
  limit: number;
}

/**
 * Custom Chart Widget
 * Allows for custom chart configuration
 */
export interface CustomChartWidget extends ReportWidgetBase {
  type: ReportWidgetType.CUSTOM_CHART;
  chartType: 'line' | 'bar' | 'pie' | 'radar' | 'scatter';
  dataSource: string;
  xAxis?: string;
  yAxis?: string;
  series: Array<{
    name: string;
    dataKey: string;
    color?: string;
  }>;
}

/**
 * Report Widget
 * Union type of all possible widget types
 */
export type ReportWidget = 
  | MetricsSummaryWidget
  | TrafficTrendWidget
  | KeywordPositionsWidget
  | TopKeywordsWidget
  | TopPagesWidget
  | TechnicalIssuesWidget
  | CompetitorComparisonWidget
  | HistoricalComparisonWidget
  | ContentOptimizationWidget
  | CustomChartWidget;

/**
 * Custom Report
 * Defines a custom report configuration
 */
export interface CustomReport {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  widgets: ReportWidget[];
  isPublic: boolean;
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    day?: number; // Day of week (0-6) or day of month (1-31)
    time: string; // HH:MM format
    recipients: string[]; // Email addresses
    format: 'pdf' | 'csv' | 'html';
  };
}

/**
 * Report Template
 * Predefined report templates
 */
export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  widgets: Omit<ReportWidget, 'id' | 'position'>[];
  thumbnail: string;
}
