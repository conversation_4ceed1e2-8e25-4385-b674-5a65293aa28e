import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';

import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Button
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  ArrowForward as ArrowForwardIcon,
  PriorityHigh as PriorityHighIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Pie,
  Cell
} from 'recharts';
// Recharts wrapper components - commented out due to TypeScript errors
// These components will be replaced with direct usage of Recharts components
/*
const BarWrapper = (props: any) => <Bar {...props} />;
const XAxisWrapper = (props: any) => <XAxis {...props} />;
const YAxisWrapper = (props: any) => <YAxis {...props} />;
const LineWrapper = (props: any) => <Line {...props} />;
const PieWrapper = (props: any) => <Pie {...props} />;
const TooltipWrapper = (props: any) => <Tooltip {...props} />;
const LegendWrapper = (props: any) => <Legend {...props} />;
*/

import { TabPanel, a11yProps } from '../../../components/common/TabsNavigation';
import { DataTabProps, SEOInsight } from '../types';

/**
 * Overview Tab component for SEO Dashboard
 */
const OverviewTab = ({ value, index, data }: DataTabProps) => {
  // Colors for pie chart
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  // Prepare data for keyword distribution pie chart
  const keywordDistributionData = [
    { name: 'Top 3', value: data.keywordPositions[0].count },
    { name: 'Positions 4-10', value: data.keywordPositions[1].count },
    { name: 'Positions 11-20', value: data.keywordPositions[2].count },
    { name: 'Positions 21+', value: data.keywordPositions[3].count + data.keywordPositions[4].count }
  ];

  // Get top insights
  const topInsights = data.insights
    .filter(insight => insight.impact === 'high')
    .slice(0, 3);

  return (
    <TabPanel value={value} index={index}>
      <Grid container spacing={3}>
        {/* Traffic Trends Chart */}
        <Grid item xs={12} md={8}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>Traffic Trends</Typography>
              <Box sx={{ height: 300 }}>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Keyword Distribution */}
        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>Keyword Distribution</Typography>
              <Box sx={{ height: 300, display: 'flex', justifyContent: 'center' }}>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Insights */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Top Insights</Typography>
              <List>
                {topInsights.map((insight, index) => (
                  <React.Fragment key={index}>
                    <ListItem alignItems="flex-start">
                      <ListItemIcon>
                        {insight.type === 'opportunity' ? (
                          <TrendingUpIcon color="primary" />
                        ) : insight.type === 'issue' ? (
                          <ErrorIcon color="error" />
                        ) : (
                          <CheckCircleIcon color="success" />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="subtitle1">{insight.title}</Typography>
                            <Chip
                              label={insight.impact}
                              size="small"
                              color={
                                insight.impact === 'high' ? 'error' :
                                insight.impact === 'medium' ? 'warning' : 'success'
                              }
                            />
                          </Box>
                        }
                        secondary={
                          <>
                            <Typography variant="body2" color="text.primary" sx={{ mt: 1 }}>
                              {insight.description}
                            </Typography>
                            {insight.recommendedAction && (
                              <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
                                <PriorityHighIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                                {insight.recommendedAction}
                              </Typography>
                            )}
                          </>
                        }
                      />
                    </ListItem>
                    {index < topInsights.length - 1 && <Divider variant="inset" component="li" />}
                  </React.Fragment>
                ))}
              </List>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button
                  endIcon={<ArrowForwardIcon />}
                  onClick={() => {/* Navigate to insights tab */}}
                >
                  View All Insights
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* SEO Health Breakdown */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>SEO Health Breakdown</Typography>
              <Box sx={{ height: 300 }}>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </TabPanel>
  );
};

export default OverviewTab;
