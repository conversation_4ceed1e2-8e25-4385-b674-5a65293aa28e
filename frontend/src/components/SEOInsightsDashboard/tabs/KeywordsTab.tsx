import {
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    Grid,
    IconButton,
    Paper,
    Tab,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Tabs,
    TextField,
    Tooltip,
    Typography
} from '@mui/material';
import React from 'react';
// InputAdornment import removed due to TypeScript errors
import FilterListIcon from '@mui/icons-material/FilterList';
import SearchIcon from '@mui/icons-material/Search';
import StarIcon from '@mui/icons-material/Star';
import StarBorderIcon from '@mui/icons-material/StarBorder';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import TrendingFlatIcon from '@mui/icons-material/TrendingFlat';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import { TabPanel } from '../../../components/common/TabsNavigation';
import { DataTabProps, SEOKeyword } from '../types';

/**
 * Keywords Tab component for SEO Dashboard
 */
const KeywordsTab = ({ value, index, data }: DataTabProps) => {
  const [keywordTabValue, setKeywordTabValue] = React.useState(0);
  const [searchTerm, setSearchTerm] = React.useState('');
  const [favoriteKeywords, setFavoriteKeywords] = React.useState<any[]>([]);

  // Handle keyword tab change
  const handleKeywordTabChange = (_event: any, newValue: number) => {
    setKeywordTabValue(newValue);
  };

  // Handle search term change
  const handleSearchChange = (event: any) => {
    setSearchTerm(event.target.value as any);
  };

  // Toggle favorite keyword
  const toggleFavorite = (keyword: string) => {
    if (favoriteKeywords.includes(keyword)) {
      setFavoriteKeywords(favoriteKeywords.filter(k => k !== keyword));
    } else {
      setFavoriteKeywords([...favoriteKeywords, keyword] as any);
    }
  };

  // Helper function to render trend icon
  const renderTrendIcon = (change: number) => {
    if (change > 0) {
      return <TrendingUpIcon fontSize="small" color="success" />;
    } else if (change < 0) {
      return <TrendingDownIcon fontSize="small" color="error" />;
    } else {
      return <TrendingFlatIcon fontSize="small" color="action" />;
    }
  };

  // Helper function to render intent chip
  const renderIntentChip = (intent?: string) => {
    if (!intent) return null;

    let color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' = 'default';

    switch (intent) {
      case 'informational':
        color = 'info';
        break;
      case 'navigational':
        color = 'secondary';
        break;
      case 'transactional':
        color = 'success';
        break;
      case 'commercial':
        color = 'primary';
        break;
    }

    return (
      <Chip
        label={intent}
        size="small"
        color={color}
        variant="outlined"
      />
    );
  };

  // Filter keywords based on search term and current tab
  const getFilteredKeywords = () => {
    let keywords: SEOKeyword[] = [];

    switch (keywordTabValue) {
      case 0: // All Keywords
        keywords = [...data.topKeywords];
        break;
      case 1: // Top Performers
        keywords = [...data.topKeywords].filter(k => k.position <= 10);
        break;
      case 2: // Opportunities
        keywords = [...data.keywordOpportunities];
        break;
      case 3: // Favorites
        keywords = [...data.topKeywords, ...data.keywordOpportunities].filter(
          k => favoriteKeywords.includes(k.keyword)
        );
        break;
    }

    if (searchTerm) {
      keywords = keywords.filter(k =>
        k.keyword.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return keywords;
  };

  const filteredKeywords = getFilteredKeywords();

  return (
    <TabPanel value={value} index={index}>
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Keyword Analysis</Typography>
            <TextField
              placeholder="Search keywords..."
              size="small"
              value={searchTerm}
              onChange={handleSearchChange}
              sx={{ width: 250 }}
              InputProps={{
                startAdornment: (
                  <Box component="span" sx={{ display: 'flex', alignItems: 'center', marginRight: 1 }}>
                    <SearchIcon fontSize="small" />
                  </Box>
                ),
                endAdornment: searchTerm && (
                  <Box component="span" sx={{ display: 'flex', alignItems: 'center', marginLeft: 1 }}>
                    <IconButton
                      size="small"
                      onClick={() => setSearchTerm('')}
                      edge="end"
                    >
                      <FilterListIcon fontSize="small" />
                    </IconButton>
                  </Box>
                )
              }}
            />
          </Box>

          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs
              value={keywordTabValue}
              onChange={handleKeywordTabChange}
              aria-label="keyword tabs"
            >
              <Tab label={`All Keywords (${data.topKeywords.length})`} />
              <Tab label={`Top Performers (${data.topKeywords.filter(k => k.position <= 10).length})`} />
              <Tab label={`Opportunities (${data.keywordOpportunities.length})`} />
              <Tab label={`Favorites (${favoriteKeywords.length})`} />
            </Tabs>
          </Box>

          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell width={30}></TableCell>
                  <TableCell>Keyword</TableCell>
                  <TableCell align="right">Volume</TableCell>
                  <TableCell align="right">Position</TableCell>
                  <TableCell align="right">Change</TableCell>
                  <TableCell align="right">Difficulty</TableCell>
                  <TableCell align="right">Traffic</TableCell>
                  <TableCell align="center">Intent</TableCell>
                  <TableCell align="right">CPC ($)</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredKeywords.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} align="center">
                      <Typography variant="body2" sx={{ py: 2 }}>
                        No keywords found matching your criteria.
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredKeywords.map((keyword) => (
                    <TableRow key={keyword.keyword} hover>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => toggleFavorite(keyword.keyword)}
                        >
                          {favoriteKeywords.includes(keyword.keyword) ? (
                            <StarIcon fontSize="small" color="warning" />
                          ) : (
                            <StarBorderIcon fontSize="small" />
                          )}
                        </IconButton>
                      </TableCell>
                      <TableCell component="th" scope="row">
                        <Tooltip title={`Click to see pages ranking for "${keyword.keyword}"`}>
                          <Typography
                            variant="body2"
                            sx={{
                              fontWeight: keyword.position <= 10 ? 'bold' : 'normal',
                              cursor: 'pointer',
                              '&:hover': { textDecoration: 'underline' }
                            }}
                          >
                            {keyword.keyword}
                          </Typography>
                        </Tooltip>
                      </TableCell>
                      <TableCell align="right">{keyword.volume.toLocaleString()}</TableCell>
                      <TableCell align="right">
                        <Chip
                          label={keyword.position}
                          size="small"
                          color={
                            keyword.position <= 3 ? 'success' :
                            keyword.position <= 10 ? 'primary' :
                            keyword.position <= 20 ? 'info' :
                            'default'
                          }
                          variant={keyword.position <= 10 ? 'filled' : 'outlined'}
                        />
                      </TableCell>
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                          {renderTrendIcon(keyword.change)}
                          <Typography variant="body2" sx={{ ml: 0.5 }}>
                            {Math.abs(keyword.change)}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="right">{keyword.difficulty}/100</TableCell>
                      <TableCell align="right">{keyword.traffic}</TableCell>
                      <TableCell align="center">{renderIntentChip(keyword.intent)}</TableCell>
                      <TableCell align="right">{keyword.cpc?.toFixed(2) || '-'}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {filteredKeywords.length > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button variant="outlined" size="small">
                Export Keywords
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Keyword Insights */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>Keyword Insights</Typography>
            <Box component="ul" sx={{ pl: 2 }}>
              <Box component="li" sx={{ mb: 1 }}>
                <Typography variant="body2">
                  <strong>{data.topKeywords.filter(k => k.position <= 3).length}</strong> keywords in top 3 positions
                </Typography>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Typography variant="body2">
                  <strong>{data.topKeywords.filter(k => k.position > 3 && k.position <= 10).length}</strong> keywords in positions 4-10
                </Typography>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Typography variant="body2">
                  <strong>{data.topKeywords.filter(k => k.change > 0).length}</strong> keywords improved in rankings
                </Typography>
              </Box>
              <Box component="li">
                <Typography variant="body2">
                  <strong>{data.topKeywords.filter(k => k.change < 0).length}</strong> keywords decreased in rankings
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>Recommendations</Typography>
            <Box component="ul" sx={{ pl: 2 }}>
              <Box component="li" sx={{ mb: 1 }}>
                <Typography variant="body2">
                  Focus on improving rankings for keywords in positions 11-20 to get to page 1
                </Typography>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Typography variant="body2">
                  Create more content around "industrial water treatment" to improve rankings
                </Typography>
              </Box>
              <Box component="li">
                <Typography variant="body2">
                  Target more commercial intent keywords to increase conversion potential
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </TabPanel>
  );
};

export default KeywordsTab;
