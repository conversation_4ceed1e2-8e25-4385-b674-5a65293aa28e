import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  TextField,
  InputAdornment,
  IconButton,
  Tabs,
  Tab,
  Button,
  Tooltip,
  LinearProgress,
  Grid,
  Paper,
  Link
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  OpenInNew as OpenInNewIcon,
  Speed as SpeedIcon,
  Article as ArticleIcon
} from '@mui/icons-material';
import { TabPanel, a11yProps } from '../../../components/common/TabsNavigation';
import { DataTabProps, SEOPageMetrics } from '../types';

/**
 * Pages Tab component for SEO Dashboard
 */
const PagesTab = ({ value, index, data }: DataTabProps) => {
  const [pageTabValue, setPageTabValue] = React.useState(0);
  const [searchTerm, setSearchTerm] = React.useState('');

  // Handle page tab change
  const handlePageTabChange = (_event: any, newValue: number) => {
    setPageTabValue(newValue);
  };

  // Handle search term change
  const handleSearchChange = (event: any) => {
    setSearchTerm(event.target.value);
  };

  // Helper function to render page speed indicator
  const renderPageSpeed = (speed?: { mobile: number, desktop: number }) => {
    if (!speed) return null;

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Tooltip title={`Mobile: ${speed.mobile}/100`}>
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', minWidth: 50 }}>
            <Typography variant="caption">Mobile</Typography>
            <LinearProgress
              variant="determinate"
              value={speed.mobile}
              sx={{
                width: '100%',
                height: 6,
                borderRadius: 3,
                bgcolor: 'rgba(0,0,0,0.1)',
                '& .MuiLinearProgress-bar': {
                  bgcolor:
                    speed.mobile >= 90 ? 'success.main' :
                    speed.mobile >= 70 ? 'warning.main' :
                    'error.main'
                }
              }}
            />
          </Box>
        </Tooltip>
        <Tooltip title={`Desktop: ${speed.desktop}/100`}>
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', minWidth: 50 }}>
            <Typography variant="caption">Desktop</Typography>
            <LinearProgress
              variant="determinate"
              value={speed.desktop}
              sx={{
                width: '100%',
                height: 6,
                borderRadius: 3,
                bgcolor: 'rgba(0,0,0,0.1)',
                '& .MuiLinearProgress-bar': {
                  bgcolor:
                    speed.desktop >= 90 ? 'success.main' :
                    speed.desktop >= 70 ? 'warning.main' :
                    'error.main'
                }
              }}
            />
          </Box>
        </Tooltip>
      </Box>
    );
  };

  // Filter pages based on search term and current tab
  const getFilteredPages = () => {
    let pages = [...data.topPages];

    switch (pageTabValue) {
      case 0: // All Pages
        break;
      case 1: // High Traffic
        pages = pages.filter(p => p.organicTraffic > 300);
        break;
      case 2: // High Bounce Rate
        pages = pages.filter(p => p.bounceRate > 35);
        break;
      case 3: // Optimization Needed
        pages = pages.filter(p =>
          (p.pageSpeed && (p.pageSpeed.mobile < 70 || p.pageSpeed.desktop < 80)) ||
          p.bounceRate > 40
        );
        break;
    }

    if (searchTerm) {
      pages = pages.filter(p =>
        p.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        p.url.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return pages;
  };

  const filteredPages = getFilteredPages();

  return (
    <TabPanel value={value} index={index}>
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Page Performance</Typography>
            <TextField
              placeholder="Search pages..."
              size="small"
              value={searchTerm}
              onChange={handleSearchChange}
              sx={{ width: 250 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                ),
                endAdornment: searchTerm && (
                  <InputAdornment position="end">
                    <IconButton
                      size="small"
                      onClick={() => setSearchTerm('')}
                      edge="end"
                    >
                      <FilterListIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
          </Box>

          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs
              value={pageTabValue}
              onChange={handlePageTabChange}
              aria-label="page tabs"
            >
              <Tab label="All Pages" />
              <Tab label="High Traffic" />
              <Tab label="High Bounce Rate" />
              <Tab label="Optimization Needed" />
            </Tabs>
          </Box>

          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Page</TableCell>
                  <TableCell align="right">Visits</TableCell>
                  <TableCell align="right">Bounce Rate</TableCell>
                  <TableCell align="right">Avg. Time</TableCell>
                  <TableCell align="right">Organic Traffic</TableCell>
                  <TableCell align="right">Keywords</TableCell>
                  <TableCell align="right">Conv. Rate</TableCell>
                  <TableCell align="center">Page Speed</TableCell>
                  <TableCell align="right">Word Count</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredPages.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} align="center">
                      <Typography variant="body2" sx={{ py: 2 }}>
                        No pages found matching your criteria.
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredPages.map((page) => (
                    <TableRow key={page.url} hover>
                      <TableCell component="th" scope="row">
                        <Box sx={{ maxWidth: 300 }}>
                          <Typography variant="body2" noWrap fontWeight="medium">
                            {page.title}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="caption" color="text.secondary" noWrap sx={{ maxWidth: 250 }}>
                              {page.url.replace('https://www.mexelenergysustain.com', '')}
                            </Typography>
                            <Tooltip title="Open page in new tab">
                              <IconButton
                                size="small"
                                href={page.url}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                <OpenInNewIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell align="right">{page.visits.toLocaleString()}</TableCell>
                      <TableCell align="right">
                        <Chip
                          label={`${page.bounceRate}%`}
                          size="small"
                          color={
                            page.bounceRate < 30 ? 'success' :
                            page.bounceRate < 50 ? 'primary' :
                            'error'
                          }
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell align="right">{page.avgTimeOnPage}s</TableCell>
                      <TableCell align="right">{page.organicTraffic.toLocaleString()}</TableCell>
                      <TableCell align="right">{page.keywordsRanked}</TableCell>
                      <TableCell align="right">{page.conversionRate ? `${page.conversionRate}%` : '-'}</TableCell>
                      <TableCell align="center">{renderPageSpeed(page.pageSpeed)}</TableCell>
                      <TableCell align="right">{page.wordCount?.toLocaleString() || '-'}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {filteredPages.length > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button variant="outlined" size="small">
                Export Page Data
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Page Insights */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <SpeedIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h6">Page Speed Insights</Typography>
            </Box>
            <Box component="ul" sx={{ pl: 2 }}>
              <Box component="li" sx={{ mb: 1 }}>
                <Typography variant="body2">
                  <strong>{data.topPages.filter(p => p.pageSpeed && p.pageSpeed.mobile < 70).length}</strong> pages have poor mobile speed scores (below 70)
                </Typography>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Typography variant="body2">
                  <strong>{data.topPages.filter(p => p.pageSpeed && p.pageSpeed.desktop < 80).length}</strong> pages have poor desktop speed scores (below 80)
                </Typography>
              </Box>
              <Box component="li">
                <Typography variant="body2">
                  Top performing page: <strong>{data.topPages.reduce((prev, current) => {
                    if (!prev.pageSpeed || !current.pageSpeed) return prev;
                    return (prev.pageSpeed.mobile + prev.pageSpeed.desktop) >
                           (current.pageSpeed.mobile + current.pageSpeed.desktop) ? prev : current;
                  }).title}</strong>
                </Typography>
              </Box>
            </Box>
            <Box sx={{ mt: 2 }}>
              <Link href="https://pagespeed.web.dev/" target="_blank" rel="noopener noreferrer">
                <Button size="small" startIcon={<SpeedIcon />}>
                  Run PageSpeed Test
                </Button>
              </Link>
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <ArticleIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h6">Content Insights</Typography>
            </Box>
            <Box component="ul" sx={{ pl: 2 }}>
              <Box component="li" sx={{ mb: 1 }}>
                <Typography variant="body2">
                  Average word count: <strong>{Math.round(data.topPages.reduce((sum, page) => sum + (page.wordCount || 0), 0) / data.topPages.length)}</strong> words per page
                </Typography>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Typography variant="body2">
                  <strong>{data.topPages.filter(p => p.wordCount && p.wordCount < 800).length}</strong> pages have less than 800 words (recommended minimum)
                </Typography>
              </Box>
              <Box component="li">
                <Typography variant="body2">
                  Pages with higher word counts (1500+) have <strong>35% lower</strong> bounce rates on average
                </Typography>
              </Box>
            </Box>
            <Box sx={{ mt: 2 }}>
              <Button size="small" startIcon={<ArticleIcon />}>
                View Content Recommendations
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </TabPanel>
  );
};

export default PagesTab;
