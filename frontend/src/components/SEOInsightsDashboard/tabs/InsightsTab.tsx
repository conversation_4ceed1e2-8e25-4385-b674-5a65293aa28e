import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import AssignmentIcon from '@mui/icons-material/Assignment';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import FilterListIcon from '@mui/icons-material/FilterList';
import SearchIcon from '@mui/icons-material/Search';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import {
    Box,
    Button,
    Card,
    CardContent,
    Checkbox,
    Chip,
    Divider,
    FormControl,
    FormControlLabel,
    Grid,
    IconButton,
    InputLabel,
    MenuItem,
    Select,
    SelectChangeEvent,
    TextField,
    Typography
} from '@mui/material';
import React from 'react';
import { TabPanel } from '../../../components/common/TabsNavigation';
import { DataTabProps } from '../types';

/**
 * Insights Tab component for SEO Dashboard
 */
const InsightsTab = ({ value, index, data }: DataTabProps) => {
  const [searchTerm, setSearchTerm] = React.useState('');
  const [typeFilter, setTypeFilter] = React.useState('all');
  const [categoryFilter, setCategoryFilter] = React.useState('all');
  const [impactFilter, setImpactFilter] = React.useState('all');
  const [actionableOnly, setActionableOnly] = React.useState(false);

  // Handle search term change
  const handleSearchChange = (event: any) => {
    setSearchTerm(event.target.value);
  };

  // Handle type filter change
  const handleTypeFilterChange = (event: SelectChangeEvent) => {
    setTypeFilter(event.target.value);
  };

  // Handle category filter change
  const handleCategoryFilterChange = (event: SelectChangeEvent) => {
    setCategoryFilter(event.target.value);
  };

  // Handle impact filter change
  const handleImpactFilterChange = (event: SelectChangeEvent) => {
    setImpactFilter(event.target.value);
  };

  // Handle actionable only toggle
  const handleActionableOnlyChange = (event: any) => {
    setActionableOnly(event.target.checked);
  };

  // Filter insights based on search term and filters
  const getFilteredInsights = () => {
    let insights = [...data.insights];

    if (searchTerm) {
      insights = insights.filter(insight =>
        insight.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        insight.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (typeFilter !== 'all') {
      insights = insights.filter(insight => insight.type === typeFilter);
    }

    if (categoryFilter !== 'all') {
      insights = insights.filter(insight => insight.category === categoryFilter);
    }

    if (impactFilter !== 'all') {
      insights = insights.filter(insight => insight.impact === impactFilter);
    }

    if (actionableOnly) {
      insights = insights.filter(insight => insight.actionable);
    }

    return insights;
  };

  const filteredInsights = getFilteredInsights();

  // Get icon for insight type
  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity':
        return <TrendingUpIcon color="primary" />;
      case 'issue':
        return <ErrorIcon color="error" />;
      case 'achievement':
        return <CheckCircleIcon color="success" />;
      default:
        return null;
    }
  };

  // Get color for insight type
  const getInsightColor = (type: string) => {
    switch (type) {
      case 'opportunity':
        return 'primary.main';
      case 'issue':
        return 'error.main';
      case 'achievement':
        return 'success.main';
      default:
        return 'grey.500';
    }
  };

  return (
    <TabPanel value={value} index={index}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>SEO Insights & Recommendations</Typography>
        <Typography variant="body2" color="text.secondary">
          Actionable insights to improve your website's search engine performance.
        </Typography>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                placeholder="Search insights..."
                size="small"
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <Box component="span" sx={{ display: 'flex', alignItems: 'center', marginRight: 1 }}>
                      <SearchIcon fontSize="small" />
                    </Box>
                  ),
                  endAdornment: searchTerm && (
                    <Box component="span" sx={{ display: 'flex', alignItems: 'center', marginLeft: 1 }}>
                      <IconButton
                        size="small"
                        onClick={() => setSearchTerm('')}
                        edge="end"
                      >
                        <FilterListIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  )
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel id="type-filter-label">Type</InputLabel>
                <Select
                  labelId="type-filter-label"
                  value={typeFilter}
                  label="Type"
                  onChange={handleTypeFilterChange}
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="opportunity">Opportunities</MenuItem>
                  <MenuItem value="issue">Issues</MenuItem>
                  <MenuItem value="achievement">Achievements</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel id="category-filter-label">Category</InputLabel>
                <Select
                  labelId="category-filter-label"
                  value={categoryFilter}
                  label="Category"
                  onChange={handleCategoryFilterChange}
                >
                  <MenuItem value="all">All Categories</MenuItem>
                  <MenuItem value="content">Content</MenuItem>
                  <MenuItem value="technical">Technical</MenuItem>
                  <MenuItem value="backlinks">Backlinks</MenuItem>
                  <MenuItem value="keywords">Keywords</MenuItem>
                  <MenuItem value="user-experience">User Experience</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel id="impact-filter-label">Impact</InputLabel>
                <Select
                  labelId="impact-filter-label"
                  value={impactFilter}
                  label="Impact"
                  onChange={handleImpactFilterChange}
                >
                  <MenuItem value="all">All Impact Levels</MenuItem>
                  <MenuItem value="high">High Impact</MenuItem>
                  <MenuItem value="medium">Medium Impact</MenuItem>
                  <MenuItem value="low">Low Impact</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={actionableOnly}
                    onChange={handleActionableOnlyChange}
                    color="primary"
                  />
                }
                label="Actionable items only"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Insights List */}
      {filteredInsights.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="body1">
            No insights found matching your criteria.
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {filteredInsights.map((insight, index) => (
            <Grid item xs={12} md={6} key={index}>
              <Card sx={{
                borderLeft: '4px solid',
                borderColor: getInsightColor(insight.type)
              }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                    <Box sx={{ mr: 1 }}>
                      {getInsightIcon(insight.type)}
                    </Box>
                    <Box sx={{ flexGrow: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                        <Typography variant="h6" gutterBottom>
                          {insight.title}
                        </Typography>
                        <Box>
                          <Chip
                            label={insight.impact}
                            size="small"
                            color={
                              insight.impact === 'high' ? 'error' :
                              insight.impact === 'medium' ? 'warning' : 'success'
                            }
                            sx={{ mr: 1 }}
                          />
                          {insight.category && (
                            <Chip
                              label={insight.category}
                              size="small"
                              variant="outlined"
                            />
                          )}
                        </Box>
                      </Box>
                      <Typography variant="body2" paragraph>
                        {insight.description}
                      </Typography>

                      {insight.recommendedAction && (
                        <>
                          <Divider sx={{ my: 1 }} />
                          <Box sx={{ display: 'flex', alignItems: 'flex-start', mt: 1 }}>
                            <AssignmentIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              <strong>Recommended Action:</strong> {insight.recommendedAction}
                            </Typography>
                          </Box>
                        </>
                      )}
                    </Box>
                  </Box>

                  {insight.actionable && (
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                      <Button
                        size="small"
                        variant="outlined"
                        endIcon={<ArrowForwardIcon />}
                      >
                        Take Action
                      </Button>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Export Button */}
      {filteredInsights.length > 0 && (
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
          <Button variant="contained">
            Export Insights Report
          </Button>
        </Box>
      )}
    </TabPanel>
  );
};

export default InsightsTab;
