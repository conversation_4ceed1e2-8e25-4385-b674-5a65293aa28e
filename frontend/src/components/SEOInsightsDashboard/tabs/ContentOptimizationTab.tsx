import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Button,
  LinearProgress,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  TextField,
  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Link
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Edit as EditIcon,
  ContentPaste as ContentPasteIcon,
  Title as TitleIcon,
  Description as DescriptionIcon,
  FormatSize as FormatSizeIcon,
  Search as SearchIcon,
  OpenInNew as OpenInNewIcon,
  CheckCircle as CheckCircleIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { TabPanel, a11yProps } from '../../../components/common/TabsNavigation';
import { DataTabProps, ContentSuggestion } from '../types';

/**
 * Content Optimization Tab component for SEO Dashboard
 */
const ContentOptimizationTab = ({ value, index, data }: DataTabProps) => {
  const [expanded, setExpanded] = React.useState(false);
  const [selectedPage, setSelectedPage] = React.useState(
    data.contentSuggestions.length > 0 ? data.contentSuggestions[0] : null
  );

  // Handle accordion change
  const handleAccordionChange = (panel: string) => (
    _event: any, 
    isExpanded: boolean
  ) => {
    setExpanded(isExpanded ? panel : false);
  };

  // Handle page selection
  const handlePageSelect = (page: ContentSuggestion) => {
    setSelectedPage(page);
  };

  return (
    <TabPanel value={value} index={index}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>Content Optimization</Typography>
        <Typography variant="body2" color="text.secondary">
          Improve your content to rank higher in search results and engage your audience better.
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Content Optimization Suggestions */}
        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>Pages to Optimize</Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Select a page to see detailed optimization suggestions.
              </Typography>

              <List sx={{ bgcolor: 'background.paper' }}>
                {data.contentSuggestions.map((suggestion, i) => (
                  <ListItem
                    key={i}
                    button
                    selected={selectedPage?.url === suggestion.url}
                    onClick={() => handlePageSelect(suggestion)}
                    sx={{
                      borderLeft: '3px solid',
                      borderColor: selectedPage?.url === suggestion.url ? 'primary.main' : 'transparent',
                      pl: 2
                    }}
                  >
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Typography
                            variant="body2"
                            noWrap
                            sx={{
                              maxWidth: 200,
                              fontWeight: selectedPage?.url === suggestion.url ? 'bold' : 'normal'
                            }}
                          >
                            {suggestion.title}
                          </Typography>
                          <Chip
                            label={`${suggestion.currentScore}/100`}
                            size="small"
                            color={
                              suggestion.currentScore >= 80 ? 'success' :
                              suggestion.currentScore >= 60 ? 'warning' :
                              'error'
                            }
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                          <Typography variant="caption" color="text.secondary" noWrap sx={{ maxWidth: 200 }}>
                            {suggestion.url.replace('https://www.mexelenergysustain.com', '')}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>

              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  size="small"
                >
                  Analyze Another Page
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Selected Page Optimization */}
        <Grid item xs={12} md={8}>
          {selectedPage ? (
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Box>
                    <Typography variant="h6">{selectedPage.title}</Typography>
                    <Link
                      href={selectedPage.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{ display: 'flex', alignItems: 'center', color: 'text.secondary' }}
                    >
                      <Typography variant="body2" color="inherit">
                        {selectedPage.url.replace('https://www.mexelenergysustain.com', '')}
                      </Typography>
                      <OpenInNewIcon fontSize="small" sx={{ ml: 0.5 }} />
                    </Link>
                  </Box>
                  <Button
                    variant="contained"
                    startIcon={<EditIcon />}
                  >
                    Edit Page
                  </Button>
                </Box>

                {/* Content Score */}
                <Paper sx={{ p: 2, bgcolor: 'background.default', mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>Content Score</Typography>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Typography variant="body2" color="text.secondary">Current Score</Typography>
                        <Box sx={{ display: 'flex', alignItems: 'baseline' }}>
                          <Typography variant="h4" color={
                            selectedPage.currentScore >= 80 ? 'success.main' :
                            selectedPage.currentScore >= 60 ? 'warning.main' :
                            'error.main'
                          }>
                            {selectedPage.currentScore}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>/100</Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={selectedPage.currentScore}
                          sx={{
                            mt: 1,
                            height: 8,
                            borderRadius: 4,
                            bgcolor: 'rgba(0, 0, 0, 0.1)',
                            '& .MuiLinearProgress-bar': {
                              borderRadius: 4,
                              bgcolor:
                                selectedPage.currentScore >= 80 ? 'success.main' :
                                selectedPage.currentScore >= 60 ? 'warning.main' :
                                'error.main'
                            }
                          }}
                        />
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Typography variant="body2" color="text.secondary">Potential Score</Typography>
                        <Box sx={{ display: 'flex', alignItems: 'baseline' }}>
                          <Typography variant="h4" color="primary.main">
                            {selectedPage.potentialScore}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>/100</Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={selectedPage.potentialScore}
                          sx={{
                            mt: 1,
                            height: 8,
                            borderRadius: 4,
                            bgcolor: 'rgba(0, 0, 0, 0.1)',
                            '& .MuiLinearProgress-bar': {
                              borderRadius: 4,
                              bgcolor: 'primary.main'
                            }
                          }}
                        />
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>

                {/* Title Optimization */}
                <Accordion
                  expanded={expanded === 'panel1'}
                  onChange={handleAccordionChange('panel1')}
                  sx={{ mb: 2 }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1-content"
                    id="panel1-header"
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <TitleIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1">Title Optimization</Typography>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" paragraph>
                      Your current title is <strong>{selectedPage.title.length}</strong> characters.
                      Ideal title length is between 50-60 characters.
                    </Typography>

                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>Current Title:</Typography>
                      <TextField
                        fullWidth
                        variant="outlined"
                        size="small"
                        value={selectedPage.title}
                        InputProps={{
                          readOnly: true,
                          endAdornment: (
                            <Tooltip title="Copy">
                              <IconButton size="small" edge="end">
                                <ContentPasteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )
                        }}
                      />
                    </Box>

                    {selectedPage.titleSuggestion && (
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>Suggested Title:</Typography>
                        <TextField
                          fullWidth
                          variant="outlined"
                          size="small"
                          value={selectedPage.titleSuggestion}
                          InputProps={{
                            readOnly: true,
                            endAdornment: (
                              <Tooltip title="Copy">
                                <IconButton size="small" edge="end">
                                  <ContentPasteIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )
                          }}
                        />
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                          {selectedPage.titleSuggestion.length} characters
                        </Typography>
                      </Box>
                    )}
                  </AccordionDetails>
                </Accordion>

                {/* Meta Description */}
                <Accordion
                  expanded={expanded === 'panel2'}
                  onChange={handleAccordionChange('panel2')}
                  sx={{ mb: 2 }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel2-content"
                    id="panel2-header"
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <DescriptionIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1">Meta Description</Typography>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    {selectedPage.metaDescriptionSuggestion && (
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>Suggested Meta Description:</Typography>
                        <TextField
                          fullWidth
                          variant="outlined"
                          size="small"
                          multiline
                          rows={2}
                          value={selectedPage.metaDescriptionSuggestion}
                          InputProps={{
                            readOnly: true,
                            endAdornment: (
                              <Tooltip title="Copy">
                                <IconButton size="small" edge="end">
                                  <ContentPasteIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )
                          }}
                        />
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                          {selectedPage.metaDescriptionSuggestion.length} characters (Ideal: 120-158)
                        </Typography>
                      </Box>
                    )}
                  </AccordionDetails>
                </Accordion>

                {/* Content Length */}
                <Accordion
                  expanded={expanded === 'panel3'}
                  onChange={handleAccordionChange('panel3')}
                  sx={{ mb: 2 }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel3-content"
                    id="panel3-header"
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <FormatSizeIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1">Content Length</Typography>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" paragraph>
                      Your current content is <strong>{selectedPage.wordCountSuggestion.current.toLocaleString()}</strong> words.
                      Recommended length for this type of page is <strong>{selectedPage.wordCountSuggestion.recommended.toLocaleString()}</strong> words.
                    </Typography>

                    <LinearProgress
                      variant="determinate"
                      value={(selectedPage.wordCountSuggestion.current / selectedPage.wordCountSuggestion.recommended) * 100}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        bgcolor: 'rgba(0, 0, 0, 0.1)',
                        mb: 2,
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 4,
                          bgcolor:
                            selectedPage.wordCountSuggestion.current >= selectedPage.wordCountSuggestion.recommended ? 'success.main' :
                            selectedPage.wordCountSuggestion.current >= selectedPage.wordCountSuggestion.recommended * 0.7 ? 'warning.main' :
                            'error.main'
                        }
                      }}
                    />

                    <Typography variant="body2">
                      Adding <strong>{(selectedPage.wordCountSuggestion.recommended - selectedPage.wordCountSuggestion.current).toLocaleString()}</strong> more words could improve your rankings.
                    </Typography>
                  </AccordionDetails>
                </Accordion>

                {/* Keyword Optimization */}
                <Accordion
                  expanded={expanded === 'panel4'}
                  onChange={handleAccordionChange('panel4')}
                  sx={{ mb: 2 }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel4-content"
                    id="panel4-header"
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <SearchIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1">Keyword Optimization</Typography>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" paragraph>
                      Consider adding these keywords to your content to improve relevance and rankings:
                    </Typography>

                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                      {selectedPage.missingKeywords.map((keyword, i) => (
                        <Chip
                          key={i}
                          label={keyword}
                          color="primary"
                          variant="outlined"
                        />
                      ))}
                    </Box>

                    <Typography variant="body2">
                      Include these keywords naturally in your headings, paragraphs, and image alt text.
                    </Typography>
                  </AccordionDetails>
                </Accordion>

                {/* Readability */}
                <Accordion
                  expanded={expanded === 'panel5'}
                  onChange={handleAccordionChange('panel5')}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel5-content"
                    id="panel5-header"
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CheckCircleIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1">Readability</Typography>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" paragraph>
                      Your content has a readability score of <strong>{selectedPage.readabilityScore}/100</strong>.
                    </Typography>

                    <LinearProgress
                      variant="determinate"
                      value={selectedPage.readabilityScore}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        bgcolor: 'rgba(0, 0, 0, 0.1)',
                        mb: 2,
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 4,
                          bgcolor:
                            selectedPage.readabilityScore >= 80 ? 'success.main' :
                            selectedPage.readabilityScore >= 60 ? 'warning.main' :
                            'error.main'
                        }
                      }}
                    />

                    <Typography variant="subtitle2" gutterBottom>Readability Tips:</Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 30 }}>
                          <CheckCircleIcon fontSize="small" color="success" />
                        </ListItemIcon>
                        <ListItemText primary="Use shorter sentences (15-20 words)" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 30 }}>
                          <CheckCircleIcon fontSize="small" color="success" />
                        </ListItemIcon>
                        <ListItemText primary="Break up content with subheadings (H2, H3)" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 30 }}>
                          <CheckCircleIcon fontSize="small" color="success" />
                        </ListItemIcon>
                        <ListItemText primary="Use bullet points for lists" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 30 }}>
                          <CheckCircleIcon fontSize="small" color="success" />
                        </ListItemIcon>
                        <ListItemText primary="Avoid jargon and complex terminology" />
                      </ListItem>
                    </List>
                  </AccordionDetails>
                </Accordion>
              </CardContent>
            </Card>
          ) : (
            <Card sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p: 4 }}>
              <Typography variant="body1" color="text.secondary">
                Select a page to see optimization suggestions
              </Typography>
            </Card>
          )}
        </Grid>
      </Grid>
    </TabPanel>
  );
};

export default ContentOptimizationTab;
