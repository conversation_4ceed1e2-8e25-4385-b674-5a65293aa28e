import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Button,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper,
  Link,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Speed as SpeedIcon,
  Security as SecurityIcon,
  Devices as DevicesIcon,
  Link as LinkIcon,
  Code as CodeIcon,
  OpenInNew as OpenInNewIcon
} from '@mui/icons-material';
import { TabPanel, a11yProps } from '../../../components/common/TabsNavigation';
import { DataTabProps, TechnicalSEOIssue } from '../types';

/**
 * Technical SEO Tab component for SEO Dashboard
 */
const TechnicalSEOTab = ({ value, index, data }: DataTabProps) => {
  const [expanded, setExpanded] = React.useState(false);

  // Handle accordion change
  const handleAccordionChange = (panel: string) => (
    _event: React.SyntheticEvent, 
    isExpanded: boolean
  ) => {
    setExpanded(isExpanded ? panel : false);
  };

  // Get icon for issue type
  const getIssueIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <ErrorIcon color="error" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'info':
        return <InfoIcon color="info" />;
      default:
        return null;
    }
  };

  // Get color for issue impact
  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'default';
    }
  };

  // Count issues by type
  const errorCount = data.technicalIssues.filter(issue => issue.type === 'error').length;
  const warningCount = data.technicalIssues.filter(issue => issue.type === 'warning').length;
  const infoCount = data.technicalIssues.filter(issue => issue.type === 'info').length;
  const totalIssues = data.technicalIssues.length;

  // Calculate health score
  const healthScore = data.seoHealth.technical;

  return (
    <TabPanel value={value} index={index}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>Technical SEO Health</Typography>
        <Typography variant="body2" color="text.secondary">
          Identify and fix technical issues that may be affecting your website's search engine performance.
        </Typography>
      </Box>

      {/* Technical SEO Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Technical Health Score</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Typography variant="h3" sx={{ mr: 2 }}>{healthScore}</Typography>
                <Box sx={{ flexGrow: 1 }}>
                  <LinearProgress
                    variant="determinate"
                    value={healthScore}
                    sx={{
                      height: 10,
                      borderRadius: 5,
                      bgcolor: 'rgba(0,0,0,0.1)',
                      '& .MuiLinearProgress-bar': {
                        bgcolor:
                          healthScore >= 80 ? 'success.main' :
                          healthScore >= 60 ? 'warning.main' :
                          'error.main'
                      }
                    }}
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                    {healthScore >= 80 ? 'Good' : healthScore >= 60 ? 'Needs Improvement' : 'Poor'}
                  </Typography>
                </Box>
              </Box>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" gutterBottom>Issues Summary</Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <ErrorIcon color="error" />
                  </ListItemIcon>
                  <ListItemText
                    primary={`${errorCount} Critical Issues`}
                    secondary="High-priority issues that need immediate attention"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <WarningIcon color="warning" />
                  </ListItemIcon>
                  <ListItemText
                    primary={`${warningCount} Warnings`}
                    secondary="Issues that should be addressed soon"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon color="info" />
                  </ListItemIcon>
                  <ListItemText
                    primary={`${infoCount} Notices`}
                    secondary="Suggestions for improvement"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Technical SEO Categories</Typography>
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={6}>
                  <Paper sx={{ p: 1.5, display: 'flex', alignItems: 'center', bgcolor: 'background.default' }}>
                    <SpeedIcon color="primary" sx={{ mr: 1 }} />
                    <Box>
                      <Typography variant="subtitle2">Page Speed</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {data.seoHealth.technical >= 70 ? 'Good' : 'Needs Improvement'}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 1.5, display: 'flex', alignItems: 'center', bgcolor: 'background.default' }}>
                    <DevicesIcon color="primary" sx={{ mr: 1 }} />
                    <Box>
                      <Typography variant="subtitle2">Mobile Usability</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {data.seoHealth.technical >= 75 ? 'Good' : 'Needs Improvement'}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 1.5, display: 'flex', alignItems: 'center', bgcolor: 'background.default' }}>
                    <LinkIcon color="primary" sx={{ mr: 1 }} />
                    <Box>
                      <Typography variant="subtitle2">Links</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {errorCount > 0 ? 'Issues Found' : 'No Issues'}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 1.5, display: 'flex', alignItems: 'center', bgcolor: 'background.default' }}>
                    <SecurityIcon color="primary" sx={{ mr: 1 }} />
                    <Box>
                      <Typography variant="subtitle2">Security</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {data.seoHealth.technical >= 80 ? 'Good' : 'Needs Improvement'}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
              </Grid>
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<CodeIcon />}
                >
                  Run Full Technical Audit
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Technical Issues List */}
      <Typography variant="h6" gutterBottom>Technical Issues ({totalIssues})</Typography>
      {data.technicalIssues.map((issue, index) => (
        <Accordion
          key={index}
          expanded={expanded === `panel${index}`}
          onChange={handleAccordionChange(`panel${index}`)}
          sx={{
            mb: 1,
            borderLeft: '4px solid',
            borderColor:
              issue.type === 'error' ? 'error.main' :
              issue.type === 'warning' ? 'warning.main' :
              'info.main'
          }}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            aria-controls={`panel${index}-content`}
            id={`panel${index}-header`}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
              <Box sx={{ mr: 2 }}>
                {getIssueIcon(issue.type)}
              </Box>
              <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
                {issue.title}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Chip
                  label={`${issue.affectedPages} pages`}
                  size="small"
                  sx={{ mr: 1 }}
                />
                <Chip
                  label={issue.impact}
                  size="small"
                  color={getImpactColor(issue.impact)}
                />
              </Box>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" paragraph>
              {issue.description}
            </Typography>

            <Typography variant="subtitle2" gutterBottom>Affected Pages:</Typography>
            <Box sx={{ mb: 2 }}>
              {issue.examples.map((example, i) => (
                <Box key={i} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                  <Typography variant="body2" noWrap sx={{ maxWidth: 500 }}>
                    {example}
                  </Typography>
                  <Tooltip title="Open in new tab">
                    <IconButton
                      size="small"
                      href={example}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <OpenInNewIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              ))}
            </Box>

            <Typography variant="subtitle2" gutterBottom>How to Fix:</Typography>
            <Typography variant="body2" paragraph>
              {issue.howToFix}
            </Typography>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                size="small"
                color={
                  issue.type === 'error' ? 'error' :
                  issue.type === 'warning' ? 'warning' :
                  'info'
                }
              >
                Fix Issue
              </Button>
            </Box>
          </AccordionDetails>
        </Accordion>
      ))}

      {/* Resources */}
      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>Technical SEO Resources</Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>Page Speed Optimization</Typography>
              <Typography variant="body2" paragraph>
                Tools and guides to improve your website's loading speed.
              </Typography>
              <Link href="https://pagespeed.web.dev/" target="_blank" rel="noopener noreferrer">
                <Button size="small" endIcon={<OpenInNewIcon />}>
                  PageSpeed Insights
                </Button>
              </Link>
            </Paper>
          </Grid>
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>Mobile Optimization</Typography>
              <Typography variant="body2" paragraph>
                Ensure your site works well on mobile devices.
              </Typography>
              <Link href="https://search.google.com/test/mobile-friendly" target="_blank" rel="noopener noreferrer">
                <Button size="small" endIcon={<OpenInNewIcon />}>
                  Mobile-Friendly Test
                </Button>
              </Link>
            </Paper>
          </Grid>
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>Structured Data</Typography>
              <Typography variant="body2" paragraph>
                Implement schema markup to enhance search results.
              </Typography>
              <Link href="https://search.google.com/test/rich-results" target="_blank" rel="noopener noreferrer">
                <Button size="small" endIcon={<OpenInNewIcon />}>
                  Rich Results Test
                </Button>
              </Link>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </TabPanel>
  );
};

export default TechnicalSEOTab;
