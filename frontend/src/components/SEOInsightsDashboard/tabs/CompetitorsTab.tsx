import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import {
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    Divider,
    Grid,
    Paper,
    Tab,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Tabs,
    Typography
} from '@mui/material';
import React from 'react';

import { TabPanel } from '../../../components/common/TabsNavigation';
import { CompetitorData, DataTabProps } from '../types';

/**
 * Competitors Tab component for SEO Dashboard
 */
const CompetitorsTab = ({ value, index, data }: DataTabProps) => {
  const [competitorTabValue, setCompetitorTabValue] = React.useState(0);
  const [selectedCompetitor, setSelectedCompetitor] = React.useState(data.competitors[0]);

  // Handle competitor tab change
  const handleCompetitorTabChange = (_event: any, newValue: number) => {
    setCompetitorTabValue(newValue);
  };

  // Handle competitor selection
  const handleCompetitorSelect = (competitor: CompetitorData) => {
    setSelectedCompetitor(competitor);
  };

  // Prepare data for competitor comparison chart
  const prepareComparisonData = () => {
    return [
      {
        metric: 'Domain Authority',
        your: data.domainAuthority,
        competitor: selectedCompetitor.domainAuthority,
      },
      {
        metric: 'Organic Traffic',
        your: data.organicTraffic / 1000, // Scale down for better visualization
        competitor: selectedCompetitor.organicTraffic / 1000,
      },
      {
        metric: 'Keywords',
        your: data.organicKeywords,
        competitor: selectedCompetitor.organicKeywords,
      },
      {
        metric: 'Backlinks',
        your: data.backlinks / 100, // Scale down for better visualization
        competitor: selectedCompetitor.backlinks / 100,
      }
    ];
  };

  // Prepare data for radar chart
  const prepareRadarData = () => {
    return [
      {
        subject: 'Domain Authority',
        your: data.domainAuthority,
        competitor: selectedCompetitor.domainAuthority,
        fullMark: 100
      },
      {
        subject: 'Traffic',
        your: (data.organicTraffic / selectedCompetitor.organicTraffic) * 50,
        competitor: 50,
        fullMark: 100
      },
      {
        subject: 'Keywords',
        your: (data.organicKeywords / selectedCompetitor.organicKeywords) * 50,
        competitor: 50,
        fullMark: 100
      },
      {
        subject: 'Backlinks',
        your: (data.backlinks / selectedCompetitor.backlinks) * 50,
        competitor: 50,
        fullMark: 100
      },
      {
        subject: 'Top 10 Keywords',
        your: (data.keywordPositions[0].count + data.keywordPositions[1].count) /
              ((data.keywordPositions[0].count + data.keywordPositions[1].count) + 10) * 100,
        competitor: 10 / ((data.keywordPositions[0].count + data.keywordPositions[1].count) + 10) * 100,
        fullMark: 100
      }
    ];
  };

  // Prepare traffic trend data
  const prepareTrafficTrendData = () => {
    return data.trafficTrend.map((item, index) => ({
      date: item.date,
      your: item.organicTraffic,
      competitor: selectedCompetitor.trafficTrend[index]?.traffic || 0
    }));
  };

  const comparisonData = prepareComparisonData();
  const radarData = prepareRadarData();
  const trafficTrendData = prepareTrafficTrendData();

  return (
    <TabPanel value={value} index={index}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>Competitor Analysis</Typography>
        <Typography variant="body2" color="text.secondary">
          Compare your website's performance against key competitors in your industry.
        </Typography>
      </Box>

      {/* Competitor Selection */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="subtitle1" gutterBottom>Select Competitor to Compare</Typography>
          <Grid container spacing={2}>
            {data.competitors.map((competitor) => (
              <Grid item key={competitor.domain}>
                <Chip
                  label={competitor.domain}
                  onClick={() => handleCompetitorSelect(competitor)}
                  color={selectedCompetitor.domain === competitor.domain ? 'primary' : 'default'}
                  variant={selectedCompetitor.domain === competitor.domain ? 'filled' : 'outlined'}
                  clickable
                />
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Competitor Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  {selectedCompetitor.domain}
                </Typography>
                <a href={`https://${selectedCompetitor.domain}`} target="_blank" rel="noopener noreferrer" style={{ textDecoration: 'none' }}>
                  <Button
                    size="small"
                    endIcon={<OpenInNewIcon />}
                    variant="outlined"
                  >
                    Visit Site
                  </Button>
                </a>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">Domain Authority</Typography>
                  <Typography variant="h5">{selectedCompetitor.domainAuthority}</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    {selectedCompetitor.domainAuthority > data.domainAuthority ? (
                      <>
                        <TrendingUpIcon fontSize="small" color="error" />
                        <Typography variant="caption" color="error.main">
                          {selectedCompetitor.domainAuthority - data.domainAuthority} higher than you
                        </Typography>
                      </>
                    ) : (
                      <>
                        <TrendingDownIcon fontSize="small" color="success" />
                        <Typography variant="caption" color="success.main">
                          {data.domainAuthority - selectedCompetitor.domainAuthority} lower than you
                        </Typography>
                      </>
                    )}
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">Organic Traffic</Typography>
                  <Typography variant="h5">{selectedCompetitor.organicTraffic.toLocaleString()}</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    {selectedCompetitor.organicTraffic > data.organicTraffic ? (
                      <>
                        <TrendingUpIcon fontSize="small" color="error" />
                        <Typography variant="caption" color="error.main">
                          {Math.round((selectedCompetitor.organicTraffic / data.organicTraffic - 1) * 100)}% higher than you
                        </Typography>
                      </>
                    ) : (
                      <>
                        <TrendingDownIcon fontSize="small" color="success" />
                        <Typography variant="caption" color="success.main">
                          {Math.round((data.organicTraffic / selectedCompetitor.organicTraffic - 1) * 100)}% lower than you
                        </Typography>
                      </>
                    )}
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">Ranking Keywords</Typography>
                  <Typography variant="h5">{selectedCompetitor.organicKeywords.toLocaleString()}</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    {selectedCompetitor.organicKeywords > data.organicKeywords ? (
                      <>
                        <TrendingUpIcon fontSize="small" color="error" />
                        <Typography variant="caption" color="error.main">
                          {selectedCompetitor.organicKeywords - data.organicKeywords} more than you
                        </Typography>
                      </>
                    ) : (
                      <>
                        <TrendingDownIcon fontSize="small" color="success" />
                        <Typography variant="caption" color="success.main">
                          {data.organicKeywords - selectedCompetitor.organicKeywords} fewer than you
                        </Typography>
                      </>
                    )}
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">Backlinks</Typography>
                  <Typography variant="h5">{selectedCompetitor.backlinks.toLocaleString()}</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    {selectedCompetitor.backlinks > data.backlinks ? (
                      <>
                        <TrendingUpIcon fontSize="small" color="error" />
                        <Typography variant="caption" color="error.main">
                          {Math.round((selectedCompetitor.backlinks / data.backlinks - 1) * 100)}% more than you
                        </Typography>
                      </>
                    ) : (
                      <>
                        <TrendingDownIcon fontSize="small" color="success" />
                        <Typography variant="caption" color="success.main">
                          {Math.round((data.backlinks / selectedCompetitor.backlinks - 1) * 100)}% fewer than you
                        </Typography>
                      </>
                    )}
                  </Box>
                </Grid>
              </Grid>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" gutterBottom>Shared Keywords: {selectedCompetitor.sharedKeywords}</Typography>
              <Typography variant="body2" color="text.secondary">
                You share {selectedCompetitor.sharedKeywords} keywords with this competitor, which is {Math.round((selectedCompetitor.sharedKeywords / data.organicKeywords) * 100)}% of your total keywords.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ height: '100%' }}>
              <Typography variant="h6" gutterBottom>Competitive Analysis</Typography>
              <Box sx={{ height: 300 }}>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs for different competitor analyses */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs
          value={competitorTabValue}
          onChange={handleCompetitorTabChange}
          aria-label="competitor analysis tabs"
        >
          <Tab label="Traffic Comparison" />
          <Tab label="Top Keywords" />
          <Tab label="Keyword Gap" />
        </Tabs>
      </Box>

      {/* Traffic Comparison Tab */}
      <Box role="tabpanel" hidden={competitorTabValue !== 0}>
        {competitorTabValue === 0 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Traffic Trend Comparison</Typography>
              <Box sx={{ height: 300 }}>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
              </Box>
            </CardContent>
          </Card>
        )}
      </Box>

      {/* Top Keywords Tab */}
      <Box role="tabpanel" hidden={competitorTabValue !== 1}>
        {competitorTabValue === 1 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Competitor's Top Keywords</Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Keyword</TableCell>
                      <TableCell align="right">Volume</TableCell>
                      <TableCell align="right">Position</TableCell>
                      <TableCell align="right">Change</TableCell>
                      <TableCell align="right">Traffic</TableCell>
                      <TableCell align="right">Your Position</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {selectedCompetitor.topKeywords.map((keyword) => {
                      // Find if you rank for this keyword
                      const yourKeyword = data.topKeywords.find(k => k.keyword === keyword.keyword);

                      return (
                        <TableRow key={keyword.keyword} hover>
                          <TableCell component="th" scope="row">
                            {keyword.keyword}
                          </TableCell>
                          <TableCell align="right">{keyword.volume}</TableCell>
                          <TableCell align="right">
                            <Chip
                              label={keyword.position}
                              size="small"
                              color={
                                keyword.position <= 3 ? 'success' :
                                keyword.position <= 10 ? 'primary' :
                                'default'
                              }
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                              {keyword.change > 0 ? (
                                <TrendingUpIcon fontSize="small" color="success" />
                              ) : keyword.change < 0 ? (
                                <TrendingDownIcon fontSize="small" color="error" />
                              ) : null}
                              <Typography variant="body2" sx={{ ml: 0.5 }}>
                                {Math.abs(keyword.change)}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell align="right">{keyword.traffic}</TableCell>
                          <TableCell align="right">
                            {yourKeyword ? (
                              <Chip
                                label={yourKeyword.position}
                                size="small"
                                color={
                                  yourKeyword.position <= 3 ? 'success' :
                                  yourKeyword.position <= 10 ? 'primary' :
                                  'default'
                                }
                                variant="outlined"
                              />
                            ) : (
                              <Typography variant="body2" color="text.secondary">
                                Not ranking
                              </Typography>
                            )}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        )}
      </Box>

      {/* Keyword Gap Tab */}
      <Box role="tabpanel" hidden={competitorTabValue !== 2}>
        {competitorTabValue === 2 && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Keyword Gap Analysis</Typography>
              <Typography variant="body2" paragraph>
                These are keywords that {selectedCompetitor.domain} ranks for but your website doesn't.
                Consider creating content to target these keywords.
              </Typography>

              <Paper sx={{ p: 2, bgcolor: 'background.default', mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Keyword Gap Summary
                </Typography>
                <Box component="ul" sx={{ pl: 2, mb: 0 }}>
                  <Box component="li">
                    <Typography variant="body2">
                      {selectedCompetitor.domain} ranks for {selectedCompetitor.organicKeywords} keywords total
                    </Typography>
                  </Box>
                  <Box component="li">
                    <Typography variant="body2">
                      You share {selectedCompetitor.sharedKeywords} keywords with this competitor
                    </Typography>
                  </Box>
                  <Box component="li">
                    <Typography variant="body2">
                      There are {selectedCompetitor.organicKeywords - selectedCompetitor.sharedKeywords} keywords you could potentially target
                    </Typography>
                  </Box>
                </Box>
              </Paper>

              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<CompareArrowsIcon />}
                >
                  Generate Full Keyword Gap Report
                </Button>
              </Box>
            </CardContent>
          </Card>
        )}
      </Box>
    </TabPanel>
  );
};

export default CompetitorsTab;
