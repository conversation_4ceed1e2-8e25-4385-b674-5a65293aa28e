// Remove unused imports
import ArticleIcon from "@mui/icons-material/Article";
import AssignmentIcon from "@mui/icons-material/Assignment";
import BugReportIcon from "@mui/icons-material/BugReport";
import CompareArrowsIcon from "@mui/icons-material/CompareArrows";
import DashboardIcon from "@mui/icons-material/Dashboard";
import EditIcon from "@mui/icons-material/Edit";
import EmailIcon from "@mui/icons-material/Email";
import LightbulbIcon from "@mui/icons-material/Lightbulb";
import SearchIcon from "@mui/icons-material/Search";
import SmartToyIcon from "@mui/icons-material/SmartToy";
import TimelineIcon from "@mui/icons-material/Timeline";
import { Box, Tab, Tabs } from "@mui/material";

interface TabsNavigationProps {
  tabValue: number;
  handleTabChange: (event: any, newValue: number) => void;
}

/**
 * TabPanel component for SEO Dashboard
 */
export const TabPanel = ({
  children,
  index,
  value,
}: {
  children?: any;
  index: number;
  value: number;
}) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`seo-tabpanel-${index}`}
      aria-labelledby={`seo-tab-${index}`}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
};

/**
 * Tabs Navigation component for SEO Dashboard
 */
const TabsNavigation = ({ tabValue, handleTabChange }: TabsNavigationProps) => {
  return (
    <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        aria-label="SEO dashboard tabs"
        variant="scrollable"
        scrollButtons="auto"
      >
        <Tab label="Overview" icon={<DashboardIcon />} iconPosition="start" />
        <Tab label="Keywords" icon={<SearchIcon />} iconPosition="start" />
        <Tab label="Pages" icon={<ArticleIcon />} iconPosition="start" />
        <Tab label="Insights" icon={<LightbulbIcon />} iconPosition="start" />
        <Tab
          label="Competitors"
          icon={<CompareArrowsIcon />}
          iconPosition="start"
        />
        <Tab
          label="Technical SEO"
          icon={<BugReportIcon />}
          iconPosition="start"
        />
        <Tab
          label="Content Optimization"
          icon={<EditIcon />}
          iconPosition="start"
        />
        <Tab
          label="Historical Analysis"
          icon={<TimelineIcon />}
          iconPosition="start"
        />
        <Tab
          label="AI Recommendations"
          icon={<SmartToyIcon />}
          iconPosition="start"
        />
        <Tab label="Tasks" icon={<AssignmentIcon />} iconPosition="start" />
        <Tab label="Email Reports" icon={<EmailIcon />} iconPosition="start" />
      </Tabs>
    </Box>
  );
};

export default TabsNavigation;
