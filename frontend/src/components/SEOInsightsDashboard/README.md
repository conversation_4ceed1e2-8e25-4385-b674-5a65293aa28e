# SEO Insights Dashboard

The SEO Insights Dashboard provides a comprehensive view of your website's SEO performance, including keyword rankings, traffic trends, technical issues, and content optimization suggestions.

## Features

- **Overview**: High-level summary of SEO performance with key metrics and trends
- **Keywords**: Detailed keyword analysis with filtering, sorting, and favoriting capabilities
- **Pages**: Page performance metrics with speed, traffic, and content analysis
- **Insights**: Actionable insights categorized by type, impact, and category
- **Competitors**: Competitive analysis with side-by-side comparisons
- **Technical SEO**: Technical issues with severity levels and fix recommendations
- **Content Optimization**: Content improvement suggestions for specific pages

## API Integration

The SEO Insights Dashboard can connect to various SEO data sources:

### Google Search Console

The Google Search Console API provides data about your website's performance in Google Search, including:

- Keyword rankings
- Click-through rates
- Impressions
- Average positions
- Top pages

To use the Google Search Console API:

1. Create a Google Cloud project
2. Enable the Google Search Console API
3. Create API credentials (OAuth 2.0 or API key)
4. Enter your API key and site URL in the dashboard settings

### Google Analytics

The Google Analytics API provides data about your website's traffic and user behavior, including:

- Traffic sources
- Page views
- Bounce rates
- Session duration
- User demographics

To use the Google Analytics API:

1. Create a Google Cloud project
2. Enable the Google Analytics API
3. Create API credentials (OAuth 2.0 or API key)
4. Enter your API key and property ID in the dashboard settings

### SEMrush

The SEMrush API provides comprehensive SEO data, including:

- Keyword rankings
- Competitor analysis
- Backlink data
- Domain authority
- Traffic estimates

To use the SEMrush API:

1. Sign up for a SEMrush account
2. Subscribe to a plan that includes API access
3. Generate an API key in your SEMrush account
4. Enter your API key, domain, and database in the dashboard settings

### Ahrefs

The Ahrefs API provides data about backlinks, keywords, and content, including:

- Backlink profiles
- Domain rating
- Keyword rankings
- Content explorer
- Competitor analysis

To use the Ahrefs API:

1. Sign up for an Ahrefs account
2. Subscribe to a plan that includes API access
3. Generate an API key in your Ahrefs account
4. Enter your API key and domain in the dashboard settings

## Configuration

The SEO Insights Dashboard can be configured through the API Settings dialog, which allows you to:

- Enable/disable mock data for development and testing
- Configure API credentials for each data source
- Specify your domain and other parameters

Configuration is saved in the browser's localStorage and persists between sessions.

## Data Flow

1. The SEODataService initializes with the provided configuration
2. The service connects to the enabled data sources
3. Data is fetched from each source and combined into a unified format
4. The dashboard displays the data in various visualizations and tables
5. If real data is not available (due to missing credentials or API errors), mock data is used as a fallback

## Development

### Adding a New Data Source

To add a new data source:

1. Create a new service class that extends BaseApiService
2. Implement methods to fetch and transform the data
3. Update the SEODataService to include the new data source
4. Update the settings dialog to include configuration options for the new data source

### Customizing Visualizations

The dashboard uses Recharts for data visualization. To customize or add new visualizations:

1. Import the required components from Recharts
2. Create a new component or modify an existing one
3. Pass the appropriate data to the visualization component
4. Style the visualization using the provided props and options

## Troubleshooting

### API Connection Issues

If you're having trouble connecting to an API:

1. Check that your API credentials are correct
2. Verify that your account has access to the API
3. Check the browser console for error messages
4. Try enabling mock data to see if the issue is with the API connection

### Data Discrepancies

If you notice discrepancies between the dashboard and your analytics tools:

1. Check the date ranges used for data collection
2. Verify that the correct domain is configured
3. Consider that different tools may calculate metrics differently
4. Check if sampling is being applied to your data

## Future Enhancements

Planned enhancements for the SEO Insights Dashboard include:

- OAuth 2.0 authentication for Google APIs
- Data export functionality
- Custom report generation
- Scheduled email reports
- Integration with additional data sources
- Advanced filtering and segmentation options
