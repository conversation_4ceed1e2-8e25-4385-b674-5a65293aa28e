import AddIcon from '@mui/icons-material/Add';
import EmailIcon from '@mui/icons-material/Email';
import HistoryIcon from '@mui/icons-material/History';
import {
    Alert,
    Box,
    Button,
    CircularProgress,
    Grid,
    Paper,
    Snackbar,
    Tab,
    Tabs,
    Typography
} from '@mui/material';
import React from 'react';
import ReportForm from './ReportForm';
import ReportHistory from './ReportHistory';
import ReportList from './ReportList';
import ReportPreview from './ReportPreview';
import {
    EmailReport,
    EmailReportsProps,
    ReportFormat,
    ReportStatus
} from './types';

/**
 * Email Reports Component
 *
 * Manages email reports for SEO performance
 */
const EmailReports = ({ reports,
  reportHistory,
  onCreateReport,
  onUpdateReport,
  onDeleteReport,
  onSendReportNow,
  onDownloadReport,
  isLoading
 }: EmailReportsProps) => {
  // State for report form
  const [isReportFormOpen, setIsReportFormOpen] = React.useState(false);
  const [reportToEdit, setReportToEdit] = React.useState(undefined);

  // State for report preview
  const [previewReport, setPreviewReport] = React.useState(null);

  // State for active tab
  const [activeTab, setActiveTab] = React.useState(0);

  // State for notification
  const [notification, setNotification] = React.useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Handle create report button click
  const handleCreateReportClick = () => {
    setReportToEdit(undefined);
    setIsReportFormOpen(true);
  };

  // Handle edit report button click
  const handleEditReportClick = (report: EmailReport) => {
    setReportToEdit(report);
    setIsReportFormOpen(true);
  };

  // Handle report form submit
  const handleReportFormSubmit = (report: Omit<EmailReport, 'id' | 'createdAt' | 'updatedAt' | 'status'>) => {
    if (reportToEdit) {
      // Update existing report
      onUpdateReport(reportToEdit.id, report);
      setNotification({
        open: true,
        message: 'Report updated successfully',
        severity: 'success'
      });
    } else {
      // Create new report
      onCreateReport(report);
      setNotification({
        open: true,
        message: 'Report created successfully',
        severity: 'success'
      });
    }

    setIsReportFormOpen(false);
  };

  // Handle report form cancel
  const handleReportFormCancel = () => {
    setIsReportFormOpen(false);
  };

  // Handle delete report
  const handleDeleteReport = (reportId: string) => {
    onDeleteReport(reportId);
    setNotification({
      open: true,
      message: 'Report deleted successfully',
      severity: 'success'
    });
  };

  // Handle send report now
  const handleSendReportNow = (reportId: string) => {
    onSendReportNow(reportId);
    setNotification({
      open: true,
      message: 'Report sent successfully',
      severity: 'success'
    });
  };

  // Handle toggle report status
  const handleToggleReportStatus = (reportId: string, status: ReportStatus) => {
    onUpdateReport(reportId, { status });
    setNotification({
      open: true,
      message: `Report ${status === ReportStatus.ACTIVE ? 'activated' : 'paused'} successfully`,
      severity: 'success'
    });
  };

  // Handle download report
  const handleDownloadReport = (reportId: string, format: ReportFormat) => {
    onDownloadReport(reportId, format);
    setNotification({
      open: true,
      message: 'Report download started',
      severity: 'info'
    });
  };

  // Handle download report history
  const handleDownloadReportHistory = (historyId: string) => {
    const historyItem = reportHistory.find(h => h.id === historyId);
    if (historyItem && historyItem.downloadUrl) {
      window.open(historyItem.downloadUrl, '_blank');
    }
  };

  // Handle preview report
  const handlePreviewReport = (report: EmailReport) => {
    setPreviewReport(report);
  };

  // Handle close preview
  const handleClosePreview = () => {
    setPreviewReport(null);
  };

  // Handle tab change
  const handleTabChange = (_event: any, newValue: number) => {
    setActiveTab(newValue);
  };

  // Handle notification close
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };

  // Calculate report statistics
  const totalReports = reports.length;
  const activeReports = reports.filter(r => r.status === ReportStatus.ACTIVE).length;
  const pausedReports = reports.filter(r => r.status === ReportStatus.PAUSED).length;
  const totalSent = reportHistory.length;
  const successfulSent = reportHistory.filter(h => h.status === 'success').length;
  const failedSent = reportHistory.filter(h => h.status === 'failed').length;

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Email Reports
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateReportClick}
        >
          Create Report
        </Button>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={6} sm={4} md={2}>
          <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
            <Typography variant="h4" color="primary.main">
              {totalReports}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total Reports
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={4} md={2}>
          <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
            <Typography variant="h4" color="success.main">
              {activeReports}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Active
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={4} md={2}>
          <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
            <Typography variant="h4" color="warning.main">
              {pausedReports}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Paused
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={4} md={2}>
          <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
            <Typography variant="h4" color="info.main">
              {totalSent}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total Sent
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={4} md={2}>
          <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
            <Typography variant="h4" color="success.main">
              {successfulSent}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Successful
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={4} md={2}>
          <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
            <Typography variant="h4" color="error.main">
              {failedSent}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Failed
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Box sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          aria-label="email reports tabs"
        >
          <Tab
            icon={<EmailIcon />}
            label="Reports"
            iconPosition="start"
          />
          <Tab
            icon={<HistoryIcon />}
            label="History"
            iconPosition="start"
          />
        </Tabs>
      </Box>

      {/* Loading State */}
      {isLoading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Empty State */}
      {!isLoading && reports.length === 0 && activeTab === 0 && (
        <Alert severity="info" sx={{ my: 2 }}>
          No reports available. Click "Create Report" to set up your first email report.
        </Alert>
      )}

      {!isLoading && reportHistory.length === 0 && activeTab === 1 && (
        <Alert severity="info" sx={{ my: 2 }}>
          No report history available. Send a report to see its history here.
        </Alert>
      )}

      {/* Content */}
      {!isLoading && (
        <Box>
          {/* Reports Tab */}
          {activeTab === 0 && reports.length > 0 && (
            <ReportList
              reports={reports}
              onEditReport={handleEditReportClick}
              onDeleteReport={handleDeleteReport}
              onSendReportNow={handleSendReportNow}
              onToggleReportStatus={handleToggleReportStatus}
              onDownloadReport={handleDownloadReport}
            />
          )}

          {/* History Tab */}
          {activeTab === 1 && reportHistory.length > 0 && (
            <ReportHistory
              history={reportHistory}
              reports={reports}
              onDownloadReport={handleDownloadReportHistory}
            />
          )}
        </Box>
      )}

      {/* Report Form Dialog */}
      {isReportFormOpen && (
        <ReportForm
          report={reportToEdit}
          onSubmit={handleReportFormSubmit}
          onCancel={handleReportFormCancel}
        />
      )}

      {/* Report Preview Dialog */}
      {previewReport && (
        <ReportPreview
          report={previewReport}
          onClose={handleClosePreview}
        />
      )}

      {/* Notification */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          variant="filled"
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default EmailReports;
