import React from 'react';
/**
 * Report Frequency
 * Defines how often a report should be sent
 */
export enum ReportFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  BIWEEKLY = 'biweekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly'
}

/**
 * Report Format
 * Defines the format of the report
 */
export enum ReportFormat {
  PDF = 'pdf',
  HTML = 'html',
  CSV = 'csv',
  EXCEL = 'excel'
}

/**
 * Report Section
 * Defines the sections that can be included in a report
 */
export enum ReportSection {
  OVERVIEW = 'overview',
  KEYWORDS = 'keywords',
  PAGES = 'pages',
  COMPETITORS = 'competitors',
  TECHNICAL = 'technical',
  CONTENT = 'content',
  HISTORICAL = 'historical',
  RECOMMENDATIONS = 'recommendations',
  TASKS = 'tasks'
}

/**
 * Report Status
 * Defines the status of a scheduled report
 */
export enum ReportStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  FAILED = 'failed',
  COMPLETED = 'completed'
}

/**
 * Email Report
 * Defines the structure of an email report
 */
export interface EmailReport {
  id: string;
  name: string;
  description?: string;
  frequency: ReportFrequency;
  format: ReportFormat;
  recipients: string[];
  sections: ReportSection[];
  customizations?: ReportCustomization;
  schedule: ReportSchedule;
  status: ReportStatus;
  createdAt: string;
  updatedAt: string;
  lastSentAt?: string;
  nextScheduledAt?: string;
  createdBy?: string;
}

/**
 * Report Schedule
 * Defines when a report should be sent
 */
export interface ReportSchedule {
  dayOfWeek?: number; // 0-6, Sunday to Saturday (for weekly reports)
  dayOfMonth?: number; // 1-31 (for monthly reports)
  month?: number; // 0-11, January to December (for quarterly reports)
  time: string; // HH:MM format
  timezone: string; // e.g., 'America/New_York'
  startDate: string; // ISO date string
  endDate?: string; // ISO date string
}

/**
 * Report Customization
 * Defines customization options for a report
 */
export interface ReportCustomization {
  logo?: string; // URL or base64 encoded image
  colors?: {
    primary: string;
    secondary: string;
    accent: string;
  };
  title?: string;
  subtitle?: string;
  footer?: string;
  includeExecutiveSummary?: boolean;
  includeRecommendations?: boolean;
  maxPages?: number;
  maxItemsPerSection?: number;
}

/**
 * Report History Item
 * Defines a historical record of a sent report
 */
export interface ReportHistoryItem {
  id: string;
  reportId: string;
  sentAt: string;
  recipients: string[];
  status: 'success' | 'failed';
  errorMessage?: string;
  downloadUrl?: string;
  openRate?: number; // Percentage of recipients who opened the email
  clickRate?: number; // Percentage of recipients who clicked links in the email
}

/**
 * Email Reports Props
 * Props for the EmailReports component
 */
export interface EmailReportsProps {
  reports: EmailReport[];
  reportHistory: ReportHistoryItem[];
  onCreateReport: (report: Omit<EmailReport, 'id' | 'createdAt' | 'updatedAt' | 'status'>) => void;
  onUpdateReport: (reportId: string, updates: Partial<EmailReport>) => void;
  onDeleteReport: (reportId: string) => void;
  onSendReportNow: (reportId: string) => void;
  onDownloadReport: (reportId: string, format: ReportFormat) => void;
  isLoading: boolean;
}

/**
 * Report Form Props
 * Props for the ReportForm component
 */
export interface ReportFormProps {
  report?: EmailReport;
  onSubmit: (report: Omit<EmailReport, 'id' | 'createdAt' | 'updatedAt' | 'status'>) => void;
  onCancel: () => void;
}

/**
 * Report List Props
 * Props for the ReportList component
 */
export interface ReportListProps {
  reports: EmailReport[];
  onEditReport: (report: EmailReport) => void;
  onDeleteReport: (reportId: string) => void;
  onSendReportNow: (reportId: string) => void;
  onToggleReportStatus: (reportId: string, status: ReportStatus) => void;
  onDownloadReport: (reportId: string, format: ReportFormat) => void;
}

/**
 * Report History Props
 * Props for the ReportHistory component
 */
export interface ReportHistoryProps {
  history: ReportHistoryItem[];
  reports: EmailReport[];
  onDownloadReport: (historyId: string) => void;
}

/**
 * Report Preview Props
 * Props for the ReportPreview component
 */
export interface ReportPreviewProps {
  report: EmailReport;
  onClose: () => void;
}

/**
 * Report Schedule Form Props
 * Props for the ReportScheduleForm component
 */
export interface ReportScheduleFormProps {
  schedule: ReportSchedule;
  frequency: ReportFrequency;
  onChange: (schedule: ReportSchedule) => void;
}

/**
 * Report Sections Form Props
 * Props for the ReportSectionsForm component
 */
export interface ReportSectionsFormProps {
  sections: ReportSection[];
  onChange: (sections: ReportSection[]) => void;
}

/**
 * Report Customization Form Props
 * Props for the ReportCustomizationForm component
 */
export interface ReportCustomizationFormProps {
  customization?: ReportCustomization;
  onChange: (customization: ReportCustomization) => void;
}

/**
 * Report Recipients Form Props
 * Props for the ReportRecipientsForm component
 */
export interface ReportRecipientsFormProps {
  recipients: string[];
  onChange: (recipients: string[]) => void;
}
