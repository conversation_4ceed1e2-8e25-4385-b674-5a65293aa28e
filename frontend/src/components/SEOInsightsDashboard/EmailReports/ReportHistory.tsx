
import SuccessIcon from '@mui/icons-material/CheckCircle';
import DownloadIcon from '@mui/icons-material/Download';
import EmailIcon from '@mui/icons-material/Email';
import ErrorIcon from '@mui/icons-material/Error';
import {
    Box,
    Chip,
    IconButton,
    LinearProgress,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Tooltip,
    Typography
} from '@mui/material';
import {
    ReportHistoryProps
} from './types';

/**
 * Report History Component
 *
 * Displays the history of sent reports
 */
const ReportHistory = ({ history,
  reports,
  onDownloadReport
 }: ReportHistoryProps) => {
  // Get report name by ID
  const getReportName = (reportId: string): string => {
    const report = reports.find(r => r.id === reportId);
    return report ? report.name : 'Unknown Report';
  };

  // Format date
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Format recipients
  const formatRecipients = (recipients: string[]): string => {
    if (recipients.length === 0) return 'None';

    return recipients.slice(0, 2).join(', ') +
      (recipients.length > 2 ? ` +${recipients.length - 2} more` : '');
  };

  // Sort history by sent date (newest first)
  const sortedHistory = [...history].sort((a, b) =>
    new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime()
  );

  return (
    <Box>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Report</TableCell>
              <TableCell>Sent At</TableCell>
              <TableCell>Recipients</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Open Rate</TableCell>
              <TableCell>Click Rate</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedHistory.map((item) => (
              <TableRow key={item.id} hover>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <EmailIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="body2">
                      {getReportName(item.reportId)}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  {formatDate(item.sentAt)}
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {item.recipients.length} recipient{item.recipients.length !== 1 ? 's' : ''}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {formatRecipients(item.recipients)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={item.status === 'success' ? <SuccessIcon /> : <ErrorIcon />}
                    label={item.status === 'success' ? 'Success' : 'Failed'}
                    color={item.status === 'success' ? 'success' : 'error'}
                    size="small"
                  />
                  {item.status === 'failed' && item.errorMessage && (
                    <Typography variant="caption" color="error" display="block">
                      {item.errorMessage}
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  {item.openRate !== undefined ? (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box sx={{ width: '100%', mr: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={item.openRate}
                          color={
                            item.openRate >= 50 ? 'success' :
                            item.openRate >= 25 ? 'warning' :
                            'error'
                          }
                          sx={{ height: 8, borderRadius: 5 }}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {item.openRate}%
                      </Typography>
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      N/A
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  {item.clickRate !== undefined ? (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box sx={{ width: '100%', mr: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={item.clickRate}
                          color={
                            item.clickRate >= 30 ? 'success' :
                            item.clickRate >= 15 ? 'warning' :
                            'error'
                          }
                          sx={{ height: 8, borderRadius: 5 }}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {item.clickRate}%
                      </Typography>
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      N/A
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex' }}>
                    {item.status === 'success' && item.downloadUrl && (
                      <Tooltip title="Download Report">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => onDownloadReport(item.id)}
                        >
                          <DownloadIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default ReportHistory;
