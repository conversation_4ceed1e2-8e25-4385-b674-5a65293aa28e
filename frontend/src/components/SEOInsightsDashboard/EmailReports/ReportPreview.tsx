import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';

import { 
  Box, 
  Typography, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions,
  Button,
  Paper,
  Divider,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress
} from '@mui/material';
import { 
  EmailReport, 
  ReportPreviewProps, 
  ReportSection 
} from './types';

/**
 * Report Preview Component
 * 
 * Displays a preview of the report
 */
const ReportPreview = ({ report, 
  onClose 
 }: ReportPreviewProps) => {
  // Format frequency
  const formatFrequency = (frequency: string): string => {
    return frequency.charAt(0).toUpperCase() + frequency.slice(1);
  };
  
  // Format date
  const formatDate = (dateString?: string): string => {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };
  
  // Get section title
  const getSectionTitle = (section: ReportSection): string => {
    switch (section) {
      case ReportSection.OVERVIEW:
        return 'SEO Overview';
      case ReportSection.KEYWORDS:
        return 'Keyword Performance';
      case ReportSection.PAGES:
        return 'Page Performance';
      case ReportSection.COMPETITORS:
        return 'Competitor Analysis';
      case ReportSection.TECHNICAL:
        return 'Technical SEO';
      case ReportSection.CONTENT:
        return 'Content Optimization';
      case ReportSection.HISTORICAL:
        return 'Historical Analysis';
      case ReportSection.RECOMMENDATIONS:
        return 'AI Recommendations';
      case ReportSection.TASKS:
        return 'SEO Tasks';
      default:
        return section.charAt(0).toUpperCase() + section.slice(1);
    }
  };
  
  return (
    <Dialog 
      open={true} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      scroll="paper"
    >
      <DialogTitle>
        Report Preview: {report.name}
      </DialogTitle>
      <DialogContent dividers>
        <Box sx={{ mb: 4 }}>
          {/* Report Header */}
          <Paper sx={{ p: 3, mb: 3, bgcolor: report.customizations?.colors?.primary || '#1976d2', color: 'white' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              {report.customizations?.logo && (
                <Box sx={{ mr: 2 }}>
                  <img 
                    src={report.customizations.logo} 
                    alt="Logo" 
                    style={{ maxHeight: 50, maxWidth: 150 }}
                  />
                </Box>
              )}
              <Box>
                <Typography variant="h4">
                  {report.customizations?.title || report.name}
                </Typography>
                {report.customizations?.subtitle && (
                  <Typography variant="subtitle1">
                    {report.customizations.subtitle}
                  </Typography>
                )}
              </Box>
            </Box>
            <Typography variant="body2">
              Generated on {new Date().toLocaleDateString()} | {formatFrequency(report.frequency)} Report
            </Typography>
          </Paper>
          
          {/* Report Metadata */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Report Details
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Frequency:
                </Typography>
                <Typography variant="body1">
                  {formatFrequency(report.frequency)}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Format:
                </Typography>
                <Typography variant="body1">
                  {report.format.toUpperCase()}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Recipients:
                </Typography>
                <Typography variant="body1">
                  {report.recipients.join(', ')}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Next Scheduled:
                </Typography>
                <Typography variant="body1">
                  {formatDate(report.nextScheduledAt)}
                </Typography>
              </Grid>
            </Grid>
          </Paper>
          
          {/* Executive Summary */}
          {report.customizations?.includeExecutiveSummary && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Executive Summary
              </Typography>
              <Typography variant="body1" paragraph>
                This report provides an overview of your SEO performance for the period ending {formatDate(new Date().toISOString())}. 
                Key highlights include improvements in keyword rankings, increased organic traffic, and several technical SEO fixes implemented.
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#f5f5f5' }}>
                    <Typography variant="h4" color={report.customizations?.colors?.primary || '#1976d2'}>
                      +15%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Organic Traffic
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#f5f5f5' }}>
                    <Typography variant="h4" color={report.customizations?.colors?.secondary || '#dc004e'}>
                      +8
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Top 10 Keywords
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#f5f5f5' }}>
                    <Typography variant="h4" color={report.customizations?.colors?.accent || '#4caf50'}>
                      -12
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Technical Issues
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Paper>
          )}
          
          {/* Report Sections */}
          {report.sections.map((section) => (
            <Paper key={section} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                {getSectionTitle(section)}
              </Typography>
              
              {section === ReportSection.OVERVIEW && (
                <Box>
                  <Typography variant="body2" paragraph>
                    Overview of key SEO metrics and performance indicators.
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Organic Traffic
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ width: '100%', mr: 1 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={75} 
                            sx={{ height: 10, borderRadius: 5 }}
                          />
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          75%
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Keyword Rankings
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ width: '100%', mr: 1 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={60} 
                            sx={{ height: 10, borderRadius: 5 }}
                          />
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          60%
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              )}
              
              {section === ReportSection.KEYWORDS && (
                <Box>
                  <Typography variant="body2" paragraph>
                    Keyword rankings, changes, and opportunities.
                  </Typography>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Keyword</TableCell>
                          <TableCell align="right">Position</TableCell>
                          <TableCell align="right">Change</TableCell>
                          <TableCell align="right">Volume</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        <TableRow>
                          <TableCell>SEO Dashboard</TableCell>
                          <TableCell align="right">3</TableCell>
                          <TableCell align="right" sx={{ color: 'success.main' }}>+2</TableCell>
                          <TableCell align="right">1,200</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>SEO Analytics</TableCell>
                          <TableCell align="right">5</TableCell>
                          <TableCell align="right" sx={{ color: 'success.main' }}>+1</TableCell>
                          <TableCell align="right">980</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>SEO Reporting</TableCell>
                          <TableCell align="right">8</TableCell>
                          <TableCell align="right" sx={{ color: 'error.main' }}>-2</TableCell>
                          <TableCell align="right">750</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              )}
              
              {section !== ReportSection.OVERVIEW && section !== ReportSection.KEYWORDS && (
                <Typography variant="body2" color="text.secondary">
                  This is a preview of the {getSectionTitle(section)} section. The actual report will contain detailed data and analysis.
                </Typography>
              )}
            </Paper>
          ))}
          
          {/* Report Footer */}
          {report.customizations?.footer && (
            <Box sx={{ textAlign: 'center', mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                {report.customizations.footer}
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ReportPreview;
