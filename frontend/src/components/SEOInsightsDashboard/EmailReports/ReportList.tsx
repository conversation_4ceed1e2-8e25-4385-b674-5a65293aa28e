import CodeIcon from '@mui/icons-material/Code';
import DeleteIcon from '@mui/icons-material/Delete';
import DownloadIcon from '@mui/icons-material/Download';
import EditIcon from '@mui/icons-material/Edit';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import SendIcon from '@mui/icons-material/Send';
import TableChartIcon from '@mui/icons-material/TableChart';
import TableViewIcon from '@mui/icons-material/TableView';
import {
    Box,
    Button,
    Chip,
    Dialog,
    DialogActions,
    DialogContent,
    DialogContentText,
    DialogTitle,
    IconButton,
    ListItemIcon,
    ListItemText,
    Menu,
    MenuItem,
    Paper,
    Switch,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Tooltip,
    Typography
} from '@mui/material';
import React from 'react';
import {
    EmailReport,
    ReportFormat,
    ReportFrequency,
    ReportListProps,
    ReportStatus
} from './types';

/**
 * Report List Component
 *
 * Displays a list of email reports
 */
const ReportList = ({ reports,
  onEditReport,
  onDeleteReport,
  onSendReportNow,
  onToggleReportStatus,
  onDownloadReport
 }: ReportListProps) => {
  // State for menu
  const [menuAnchorEl, setMenuAnchorEl] = React.useState({});

  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [reportToDelete, setReportToDelete] = React.useState(null);

  // State for download menu
  const [downloadMenuAnchorEl, setDownloadMenuAnchorEl] = React.useState({});

  // Handle menu open
  const handleMenuOpen = (event: any, reportId: string) => {
    event.stopPropagation();
    setMenuAnchorEl({
      ...menuAnchorEl,
      [reportId]: event.currentTarget
    });
  };

  // Handle menu close
  const handleMenuClose = (reportId: string) => {
    setMenuAnchorEl({
      ...menuAnchorEl,
      [reportId]: null
    });
  };

  // Handle download menu open
  const handleDownloadMenuOpen = (event: any, reportId: string) => {
    event.stopPropagation();
    setDownloadMenuAnchorEl({
      ...downloadMenuAnchorEl,
      [reportId]: event.currentTarget
    });
  };

  // Handle download menu close
  const handleDownloadMenuClose = (reportId: string) => {
    setDownloadMenuAnchorEl({
      ...downloadMenuAnchorEl,
      [reportId]: null
    });
  };

  // Handle edit report
  const handleEditReport = (report: EmailReport) => {
    handleMenuClose(report.id);
    onEditReport(report);
  };

  // Handle delete dialog open
  const handleDeleteDialogOpen = (reportId: string) => {
    handleMenuClose(reportId);
    setReportToDelete(reportId);
    setDeleteDialogOpen(true);
  };

  // Handle delete dialog close
  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setReportToDelete(null);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (reportToDelete) {
      onDeleteReport(reportToDelete);
      handleDeleteDialogClose();
    }
  };

  // Handle send report now
  const handleSendReportNow = (reportId: string) => {
    handleMenuClose(reportId);
    onSendReportNow(reportId);
  };

  // Handle download report
  const handleDownloadReport = (reportId: string, format: ReportFormat) => {
    handleDownloadMenuClose(reportId);
    onDownloadReport(reportId, format);
  };

  // Handle toggle report status
  const handleToggleReportStatus = (event: any, reportId: string) => {
    event.stopPropagation();
    const newStatus = event.target.checked ? ReportStatus.ACTIVE : ReportStatus.PAUSED;
    onToggleReportStatus(reportId, newStatus);
  };

  // Format frequency
  const formatFrequency = (frequency: ReportFrequency): string => {
    switch (frequency) {
      case ReportFrequency.DAILY:
        return 'Daily';
      case ReportFrequency.WEEKLY:
        return 'Weekly';
      case ReportFrequency.BIWEEKLY:
        return 'Biweekly';
      case ReportFrequency.MONTHLY:
        return 'Monthly';
      case ReportFrequency.QUARTERLY:
        return 'Quarterly';
      default:
        return frequency;
    }
  };

  // Format format
  const formatFormat = (format: ReportFormat): string => {
    switch (format) {
      case ReportFormat.PDF:
        return 'PDF';
      case ReportFormat.HTML:
        return 'HTML';
      case ReportFormat.CSV:
        return 'CSV';
      case ReportFormat.EXCEL:
        return 'Excel';
      default:
        return format;
    }
  };

  // Format date
  const formatDate = (dateString?: string): string => {
    if (!dateString) return 'Never';

    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get format icon
  const getFormatIcon = (format: ReportFormat) => {
    switch (format) {
      case ReportFormat.PDF:
        return <PictureAsPdfIcon />;
      case ReportFormat.HTML:
        return <CodeIcon />;
      case ReportFormat.CSV:
        return <TableChartIcon />;
      case ReportFormat.EXCEL:
        return <TableViewIcon />;
      default:
        return null;
    }
  };

  // Get status color
  const getStatusColor = (status: ReportStatus) => {
    switch (status) {
      case ReportStatus.ACTIVE:
        return 'success';
      case ReportStatus.PAUSED:
        return 'warning';
      case ReportStatus.FAILED:
        return 'error';
      case ReportStatus.COMPLETED:
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Frequency</TableCell>
              <TableCell>Format</TableCell>
              <TableCell>Recipients</TableCell>
              <TableCell>Last Sent</TableCell>
              <TableCell>Next Scheduled</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {reports.map((report) => (
              <TableRow key={report.id} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {report.name}
                  </Typography>
                  {report.description && (
                    <Typography variant="caption" color="text.secondary">
                      {report.description}
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  {formatFrequency(report.frequency)}
                </TableCell>
                <TableCell>
                  <Chip
                    icon={getFormatIcon(report.format)}
                    label={formatFormat(report.format)}
                    size="small"
                    variant="outlined"
                    color="default"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {report.recipients.length} recipient{report.recipients.length !== 1 ? 's' : ''}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {report.recipients.slice(0, 2).join(', ')}
                    {report.recipients.length > 2 ? ` +${report.recipients.length - 2} more` : ''}
                  </Typography>
                </TableCell>
                <TableCell>
                  {formatDate(report.lastSentAt)}
                </TableCell>
                <TableCell>
                  {formatDate(report.nextScheduledAt)}
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Switch
                      checked={report.status === ReportStatus.ACTIVE}
                      onChange={(e) => handleToggleReportStatus(e, report.id)}
                      size="small"
                      color="success"
                    />
                    <Chip
                      label={report.status}
                      size="small"
                      color={getStatusColor(report.status)}
                      sx={{ ml: 1 }}
                    />
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex' }}>
                    <Tooltip title="Send Now">
                      <IconButton
                        size="small"
                        color="primary"
                        onClick={() => handleSendReportNow(report.id)}
                      >
                        <SendIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Download">
                      <IconButton
                        size="small"
                        color="primary"
                        onClick={(e) => handleDownloadMenuOpen(e, report.id)}
                      >
                        <DownloadIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Menu
                      anchorEl={downloadMenuAnchorEl[report.id]}
                      open={Boolean(downloadMenuAnchorEl[report.id])}
                      onClose={() => handleDownloadMenuClose(report.id)}
                    >
                      <MenuItem onClick={() => handleDownloadReport(report.id, ReportFormat.PDF)}>
                        <ListItemIcon>
                          <PictureAsPdfIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText>PDF</ListItemText>
                      </MenuItem>
                      <MenuItem onClick={() => handleDownloadReport(report.id, ReportFormat.HTML)}>
                        <ListItemIcon>
                          <CodeIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText>HTML</ListItemText>
                      </MenuItem>
                      <MenuItem onClick={() => handleDownloadReport(report.id, ReportFormat.CSV)}>
                        <ListItemIcon>
                          <TableChartIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText>CSV</ListItemText>
                      </MenuItem>
                      <MenuItem onClick={() => handleDownloadReport(report.id, ReportFormat.EXCEL)}>
                        <ListItemIcon>
                          <TableViewIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText>Excel</ListItemText>
                      </MenuItem>
                    </Menu>
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={(e) => handleMenuOpen(e, report.id)}
                    >
                      <MoreVertIcon fontSize="small" />
                    </IconButton>
                    <Menu
                      anchorEl={menuAnchorEl[report.id]}
                      open={Boolean(menuAnchorEl[report.id])}
                      onClose={() => handleMenuClose(report.id)}
                    >
                      <MenuItem onClick={() => handleEditReport(report)}>
                        <ListItemIcon>
                          <EditIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText>Edit</ListItemText>
                      </MenuItem>
                      <MenuItem onClick={() => handleDeleteDialogOpen(report.id)}>
                        <ListItemIcon>
                          <DeleteIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText>Delete</ListItemText>
                      </MenuItem>
                    </Menu>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteDialogClose}
      >
        <DialogTitle>Delete Report</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this report? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">Delete</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ReportList;
