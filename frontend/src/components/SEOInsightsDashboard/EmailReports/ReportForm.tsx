import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';
import { 
  <PERSON>, 
  Typo<PERSON>, 
  TextField, 
  Button, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Chip,
  OutlinedInput,
  Checkbox,
  ListItemText,
  FormHelperText,
  Grid,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Divider
} from '@mui/material';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { 
  EmailReport, 
  ReportFrequency, 
  ReportFormat, 
  ReportSection, 
  ReportFormProps,
  ReportSchedule,
  ReportCustomization
} from './types';
import ReportScheduleForm from './ReportScheduleForm';
import ReportSectionsForm from './ReportSectionsForm';
import ReportCustomizationForm from './ReportCustomizationForm';
import ReportRecipientsForm from './ReportRecipientsForm';

/**
 * Report Form Component
 * 
 * Form for creating or editing an email report
 */
const ReportForm = ({ report, 
  onSubmit, 
  onCancel 
 }: ReportFormProps) => {
  // State for active step
  const [activeStep, setActiveStep] = React.useState(0);
  
  // Form state
  const [name, setName] = React.useState(report?.name || '');
  const [description, setDescription] = React.useState(report?.description || '');
  const [frequency, setFrequency] = React.useState(report?.frequency || ReportFrequency.WEEKLY);
  const [format, setFormat] = React.useState(report?.format || ReportFormat.PDF);
  const [recipients, setRecipients] = React.useState(report?.recipients || []);
  const [sections, setSections] = React.useState(report?.sections || [
    ReportSection.OVERVIEW,
    ReportSection.KEYWORDS,
    ReportSection.PAGES
  ]);
  const [schedule, setSchedule] = React.useState(report?.schedule || {
    dayOfWeek: 1, // Monday
    time: '09:00',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    startDate: new Date().toISOString()
  });
  const [customizations, setCustomizations] = React.useState(
    report?.customizations
  );
  
  // Validation state
  const [nameError, setNameError] = React.useState('');
  const [recipientsError, setRecipientsError] = React.useState('');
  const [sectionsError, setSectionsError] = React.useState('');
  
  // Steps
  const steps = [
    'Basic Information',
    'Report Content',
    'Schedule',
    'Recipients',
    'Customization'
  ];
  
  // Handle name change
  const handleNameChange = (event: any) => {
    const value = event.target.value;
    setName(value);
    
    // Validate name
    if (!value.trim()) {
      setNameError('Name is required');
    } else {
      setNameError('');
    }
  };
  
  // Handle description change
  const handleDescriptionChange = (event: any) => {
    setDescription(event.target.value);
  };
  
  // Handle frequency change
  const handleFrequencyChange = (event: SelectChangeEvent<ReportFrequency>) => {
    const newFrequency = event.target.value as ReportFrequency;
    setFrequency(newFrequency);
    
    // Update schedule based on frequency
    let updatedSchedule = { ...schedule };
    
    switch (newFrequency) {
      case ReportFrequency.DAILY:
        // Remove day of week, day of month, and month
        delete updatedSchedule.dayOfWeek;
        delete updatedSchedule.dayOfMonth;
        delete updatedSchedule.month;
        break;
        
      case ReportFrequency.WEEKLY:
      case ReportFrequency.BIWEEKLY:
        // Add day of week, remove day of month and month
        updatedSchedule.dayOfWeek = 1; // Monday
        delete updatedSchedule.dayOfMonth;
        delete updatedSchedule.month;
        break;
        
      case ReportFrequency.MONTHLY:
        // Add day of month, remove day of week and month
        delete updatedSchedule.dayOfWeek;
        updatedSchedule.dayOfMonth = 1;
        delete updatedSchedule.month;
        break;
        
      case ReportFrequency.QUARTERLY:
        // Add day of month and month, remove day of week
        delete updatedSchedule.dayOfWeek;
        updatedSchedule.dayOfMonth = 1;
        updatedSchedule.month = 0; // January
        break;
    }
    
    setSchedule(updatedSchedule);
  };
  
  // Handle format change
  const handleFormatChange = (event: SelectChangeEvent<ReportFormat>) => {
    setFormat(event.target.value as ReportFormat);
  };
  
  // Handle sections change
  const handleSectionsChange = (newSections: ReportSection[]) => {
    setSections(newSections);
    
    // Validate sections
    if (newSections.length === 0) {
      setSectionsError('At least one section is required');
    } else {
      setSectionsError('');
    }
  };
  
  // Handle schedule change
  const handleScheduleChange = (newSchedule: ReportSchedule) => {
    setSchedule(newSchedule);
  };
  
  // Handle recipients change
  const handleRecipientsChange = (newRecipients: string[]) => {
    setRecipients(newRecipients);
    
    // Validate recipients
    if (newRecipients.length === 0) {
      setRecipientsError('At least one recipient is required');
    } else {
      setRecipientsError('');
    }
  };
  
  // Handle customizations change
  const handleCustomizationsChange = (newCustomizations: ReportCustomization) => {
    setCustomizations(newCustomizations);
  };
  
  // Handle next step
  const handleNext = () => {
    // Validate current step
    if (activeStep === 0) {
      if (!name.trim()) {
        setNameError('Name is required');
        return;
      }
    } else if (activeStep === 1) {
      if (sections.length === 0) {
        setSectionsError('At least one section is required');
        return;
      }
    } else if (activeStep === 3) {
      if (recipients.length === 0) {
        setRecipientsError('At least one recipient is required');
        return;
      }
    }
    
    setActiveStep((prevStep) => prevStep + 1);
  };
  
  // Handle back step
  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };
  
  // Handle form submit
  const handleSubmit = () => {
    // Validate form
    if (!name.trim()) {
      setNameError('Name is required');
      setActiveStep(0);
      return;
    }
    
    if (sections.length === 0) {
      setSectionsError('At least one section is required');
      setActiveStep(1);
      return;
    }
    
    if (recipients.length === 0) {
      setRecipientsError('At least one recipient is required');
      setActiveStep(3);
      return;
    }
    
    // Prepare report data
    const reportData: Omit<EmailReport, 'id' | 'createdAt' | 'updatedAt' | 'status'> = {
      name,
      description,
      frequency,
      format,
      recipients,
      sections,
      schedule,
      customizations
    };
    
    // Submit form
    onSubmit(reportData);
  };
  
  // Render step content
  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  label="Report Name"
                  value={name}
                  onChange={handleNameChange}
                  fullWidth
                  required
                  error={!!nameError}
                  helperText={nameError}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Description"
                  value={description}
                  onChange={handleDescriptionChange}
                  fullWidth
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel id="frequency-label">Frequency</InputLabel>
                  <Select
                    labelId="frequency-label"
                    value={frequency}
                    onChange={handleFrequencyChange}
                    label="Frequency"
                  >
                    <MenuItem value={ReportFrequency.DAILY}>Daily</MenuItem>
                    <MenuItem value={ReportFrequency.WEEKLY}>Weekly</MenuItem>
                    <MenuItem value={ReportFrequency.BIWEEKLY}>Biweekly</MenuItem>
                    <MenuItem value={ReportFrequency.MONTHLY}>Monthly</MenuItem>
                    <MenuItem value={ReportFrequency.QUARTERLY}>Quarterly</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel id="format-label">Format</InputLabel>
                  <Select
                    labelId="format-label"
                    value={format}
                    onChange={handleFormatChange}
                    label="Format"
                  >
                    <MenuItem value={ReportFormat.PDF}>PDF</MenuItem>
                    <MenuItem value={ReportFormat.HTML}>HTML</MenuItem>
                    <MenuItem value={ReportFormat.CSV}>CSV</MenuItem>
                    <MenuItem value={ReportFormat.EXCEL}>Excel</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        );
      case 1:
        return (
          <Box>
            <ReportSectionsForm
              sections={sections}
              onChange={handleSectionsChange}
            />
            {sectionsError && (
              <FormHelperText error>{sectionsError}</FormHelperText>
            )}
          </Box>
        );
      case 2:
        return (
          <Box>
            <ReportScheduleForm
              schedule={schedule}
              frequency={frequency}
              onChange={handleScheduleChange}
            />
          </Box>
        );
      case 3:
        return (
          <Box>
            <ReportRecipientsForm
              recipients={recipients}
              onChange={handleRecipientsChange}
            />
            {recipientsError && (
              <FormHelperText error>{recipientsError}</FormHelperText>
            )}
          </Box>
        );
      case 4:
        return (
          <Box>
            <ReportCustomizationForm
              customization={customizations}
              onChange={handleCustomizationsChange}
            />
          </Box>
        );
      default:
        return 'Unknown step';
    }
  };
  
  return (
    <Dialog 
      open={true} 
      onClose={onCancel}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        {report ? 'Edit Report' : 'Create New Report'}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
          
          <Paper sx={{ p: 3 }}>
            {getStepContent(activeStep)}
          </Paper>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onCancel}>Cancel</Button>
        {activeStep > 0 && (
          <Button onClick={handleBack}>
            Back
          </Button>
        )}
        {activeStep < steps.length - 1 ? (
          <Button 
            onClick={handleNext} 
            variant="contained" 
            color="primary"
          >
            Next
          </Button>
        ) : (
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
          >
            {report ? 'Update Report' : 'Create Report'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default ReportForm;
