import React from "react";
import { FC, ReactNode, ChangeEvent, MouseEvent } from "react";
import {
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  FormControlLabel,
  Checkbox,
  InputAdornment,
  Paper,
  Divider,
  Slider,
  FormControl,
  FormLabel,
  RadioGroup,
  Radio,
} from "@mui/material";
import { ReportCustomization, ReportCustomizationFormProps } from "./types";
import { ChromePicker, ChromePickerProps } from "react-color";

// Type assertion to fix the TypeScript error
const TypedChromePicker =
  ChromePicker as unknown as React.ComponentType<ChromePickerProps>;

/**
 * Report Customization Form Component
 *
 * Form for customizing report appearance
 */
const ReportCustomizationForm = ({
  customization,
  onChange,
}: ReportCustomizationFormProps) => {
  // Initialize customization with defaults if not provided
  const [currentCustomization, setCurrentCustomization] = React.useState(
    customization || {
      colors: {
        primary: "#1976d2",
        secondary: "#dc004e",
        accent: "#4caf50",
      },
      includeExecutiveSummary: true,
      includeRecommendations: true,
      maxPages: 10,
      maxItemsPerSection: 5,
    }
  );

  // State for color picker
  const [colorPickerOpen, setColorPickerOpen] = React.useState({
    primary: false,
    secondary: false,
    accent: false,
  });

  // Handle title change
  const handleTitleChange = (event: any) => {
    const newCustomization = {
      ...currentCustomization,
      title: event.target.value,
    };

    setCurrentCustomization(newCustomization);
    onChange(newCustomization);
  };

  // Handle subtitle change
  const handleSubtitleChange = (event: any) => {
    const newCustomization = {
      ...currentCustomization,
      subtitle: event.target.value,
    };

    setCurrentCustomization(newCustomization);
    onChange(newCustomization);
  };

  // Handle footer change
  const handleFooterChange = (event: any) => {
    const newCustomization = {
      ...currentCustomization,
      footer: event.target.value,
    };

    setCurrentCustomization(newCustomization);
    onChange(newCustomization);
  };

  // Handle logo change
  const handleLogoChange = (event: any) => {
    const newCustomization = {
      ...currentCustomization,
      logo: event.target.value,
    };

    setCurrentCustomization(newCustomization);
    onChange(newCustomization);
  };

  // Handle color change
  const handleColorChange = (
    color: string,
    type: "primary" | "secondary" | "accent"
  ) => {
    const newCustomization = {
      ...currentCustomization,
      colors: {
        ...currentCustomization.colors,
        [type]: color,
      },
    };

    setCurrentCustomization(newCustomization);
    onChange(newCustomization);
  };

  // Handle toggle color picker
  const handleToggleColorPicker = (
    type: "primary" | "secondary" | "accent"
  ) => {
    setColorPickerOpen({
      ...colorPickerOpen,
      [type]: !colorPickerOpen[type],
    });
  };

  // Handle checkbox change
  const handleCheckboxChange = (event: any) => {
    const { name, checked } = event.target;

    const newCustomization = {
      ...currentCustomization,
      [name]: checked,
    };

    setCurrentCustomization(newCustomization);
    onChange(newCustomization);
  };

  // Handle max pages change
  const handleMaxPagesChange = (_event: Event, value: number | number[]) => {
    const newCustomization = {
      ...currentCustomization,
      maxPages: value as number,
    };

    setCurrentCustomization(newCustomization);
    onChange(newCustomization);
  };

  // Handle max items per section change
  const handleMaxItemsPerSectionChange = (
    _event: Event,
    value: number | number[]
  ) => {
    const newCustomization = {
      ...currentCustomization,
      maxItemsPerSection: value as number,
    };

    setCurrentCustomization(newCustomization);
    onChange(newCustomization);
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Report Customization
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        Customize the appearance and content of your report. All fields are
        optional.
      </Typography>

      <Grid container spacing={3}>
        {/* Branding */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" gutterBottom>
            Branding
          </Typography>
          <Paper sx={{ p: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  label="Report Title"
                  value={currentCustomization.title || ""}
                  onChange={handleTitleChange}
                  fullWidth
                  placeholder="SEO Performance Report"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Subtitle"
                  value={currentCustomization.subtitle || ""}
                  onChange={handleSubtitleChange}
                  fullWidth
                  placeholder="Monthly performance overview"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Logo URL"
                  value={currentCustomization.logo || ""}
                  onChange={handleLogoChange}
                  fullWidth
                  placeholder="https://example.com/logo.png"
                  helperText="Enter a URL to your company logo"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Footer Text"
                  value={currentCustomization.footer || ""}
                  onChange={handleFooterChange}
                  fullWidth
                  placeholder="© 2023 Your Company Name"
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Colors */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" gutterBottom>
            Colors
          </Typography>
          <Paper sx={{ p: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Box sx={{ mb: 1 }}>
                  <Typography variant="body2" gutterBottom>
                    Primary Color
                  </Typography>
                  <Button
                    variant="outlined"
                    onClick={() => handleToggleColorPicker("primary")}
                    sx={{
                      backgroundColor:
                        currentCustomization.colors?.primary || "#1976d2",
                      color: "#fff",
                      width: "100%",
                      height: 40,
                      "&:hover": {
                        backgroundColor:
                          currentCustomization.colors?.primary || "#1976d2",
                        opacity: 0.9,
                      },
                    }}
                  >
                    {currentCustomization.colors?.primary || "#1976d2"}
                  </Button>
                  {colorPickerOpen.primary && (
                    <Box sx={{ position: "relative", mt: 1 }}>
                      <Box sx={{ position: "absolute", zIndex: 2 }}>
                        <TypedChromePicker
                          color={
                            currentCustomization.colors?.primary || "#1976d2"
                          }
                          onChange={(color) =>
                            handleColorChange(color.hex, "primary")
                          }
                        />
                        <Button
                          size="small"
                          onClick={() => handleToggleColorPicker("primary")}
                          sx={{ mt: 1, width: "100%" }}
                        >
                          Close
                        </Button>
                      </Box>
                    </Box>
                  )}
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ mb: 1 }}>
                  <Typography variant="body2" gutterBottom>
                    Secondary Color
                  </Typography>
                  <Button
                    variant="outlined"
                    onClick={() => handleToggleColorPicker("secondary")}
                    sx={{
                      backgroundColor:
                        currentCustomization.colors?.secondary || "#dc004e",
                      color: "#fff",
                      width: "100%",
                      height: 40,
                      "&:hover": {
                        backgroundColor:
                          currentCustomization.colors?.secondary || "#dc004e",
                        opacity: 0.9,
                      },
                    }}
                  >
                    {currentCustomization.colors?.secondary || "#dc004e"}
                  </Button>
                  {colorPickerOpen.secondary && (
                    <Box sx={{ position: "relative", mt: 1 }}>
                      <Box sx={{ position: "absolute", zIndex: 2 }}>
                        <TypedChromePicker
                          color={
                            currentCustomization.colors?.secondary || "#dc004e"
                          }
                          onChange={(color) =>
                            handleColorChange(color.hex, "secondary")
                          }
                        />
                        <Button
                          size="small"
                          onClick={() => handleToggleColorPicker("secondary")}
                          sx={{ mt: 1, width: "100%" }}
                        >
                          Close
                        </Button>
                      </Box>
                    </Box>
                  )}
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ mb: 1 }}>
                  <Typography variant="body2" gutterBottom>
                    Accent Color
                  </Typography>
                  <Button
                    variant="outlined"
                    onClick={() => handleToggleColorPicker("accent")}
                    sx={{
                      backgroundColor:
                        currentCustomization.colors?.accent || "#4caf50",
                      color: "#fff",
                      width: "100%",
                      height: 40,
                      "&:hover": {
                        backgroundColor:
                          currentCustomization.colors?.accent || "#4caf50",
                        opacity: 0.9,
                      },
                    }}
                  >
                    {currentCustomization.colors?.accent || "#4caf50"}
                  </Button>
                  {colorPickerOpen.accent && (
                    <Box sx={{ position: "relative", mt: 1 }}>
                      <Box sx={{ position: "absolute", zIndex: 2 }}>
                        <TypedChromePicker
                          color={
                            currentCustomization.colors?.accent || "#4caf50"
                          }
                          onChange={(color) =>
                            handleColorChange(color.hex, "accent")
                          }
                        />
                        <Button
                          size="small"
                          onClick={() => handleToggleColorPicker("accent")}
                          sx={{ mt: 1, width: "100%" }}
                        >
                          Close
                        </Button>
                      </Box>
                    </Box>
                  )}
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Content Options */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" gutterBottom>
            Content Options
          </Typography>
          <Paper sx={{ p: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={
                        currentCustomization.includeExecutiveSummary || false
                      }
                      onChange={handleCheckboxChange}
                      name="includeExecutiveSummary"
                    />
                  }
                  label="Include Executive Summary"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={
                        currentCustomization.includeRecommendations || false
                      }
                      onChange={handleCheckboxChange}
                      name="includeRecommendations"
                    />
                  }
                  label="Include Recommendations"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography id="max-pages-slider" gutterBottom>
                  Maximum Pages: {currentCustomization.maxPages || 10}
                </Typography>
                <Slider
                  value={currentCustomization.maxPages || 10}
                  onChange={handleMaxPagesChange}
                  aria-labelledby="max-pages-slider"
                  valueLabelDisplay="auto"
                  step={1}
                  marks
                  min={1}
                  max={50}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography id="max-items-slider" gutterBottom>
                  Max Items Per Section:{" "}
                  {currentCustomization.maxItemsPerSection || 5}
                </Typography>
                <Slider
                  value={currentCustomization.maxItemsPerSection || 5}
                  onChange={handleMaxItemsPerSectionChange}
                  aria-labelledby="max-items-slider"
                  valueLabelDisplay="auto"
                  step={1}
                  marks
                  min={1}
                  max={20}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ReportCustomizationForm;
