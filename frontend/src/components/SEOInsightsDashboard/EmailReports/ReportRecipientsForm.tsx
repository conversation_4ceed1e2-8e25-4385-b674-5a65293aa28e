import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EmailIcon from '@mui/icons-material/Email';
import {
    Box,
    Button,
    Divider,
    Grid,
    IconButton,
    List,
    ListItem,
    ListItemText,
    Paper,
    TextField,
    Typography
} from '@mui/material';
import React from 'react';
import { ReportRecipientsFormProps } from './types';

/**
 * Report Recipients Form Component
 *
 * Form for managing report recipients
 */
const ReportRecipientsForm = ({ recipients,
  onChange
 }: ReportRecipientsFormProps) => {
  // State for new recipient
  const [newRecipient, setNewRecipient] = React.useState('');
  const [emailError, setEmailError] = React.useState('');

  // Handle new recipient change
  const handleNewRecipientChange = (event: any) => {
    setNewRecipient(event.target.value);
    setEmailError('');
  };

  // Validate email
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Handle add recipient
  const handleAddRecipient = () => {
    const email = newRecipient.trim();

    if (!email) {
      setEmailError('Email is required');
      return;
    }

    if (!validateEmail(email)) {
      setEmailError('Invalid email format');
      return;
    }

    if (recipients.includes(email)) {
      setEmailError('Email already added');
      return;
    }

    onChange([...recipients, email]);
    setNewRecipient('');
  };

  // Handle remove recipient
  const handleRemoveRecipient = (email: string) => {
    onChange(recipients.filter(r => r !== email));
  };

  // Handle key press
  const handleKeyPress = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleAddRecipient();
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Report Recipients
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        Add email addresses of people who should receive this report. At least one recipient is required.
      </Typography>

      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', mb: 2 }}>
            <TextField
              label="Email Address"
              value={newRecipient}
              onChange={handleNewRecipientChange}
              onKeyPress={handleKeyPress}
              fullWidth
              error={!!emailError}
              helperText={emailError}
              sx={{ mr: 1 }}
            />
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddRecipient}
              disabled={!newRecipient.trim()}
            >
              Add
            </Button>
          </Box>
        </Grid>

        <Grid item xs={12}>
          {recipients.length === 0 ? (
            <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'background.default' }}>
              <Typography variant="body1" color="text.secondary">
                No recipients added yet. Add at least one email address.
              </Typography>
            </Paper>
          ) : (
            <Paper>
              <List>
                {recipients.map((email, index) => (
                  <React.Fragment key={email}>
                    {index > 0 && <Divider />}
                    <ListItem
                      secondaryAction={
                        <IconButton
                          edge="end"
                          aria-label="delete"
                          onClick={() => handleRemoveRecipient(email)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      }
                    >
                      <EmailIcon sx={{ mr: 2, color: 'primary.main' }} />
                      <ListItemText primary={email} />
                    </ListItem>
                  </React.Fragment>
                ))}
              </List>
            </Paper>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default ReportRecipientsForm;
