import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';

import { 
  Box, 
  Typography, 
  FormControl, 
  FormGroup, 
  FormControlLabel, 
  Checkbox,
  Grid,
  Paper,
  Divider
} from '@mui/material';
import {
  Dashboard as OverviewIcon,
  Search as KeywordsIcon,
  Article as PagesIcon,
  CompareArrows as CompetitorsIcon,
  BugReport as TechnicalIcon,
  Edit as ContentIcon,
  Timeline as HistoricalIcon,
  SmartToy as RecommendationsIcon,
  Assignment as TasksIcon
} from '@mui/icons-material';
import { 
  ReportSection, 
  ReportSectionsFormProps 
} from './types';

/**
 * Report Sections Form Component
 * 
 * Form for selecting report sections
 */
const ReportSectionsForm = ({ sections, 
  onChange 
 }: ReportSectionsFormProps) => {
  // Handle section toggle
  const handleSectionToggle = (section: ReportSection) => {
    const newSections = sections.includes(section)
      ? sections.filter(s => s !== section)
      : [...sections, section];
    
    onChange(newSections);
  };
  
  // Get section icon
  const getSectionIcon = (section: ReportSection) => {
    switch (section) {
      case ReportSection.OVERVIEW:
        return <OverviewIcon />;
      case ReportSection.KEYWORDS:
        return <KeywordsIcon />;
      case ReportSection.PAGES:
        return <PagesIcon />;
      case ReportSection.COMPETITORS:
        return <CompetitorsIcon />;
      case ReportSection.TECHNICAL:
        return <TechnicalIcon />;
      case ReportSection.CONTENT:
        return <ContentIcon />;
      case ReportSection.HISTORICAL:
        return <HistoricalIcon />;
      case ReportSection.RECOMMENDATIONS:
        return <RecommendationsIcon />;
      case ReportSection.TASKS:
        return <TasksIcon />;
      default:
        return null;
    }
  };
  
  // Get section description
  const getSectionDescription = (section: ReportSection): string => {
    switch (section) {
      case ReportSection.OVERVIEW:
        return 'Summary of key SEO metrics and performance indicators';
      case ReportSection.KEYWORDS:
        return 'Keyword rankings, changes, and opportunities';
      case ReportSection.PAGES:
        return 'Page performance, traffic, and optimization status';
      case ReportSection.COMPETITORS:
        return 'Competitor analysis and comparison';
      case ReportSection.TECHNICAL:
        return 'Technical SEO issues and recommendations';
      case ReportSection.CONTENT:
        return 'Content optimization suggestions and performance';
      case ReportSection.HISTORICAL:
        return 'Historical performance data and trends';
      case ReportSection.RECOMMENDATIONS:
        return 'AI-powered recommendations for improvement';
      case ReportSection.TASKS:
        return 'SEO tasks status and progress';
      default:
        return '';
    }
  };
  
  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Report Sections
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        Select the sections to include in your report. At least one section is required.
      </Typography>
      
      <Grid container spacing={2}>
        {Object.values(ReportSection).map((section) => (
          <Grid item xs={12} sm={6} key={section}>
            <Paper 
              sx={{ 
                p: 2, 
                display: 'flex', 
                flexDirection: 'column',
                height: '100%',
                border: sections.includes(section) ? 2 : 0,
                borderColor: 'primary.main',
                cursor: 'pointer',
                transition: 'all 0.2s',
                '&:hover': {
                  boxShadow: 2
                }
              }}
              onClick={() => handleSectionToggle(section)}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Box sx={{ 
                  mr: 1, 
                  color: sections.includes(section) ? 'primary.main' : 'text.secondary' 
                }}>
                  {getSectionIcon(section)}
                </Box>
                <Typography 
                  variant="subtitle1" 
                  sx={{ 
                    fontWeight: sections.includes(section) ? 'bold' : 'normal',
                    color: sections.includes(section) ? 'primary.main' : 'text.primary'
                  }}
                >
                  {section.charAt(0).toUpperCase() + section.slice(1)}
                </Typography>
                <Box sx={{ ml: 'auto' }}>
                  <Checkbox 
                    checked={sections.includes(section)} 
                    onChange={() => handleSectionToggle(section)}
                    onClick={(e) => e.stopPropagation()}
                    color="primary"
                  />
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary">
                {getSectionDescription(section)}
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default ReportSectionsForm;
