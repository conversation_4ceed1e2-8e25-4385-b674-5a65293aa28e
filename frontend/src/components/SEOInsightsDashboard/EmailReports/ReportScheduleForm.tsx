
import {
    Box,
    FormControl,
    Grid,
    InputLabel,
    MenuItem,
    Select,
    SelectChangeEvent,
    TextField,
    Typography
} from '@mui/material';
// Temporarily replace date pickers with TextField components due to TypeScript errors
// import { DatePicker, TimePicker, LocalizationProvider } from '@mui/x-date-pickers';
// import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
    ReportFrequency,
    ReportScheduleFormProps
} from './types';

/**
 * Report Schedule Form Component
 *
 * Form for configuring report schedule
 */
const ReportScheduleForm = ({ schedule,
  frequency,
  onChange
 }: ReportScheduleFormProps) => {
  // Convert time string to Date object
  const getTimeAsDate = (): Date => {
    const now = new Date();
    if (schedule.time) {
      const [hours, minutes] = schedule.time.split(':').map(Number);
      now.setHours(hours, minutes, 0, 0);
    }
    return now;
  };

  // Convert start date string to Date object
  const getStartDate = (): Date => {
    return schedule.startDate ? new Date(schedule.startDate) : new Date();
  };

  // Convert end date string to Date object
  const getEndDate = (): Date | null => {
    return schedule.endDate ? new Date(schedule.endDate) : null;
  };

  // Handle time change
  const handleTimeChange = (date: Date | null) => {
    if (date) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const time = `${hours}:${minutes}`;

      onChange({
        ...schedule,
        time
      });
    }
  };

  // Handle day of week change
  const handleDayOfWeekChange = (event: SelectChangeEvent<number>) => {
    onChange({
      ...schedule,
      dayOfWeek: event.target.value as number
    });
  };

  // Handle day of month change
  const handleDayOfMonthChange = (event: any) => {
    const value = parseInt(event.target.value);

    if (!isNaN(value) && value >= 1 && value <= 31) {
      onChange({
        ...schedule,
        dayOfMonth: value
      });
    }
  };

  // Handle month change
  const handleMonthChange = (event: SelectChangeEvent<number>) => {
    onChange({
      ...schedule,
      month: event.target.value as number
    });
  };

  // Handle timezone change
  const handleTimezoneChange = (event: SelectChangeEvent<string>) => {
    onChange({
      ...schedule,
      timezone: event.target.value
    });
  };

  // Handle start date change
  const handleStartDateChange = (date: Date | null) => {
    if (date) {
      onChange({
        ...schedule,
        startDate: date.toISOString()
      });
    }
  };

  // Handle end date change
  const handleEndDateChange = (date: Date | null) => {
    onChange({
      ...schedule,
      endDate: date ? date.toISOString() : undefined
    });
  };

  // Get day of week options
  const dayOfWeekOptions = [
    { value: 0, label: 'Sunday' },
    { value: 1, label: 'Monday' },
    { value: 2, label: 'Tuesday' },
    { value: 3, label: 'Wednesday' },
    { value: 4, label: 'Thursday' },
    { value: 5, label: 'Friday' },
    { value: 6, label: 'Saturday' }
  ];

  // Get month options
  const monthOptions = [
    { value: 0, label: 'January' },
    { value: 1, label: 'February' },
    { value: 2, label: 'March' },
    { value: 3, label: 'April' },
    { value: 4, label: 'May' },
    { value: 5, label: 'June' },
    { value: 6, label: 'July' },
    { value: 7, label: 'August' },
    { value: 8, label: 'September' },
    { value: 9, label: 'October' },
    { value: 10, label: 'November' },
    { value: 11, label: 'December' }
  ];

  // Get timezone options
  const timezoneOptions = [
    'America/New_York',
    'America/Chicago',
    'America/Denver',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Asia/Tokyo',
    'Australia/Sydney',
    Intl.DateTimeFormat().resolvedOptions().timeZone // User's timezone
  ];

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Schedule Settings
      </Typography>

      <Grid container spacing={3}>
        {/* Time */}
        <Grid item xs={12} sm={6}>
          <TextField
            label="Time"
            type="time"
            value={schedule.time || "12:00"}
            onChange={(e) => handleTimeChange(e.target.value ? new Date(`2000-01-01T${e.target.value}`) : null)}
            fullWidth
            InputLabelProps={{
              shrink: true
            }}
            inputProps={{
              step: 300 // 5 min
            }}
          />
        </Grid>

        {/* Timezone */}
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <InputLabel id="timezone-label">Timezone</InputLabel>
            <Select
              labelId="timezone-label"
              value={schedule.timezone}
              onChange={handleTimezoneChange}
              label="Timezone"
            >
              {timezoneOptions.map((timezone) => (
                <MenuItem key={timezone} value={timezone}>
                  {timezone}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

          {/* Day of Week (for weekly and biweekly) */}
          {(frequency === ReportFrequency.WEEKLY || frequency === ReportFrequency.BIWEEKLY) && (
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="day-of-week-label">Day of Week</InputLabel>
                <Select
                  labelId="day-of-week-label"
                  value={schedule.dayOfWeek !== undefined ? schedule.dayOfWeek : 1}
                  onChange={handleDayOfWeekChange}
                  label="Day of Week"
                >
                  {dayOfWeekOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          )}

          {/* Day of Month (for monthly and quarterly) */}
          {(frequency === ReportFrequency.MONTHLY || frequency === ReportFrequency.QUARTERLY) && (
            <Grid item xs={12} sm={6}>
              <TextField
                label="Day of Month"
                type="number"
                value={schedule.dayOfMonth !== undefined ? schedule.dayOfMonth : 1}
                onChange={handleDayOfMonthChange}
                fullWidth
                inputProps={{ min: 1, max: 31 }}
                helperText="Enter a day between 1-31. For months with fewer days, the last day will be used."
              />
            </Grid>
          )}

          {/* Month (for quarterly) */}
          {frequency === ReportFrequency.QUARTERLY && (
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="month-label">Month</InputLabel>
                <Select
                  labelId="month-label"
                  value={schedule.month !== undefined ? schedule.month : 0}
                  onChange={handleMonthChange}
                  label="Month"
                >
                  {monthOptions.filter((_, index) => index % 3 === 0).map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label} (Q{Math.floor(option.value / 3) + 1})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          )}

        {/* Start Date */}
        <Grid item xs={12} sm={6}>
          <TextField
            label="Start Date"
            type="date"
            value={schedule.startDate ? schedule.startDate.split('T')[0] : new Date().toISOString().split('T')[0]}
            onChange={(e) => handleStartDateChange(e.target.value ? new Date(e.target.value) : null)}
            fullWidth
            InputLabelProps={{
              shrink: true
            }}
          />
        </Grid>

        {/* End Date (optional) */}
        <Grid item xs={12} sm={6}>
          <TextField
            label="End Date (Optional)"
            type="date"
            value={schedule.endDate ? schedule.endDate.split('T')[0] : ""}
            onChange={(e) => handleEndDateChange(e.target.value ? new Date(e.target.value) : null)}
            fullWidth
            InputLabelProps={{
              shrink: true
            }}
            helperText="Leave blank for no end date"
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default ReportScheduleForm;
