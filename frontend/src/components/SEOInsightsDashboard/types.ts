import React from "react";
import { FC, ReactNode, ChangeEvent, MouseEvent } from "react";

// SEO Keyword interface
export interface SEOKeyword {
  keyword: string;
  volume: number;
  position: number;
  previousPosition: number;
  change: number;
  difficulty: number;
  traffic: number;
  intent?: "informational" | "navigational" | "transactional" | "commercial";
  cpc?: number;
  competition?: number;
}

// SEO Page Metrics interface
export interface SEOPageMetrics {
  url: string;
  title: string;
  visits: number;
  bounceRate: number;
  avgTimeOnPage: number;
  organicTraffic: number;
  keywordsRanked: number;
  conversionRate?: number;
  lastUpdated?: string;
  pageSpeed?: {
    mobile: number;
    desktop: number;
  };
  wordCount?: number;
}

// SEO Insight interface
export interface SEOInsight {
  type: "opportunity" | "issue" | "achievement";
  title: string;
  description: string;
  impact: "high" | "medium" | "low";
  category?:
    | "content"
    | "technical"
    | "backlinks"
    | "keywords"
    | "user-experience";
  actionable?: boolean;
  recommendedAction?: string;
}

// Competitor Data interface
export interface CompetitorData {
  domain: string;
  domainAuthority: number;
  organicTraffic: number;
  organicKeywords: number;
  backlinks: number;
  topKeywords: SEOKeyword[];
  sharedKeywords: number;
  trafficTrend: {
    date: string;
    traffic: number;
  }[];
}

// Technical SEO Issue interface
export interface TechnicalSEOIssue {
  type: "error" | "warning" | "info";
  title: string;
  description: string;
  impact: "high" | "medium" | "low";
  affectedPages: number;
  examples: string[];
  howToFix: string;
}

// Content Optimization Suggestion interface
export interface ContentSuggestion {
  url: string;
  title: string;
  currentScore: number;
  potentialScore: number;
  missingKeywords: string[];
  wordCountSuggestion: {
    current: number;
    recommended: number;
  };
  readabilityScore: number;
  titleSuggestion?: string;
  metaDescriptionSuggestion?: string;
}

// Historical Data Period interface
export interface HistoricalDataPeriod {
  id: string;
  label: string;
  startDate: string;
  endDate: string;
}

// Historical Data Point interface
export interface HistoricalDataPoint<T> {
  period: string; // Period ID
  data: T;
}

// Historical Metrics interface
export interface HistoricalMetrics {
  domainAuthority: number;
  organicTraffic: number;
  organicKeywords: number;
  backlinks: number;
  keywordPositionDistribution: {
    position: string;
    count: number;
  }[];
  averagePosition: number;
  topPositionCount: number; // Keywords in positions 1-3
  firstPageCount: number; // Keywords in positions 1-10
  clickThroughRate: number;
  impressions: number;
  clicks: number;
}

// Overview metrics interface
export interface SEOOverview {
  organicTraffic: number;
  organicTrafficChange: number;
  keywordsRanked: number;
  keywordsChange: number;
  backlinks: number;
  backlinksChange: number;
  averagePageSpeed: number;
  pageSpeedChange: number;
}

// Main SEO Insights Data interface
export interface SEOInsightsData {
  domainAuthority: number;
  organicTraffic: number;
  organicKeywords: number;
  backlinks: number;
  overview: SEOOverview;
  topKeywords: SEOKeyword[];
  topPages: SEOPageMetrics[];
  trafficTrend: {
    date: string;
    organicTraffic: number;
    directTraffic: number;
    referralTraffic: number;
  }[];
  keywordPositions: {
    position: string;
    count: number;
  }[];
  insights: SEOInsight[];
  competitors: CompetitorData[];
  technicalIssues: TechnicalSEOIssue[];
  contentSuggestions: ContentSuggestion[];
  keywordOpportunities: SEOKeyword[];
  seoHealth: {
    overall: number;
    technical: number;
    content: number;
    backlinks: number;
    onPage: number;
  };
  historicalData?: {
    availablePeriods: HistoricalDataPeriod[];
    metrics: HistoricalDataPoint<HistoricalMetrics>[];
    keywordTrends?: {
      keyword: string;
      positions: {
        period: string;
        position: number;
      }[];
    }[];
    pageTrends?: {
      url: string;
      metrics: {
        period: string;
        organicTraffic: number;
        position: number;
      }[];
    }[];
  };
  lastUpdated: string;
}

// Tab Panel Props interface
export interface TabPanelProps {
  children?: any;
  index: number;
  value: number;
}

// Data Tab Props interface
export interface DataTabProps {
  value: number;
  index: number;
  data: SEOInsightsData;
}
