import React from 'react';
import {
    BacklinkRecommendation,
    CompetitorRecommendation,
    ContentRecommendation,
    KeywordRecommendation,
    Recommendation,
    RecommendationDifficulty,
    RecommendationPriority,
    RecommendationStatus,
    RecommendationType,
    TechnicalRecommendation
} from '../../../services/AIRecommendationService';

/**
 * AI Recommendations Props
 * Props for the AIRecommendations component
 */
export interface AIRecommendationsProps {
  recommendations: Recommendation[];
  onStatusChange: (id: string, status: RecommendationStatus) => void;
  onRefresh: () => void;
  isLoading: boolean;
}

/**
 * Recommendation Filter
 * Filter options for recommendations
 */
export interface RecommendationFilter {
  types: RecommendationType[];
  priorities: RecommendationPriority[];
  difficulties: RecommendationDifficulty[];
  statuses: RecommendationStatus[];
  searchTerm: string;
}

/**
 * Recommendation Sort Option
 * Options for sorting recommendations
 */
export enum RecommendationSortOption {
  PRIORITY_HIGH_TO_LOW = 'priority_high_to_low',
  PRIORITY_LOW_TO_HIGH = 'priority_low_to_high',
  IMPACT_HIGH_TO_LOW = 'impact_high_to_low',
  IMPACT_LOW_TO_HIGH = 'impact_low_to_high',
  DIFFICULTY_EASY_TO_HARD = 'difficulty_easy_to_hard',
  DIFFICULTY_HARD_TO_EASY = 'difficulty_hard_to_easy',
  NEWEST_FIRST = 'newest_first',
  OLDEST_FIRST = 'oldest_first'
}

/**
 * Recommendation Detail Props
 * Props for the RecommendationDetail component
 */
export interface RecommendationDetailProps {
  recommendation: Recommendation;
  onStatusChange: (id: string, status: RecommendationStatus) => void;
  onClose: () => void;
}

/**
 * Content Recommendation Detail Props
 * Props for the ContentRecommendationDetail component
 */
export interface ContentRecommendationDetailProps {
  recommendation: ContentRecommendation;
  onStatusChange: (id: string, status: RecommendationStatus) => void;
}

/**
 * Technical Recommendation Detail Props
 * Props for the TechnicalRecommendationDetail component
 */
export interface TechnicalRecommendationDetailProps {
  recommendation: TechnicalRecommendation;
  onStatusChange: (id: string, status: RecommendationStatus) => void;
}

/**
 * Keyword Recommendation Detail Props
 * Props for the KeywordRecommendationDetail component
 */
export interface KeywordRecommendationDetailProps {
  recommendation: KeywordRecommendation;
  onStatusChange: (id: string, status: RecommendationStatus) => void;
}

/**
 * Backlink Recommendation Detail Props
 * Props for the BacklinkRecommendationDetail component
 */
export interface BacklinkRecommendationDetailProps {
  recommendation: BacklinkRecommendation;
  onStatusChange: (id: string, status: RecommendationStatus) => void;
}

/**
 * Competitor Recommendation Detail Props
 * Props for the CompetitorRecommendationDetail component
 */
export interface CompetitorRecommendationDetailProps {
  recommendation: CompetitorRecommendation;
  onStatusChange: (id: string, status: RecommendationStatus) => void;
}

/**
 * Recommendation List Props
 * Props for the RecommendationList component
 */
export interface RecommendationListProps {
  recommendations: Recommendation[];
  onSelectRecommendation: (recommendation: Recommendation) => void;
  filter: RecommendationFilter;
  sortOption: RecommendationSortOption;
}

/**
 * Recommendation Filter Panel Props
 * Props for the RecommendationFilterPanel component
 */
export interface RecommendationFilterPanelProps {
  filter: RecommendationFilter;
  onFilterChange: (filter: RecommendationFilter) => void;
  sortOption: RecommendationSortOption;
  onSortOptionChange: (option: RecommendationSortOption) => void;
  onReset: () => void;
}

/**
 * Recommendation Card Props
 * Props for the RecommendationCard component
 */
export interface RecommendationCardProps extends React.PropsWithChildren<any> {
  recommendation: Recommendation;
  onClick: () => void;
  // Allow key prop for React
  key?: React.Key;
}

/**
 * Recommendation Status Badge Props
 * Props for the RecommendationStatusBadge component
 */
export interface RecommendationStatusBadgeProps {
  status: RecommendationStatus;
}

/**
 * Recommendation Priority Badge Props
 * Props for the RecommendationPriorityBadge component
 */
export interface RecommendationPriorityBadgeProps {
  priority: RecommendationPriority;
}

/**
 * Recommendation Difficulty Badge Props
 * Props for the RecommendationDifficultyBadge component
 */
export interface RecommendationDifficultyBadgeProps {
  difficulty: RecommendationDifficulty;
}

/**
 * Recommendation Type Badge Props
 * Props for the RecommendationTypeBadge component
 */
export interface RecommendationTypeBadgeProps {
  type: RecommendationType;
}

/**
 * Recommendation Impact Meter Props
 * Props for the RecommendationImpactMeter component
 */
export interface RecommendationImpactMeterProps {
  impact: number;
}
