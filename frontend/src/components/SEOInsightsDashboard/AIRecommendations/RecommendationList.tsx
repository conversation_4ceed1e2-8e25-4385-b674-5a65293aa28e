
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import {
    <PERSON><PERSON>,
    <PERSON>,
    Chip,
    Typography
} from '@mui/material';
import {
    RecommendationDifficulty,
    RecommendationPriority
} from '../../../services/AIRecommendationService';
import RecommendationCard from './RecommendationCard';
import {
    RecommendationListProps,
    RecommendationSortOption
} from './types';

/**
 * Recommendation List Component
 *
 * Displays a list of AI-powered recommendations with filtering and sorting
 */
const RecommendationList = ({ recommendations,
  onSelectRecommendation,
  filter,
  sortOption
 }: RecommendationListProps) => {
  // Filter recommendations based on filter criteria
  const filteredRecommendations = recommendations.filter(recommendation => {
    // Filter by type
    if (!filter.types.includes(recommendation.type)) {
      return false;
    }

    // Filter by priority
    if (!filter.priorities.includes(recommendation.priority)) {
      return false;
    }

    // Filter by difficulty
    if (!filter.difficulties.includes(recommendation.difficulty)) {
      return false;
    }

    // Filter by status
    if (!filter.statuses.includes(recommendation.status)) {
      return false;
    }

    // Filter by search term
    if (filter.searchTerm) {
      const searchTerm = filter.searchTerm.toLowerCase();
      return (
        recommendation.title.toLowerCase().includes(searchTerm) ||
        recommendation.description.toLowerCase().includes(searchTerm)
      );
    }

    return true;
  });

  // Sort recommendations based on sort option
  const sortedRecommendations = [...filteredRecommendations].sort((a, b) => {
    switch (sortOption) {
      case RecommendationSortOption.PRIORITY_HIGH_TO_LOW:
        return getPriorityValue(b.priority) - getPriorityValue(a.priority);

      case RecommendationSortOption.PRIORITY_LOW_TO_HIGH:
        return getPriorityValue(a.priority) - getPriorityValue(b.priority);

      case RecommendationSortOption.IMPACT_HIGH_TO_LOW:
        return b.potentialImpact - a.potentialImpact;

      case RecommendationSortOption.IMPACT_LOW_TO_HIGH:
        return a.potentialImpact - b.potentialImpact;

      case RecommendationSortOption.DIFFICULTY_EASY_TO_HARD:
        return getDifficultyValue(a.difficulty) - getDifficultyValue(b.difficulty);

      case RecommendationSortOption.DIFFICULTY_HARD_TO_EASY:
        return getDifficultyValue(b.difficulty) - getDifficultyValue(a.difficulty);

      case RecommendationSortOption.NEWEST_FIRST:
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();

      case RecommendationSortOption.OLDEST_FIRST:
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();

      default:
        return 0;
    }
  });

  // Helper function to get priority value for sorting
  function getPriorityValue(priority: RecommendationPriority): number {
    switch (priority) {
      case RecommendationPriority.HIGH:
        return 3;
      case RecommendationPriority.MEDIUM:
        return 2;
      case RecommendationPriority.LOW:
        return 1;
      default:
        return 0;
    }
  }

  // Helper function to get difficulty value for sorting
  function getDifficultyValue(difficulty: RecommendationDifficulty): number {
    switch (difficulty) {
      case RecommendationDifficulty.EASY:
        return 1;
      case RecommendationDifficulty.MODERATE:
        return 2;
      case RecommendationDifficulty.DIFFICULT:
        return 3;
      default:
        return 0;
    }
  }

  // Get sort direction icon
  const getSortDirectionIcon = () => {
    return <ArrowForwardIcon fontSize="small" />;
  };

  // Get sort field name
  const getSortFieldName = () => {
    switch (sortOption) {
      case RecommendationSortOption.PRIORITY_HIGH_TO_LOW:
      case RecommendationSortOption.PRIORITY_LOW_TO_HIGH:
        return 'Priority';

      case RecommendationSortOption.IMPACT_HIGH_TO_LOW:
      case RecommendationSortOption.IMPACT_LOW_TO_HIGH:
        return 'Impact';

      case RecommendationSortOption.DIFFICULTY_EASY_TO_HARD:
      case RecommendationSortOption.DIFFICULTY_HARD_TO_EASY:
        return 'Difficulty';

      case RecommendationSortOption.NEWEST_FIRST:
      case RecommendationSortOption.OLDEST_FIRST:
        return 'Date';

      default:
        return '';
    }
  };

  return (
    <Box>
      {/* List Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Recommendations ({filteredRecommendations.length})
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
            Sorted by:
          </Typography>
          <Chip
            label={getSortFieldName()}
            size="small"
            icon={getSortDirectionIcon()}
            color="default"
          />
        </Box>
      </Box>

      {/* Empty State */}
      {filteredRecommendations.length === 0 && (
        <Alert severity="info">
          No recommendations match your filter criteria. Try adjusting your filters.
        </Alert>
      )}

      {/* Recommendation Cards */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        {sortedRecommendations.map((recommendation) => (
          <RecommendationCard
            key={recommendation.id}
            recommendation={recommendation}
            onClick={() => onSelectRecommendation(recommendation)}
          />
        ))}
      </Box>
    </Box>
  );
};

export default RecommendationList;
