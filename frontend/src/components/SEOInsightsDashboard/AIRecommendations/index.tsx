import FilterListIcon from "@mui/icons-material/FilterList";
import RefreshIcon from "@mui/icons-material/Refresh";
import SmartToyIcon from "@mui/icons-material/SmartToy";
import {
  <PERSON>ert,
  Box,
  Button,
  CircularProgress,
  Grid,
  Paper,
  Snackbar,
  Typography
} from "@mui/material";
import React, { useState } from "react";
import {
  Recommendation,
  RecommendationDifficulty,
  RecommendationPriority,
  RecommendationStatus,
  RecommendationType,
} from "../../../services/AIRecommendationService";
import RecommendationDetail from "./RecommendationDetail";
import RecommendationFilterPanel from "./RecommendationFilterPanel";
import RecommendationList from "./RecommendationList";
import {
  AIRecommendationsProps,
  RecommendationFilter,
  RecommendationSortOption,
} from "./types";

/**
 * AI Recommendations Component
 *
 * Displays AI-powered recommendations for improving SEO performance
 */
const AIRecommendations: React.FC<AIRecommendationsProps> = ({
  recommendations,
  onStatusChange,
  onRefresh,
  isLoading,
}) => {
  // State for selected recommendation
  const [selectedRecommendation, setSelectedRecommendation] =
    React.useState<Recommendation | null>(null);

  // State for filter panel visibility
  const [isFilterPanelOpen, setIsFilterPanelOpen] = React.useState(false);

  // State for filter options
  const [filter, setFilter] = React.useState({
    types: Object.values(RecommendationType),
    priorities: Object.values(RecommendationPriority),
    difficulties: Object.values(RecommendationDifficulty),
    statuses: Object.values(RecommendationStatus),
    searchTerm: "",
  });

  // State for sort option
  const [sortOption, setSortOption] = useState(
    RecommendationSortOption.PRIORITY_HIGH_TO_LOW
  );

  // State for notification
  const [notification, setNotification] = React.useState<{
    open: boolean;
    message: string;
    severity: 'error' | 'info' | 'success' | 'warning';
  }>({
    open: false,
    message: "",
    severity: "success",
  });

  // Handle recommendation selection
  const handleSelectRecommendation = (recommendation: Recommendation) => {
    setSelectedRecommendation(recommendation);
  };

  // Handle recommendation detail close
  const handleCloseDetail = () => {
    setSelectedRecommendation(null);
  };

  // Handle filter change
  const handleFilterChange = (newFilter: RecommendationFilter) => {
    setFilter(newFilter);
  };

  // Handle sort option change
  const handleSortOptionChange = (option: RecommendationSortOption) => {
    setSortOption(option);
  };

  // Handle filter panel toggle
  const handleToggleFilterPanel = () => {
    setIsFilterPanelOpen(!isFilterPanelOpen);
  };

  // Handle filter reset
  const handleResetFilter = () => {
    setFilter({
      types: Object.values(RecommendationType),
      priorities: Object.values(RecommendationPriority),
      difficulties: Object.values(RecommendationDifficulty),
      statuses: Object.values(RecommendationStatus),
      searchTerm: "",
    });
    setSortOption(RecommendationSortOption.PRIORITY_HIGH_TO_LOW);
  };

  // Handle recommendation status change
  const handleStatusChange = (id: string, status: RecommendationStatus) => {
    onStatusChange(id, status);

    // Show notification
    setNotification({
      open: true,
      message: `Recommendation status updated to ${status.replace("_", " ")}`,
      severity: "success",
    });

    // Close detail view if status is completed or dismissed
    if (
      status === RecommendationStatus.COMPLETED ||
      status === RecommendationStatus.DISMISSED
    ) {
      setSelectedRecommendation(null);
    }
  };

  // Handle notification close
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false,
    });
  };

  // Calculate recommendation statistics
  const totalRecommendations = recommendations.length;
  const completedRecommendations = recommendations.filter(
    (r) => r.status === RecommendationStatus.COMPLETED
  ).length;
  const inProgressRecommendations = recommendations.filter(
    (r) => r.status === RecommendationStatus.IN_PROGRESS
  ).length;
  const highPriorityRecommendations = recommendations.filter(
    (r) =>
      r.priority === RecommendationPriority.HIGH &&
      r.status !== RecommendationStatus.COMPLETED &&
      r.status !== RecommendationStatus.DISMISSED
  ).length;

  return (
    <Box sx={{ width: "100%" }}>
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <SmartToyIcon sx={{ mr: 1, color: "primary.main" }} />
          <Typography variant="h5" component="h2">
            AI-Powered Recommendations
          </Typography>
        </Box>
        <Box>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={handleToggleFilterPanel}
            sx={{ mr: 1 }}
          >
            Filter
          </Button>
          <Button
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={onRefresh}
            disabled={isLoading}
          >
            {isLoading ? "Generating..." : "Generate New Recommendations"}
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: "center", height: "100%" }}>
            <Typography variant="h4" color="primary.main">
              {totalRecommendations}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total Recommendations
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: "center", height: "100%" }}>
            <Typography variant="h4" color="success.main">
              {completedRecommendations}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Completed
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: "center", height: "100%" }}>
            <Typography variant="h4" color="info.main">
              {inProgressRecommendations}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              In Progress
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: "center", height: "100%" }}>
            <Typography variant="h4" color="error.main">
              {highPriorityRecommendations}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              High Priority
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Filter Panel */}
      {isFilterPanelOpen && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <RecommendationFilterPanel
            filter={filter}
            onFilterChange={handleFilterChange}
            sortOption={sortOption}
            onSortOptionChange={handleSortOptionChange}
            onReset={handleResetFilter}
          />
        </Paper>
      )}

      {/* Loading State */}
      {isLoading && (
        <Box sx={{ display: "flex", justifyContent: "center", my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Empty State */}
      {!isLoading && recommendations.length === 0 && (
        <Alert severity="info" sx={{ my: 2 }}>
          No recommendations available. Click "Generate New Recommendations" to
          get AI-powered suggestions for improving your SEO performance.
        </Alert>
      )}

      {/* Content */}
      {!isLoading && recommendations.length > 0 && (
        <Grid container spacing={3}>
          {/* Recommendation List */}
          <Grid item xs={12} md={selectedRecommendation ? 5 : 12}>
            <RecommendationList
              recommendations={recommendations}
              onSelectRecommendation={handleSelectRecommendation}
              filter={filter}
              sortOption={sortOption}
            />
          </Grid>

          {/* Recommendation Detail */}
          {selectedRecommendation && (
            <Grid item xs={12} md={7}>
              <RecommendationDetail
                recommendation={selectedRecommendation}
                onStatusChange={handleStatusChange}
                onClose={handleCloseDetail}
              />
            </Grid>
          )}
        </Grid>
      )}

      {/* Notification */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          variant="filled"
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AIRecommendations;
