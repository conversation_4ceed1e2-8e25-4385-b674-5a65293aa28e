
import SearchIcon from '@mui/icons-material/Search';
import {
    Box,
    Button,
    Checkbox,
    Divider,
    FormControl,
    Grid,
    InputLabel,
    ListItemText,
    MenuItem,
    Select,
    SelectChangeEvent,
    TextField,
    Typography
} from '@mui/material';
import {
    RecommendationDifficulty,
    RecommendationPriority,
    RecommendationStatus,
    RecommendationType
} from '../../../services/AIRecommendationService';
import {
    RecommendationFilterPanelProps,
    RecommendationSortOption
} from './types';

/**
 * Recommendation Filter Panel Component
 *
 * Provides controls for filtering and sorting recommendations
 */
const RecommendationFilterPanel = ({ filter,
  onFilterChange,
  sortOption,
  onSortOptionChange,
  onReset
 }: RecommendationFilterPanelProps) => {
  // Handle type filter change
  const handleTypeChange = (event: SelectChangeEvent<RecommendationType[]>) => {
    const value = event.target.value as RecommendationType[];
    onFilterChange({
      ...filter,
      types: value
    });
  };

  // Handle priority filter change
  const handlePriorityChange = (event: SelectChangeEvent<RecommendationPriority[]>) => {
    const value = event.target.value as RecommendationPriority[];
    onFilterChange({
      ...filter,
      priorities: value
    });
  };

  // Handle difficulty filter change
  const handleDifficultyChange = (event: SelectChangeEvent<RecommendationDifficulty[]>) => {
    const value = event.target.value as RecommendationDifficulty[];
    onFilterChange({
      ...filter,
      difficulties: value
    });
  };

  // Handle status filter change
  const handleStatusChange = (event: SelectChangeEvent<RecommendationStatus[]>) => {
    const value = event.target.value as RecommendationStatus[];
    onFilterChange({
      ...filter,
      statuses: value
    });
  };

  // Handle search term change
  const handleSearchChange = (event: any) => {
    onFilterChange({
      ...filter,
      searchTerm: event.target.value
    });
  };

  // Handle sort option change
  const handleSortOptionChange = (event: SelectChangeEvent) => {
    onSortOptionChange(event.target.value as RecommendationSortOption);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Filter & Sort</Typography>
        <Button
          onClick={onReset}
          size="small"
        >
          Reset
        </Button>
      </Box>

      <Grid container spacing={2}>
        {/* Search */}
        <Grid item xs={12}>
          <TextField
            fullWidth
            placeholder="Search recommendations..."
            value={filter.searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <Box component="span" sx={{ display: 'flex', alignItems: 'center', marginRight: 1 }}>
                  <SearchIcon />
                </Box>
              )
            }}
          />
        </Grid>

        {/* Type Filter */}
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel id="type-filter-label">Type</InputLabel>
            <Select
              labelId="type-filter-label"
              multiple
              value={filter.types}
              onChange={handleTypeChange}
              renderValue={(selected) => selected.length === Object.values(RecommendationType).length
                ? 'All Types'
                : `${selected.length} Types`}
            >
              {Object.values(RecommendationType).map((type) => (
                <MenuItem key={type} value={type}>
                  <Checkbox checked={filter.types.includes(type)} />
                  <ListItemText primary={type.charAt(0).toUpperCase() + type.slice(1)} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Priority Filter */}
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel id="priority-filter-label">Priority</InputLabel>
            <Select
              labelId="priority-filter-label"
              multiple
              value={filter.priorities}
              onChange={handlePriorityChange}
              renderValue={(selected) => selected.length === Object.values(RecommendationPriority).length
                ? 'All Priorities'
                : `${selected.length} Priorities`}
            >
              {Object.values(RecommendationPriority).map((priority) => (
                <MenuItem key={priority} value={priority}>
                  <Checkbox checked={filter.priorities.includes(priority)} />
                  <ListItemText primary={priority.charAt(0).toUpperCase() + priority.slice(1)} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Difficulty Filter */}
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel id="difficulty-filter-label">Difficulty</InputLabel>
            <Select
              labelId="difficulty-filter-label"
              multiple
              value={filter.difficulties}
              onChange={handleDifficultyChange}
              renderValue={(selected) => selected.length === Object.values(RecommendationDifficulty).length
                ? 'All Difficulties'
                : `${selected.length} Difficulties`}
            >
              {Object.values(RecommendationDifficulty).map((difficulty) => (
                <MenuItem key={difficulty} value={difficulty}>
                  <Checkbox checked={filter.difficulties.includes(difficulty)} />
                  <ListItemText primary={difficulty.charAt(0).toUpperCase() + difficulty.slice(1)} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Status Filter */}
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel id="status-filter-label">Status</InputLabel>
            <Select
              labelId="status-filter-label"
              multiple
              value={filter.statuses}
              onChange={handleStatusChange}
              renderValue={(selected) => selected.length === Object.values(RecommendationStatus).length
                ? 'All Statuses'
                : `${selected.length} Statuses`}
            >
              {Object.values(RecommendationStatus).map((status) => (
                <MenuItem key={status} value={status}>
                  <Checkbox checked={filter.statuses.includes(status)} />
                  <ListItemText primary={status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Sort Option */}
        <Grid item xs={12}>
          <Divider sx={{ my: 1 }} />
          <FormControl fullWidth size="small">
            <InputLabel id="sort-option-label">Sort By</InputLabel>
            <Select
              labelId="sort-option-label"
              value={sortOption}
              onChange={handleSortOptionChange}
              label="Sort By"
            >
              <MenuItem value={RecommendationSortOption.PRIORITY_HIGH_TO_LOW}>
                Priority: High to Low
              </MenuItem>
              <MenuItem value={RecommendationSortOption.PRIORITY_LOW_TO_HIGH}>
                Priority: Low to High
              </MenuItem>
              <MenuItem value={RecommendationSortOption.IMPACT_HIGH_TO_LOW}>
                Impact: High to Low
              </MenuItem>
              <MenuItem value={RecommendationSortOption.IMPACT_LOW_TO_HIGH}>
                Impact: Low to High
              </MenuItem>
              <MenuItem value={RecommendationSortOption.DIFFICULTY_EASY_TO_HARD}>
                Difficulty: Easy to Hard
              </MenuItem>
              <MenuItem value={RecommendationSortOption.DIFFICULTY_HARD_TO_EASY}>
                Difficulty: Hard to Easy
              </MenuItem>
              <MenuItem value={RecommendationSortOption.NEWEST_FIRST}>
                Date: Newest First
              </MenuItem>
              <MenuItem value={RecommendationSortOption.OLDEST_FIRST}>
                Date: Oldest First
              </MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>
    </Box>
  );
};

export default RecommendationFilterPanel;
