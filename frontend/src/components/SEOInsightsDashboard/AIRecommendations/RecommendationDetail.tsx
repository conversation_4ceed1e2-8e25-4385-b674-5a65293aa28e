
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CancelIcon from '@mui/icons-material/Cancel';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CloseIcon from '@mui/icons-material/Close';
import CodeIcon from '@mui/icons-material/Code';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import ContentPasteIcon from '@mui/icons-material/ContentPaste';
import LinkIcon from '@mui/icons-material/Link';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import SearchIcon from '@mui/icons-material/Search';
import {
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    Divider,
    Grid,
    IconButton,
    LinearProgress,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    Paper,
    Typography
} from '@mui/material';
import {
    BacklinkRecommendation,
    CompetitorRecommendation,
    ContentRecommendation,
    KeywordRecommendation,
    RecommendationStatus,
    RecommendationType,
    TechnicalRecommendation
} from '../../../services/AIRecommendationService';
import { RecommendationDetailProps } from './types';

/**
 * Recommendation Detail Component
 *
 * Displays detailed information about a recommendation
 */
const RecommendationDetail = ({ recommendation,
  onStatusChange,
  onClose
 }: RecommendationDetailProps) => {
  // Handle status change
  const handleStatusChange = (status: RecommendationStatus) => {
    onStatusChange(recommendation.id, status);
  };

  // Get icon for recommendation type
  const getTypeIcon = () => {
    switch (recommendation.type) {
      case RecommendationType.CONTENT:
        return <ContentPasteIcon fontSize="large" color="primary" />;
      case RecommendationType.TECHNICAL:
        return <CodeIcon fontSize="large" color="error" />;
      case RecommendationType.KEYWORD:
        return <SearchIcon fontSize="large" color="success" />;
      case RecommendationType.BACKLINK:
        return <LinkIcon fontSize="large" color="info" />;
      case RecommendationType.COMPETITOR:
        return <CompareArrowsIcon fontSize="large" color="warning" />;
      default:
        return null;
    }
  };

  // Get color for impact meter
  const getImpactColor = () => {
    if (recommendation.potentialImpact >= 70) {
      return 'success.main';
    } else if (recommendation.potentialImpact >= 40) {
      return 'warning.main';
    } else {
      return 'info.main';
    }
  };

  // Render content recommendation details
  const renderContentRecommendationDetails = (rec: ContentRecommendation) => (
    <Box>
      {rec.url && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Target URL:</Typography>
          <a
            href={rec.url}
            target="_blank"
            rel="noopener noreferrer"
            style={{ display: 'flex', alignItems: 'center' }}
          >
            {rec.url}
            <OpenInNewIcon fontSize="small" sx={{ ml: 0.5 }} />
          </a>
        </Box>
      )}

      {rec.contentIssues && rec.contentIssues.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Content Issues:</Typography>
          <List dense disablePadding>
            {rec.contentIssues.map((issue, index) => (
              <ListItem key={index} disablePadding sx={{ mb: 1 }}>
                <ListItemIcon sx={{ minWidth: 30 }}>
                  <ArrowForwardIcon color="primary" fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary={issue.issue}
                  secondary={issue.suggestion}
                />
              </ListItem>
            ))}
          </List>
        </Box>
      )}

      {rec.wordCountSuggestion && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Word Count:</Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Typography variant="body2" sx={{ mr: 1 }}>
              Current: <strong>{rec.wordCountSuggestion.current}</strong> words
            </Typography>
            <ArrowForwardIcon color="action" />
            <Typography variant="body2" sx={{ ml: 1 }}>
              Recommended: <strong>{rec.wordCountSuggestion.recommended}</strong> words
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={(rec.wordCountSuggestion.current / rec.wordCountSuggestion.recommended) * 100}
            sx={{
              height: 8,
              borderRadius: 5,
              bgcolor: 'background.paper',
              '& .MuiLinearProgress-bar': {
                bgcolor: rec.wordCountSuggestion.current >= rec.wordCountSuggestion.recommended
                  ? 'success.main'
                  : 'warning.main'
              }
            }}
          />
        </Box>
      )}

      {rec.headingSuggestions && rec.headingSuggestions.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Heading Suggestions:</Typography>
          <List dense disablePadding>
            {rec.headingSuggestions.map((heading, index) => (
              <ListItem key={index} disablePadding sx={{ mb: 1 }}>
                <ListItemIcon sx={{ minWidth: 30 }}>
                  <Chip
                    label={heading.type}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                </ListItemIcon>
                <ListItemText
                  primary={heading.suggested}
                  secondary={heading.current ? `Current: ${heading.current}` : null}
                />
              </ListItem>
            ))}
          </List>
        </Box>
      )}

      {rec.keywordsToTarget && rec.keywordsToTarget.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Keywords to Target:</Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {rec.keywordsToTarget.map((keyword, index) => (
              <Chip
                key={index}
                label={keyword}
                size="small"
                color="primary"
              />
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );

  // Render technical recommendation details
  const renderTechnicalRecommendationDetails = (rec: TechnicalRecommendation) => (
    <Box>
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2" gutterBottom>Issue Type:</Typography>
        <Chip
          label={rec.issueType.charAt(0).toUpperCase() + rec.issueType.slice(1)}
          color="error"
        />
      </Box>

      {rec.affectedPages && rec.affectedPages.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Affected Pages:</Typography>
          <List dense disablePadding>
            {rec.affectedPages.slice(0, 5).map((page, index) => (
              <ListItem key={index} disablePadding sx={{ mb: 0.5 }}>
                <ListItemIcon sx={{ minWidth: 30 }}>
                  <ArrowForwardIcon color="action" fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <a
                      href={page}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ display: 'flex', alignItems: 'center' }}
                    >
                      {page.length > 50 ? `${page.substring(0, 50)}...` : page}
                      <OpenInNewIcon fontSize="small" sx={{ ml: 0.5 }} />
                    </a>
                  }
                />
              </ListItem>
            ))}
            {rec.affectedPages.length > 5 && (
              <ListItem disablePadding>
                <ListItemText
                  primary={`And ${rec.affectedPages.length - 5} more pages...`}
                  primaryTypographyProps={{ variant: 'caption', color: 'text.secondary' }}
                />
              </ListItem>
            )}
          </List>
        </Box>
      )}

      {rec.codeSnippet && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Code Snippet:</Typography>
          <Paper sx={{ p: 1, bgcolor: 'grey.900', color: 'common.white', fontFamily: 'monospace', fontSize: '0.875rem', overflow: 'auto' }}>
            <pre style={{ margin: 0 }}>{rec.codeSnippet}</pre>
          </Paper>
        </Box>
      )}

      {rec.testUrl && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Test URL:</Typography>
          <a
            href={rec.testUrl}
            target="_blank"
            rel="noopener noreferrer"
            style={{ display: 'flex', alignItems: 'center' }}
          >
            {rec.testUrl}
            <OpenInNewIcon fontSize="small" sx={{ ml: 0.5 }} />
          </a>
        </Box>
      )}
    </Box>
  );

  // Render keyword recommendation details
  const renderKeywordRecommendationDetails = (rec: KeywordRecommendation) => (
    <Box>
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2" gutterBottom>Keyword:</Typography>
        <Chip
          label={rec.keyword}
          color="success"
          sx={{ fontWeight: 'bold' }}
        />
      </Box>

      {(rec.currentPosition !== undefined || rec.targetPosition !== undefined) && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Position:</Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {rec.currentPosition !== undefined && (
              <Chip
                label={`Current: ${rec.currentPosition}`}
                size="small"
                color={
                  rec.currentPosition <= 3 ? 'success' :
                  rec.currentPosition <= 10 ? 'primary' :
                  rec.currentPosition <= 20 ? 'info' :
                  'default'
                }
                sx={{ mr: 1 }}
              />
            )}
            {rec.targetPosition !== undefined && (
              <>
                <ArrowForwardIcon color="action" sx={{ mx: 1 }} />
                <Chip
                  label={`Target: ${rec.targetPosition}`}
                  size="small"
                  color="primary"
                />
              </>
            )}
          </Box>
        </Box>
      )}

      {rec.searchVolume !== undefined && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Search Volume:</Typography>
          <Typography variant="body1">{rec.searchVolume.toLocaleString()} monthly searches</Typography>
        </Box>
      )}

      {rec.competitiveDifficulty !== undefined && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Competitive Difficulty:</Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <LinearProgress
              variant="determinate"
              value={rec.competitiveDifficulty}
              sx={{
                height: 8,
                borderRadius: 5,
                width: 200,
                bgcolor: 'background.paper',
                mr: 2,
                '& .MuiLinearProgress-bar': {
                  bgcolor:
                    rec.competitiveDifficulty <= 30 ? 'success.main' :
                    rec.competitiveDifficulty <= 70 ? 'warning.main' :
                    'error.main'
                }
              }}
            />
            <Typography variant="body2">
              {rec.competitiveDifficulty}/100
            </Typography>
          </Box>
        </Box>
      )}

      {rec.suggestedPages && rec.suggestedPages.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Suggested Pages to Optimize:</Typography>
          <List dense disablePadding>
            {rec.suggestedPages.map((page, index) => (
              <ListItem key={index} disablePadding sx={{ mb: 0.5 }}>
                <ListItemIcon sx={{ minWidth: 30 }}>
                  <ArrowForwardIcon color="action" fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <a
                      href={page}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ display: 'flex', alignItems: 'center' }}
                    >
                      {page.length > 50 ? `${page.substring(0, 50)}...` : page}
                      <OpenInNewIcon fontSize="small" sx={{ ml: 0.5 }} />
                    </a>
                  }
                />
              </ListItem>
            ))}
          </List>
        </Box>
      )}

      {rec.relatedKeywords && rec.relatedKeywords.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Related Keywords:</Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {rec.relatedKeywords.map((keyword, index) => (
              <Chip
                key={index}
                label={keyword}
                size="small"
                color="info"
              />
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );

  // Render backlink recommendation details
  const renderBacklinkRecommendationDetails = (rec: BacklinkRecommendation) => (
    <Box>
      {rec.targetUrl && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Target URL:</Typography>
          <a
            href={rec.targetUrl}
            target="_blank"
            rel="noopener noreferrer"
            style={{ display: 'flex', alignItems: 'center' }}
          >
            {rec.targetUrl}
            <OpenInNewIcon fontSize="small" sx={{ ml: 0.5 }} />
          </a>
        </Box>
      )}

      {rec.potentialSources && rec.potentialSources.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Potential Sources:</Typography>
          <Grid container spacing={2}>
            {rec.potentialSources.map((source, index) => (
              <Grid item xs={12} sm={6} key={index}>
                <Card variant="outlined">
                  <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
                    <Typography variant="subtitle2">
                      {source.domain}
                    </Typography>
                    {source.domainAuthority && (
                      <Typography variant="caption" color="text.secondary">
                        Domain Authority: {source.domainAuthority}
                      </Typography>
                    )}
                    {source.contactInfo && (
                      <Typography variant="caption" display="block">
                        Contact: {source.contactInfo}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {rec.competitorBacklinks && rec.competitorBacklinks.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Competitor Backlinks:</Typography>
          <Grid container spacing={2}>
            {rec.competitorBacklinks.map((backlink, index) => (
              <Grid item xs={12} sm={6} key={index}>
                <Card variant="outlined">
                  <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
                    <Typography variant="subtitle2">
                      {backlink.backlink}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" display="block">
                      Competitor: {backlink.competitor}
                    </Typography>
                    {backlink.domainAuthority && (
                      <Typography variant="caption" color="text.secondary">
                        Domain Authority: {backlink.domainAuthority}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
    </Box>
  );

  // Render competitor recommendation details
  const renderCompetitorRecommendationDetails = (rec: CompetitorRecommendation) => (
    <Box>
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2" gutterBottom>Competitor:</Typography>
        <Chip
          label={rec.competitor}
          color="warning"
          sx={{ fontWeight: 'bold' }}
        />
      </Box>

      {rec.competitorMetric && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>{rec.competitorMetric.metric} Comparison:</Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Typography variant="body2" sx={{ mr: 1 }}>
              Your value: <strong>{rec.competitorMetric.yourValue.toLocaleString()}</strong>
            </Typography>
            <ArrowForwardIcon color="action" />
            <Typography variant="body2" sx={{ ml: 1 }}>
              Competitor value: <strong>{rec.competitorMetric.competitorValue.toLocaleString()}</strong>
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={(rec.competitorMetric.yourValue / rec.competitorMetric.competitorValue) * 100}
            sx={{
              height: 8,
              borderRadius: 5,
              bgcolor: 'background.paper',
              '& .MuiLinearProgress-bar': {
                bgcolor: rec.competitorMetric.yourValue >= rec.competitorMetric.competitorValue
                  ? 'success.main'
                  : 'warning.main'
              }
            }}
          />
        </Box>
      )}

      {rec.competitorStrategies && rec.competitorStrategies.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Competitor Strategies:</Typography>
          <List dense disablePadding>
            {rec.competitorStrategies.map((strategy, index) => (
              <ListItem key={index} disablePadding sx={{ mb: 0.5 }}>
                <ListItemIcon sx={{ minWidth: 30 }}>
                  <ArrowForwardIcon color="action" fontSize="small" />
                </ListItemIcon>
                <ListItemText primary={strategy} />
              </ListItem>
            ))}
          </List>
        </Box>
      )}

      {rec.keywordGaps && rec.keywordGaps.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>Keyword Gaps:</Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {rec.keywordGaps.map((keyword, index) => (
              <Chip
                key={index}
                label={keyword}
                size="small"
                color="info"
              />
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );

  // Render recommendation details based on type
  const renderRecommendationDetails = () => {
    switch (recommendation.type) {
      case RecommendationType.CONTENT:
        return renderContentRecommendationDetails(recommendation as ContentRecommendation);

      case RecommendationType.TECHNICAL:
        return renderTechnicalRecommendationDetails(recommendation as TechnicalRecommendation);

      case RecommendationType.KEYWORD:
        return renderKeywordRecommendationDetails(recommendation as KeywordRecommendation);

      case RecommendationType.BACKLINK:
        return renderBacklinkRecommendationDetails(recommendation as BacklinkRecommendation);

      case RecommendationType.COMPETITOR:
        return renderCompetitorRecommendationDetails(recommendation as CompetitorRecommendation);

      default:
        return null;
    }
  };

  return (
    <Paper sx={{ p: 3, height: '100%', position: 'relative' }}>
      {/* Close Button */}
      <IconButton
        sx={{ position: 'absolute', top: 8, right: 8 }}
        onClick={onClose}
      >
        <CloseIcon />
      </IconButton>

      {/* Header */}
      <Box sx={{ display: 'flex', mb: 3 }}>
        <Box sx={{ mr: 2 }}>
          {getTypeIcon()}
        </Box>
        <Box>
          <Typography variant="h5" gutterBottom>
            {recommendation.title}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {recommendation.description}
          </Typography>
        </Box>
      </Box>

      <Divider sx={{ mb: 3 }} />

      {/* Metrics */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={4}>
          <Box>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              Priority
            </Typography>
            <Typography variant="h6" color={
              recommendation.priority === 'high' ? 'error.main' :
              recommendation.priority === 'medium' ? 'warning.main' :
              'info.main'
            }>
              {recommendation.priority.charAt(0).toUpperCase() + recommendation.priority.slice(1)}
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Box>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              Difficulty
            </Typography>
            <Typography variant="h6" color={
              recommendation.difficulty === 'difficult' ? 'error.main' :
              recommendation.difficulty === 'moderate' ? 'warning.main' :
              'success.main'
            }>
              {recommendation.difficulty.charAt(0).toUpperCase() + recommendation.difficulty.slice(1)}
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Box>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              Potential Impact
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="h6" sx={{ mr: 1 }}>
                {recommendation.potentialImpact}%
              </Typography>
              <Box sx={{ flexGrow: 1 }}>
                <LinearProgress
                  variant="determinate"
                  value={recommendation.potentialImpact}
                  sx={{
                    height: 8,
                    borderRadius: 5,
                    bgcolor: 'background.paper',
                    '& .MuiLinearProgress-bar': {
                      bgcolor: getImpactColor()
                    }
                  }}
                />
              </Box>
            </Box>
          </Box>
        </Grid>
      </Grid>

      {/* Type-specific Details */}
      {renderRecommendationDetails()}

      <Divider sx={{ my: 3 }} />

      {/* Implementation Steps */}
      {recommendation.implementationSteps && recommendation.implementationSteps.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>Implementation Steps</Typography>
          <List>
            {recommendation.implementationSteps.map((step, index) => (
              <ListItem key={index} disablePadding sx={{ mb: 1 }}>
                <ListItemIcon>
                  <Chip
                    label={index + 1}
                    size="small"
                    color="primary"
                  />
                </ListItemIcon>
                <ListItemText primary={step} />
              </ListItem>
            ))}
          </List>
        </Box>
      )}

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
        {recommendation.status !== RecommendationStatus.DISMISSED && (
          <Button
            variant="outlined"
            color="error"
            startIcon={<CancelIcon />}
            onClick={() => handleStatusChange(RecommendationStatus.DISMISSED)}
            sx={{ mr: 1 }}
          >
            Dismiss
          </Button>
        )}

        {recommendation.status === RecommendationStatus.NEW && (
          <Button
            variant="outlined"
            color="warning"
            startIcon={<PlayArrowIcon />}
            onClick={() => handleStatusChange(RecommendationStatus.IN_PROGRESS)}
            sx={{ mr: 1 }}
          >
            Start
          </Button>
        )}

        {recommendation.status === RecommendationStatus.IN_PROGRESS && (
          <Button
            variant="contained"
            color="success"
            startIcon={<CheckCircleIcon />}
            onClick={() => handleStatusChange(RecommendationStatus.COMPLETED)}
          >
            Mark as Completed
          </Button>
        )}

        {recommendation.status === RecommendationStatus.COMPLETED && (
          <Button
            variant="outlined"
            color="warning"
            startIcon={<PlayArrowIcon />}
            onClick={() => handleStatusChange(RecommendationStatus.IN_PROGRESS)}
          >
            Reopen
          </Button>
        )}

        {recommendation.status === RecommendationStatus.DISMISSED && (
          <Button
            variant="outlined"
            color="primary"
            startIcon={<PlayArrowIcon />}
            onClick={() => handleStatusChange(RecommendationStatus.NEW)}
          >
            Restore
          </Button>
        )}
      </Box>
    </Paper>
  );
};

export default RecommendationDetail;
