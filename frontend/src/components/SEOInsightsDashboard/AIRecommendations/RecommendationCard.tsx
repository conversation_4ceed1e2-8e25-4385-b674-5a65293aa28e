
import CancelIcon from '@mui/icons-material/Cancel';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CodeIcon from '@mui/icons-material/Code';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import ContentPasteIcon from '@mui/icons-material/ContentPaste';
import LinkIcon from '@mui/icons-material/Link';
import LowPriorityIcon from '@mui/icons-material/LowPriority';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import NewReleasesIcon from '@mui/icons-material/NewReleases';
import PendingIcon from '@mui/icons-material/Pending';
import PriorityHighIcon from '@mui/icons-material/PriorityHigh';
import SearchIcon from '@mui/icons-material/Search';
import SentimentNeutralIcon from '@mui/icons-material/SentimentNeutral';
import SentimentVeryDissatisfiedIcon from '@mui/icons-material/SentimentVeryDissatisfied';
import SentimentVerySatisfiedIcon from '@mui/icons-material/SentimentVerySatisfied';
import {
    Box,
    Chip,
    LinearProgress,
    Paper,
    Tooltip,
    Typography
} from '@mui/material';
import {
    RecommendationDifficulty,
    RecommendationPriority,
    RecommendationStatus,
    RecommendationType
} from '../../../services/AIRecommendationService';
import { RecommendationCardProps } from './types';

/**
 * Recommendation Card Component
 *
 * Displays a card with recommendation details
 */
const RecommendationCard = ({ recommendation,
  onClick
 }: RecommendationCardProps) => {
  // Get icon for recommendation type
  const getTypeIcon = () => {
    switch (recommendation.type) {
      case RecommendationType.CONTENT:
        return <ContentPasteIcon />;
      case RecommendationType.TECHNICAL:
        return <CodeIcon />;
      case RecommendationType.KEYWORD:
        return <SearchIcon />;
      case RecommendationType.BACKLINK:
        return <LinkIcon />;
      case RecommendationType.COMPETITOR:
        return <CompareArrowsIcon />;
      default:
        return null;
    }
  };

  // Get color for recommendation type
  const getTypeColor = () => {
    switch (recommendation.type) {
      case RecommendationType.CONTENT:
        return 'primary';
      case RecommendationType.TECHNICAL:
        return 'error';
      case RecommendationType.KEYWORD:
        return 'success';
      case RecommendationType.BACKLINK:
        return 'info';
      case RecommendationType.COMPETITOR:
        return 'warning';
      default:
        return 'default';
    }
  };

  // Get label for recommendation type
  const getTypeLabel = () => {
    switch (recommendation.type) {
      case RecommendationType.CONTENT:
        return 'Content';
      case RecommendationType.TECHNICAL:
        return 'Technical';
      case RecommendationType.KEYWORD:
        return 'Keyword';
      case RecommendationType.BACKLINK:
        return 'Backlink';
      case RecommendationType.COMPETITOR:
        return 'Competitor';
      default:
        return '';
    }
  };

  // Get icon for recommendation priority
  const getPriorityIcon = () => {
    switch (recommendation.priority) {
      case RecommendationPriority.HIGH:
        return <PriorityHighIcon />;
      case RecommendationPriority.MEDIUM:
        return <MoreHorizIcon />;
      case RecommendationPriority.LOW:
        return <LowPriorityIcon />;
      default:
        return null;
    }
  };

  // Get color for recommendation priority
  const getPriorityColor = () => {
    switch (recommendation.priority) {
      case RecommendationPriority.HIGH:
        return 'error';
      case RecommendationPriority.MEDIUM:
        return 'warning';
      case RecommendationPriority.LOW:
        return 'info';
      default:
        return 'default';
    }
  };

  // Get label for recommendation priority
  const getPriorityLabel = () => {
    switch (recommendation.priority) {
      case RecommendationPriority.HIGH:
        return 'High';
      case RecommendationPriority.MEDIUM:
        return 'Medium';
      case RecommendationPriority.LOW:
        return 'Low';
      default:
        return '';
    }
  };

  // Get icon for recommendation difficulty
  const getDifficultyIcon = () => {
    switch (recommendation.difficulty) {
      case RecommendationDifficulty.EASY:
        return <SentimentVerySatisfiedIcon />;
      case RecommendationDifficulty.MODERATE:
        return <SentimentNeutralIcon />;
      case RecommendationDifficulty.DIFFICULT:
        return <SentimentVeryDissatisfiedIcon />;
      default:
        return null;
    }
  };

  // Get color for recommendation difficulty
  const getDifficultyColor = () => {
    switch (recommendation.difficulty) {
      case RecommendationDifficulty.EASY:
        return 'success';
      case RecommendationDifficulty.MODERATE:
        return 'warning';
      case RecommendationDifficulty.DIFFICULT:
        return 'error';
      default:
        return 'default';
    }
  };

  // Get label for recommendation difficulty
  const getDifficultyLabel = () => {
    switch (recommendation.difficulty) {
      case RecommendationDifficulty.EASY:
        return 'Easy';
      case RecommendationDifficulty.MODERATE:
        return 'Moderate';
      case RecommendationDifficulty.DIFFICULT:
        return 'Difficult';
      default:
        return '';
    }
  };

  // Get icon for recommendation status
  const getStatusIcon = () => {
    switch (recommendation.status) {
      case RecommendationStatus.NEW:
        return <NewReleasesIcon />;
      case RecommendationStatus.IN_PROGRESS:
        return <PendingIcon />;
      case RecommendationStatus.COMPLETED:
        return <CheckCircleIcon />;
      case RecommendationStatus.DISMISSED:
        return <CancelIcon />;
      default:
        return null;
    }
  };

  // Get color for recommendation status
  const getStatusColor = () => {
    switch (recommendation.status) {
      case RecommendationStatus.NEW:
        return 'info';
      case RecommendationStatus.IN_PROGRESS:
        return 'warning';
      case RecommendationStatus.COMPLETED:
        return 'success';
      case RecommendationStatus.DISMISSED:
        return 'default';
      default:
        return 'default';
    }
  };

  // Get label for recommendation status
  const getStatusLabel = () => {
    switch (recommendation.status) {
      case RecommendationStatus.NEW:
        return 'New';
      case RecommendationStatus.IN_PROGRESS:
        return 'In Progress';
      case RecommendationStatus.COMPLETED:
        return 'Completed';
      case RecommendationStatus.DISMISSED:
        return 'Dismissed';
      default:
        return '';
    }
  };

  // Get color for impact meter
  const getImpactColor = () => {
    if (recommendation.potentialImpact >= 70) {
      return 'success.main';
    } else if (recommendation.potentialImpact >= 40) {
      return 'warning.main';
    } else {
      return 'info.main';
    }
  };

  return (
    <Paper
      sx={{
        p: 2,
        cursor: 'pointer',
        transition: 'all 0.2s',
        '&:hover': {
          boxShadow: 3,
          transform: 'translateY(-2px)'
        },
        opacity: recommendation.status === RecommendationStatus.DISMISSED ? 0.7 : 1,
        borderLeft: '4px solid',
        borderColor: `${getTypeColor()}.main`
      }}
      onClick={onClick}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
        <Chip
        icon={getTypeIcon()}
        label={getTypeLabel()}
        size="small"
        color={getTypeColor() as "primary" | "error" | "success" | "info" | "warning" | "default"}
        sx={{ mr: 1 }}
        />
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title={`Priority: ${getPriorityLabel()}`}>
            <Chip
            icon={getPriorityIcon()}
            label={getPriorityLabel()}
            size="small"
            color={getPriorityColor() as "error" | "warning" | "info" | "default"}
            variant="outlined"
            />
          </Tooltip>
          <Tooltip title={`Status: ${getStatusLabel()}`}>
            <Chip
            icon={getStatusIcon()}
            label={getStatusLabel()}
            size="small"
            color={getStatusColor() as "info" | "warning" | "success" | "default"}
            variant="outlined"
            />
          </Tooltip>
        </Box>
      </Box>

      <Typography variant="h6" sx={{ mb: 1 }}>
        {recommendation.title}
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        {recommendation.description.length > 150
          ? `${recommendation.description.substring(0, 150)}...`
          : recommendation.description}
      </Typography>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title={`Difficulty: ${getDifficultyLabel()}`}>
            <Chip
            icon={getDifficultyIcon()}
            label={getDifficultyLabel()}
            size="small"
            color={getDifficultyColor() as "success" | "warning" | "error" | "default"}
            variant="outlined"
            />
          </Tooltip>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', width: '40%' }}>
          <Typography variant="caption" color="text.secondary" sx={{ mr: 1, minWidth: 60 }}>
            Impact:
          </Typography>
          <Box sx={{ width: '100%' }}>
            <Tooltip title={`Potential Impact: ${recommendation.potentialImpact}%`}>
              <LinearProgress
                variant="determinate"
                value={recommendation.potentialImpact}
                sx={{
                  height: 8,
                  borderRadius: 5,
                  bgcolor: 'background.paper',
                  '& .MuiLinearProgress-bar': {
                    bgcolor: getImpactColor()
                  }
                }}
              />
            </Tooltip>
          </Box>
        </Box>
      </Box>
    </Paper>
  );
};

export default RecommendationCard;
