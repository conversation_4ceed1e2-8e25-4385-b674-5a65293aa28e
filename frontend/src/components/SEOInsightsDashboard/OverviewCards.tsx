import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';

import { 
  Grid, 
  Card, 
  CardContent, 
  Typography, 
  Box, 
  LinearProgress, 
  Tooltip, 
  IconButton 
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { SEOInsightsData } from './types';

interface OverviewCardsProps {
  data: SEOInsightsData;
  loading: boolean;
}

/**
 * Overview Cards component for SEO Dashboard
 * Displays key metrics in card format
 */
const OverviewCards = ({ data, loading  }: OverviewCardsProps) => {
  // Helper function to render trend indicator
  const renderTrend = (current: number, previous: number) => {
    const percentChange = previous > 0 ? ((current - previous) / previous) * 100 : 0;
    
    if (percentChange > 3) {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', color: 'success.main' }}>
          <TrendingUpIcon fontSize="small" sx={{ mr: 0.5 }} />
          <Typography variant="caption" color="success.main">
            +{percentChange.toFixed(1)}%
          </Typography>
        </Box>
      );
    } else if (percentChange < -3) {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', color: 'error.main' }}>
          <TrendingDownIcon fontSize="small" sx={{ mr: 0.5 }} />
          <Typography variant="caption" color="error.main">
            {percentChange.toFixed(1)}%
          </Typography>
        </Box>
      );
    } else {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', color: 'text.secondary' }}>
          <TrendingFlatIcon fontSize="small" sx={{ mr: 0.5 }} />
          <Typography variant="caption" color="text.secondary">
            {percentChange.toFixed(1)}%
          </Typography>
        </Box>
      );
    }
  };

  // Calculate previous month traffic (for trend)
  const currentMonthTraffic = data.trafficTrend[data.trafficTrend.length - 1].organicTraffic;
  const previousMonthTraffic = data.trafficTrend[data.trafficTrend.length - 2].organicTraffic;

  return (
    <Grid container spacing={3} sx={{ mb: 4 }}>
      {/* Domain Authority */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Typography variant="subtitle2" color="textSecondary">Domain Authority</Typography>
              <Tooltip title="Domain Authority is a search engine ranking score that predicts how likely a website is to rank in search engine result pages.">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Typography variant="h4" sx={{ my: 1 }}>{data.domainAuthority}</Typography>
            <Box sx={{ width: '100%', mt: 1 }}>
              <LinearProgress 
                variant="determinate" 
                value={data.domainAuthority} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                    backgroundColor: data.domainAuthority > 50 ? 'success.main' : 'warning.main'
                  }
                }}
              />
            </Box>
            <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
              Out of 100
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Organic Traffic */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Typography variant="subtitle2" color="textSecondary">Organic Traffic</Typography>
              <Tooltip title="Monthly visitors from search engines">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'baseline', mt: 1 }}>
              <Typography variant="h4">{data.organicTraffic.toLocaleString()}</Typography>
              <Box sx={{ ml: 2 }}>
                {renderTrend(currentMonthTraffic, previousMonthTraffic)}
              </Box>
            </Box>
            <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
              Monthly visitors
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Ranking Keywords */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Typography variant="subtitle2" color="textSecondary">Ranking Keywords</Typography>
              <Tooltip title="Total number of keywords your website ranks for in the top 100 search results">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Typography variant="h4" sx={{ my: 1 }}>{data.organicKeywords.toLocaleString()}</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Typography variant="body2" color="textSecondary" sx={{ mr: 1 }}>
                Top 10 positions:
              </Typography>
              <Typography variant="body2" color="primary.main" fontWeight="bold">
                {data.keywordPositions[0].count + data.keywordPositions[1].count}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* SEO Health */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Typography variant="subtitle2" color="textSecondary">SEO Health</Typography>
              <Tooltip title="Overall SEO health score based on technical, content, and backlink factors">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Typography variant="h4" sx={{ my: 1 }}>{data.seoHealth.overall}/100</Typography>
            <Box sx={{ width: '100%', mt: 1 }}>
              <LinearProgress 
                variant="determinate" 
                value={data.seoHealth.overall} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                    backgroundColor: 
                      data.seoHealth.overall >= 80 ? 'success.main' : 
                      data.seoHealth.overall >= 60 ? 'warning.main' : 
                      'error.main'
                  }
                }}
              />
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
              <Typography variant="caption" color="textSecondary">Technical: {data.seoHealth.technical}</Typography>
              <Typography variant="caption" color="textSecondary">Content: {data.seoHealth.content}</Typography>
              <Typography variant="caption" color="textSecondary">Links: {data.seoHealth.backlinks}</Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default OverviewCards;
