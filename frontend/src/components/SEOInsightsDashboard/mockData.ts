import React from "react";
import {
  SEOInsightsData,
  HistoricalDataPeriod,
  HistoricalMetrics,
  HistoricalDataPoint,
} from "./types";

// Generate historical periods (last 6 months)
function generateHistoricalPeriods(): HistoricalDataPeriod[] {
  const periods: HistoricalDataPeriod[] = [];
  const now = new Date();

  // Current month
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();

  // Generate periods for the last 6 months
  for (let i = 0; i < 6; i++) {
    const monthIndex = currentMonth - i;
    const year = monthIndex < 0 ? currentYear - 1 : currentYear;
    const month = monthIndex < 0 ? 12 + monthIndex : monthIndex;

    const startDate = new Date(year, month, 1);
    const endDate = new Date(year, month + 1, 0);

    periods.push({
      id: `${year}-${(month + 1).toString().padStart(2, "0")}`,
      label: startDate.toLocaleString("default", {
        month: "long",
        year: "numeric",
      }),
      startDate: startDate.toISOString().split("T")[0],
      endDate: endDate.toISOString().split("T")[0],
    });
  }

  // Reverse to have oldest first
  return periods.reverse();
}

// Generate historical metrics
function generateHistoricalMetrics(
  periods: HistoricalDataPeriod[]
): HistoricalDataPoint<HistoricalMetrics>[] {
  const metrics: HistoricalDataPoint<HistoricalMetrics>[] = [];

  // Base values
  let domainAuthority = 28;
  let organicTraffic = 3200;
  let organicKeywords = 150;
  let backlinks = 280;
  let averagePosition = 24;
  let topPositionCount = 8;
  let firstPageCount = 22;
  let clickThroughRate = 2.1;
  let impressions = 120000;
  let clicks = 2520;

  // Generate metrics for each period with some growth
  periods.forEach((period) => {
    // Generate keyword position distribution
    const keywordPositionDistribution = [
      { position: "1-3", count: topPositionCount },
      { position: "4-10", count: firstPageCount - topPositionCount },
      { position: "11-20", count: Math.round(organicKeywords * 0.25) },
      { position: "21-50", count: Math.round(organicKeywords * 0.35) },
      { position: "51-100", count: Math.round(organicKeywords * 0.15) },
    ];

    metrics.push({
      period: period.id,
      data: {
        domainAuthority,
        organicTraffic,
        organicKeywords,
        backlinks,
        keywordPositionDistribution,
        averagePosition,
        topPositionCount,
        firstPageCount,
        clickThroughRate,
        impressions,
        clicks,
      },
    });

    // Increase values for next period (simulating growth)
    domainAuthority += Math.round(Math.random() * 2);
    organicTraffic += Math.round(organicTraffic * (0.05 + Math.random() * 0.1));
    organicKeywords += Math.round(Math.random() * 10);
    backlinks += Math.round(backlinks * (0.03 + Math.random() * 0.08));
    averagePosition = Math.max(12, averagePosition - Math.random() * 2);
    topPositionCount += Math.round(Math.random() * 2);
    firstPageCount += Math.round(Math.random() * 3);
    clickThroughRate += 0.1 + Math.random() * 0.2;
    impressions += Math.round(impressions * (0.05 + Math.random() * 0.1));
    clicks += Math.round(clicks * (0.08 + Math.random() * 0.12));
  });

  return metrics;
}

// Generate keyword trends
function generateKeywordTrends(
  periods: HistoricalDataPeriod[],
  keywords: string[]
): any[] {
  return keywords.map((keyword) => {
    // Start with a random position between 5 and 50
    let position = Math.floor(5 + Math.random() * 45);

    // Generate position for each period with some fluctuation
    const positions = periods.map((period) => {
      const currentPosition = position;

      // Adjust position for next period (generally improving)
      const change =
        Math.random() > 0.7
          ? Math.round(Math.random() * 5) // Occasional setback
          : -Math.round(Math.random() * 3); // Improvement

      position = Math.max(1, Math.min(100, position + change));

      return {
        period: period.id,
        position: currentPosition,
      };
    });

    return {
      keyword,
      positions,
    };
  });
}

// Generate page trends
function generatePageTrends(
  periods: HistoricalDataPeriod[],
  pages: string[]
): any[] {
  return pages.map((url) => {
    // Start with a random traffic value
    let traffic = Math.floor(100 + Math.random() * 500);
    let position = Math.floor(5 + Math.random() * 20);

    // Generate metrics for each period
    const metrics = periods.map((period) => {
      const currentTraffic = traffic;
      const currentPosition = position;

      // Adjust values for next period
      traffic = Math.round(traffic * (0.9 + Math.random() * 0.3));
      position = Math.max(
        1,
        Math.min(100, position + (Math.random() > 0.7 ? 2 : -1))
      );

      return {
        period: period.id,
        organicTraffic: currentTraffic,
        position: currentPosition,
      };
    });

    return {
      url,
      metrics,
    };
  });
}

// Mock data generator function
export function getMockSEOData(): SEOInsightsData {
  // Generate historical periods
  const historicalPeriods = generateHistoricalPeriods();
  // Generate historical metrics
  const historicalMetrics = generateHistoricalMetrics(historicalPeriods);

  // Extract top keywords for trends
  const topKeywordNames = [
    "water treatment chemicals",
    "film forming amines",
    "cooling tower chemicals",
    "boiler water treatment",
    "mexel 432",
  ];

  // Generate keyword trends
  const keywordTrends = generateKeywordTrends(
    historicalPeriods,
    topKeywordNames
  );

  // Extract top pages for trends
  const topPageUrls = [
    "https://www.mexelenergysustain.com/products/mexel-432",
    "https://www.mexelenergysustain.com/solutions/cooling-tower-treatment",
    "https://www.mexelenergysustain.com/blog/film-forming-amines-vs-traditional",
  ];

  // Generate page trends
  const pageTrends = generatePageTrends(historicalPeriods, topPageUrls);

  return {
    domainAuthority: 32,
    organicTraffic: 4250,
    organicKeywords: 187,
    backlinks: 342,
    // Add overview property for MobileDashboard component
    overview: {
      organicTraffic: 4250,
      organicTrafficChange: 6.2,
      keywordsRanked: 187,
      keywordsChange: 4.8,
      backlinks: 342,
      backlinksChange: 3.5,
      averagePageSpeed: 75,
      pageSpeedChange: 2.1,
    },
    topKeywords: [
      {
        keyword: "water treatment chemicals",
        volume: 1200,
        position: 8,
        previousPosition: 12,
        change: 4,
        difficulty: 65,
        traffic: 120,
        intent: "commercial",
        cpc: 3.45,
        competition: 0.72,
      },
      {
        keyword: "film forming amines",
        volume: 320,
        position: 3,
        previousPosition: 5,
        change: 2,
        difficulty: 45,
        traffic: 85,
        intent: "informational",
        cpc: 2.1,
        competition: 0.45,
      },
      {
        keyword: "cooling tower chemicals",
        volume: 880,
        position: 15,
        previousPosition: 14,
        change: -1,
        difficulty: 58,
        traffic: 42,
        intent: "commercial",
        cpc: 4.2,
        competition: 0.68,
      },
      {
        keyword: "boiler water treatment",
        volume: 1500,
        position: 22,
        previousPosition: 28,
        change: 6,
        difficulty: 72,
        traffic: 35,
        intent: "informational",
        cpc: 3.85,
        competition: 0.75,
      },
      {
        keyword: "mexel 432",
        volume: 90,
        position: 1,
        previousPosition: 1,
        change: 0,
        difficulty: 12,
        traffic: 82,
        intent: "navigational",
        cpc: 1.2,
        competition: 0.15,
      },
      {
        keyword: "energy efficient water treatment",
        volume: 210,
        position: 12,
        previousPosition: 18,
        change: 6,
        difficulty: 54,
        traffic: 15,
        intent: "informational",
        cpc: 2.75,
        competition: 0.52,
      },
      {
        keyword: "industrial water treatment south africa",
        volume: 170,
        position: 5,
        previousPosition: 9,
        change: 4,
        difficulty: 38,
        traffic: 28,
        intent: "commercial",
        cpc: 3.1,
        competition: 0.48,
      },
    ],
    topPages: [
      {
        url: "https://www.mexelenergysustain.com/products/mexel-432",
        title: "Mexel 432 - Film Forming Amine Technology",
        visits: 850,
        bounceRate: 32,
        avgTimeOnPage: 145,
        organicTraffic: 620,
        keywordsRanked: 18,
        conversionRate: 3.2,
        pageSpeed: { mobile: 72, desktop: 88 },
        wordCount: 1250,
      },
      {
        url: "https://www.mexelenergysustain.com/solutions/cooling-tower-treatment",
        title: "Cooling Tower Water Treatment Solutions",
        visits: 720,
        bounceRate: 41,
        avgTimeOnPage: 98,
        organicTraffic: 480,
        keywordsRanked: 24,
        conversionRate: 2.1,
        pageSpeed: { mobile: 65, desktop: 82 },
        wordCount: 980,
      },
      {
        url: "https://www.mexelenergysustain.com/blog/film-forming-amines-vs-traditional",
        title: "Film Forming Amines vs Traditional Chemicals: A Comparison",
        visits: 540,
        bounceRate: 28,
        avgTimeOnPage: 203,
        organicTraffic: 410,
        keywordsRanked: 12,
        conversionRate: 1.8,
        pageSpeed: { mobile: 78, desktop: 92 },
        wordCount: 1850,
      },
      {
        url: "https://www.mexelenergysustain.com/case-studies/power-plant-efficiency",
        title: "Case Study: Improving Power Plant Efficiency with Mexel",
        visits: 480,
        bounceRate: 22,
        avgTimeOnPage: 245,
        organicTraffic: 320,
        keywordsRanked: 9,
        conversionRate: 4.5,
        pageSpeed: { mobile: 81, desktop: 94 },
        wordCount: 2100,
      },
      {
        url: "https://www.mexelenergysustain.com/solutions/boiler-treatment",
        title: "Boiler Water Treatment Solutions",
        visits: 420,
        bounceRate: 38,
        avgTimeOnPage: 112,
        organicTraffic: 290,
        keywordsRanked: 15,
        conversionRate: 2.4,
        pageSpeed: { mobile: 68, desktop: 85 },
        wordCount: 1050,
      },
    ],
    trafficTrend: [
      {
        date: "Jan",
        organicTraffic: 2800,
        directTraffic: 1200,
        referralTraffic: 800,
      },
      {
        date: "Feb",
        organicTraffic: 3100,
        directTraffic: 1300,
        referralTraffic: 850,
      },
      {
        date: "Mar",
        organicTraffic: 3400,
        directTraffic: 1400,
        referralTraffic: 900,
      },
      {
        date: "Apr",
        organicTraffic: 3200,
        directTraffic: 1350,
        referralTraffic: 880,
      },
      {
        date: "May",
        organicTraffic: 3600,
        directTraffic: 1450,
        referralTraffic: 920,
      },
      {
        date: "Jun",
        organicTraffic: 4000,
        directTraffic: 1500,
        referralTraffic: 950,
      },
      {
        date: "Jul",
        organicTraffic: 4250,
        directTraffic: 1550,
        referralTraffic: 980,
      },
    ],
    keywordPositions: [
      { position: "1-3", count: 12 },
      { position: "4-10", count: 28 },
      { position: "11-20", count: 45 },
      { position: "21-50", count: 72 },
      { position: "51-100", count: 30 },
    ],
    insights: [
      {
        type: "opportunity",
        title: 'Optimize for "industrial water treatment"',
        description:
          "This keyword has high search volume (2,400/month) and your site currently ranks #18. Improving to top 10 could bring ~200 more monthly visitors.",
        impact: "high",
        category: "keywords",
        actionable: true,
        recommendedAction:
          "Create a comprehensive guide on industrial water treatment with case studies.",
      },
      {
        type: "issue",
        title: "High bounce rate on product pages",
        description:
          "Product pages have a 45% bounce rate on average, which is 15% higher than industry standard. Consider improving page layout and call-to-actions.",
        impact: "medium",
        category: "user-experience",
        actionable: true,
        recommendedAction:
          "Add more engaging visuals, customer testimonials, and clearer CTAs.",
      },
      {
        type: "achievement",
        title: "Blog content performing well",
        description:
          "Your blog posts are generating 35% of organic traffic with good engagement metrics. Continue publishing comparison and case study content.",
        impact: "medium",
        category: "content",
        actionable: true,
        recommendedAction:
          "Increase blog publishing frequency to twice monthly.",
      },
      {
        type: "opportunity",
        title: "Missing meta descriptions",
        description:
          "8 important pages are missing meta descriptions, which may affect click-through rates from search results.",
        impact: "low",
        category: "technical",
        actionable: true,
        recommendedAction:
          "Add compelling meta descriptions to all pages missing them.",
      },
      {
        type: "issue",
        title: "Slow mobile page speed",
        description:
          "Mobile page speed scores average 68/100, which is below the recommended 75+. This may affect mobile rankings and user experience.",
        impact: "high",
        category: "technical",
        actionable: true,
        recommendedAction:
          "Optimize images, implement lazy loading, and minimize render-blocking resources.",
      },
      {
        type: "opportunity",
        title: "Competitor keyword gap",
        description:
          "Your competitors rank for 45 valuable keywords that your site doesn't currently target.",
        impact: "high",
        category: "keywords",
        actionable: true,
        recommendedAction:
          "Create content targeting the top 10 competitor keywords with highest potential.",
      },
    ],
    competitors: [
      {
        domain: "watertreatmentcompetitor.com",
        domainAuthority: 42,
        organicTraffic: 6800,
        organicKeywords: 320,
        backlinks: 580,
        sharedKeywords: 78,
        topKeywords: [
          {
            keyword: "industrial water treatment",
            volume: 2400,
            position: 4,
            previousPosition: 5,
            change: 1,
            difficulty: 68,
            traffic: 320,
          },
          {
            keyword: "cooling tower chemicals",
            volume: 880,
            position: 3,
            previousPosition: 3,
            change: 0,
            difficulty: 58,
            traffic: 210,
          },
        ],
        trafficTrend: [
          { date: "Jan", traffic: 5200 },
          { date: "Feb", traffic: 5400 },
          { date: "Mar", traffic: 5800 },
          { date: "Apr", traffic: 6100 },
          { date: "May", traffic: 6300 },
          { date: "Jun", traffic: 6500 },
          { date: "Jul", traffic: 6800 },
        ],
      },
      {
        domain: "chemicaltreatmentsolutions.com",
        domainAuthority: 38,
        organicTraffic: 5200,
        organicKeywords: 245,
        backlinks: 420,
        sharedKeywords: 65,
        topKeywords: [
          {
            keyword: "boiler water treatment chemicals",
            volume: 720,
            position: 2,
            previousPosition: 4,
            change: 2,
            difficulty: 62,
            traffic: 180,
          },
          {
            keyword: "water treatment for cooling towers",
            volume: 590,
            position: 5,
            previousPosition: 7,
            change: 2,
            difficulty: 55,
            traffic: 110,
          },
        ],
        trafficTrend: [
          { date: "Jan", traffic: 4100 },
          { date: "Feb", traffic: 4300 },
          { date: "Mar", traffic: 4500 },
          { date: "Apr", traffic: 4700 },
          { date: "May", traffic: 4900 },
          { date: "Jun", traffic: 5100 },
          { date: "Jul", traffic: 5200 },
        ],
      },
      {
        domain: "efficientwatertreatment.co.za",
        domainAuthority: 29,
        organicTraffic: 3800,
        organicKeywords: 165,
        backlinks: 290,
        sharedKeywords: 52,
        topKeywords: [
          {
            keyword: "water treatment south africa",
            volume: 480,
            position: 3,
            previousPosition: 5,
            change: 2,
            difficulty: 45,
            traffic: 95,
          },
          {
            keyword: "industrial cooling water treatment",
            volume: 320,
            position: 6,
            previousPosition: 8,
            change: 2,
            difficulty: 50,
            traffic: 65,
          },
        ],
        trafficTrend: [
          { date: "Jan", traffic: 3100 },
          { date: "Feb", traffic: 3200 },
          { date: "Mar", traffic: 3400 },
          { date: "Apr", traffic: 3500 },
          { date: "May", traffic: 3600 },
          { date: "Jun", traffic: 3700 },
          { date: "Jul", traffic: 3800 },
        ],
      },
    ],
    technicalIssues: [
      {
        type: "error",
        title: "Broken internal links",
        description:
          "12 internal links on your site are pointing to non-existent pages, creating a poor user experience and wasting crawl budget.",
        impact: "medium",
        affectedPages: 5,
        examples: [
          "https://www.mexelenergysustain.com/products/old-product",
          "https://www.mexelenergysustain.com/blog/draft-post",
        ],
        howToFix:
          "Update these links to point to relevant existing pages or create 301 redirects.",
      },
      {
        type: "warning",
        title: "Missing alt text on images",
        description:
          "24 images across your site are missing alt text, which affects accessibility and image SEO.",
        impact: "medium",
        affectedPages: 8,
        examples: [
          "https://www.mexelenergysustain.com/products/mexel-432",
          "https://www.mexelenergysustain.com/about-us",
        ],
        howToFix:
          "Add descriptive alt text to all images that accurately describes the image content.",
      },
      {
        type: "error",
        title: "Duplicate title tags",
        description:
          "3 pages have identical title tags, which can confuse search engines about which page to rank.",
        impact: "high",
        affectedPages: 3,
        examples: [
          "https://www.mexelenergysustain.com/services",
          "https://www.mexelenergysustain.com/solutions",
        ],
        howToFix:
          "Create unique, descriptive title tags for each page that accurately reflect the page content.",
      },
      {
        type: "warning",
        title: "Low word count on key pages",
        description:
          "5 important pages have less than 500 words, which may not provide enough content for search engines to understand the topic.",
        impact: "medium",
        affectedPages: 5,
        examples: [
          "https://www.mexelenergysustain.com/contact",
          "https://www.mexelenergysustain.com/services/maintenance",
        ],
        howToFix:
          "Expand content on these pages to at least 800 words with relevant, valuable information.",
      },
      {
        type: "info",
        title: "Missing structured data",
        description:
          "Your site could benefit from implementing structured data for products, services, and FAQ content.",
        impact: "low",
        affectedPages: 12,
        examples: [
          "https://www.mexelenergysustain.com/products/mexel-432",
          "https://www.mexelenergysustain.com/faq",
        ],
        howToFix:
          "Implement schema.org markup for products, services, and FAQ content to enhance search results appearance.",
      },
    ],
    contentSuggestions: [
      {
        url: "https://www.mexelenergysustain.com/products/mexel-432",
        title: "Mexel 432 - Film Forming Amine Technology",
        currentScore: 72,
        potentialScore: 92,
        missingKeywords: [
          "film forming amine benefits",
          "FFA technology",
          "cooling system efficiency",
        ],
        wordCountSuggestion: {
          current: 1250,
          recommended: 1800,
        },
        readabilityScore: 65,
        titleSuggestion:
          "Mexel 432: Revolutionary Film Forming Amine Technology for Industrial Cooling Systems",
        metaDescriptionSuggestion:
          "Discover how Mexel 432 Film Forming Amine technology can reduce energy consumption by 15% and extend equipment lifespan in industrial cooling systems. Learn more!",
      },
      {
        url: "https://www.mexelenergysustain.com/solutions/cooling-tower-treatment",
        title: "Cooling Tower Water Treatment Solutions",
        currentScore: 68,
        potentialScore: 85,
        missingKeywords: [
          "cooling tower efficiency",
          "industrial cooling tower maintenance",
          "reduce cooling tower costs",
        ],
        wordCountSuggestion: {
          current: 980,
          recommended: 1500,
        },
        readabilityScore: 72,
        titleSuggestion:
          "Industrial Cooling Tower Water Treatment Solutions: Boost Efficiency & Reduce Costs",
        metaDescriptionSuggestion:
          "Our cooling tower water treatment solutions help industrial facilities reduce maintenance costs, improve efficiency, and extend equipment life. Get a free assessment today!",
      },
      {
        url: "https://www.mexelenergysustain.com/solutions/boiler-treatment",
        title: "Boiler Water Treatment Solutions",
        currentScore: 65,
        potentialScore: 88,
        missingKeywords: [
          "industrial boiler efficiency",
          "boiler scale prevention",
          "boiler corrosion control",
        ],
        wordCountSuggestion: {
          current: 1050,
          recommended: 1600,
        },
        readabilityScore: 68,
        titleSuggestion:
          "Industrial Boiler Water Treatment: Prevent Scale & Corrosion While Improving Efficiency",
        metaDescriptionSuggestion:
          "Our specialized boiler water treatment solutions prevent scale buildup, control corrosion, and improve energy efficiency in industrial boiler systems. Learn more!",
      },
    ],
    keywordOpportunities: [
      {
        keyword: "industrial water treatment solutions",
        volume: 880,
        position: 12,
        previousPosition: 15,
        change: 3,
        difficulty: 62,
        traffic: 45,
        intent: "commercial",
        cpc: 4.1,
        competition: 0.65,
      },
      {
        keyword: "cooling tower chemical treatment",
        volume: 590,
        position: 18,
        previousPosition: 22,
        change: 4,
        difficulty: 58,
        traffic: 28,
        intent: "commercial",
        cpc: 3.75,
        competition: 0.6,
      },
      {
        keyword: "energy efficient water treatment",
        volume: 320,
        position: 9,
        previousPosition: 14,
        change: 5,
        difficulty: 52,
        traffic: 35,
        intent: "informational",
        cpc: 2.9,
        competition: 0.55,
      },
      {
        keyword: "film forming amines for boilers",
        volume: 210,
        position: 4,
        previousPosition: 7,
        change: 3,
        difficulty: 45,
        traffic: 42,
        intent: "informational",
        cpc: 2.45,
        competition: 0.48,
      },
      {
        keyword: "industrial cooling water efficiency",
        volume: 170,
        position: 11,
        previousPosition: 16,
        change: 5,
        difficulty: 50,
        traffic: 18,
        intent: "commercial",
        cpc: 3.2,
        competition: 0.52,
      },
    ],
    seoHealth: {
      overall: 72,
      technical: 68,
      content: 78,
      backlinks: 65,
      onPage: 75,
    },
    historicalData: {
      availablePeriods: historicalPeriods,
      metrics: historicalMetrics,
      keywordTrends: keywordTrends,
      pageTrends: pageTrends,
    },
    lastUpdated: new Date().toISOString(),
  };
}
