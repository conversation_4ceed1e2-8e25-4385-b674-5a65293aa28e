import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Checkbox,
  FormControlLabel,
  Alert,
  Tab,
  Tabs,
} from "@mui/material";
import {
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
} from "@mui/icons-material";
import OverviewCards from "./OverviewCards";
import TabsNavigation from "./TabsNavigation";
import OverviewTab from "./tabs/OverviewTab";
import KeywordsTab from "./tabs/KeywordsTab";
import PagesTab from "./tabs/PagesTab";
import InsightsTab from "./tabs/InsightsTab";
import CompetitorsTab from "./tabs/CompetitorsTab";
import TechnicalSEOTab from "./tabs/TechnicalSEOTab";
import ContentOptimizationTab from "./tabs/ContentOptimizationTab";
import HistoricalAnalysis from "./HistoricalAnalysis";
import AIRecommendations from "./AIRecommendations";
import TaskManagement from "./TaskManagement";
import EmailReports from "./EmailReports";
import { SEOInsightsData } from "./types";
import { EmailReport, ReportHistoryItem } from "./EmailReports/types";
import { Recommendation } from "../../services/AIRecommendationService"; // Corrected path
import { Task } from "./TaskManagement/types";
import {
  SEODataService,
  SEODataServiceConfig,
} from "../../services/SEODataService"; // Import SEODataServiceConfig
import { AIRecommendationService } from "../../services/AIRecommendationService"; // Import AIRecommendationService
import { TaskService } from "../../services/TaskService"; // Import TaskService
import { EmailReportService } from "../../services/EmailReportService"; // Import EmailReportService

/**
 * SEO Insights Dashboard Component
 *
 * Displays comprehensive SEO metrics, trends, and insights for the website
 */
const SEOInsightsDashboard = () => {
  // Initialize services
  const initialConfig: SEODataServiceConfig = { useMockData: true };
  const [config, setConfig] = React.useState(initialConfig);
  const [seoDataService, setSeoDataService] = useState(
    () => new SEODataService(config)
  );
  const [aiRecommendationService] = useState(
    () => new AIRecommendationService()
  );
  const [taskService] = useState(() => new TaskService());
  const [emailReportService] = useState(() => new EmailReportService());

  const [data, setData] = React.useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = React.useState(null);
  const [activeTab, setActiveTab] = useState(0);

  // State for AI Recommendations
  const [recommendations, setRecommendations] = React.useState<any[]>([]);
  const [loadingRecommendations, setLoadingRecommendations] = useState(false);

  // State for Task Management
  const [tasks, setTasks] = React.useState<any[]>([]);
  const [loadingTasks, setLoadingTasks] = useState(false);

  // State for Email Reporting
  const [reports, setReports] = React.useState<any[]>([]);
  const [reportHistory, setReportHistory] = React.useState<any[]>([]);
  // Remove unused state or add comments to indicate it will be used in future
  // const [selectedReport, setSelectedReport] = React.useState(null);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [loadingReports, setLoadingReports] = useState(false); // Added missing state

  // Fetch SEO data
  const fetchData = async (service: SEODataService | null = seoDataService) => {
    if (!service) return;

    setLoading(true);
    try {
      const seoData = await service.getAllSEOData();
      setData(seoData);
      setError(null);
    } catch (err) {
      setError("Failed to load SEO data. Please try again later.");
      console.error("Error fetching SEO data:", err);
    } finally {
      setLoading(false);
    }
  };

  // Effect to re-initialize SEODataService when config changes
  useEffect(() => {
    setSeoDataService(new SEODataService(config));
  }, [config]);

  // Fetch data on component mount
  useEffect(() => {
    if (seoDataService) {
      fetchData(seoDataService);
    }
  }, [seoDataService]); // eslint-disable-line react-hooks/exhaustive-deps

  // Handle refresh button click
  const handleRefresh = () => {
    fetchData();
  };

  // Handle tab change
  const handleTabChange = (_event: any, newValue: string | number) => {
    const tabValue =
      typeof newValue === "string" ? parseInt(newValue, 10) : newValue;
    setActiveTab(tabValue);

    // Load recommendations when navigating to AI Recommendations tab
    if (
      tabValue === 8 &&
      recommendations.length === 0 &&
      !loadingRecommendations
    ) {
      generateRecommendations();
    }

    // Load tasks when navigating to Tasks tab
    if (tabValue === 9 && tasks.length === 0 && !loadingTasks) {
      loadTasks();
    }

    // Load email reports when navigating to Email Reports tab
    if (tabValue === 10 && reports.length === 0 && !loadingReports) {
      loadReports();
    }
  };

  // Generate AI recommendations
  const generateRecommendations = async () => {
    if (!data) return;

    setLoadingRecommendations(true);
    try {
      const newRecommendations =
        await aiRecommendationService.generateRecommendations(data);
      setRecommendations(newRecommendations);
    } catch (error) {
      console.error("Error generating recommendations:", error);
    } finally {
      setLoadingRecommendations(false);
    }
  };

  // Handle recommendation status change
  const handleRecommendationStatusChange = (id: string, status: any) => {
    setRecommendations((prevRecommendations) =>
      prevRecommendations.map((recommendation) =>
        recommendation.id === id
          ? { ...recommendation, status, updatedAt: new Date().toISOString() }
          : recommendation
      )
    );
  };

  // Load tasks
  const loadTasks = async () => {
    setLoadingTasks(true);
    try {
      let loadedTasks = await taskService.getTasks();

      // If no tasks are found, generate mock tasks
      if (loadedTasks.length === 0) {
        loadedTasks = taskService.generateMockTasks(15);

        // Save mock tasks
        for (const task of loadedTasks) {
          await taskService.createTask({
            title: task.title,
            description: task.description,
            category: task.category,
            priority: task.priority,
            status: task.status,
            dueDate: task.dueDate,
            assignee: task.assignee,
            tags: task.tags,
            estimatedHours: task.estimatedHours,
            relatedUrls: task.relatedUrls,
          });
        }

        // Reload tasks
        loadedTasks = await taskService.getTasks();
      }

      setTasks(loadedTasks);
    } catch (error) {
      console.error("Error loading tasks:", error);
    } finally {
      setLoadingTasks(false);
    }
  };

  // Handle task create
  const handleTaskCreate = async (
    task: Omit<any, "id" | "createdAt" | "updatedAt">
  ) => {
    try {
      const newTask = await taskService.createTask(task);
      setTasks((prevTasks) => [...prevTasks, newTask]);
    } catch (error) {
      console.error("Error creating task:", error);
    }
  };

  // Handle task update
  const handleTaskUpdate = async (taskId: string, updates: Partial<any>) => {
    try {
      const updatedTask = await taskService.updateTask(taskId, updates);
      setTasks((prevTasks) =>
        prevTasks.map((task) => (task.id === taskId ? updatedTask : task))
      );
    } catch (error) {
      console.error("Error updating task:", error);
    }
  };

  // Handle task delete
  const handleTaskDelete = async (taskId: string) => {
    try {
      await taskService.deleteTask(taskId);
      setTasks((prevTasks) => prevTasks.filter((task) => task.id !== taskId));
    } catch (error) {
      console.error("Error deleting task:", error);
    }
  };

  // Handle task status change
  const handleTaskStatusChange = async (taskId: string, status: any) => {
    try {
      const updatedTask = await taskService.changeTaskStatus(taskId, status);
      setTasks((prevTasks) =>
        prevTasks.map((task) => (task.id === taskId ? updatedTask : task))
      );
    } catch (error) {
      console.error("Error changing task status:", error);
    }
  };

  // Handle task assign
  const handleTaskAssign = async (taskId: string, assignee: string) => {
    try {
      const updatedTask = await taskService.assignTask(taskId, assignee);
      setTasks((prevTasks) =>
        prevTasks.map((task) => (task.id === taskId ? updatedTask : task))
      );
    } catch (error) {
      console.error("Error assigning task:", error);
    }
  };

  // Load email reports
  const loadReports = async () => {
    setLoadingReports(true);
    try {
      let loadedReports = await emailReportService.getReports();
      let loadedHistory = await emailReportService.getReportHistory();

      // If no reports are found, generate mock reports
      if (loadedReports.length === 0) {
        loadedReports = emailReportService.generateMockReports(5);

        // Save mock reports
        for (const report of loadedReports) {
          await emailReportService.createReport({
            name: report.name,
            description: report.description,
            frequency: report.frequency,
            format: report.format,
            recipients: report.recipients,
            sections: report.sections,
            schedule: report.schedule,
            customizations: report.customizations,
          });
        }

        // Generate mock history
        loadedHistory =
          emailReportService.generateMockReportHistory(loadedReports);

        // Reload reports and history
        loadedReports = await emailReportService.getReports();
        loadedHistory = await emailReportService.getReportHistory();
      }

      setReports(loadedReports);
      setReportHistory(loadedHistory);
    } catch (error) {
      console.error("Error loading reports:", error);
    } finally {
      setLoadingReports(false);
    }
  };

  // Handle create report
  const handleCreateReport = async (
    report: Omit<EmailReport, "id" | "createdAt" | "updatedAt" | "status">
  ) => {
    try {
      const newReport = await emailReportService.createReport(report);
      setReports((prevReports) => [...prevReports, newReport]);
    } catch (error) {
      console.error("Error creating report:", error);
    }
  };

  // Handle update report
  const handleUpdateReport = async (
    reportId: string,
    updates: Partial<EmailReport>
  ) => {
    try {
      const updatedReport = await emailReportService.updateReport(
        reportId,
        updates
      );
      setReports((prevReports) =>
        prevReports.map((report) =>
          report.id === reportId ? updatedReport : report
        )
      );
    } catch (error) {
      console.error("Error updating report:", error);
    }
  };

  // Handle delete report
  const handleDeleteReport = async (reportId: string) => {
    try {
      await emailReportService.deleteReport(reportId);
      setReports((prevReports) =>
        prevReports.filter((report) => report.id !== reportId)
      );
    } catch (error) {
      console.error("Error deleting report:", error);
    }
  };

  // Handle send report now
  const handleSendReportNow = async (reportId: string) => {
    try {
      await emailReportService.sendReportNow(reportId);

      // Reload report history
      const loadedHistory = await emailReportService.getReportHistory();
      setReportHistory(loadedHistory);

      // Update report in state
      const updatedReport = await emailReportService.getReport(reportId);
      setReports((prevReports) =>
        prevReports.map((report) =>
          report.id === reportId ? updatedReport : report
        )
      );
    } catch (error) {
      console.error("Error sending report:", error);
    }
  };

  // Handle download report
  const handleDownloadReport = async (reportId: string, format: any) => {
    try {
      const downloadUrl = await emailReportService.downloadReport(
        reportId,
        format
      );
      window.open(downloadUrl, "_blank");
    } catch (error) {
      console.error("Error downloading report:", error);
    }
  };

  // Handle settings dialog open
  const handleOpenSettings = () => {
    setSettingsOpen(true);
  };

  // Handle settings dialog close
  const handleCloseSettings = () => {
    setSettingsOpen(false);
  };

  const handleSaveSettings = () => {
    // Add logic to save settings if needed, then close
    console.log("Settings saved (mock)");
    setSettingsOpen(false);
  };

  // Handle config change
  const handleConfigChange = (path: string, value: any) => {
    setConfig((prevConfig) => {
      const newConfig = { ...prevConfig };

      // Split the path into parts
      const parts = path.split(".");

      // Navigate to the correct part of the config
      let current: any = newConfig;
      for (let i = 0; i < parts.length - 1; i++) {
        if (!current[parts[i]]) {
          current[parts[i]] = {};
        }
        current = current[parts[i]];
      }

      // Set the value
      current[parts[parts.length - 1]] = value;

      return newConfig;
    });
  };

  // Loading state
  if (loading && !data) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  // No data state
  if (!data) {
    return (
      <Alert severity="info" sx={{ mb: 2 }}>
        No SEO data available.
      </Alert>
    );
  }

  // Define our own TabPanel component
  interface TabPanelProps {
    children?: any;
    index: number;
    value: number;
  }

  // Simple TabPanel component to replace the one from @mui/lab
  const TabPanel = (props: TabPanelProps) => {
    const { children, value, index, ...other } = props;

    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`seo-tabpanel-${index}`}
        aria-labelledby={`seo-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
      </div>
    );
  };

  return (
    <Box sx={{ width: "100%" }}>
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography variant="h5" component="h1">
          SEO Insights Dashboard
        </Typography>
        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            startIcon={<SettingsIcon />}
            onClick={handleOpenSettings}
            variant="outlined"
            size="small"
          >
            API Settings
          </Button>
          <Button
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={loading}
            variant="outlined"
            size="small"
          >
            {loading ? "Refreshing..." : "Refresh Data"}
          </Button>
        </Box>
      </Box>

      {/* Data Source Indicator */}
      <Box
        sx={{
          mb: 3,
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-end",
        }}
      >
        <Typography variant="caption" color="textSecondary">
          Data Source:{" "}
          {config.useMockData
            ? "Mock Data"
            : (config.googleSearchConsole?.apiKey
                ? "Google Search Console, "
                : "") +
              (config.googleAnalytics?.apiKey ? "Google Analytics, " : "") +
              (config.semrush?.apiKey ? "SEMrush, " : "") +
              (config.ahrefs?.apiKey ? "Ahrefs" : "")}
          {!config.useMockData &&
            !config.googleSearchConsole?.apiKey &&
            !config.googleAnalytics?.apiKey &&
            !config.semrush?.apiKey &&
            !config.ahrefs?.apiKey &&
            "No API configured, using Mock Data"}
        </Typography>
      </Box>

      {/* Overview Cards */}
      <OverviewCards data={data} loading={loading} />

      {/* Tabs Navigation */}
      <TabsNavigation tabValue={activeTab} handleTabChange={handleTabChange} />

      {/* Tab Content */}
      <Tabs value={activeTab} onChange={handleTabChange}>
        <Tab label="Overview" value={0} />
        <Tab label="Keywords" value={1} />
        <Tab label="Pages" value={2} />
        <Tab label="Insights" value={3} />
        <Tab label="Competitors" value={4} />
        <Tab label="Technical SEO" value={5} />
        <Tab label="Content Optimization" value={6} />
        <Tab label="Historical Analysis" value={7} />
        <Tab label="AI Recommendations" value={8} />
        <Tab label="Task Management" value={9} />
        <Tab label="Email Reports" value={10} />
      </Tabs>

      {/* Tab Panels */}
      <TabPanel value={activeTab} index={0}>
        <OverviewTab data={data} value={activeTab} index={0} />
      </TabPanel>
      <TabPanel value={activeTab} index={1}>
        <KeywordsTab data={data} value={activeTab} index={1} />
      </TabPanel>
      <TabPanel value={activeTab} index={2}>
        <PagesTab data={data} value={activeTab} index={2} />
      </TabPanel>
      <TabPanel value={activeTab} index={3}>
        <InsightsTab data={data} value={activeTab} index={3} />
      </TabPanel>
      <TabPanel value={activeTab} index={4}>
        <CompetitorsTab data={data} value={activeTab} index={4} />
      </TabPanel>
      <TabPanel value={activeTab} index={5}>
        <TechnicalSEOTab data={data} value={activeTab} index={5} />
      </TabPanel>
      <TabPanel value={activeTab} index={6}>
        <ContentOptimizationTab data={data} value={activeTab} index={6} />
      </TabPanel>
      <TabPanel value={activeTab} index={7}>
        <HistoricalAnalysis data={data} />
      </TabPanel>
      <TabPanel value={activeTab} index={8}>
        <AIRecommendations
          recommendations={recommendations}
          onStatusChange={handleRecommendationStatusChange}
          onRefresh={generateRecommendations}
          isLoading={loadingRecommendations}
        />
      </TabPanel>
      <TabPanel value={activeTab} index={9}>
        <TaskManagement
          tasks={tasks}
          onTaskCreate={handleTaskCreate}
          onTaskUpdate={handleTaskUpdate}
          onTaskDelete={handleTaskDelete}
          onTaskStatusChange={handleTaskStatusChange}
          onTaskAssign={handleTaskAssign}
          isLoading={loadingTasks}
        />
      </TabPanel>
      <TabPanel value={activeTab} index={10}>
        <EmailReports
          reports={reports}
          reportHistory={reportHistory}
          onCreateReport={handleCreateReport}
          onUpdateReport={handleUpdateReport}
          onDeleteReport={handleDeleteReport}
          onSendReportNow={handleSendReportNow}
          onDownloadReport={handleDownloadReport}
          isLoading={loadingReports}
        />
      </TabPanel>

      {/* Footer */}
      <Box sx={{ mt: 2, display: "flex", justifyContent: "flex-end" }}>
        <Typography variant="caption" color="textSecondary">
          Last updated: {new Date(data.lastUpdated).toLocaleString()}
        </Typography>
      </Box>

      {/* Settings Dialog */}
      <Dialog
        open={settingsOpen}
        onClose={handleCloseSettings}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>SEO Data API Settings</DialogTitle>
        <DialogContent dividers>
          <Box sx={{ mb: 3 }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={config.useMockData}
                  onChange={(e) =>
                    handleConfigChange("useMockData", e.target.checked)
                  }
                />
              }
              label="Use Mock Data (for development and testing)"
            />
            <Typography
              variant="caption"
              color="text.secondary"
              display="block"
            >
              When enabled, mock data will be used instead of real API data.
              Useful for development and testing.
            </Typography>
          </Box>

          <Typography variant="h6" gutterBottom>
            Google Search Console API
          </Typography>
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <TextField
                label="API Key"
                fullWidth
                value={config.googleSearchConsole?.apiKey || ""}
                onChange={(e) =>
                  handleConfigChange(
                    "googleSearchConsole.apiKey",
                    e.target.value
                  )
                }
                placeholder="Enter your Google Search Console API key"
                disabled={config.useMockData}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Site URL"
                fullWidth
                value={config.googleSearchConsole?.siteUrl || ""}
                onChange={(e) =>
                  handleConfigChange(
                    "googleSearchConsole.siteUrl",
                    e.target.value
                  )
                }
                placeholder="Enter your website URL (e.g., https://www.example.com)"
                disabled={config.useMockData}
              />
            </Grid>
          </Grid>

          <Typography variant="h6" gutterBottom>
            Google Analytics API
          </Typography>
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <TextField
                label="API Key"
                fullWidth
                value={config.googleAnalytics?.apiKey || ""}
                onChange={(e) =>
                  handleConfigChange("googleAnalytics.apiKey", e.target.value)
                }
                placeholder="Enter your Google Analytics API key"
                disabled={config.useMockData}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Property ID"
                fullWidth
                value={config.googleAnalytics?.propertyId || ""}
                onChange={(e) =>
                  handleConfigChange(
                    "googleAnalytics.propertyId",
                    e.target.value
                  )
                }
                placeholder="Enter your Google Analytics Property ID"
                disabled={config.useMockData}
              />
            </Grid>
          </Grid>

          <Typography variant="h6" gutterBottom>
            SEMrush API
          </Typography>
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <TextField
                label="API Key"
                fullWidth
                value={config.semrush?.apiKey || ""}
                onChange={(e) =>
                  handleConfigChange("semrush.apiKey", e.target.value)
                }
                placeholder="Enter your SEMrush API key"
                disabled={config.useMockData}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Domain"
                fullWidth
                value={config.semrush?.domain || ""}
                onChange={(e) =>
                  handleConfigChange("semrush.domain", e.target.value)
                }
                placeholder="Enter your domain (e.g., example.com)"
                disabled={config.useMockData}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth disabled={config.useMockData}>
                <InputLabel id="semrush-database-label">Database</InputLabel>
                <Select
                  labelId="semrush-database-label"
                  value={config.semrush?.database || "us"}
                  label="Database"
                  onChange={(e) =>
                    handleConfigChange("semrush.database", e.target.value)
                  }
                >
                  <MenuItem value="us">United States (us)</MenuItem>
                  <MenuItem value="uk">United Kingdom (uk)</MenuItem>
                  <MenuItem value="ca">Canada (ca)</MenuItem>
                  <MenuItem value="au">Australia (au)</MenuItem>
                  <MenuItem value="za">South Africa (za)</MenuItem>
                  <MenuItem value="de">Germany (de)</MenuItem>
                  <MenuItem value="fr">France (fr)</MenuItem>
                  <MenuItem value="es">Spain (es)</MenuItem>
                  <MenuItem value="it">Italy (it)</MenuItem>
                  <MenuItem value="br">Brazil (br)</MenuItem>
                  <MenuItem value="ru">Russia (ru)</MenuItem>
                  <MenuItem value="jp">Japan (jp)</MenuItem>
                  <MenuItem value="in">India (in)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          <Typography variant="h6" gutterBottom>
            Ahrefs API
          </Typography>
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <TextField
                label="API Key"
                fullWidth
                value={config.ahrefs?.apiKey || ""}
                onChange={(e) =>
                  handleConfigChange("ahrefs.apiKey", e.target.value)
                }
                placeholder="Enter your Ahrefs API key"
                disabled={config.useMockData}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Domain"
                fullWidth
                value={config.ahrefs?.domain || ""}
                onChange={(e) =>
                  handleConfigChange("ahrefs.domain", e.target.value)
                }
                placeholder="Enter your domain (e.g., example.com)"
                disabled={config.useMockData}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseSettings}>Cancel</Button>
          <Button
            onClick={handleSaveSettings}
            variant="contained"
            color="primary"
          >
            Save Settings
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SEOInsightsDashboard;
