import {
    Refresh as RefreshIcon,
    Send as SendIcon,
    Settings as SettingsIcon,
} from "@mui/icons-material";
import LightbulbIcon from "@mui/icons-material/Lightbulb";
import {
    Autocomplete,
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    CircularProgress,
    Divider,
    FormControl,
    FormControlLabel,
    Grid,
    InputLabel,
    MenuItem,
    Paper,
    Select,
    Slider,
    Switch,
    TextField,
    Typography
} from "@mui/material";
import React from "react";

interface EmailGeneratorProps {
  onGenerate: (prompt: string, recipient: string) => void;
  isGenerating: boolean;
}

const industries = [
  "Water Treatment",
  "Cooling Systems",
  "Boiler Systems",
  "Industrial Manufacturing",
  "Power Generation",
  "Oil & Gas",
  "Food & Beverage",
  "Pharmaceuticals",
  "Municipal Water",
  "Wastewater Treatment",
];

const tones = [
  "Professional",
  "Friendly",
  "Technical",
  "Persuasive",
  "Informative",
  "Formal",
  "Conversational",
];

const keywords = [
  "water treatment",
  "cooling tower",
  "boiler",
  "chemical dosing",
  "corrosion",
  "fouling",
  "energy efficiency",
  "maintenance",
  "cost reduction",
  "environmental compliance",
  "water quality",
  "film forming amine",
  "FFA",
  "Mexel 432",
  "surfactant",
  "online dosing",
  "real-time analytics",
  "smart dosing",
  "eco-efficient",
  "ROI",
];

export const EmailGenerator = ({
  onGenerate,
  isGenerating,
}: {
  onGenerate: any;
  isGenerating: boolean;
}) => {
  const [recipient, setRecipient] = React.useState("");
  const [prompt, setPrompt] = React.useState("");
  const [industry, setIndustry] = React.useState("");
  const [tone, setTone] = React.useState("Professional");
  const [selectedKeywords, setSelectedKeywords] = React.useState<any[]>([]);
  const [showAdvanced, setShowAdvanced] = React.useState(false);
  const [creativity, setCreativity] = React.useState(0.7);
  const [emailLength, setEmailLength] = React.useState(2); // 1=Short, 2=Medium, 3=Long

  const handleGenerateClick = () => {
    if (!recipient || !prompt) return;

    // Combine prompt with selected options
    const fullPrompt = `
      Industry: ${industry}
      Tone: ${tone}
      Keywords: ${selectedKeywords.join(", ")}
      Main points: ${prompt}
    `;

    onGenerate(fullPrompt, recipient);
  };

  const handleResetClick = () => {
    setRecipient("");
    setPrompt("");
    setIndustry("");
    setTone("Professional");
    setSelectedKeywords([]);
    setCreativity(0.7);
    setEmailLength(2);
  };

  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Generate New Email
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              Provide information about the recipient and what you want to
              communicate. Our AI will generate a personalized email template.
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  label="Recipient Email"
                  fullWidth
                  value={recipient}
                  onChange={(e) => setRecipient(e.target.value as any)}
                  required
                  placeholder="e.g., <EMAIL>"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel id="industry-label">Industry</InputLabel>
                  <Select
                    labelId="industry-label"
                    value={industry}
                    label="Industry"
                    onChange={(e) => setIndustry(e.target.value as any)}
                  >
                    {industries.map((ind) => (
                      <MenuItem key={ind} value={ind}>
                        {ind}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel id="tone-label">Tone</InputLabel>
                  <Select
                    labelId="tone-label"
                    value={tone}
                    label="Tone"
                    onChange={(e) => setTone(e.target.value as any)}
                  >
                    {tones.map((t) => (
                      <MenuItem key={t} value={t}>
                        {t}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <Autocomplete
                  multiple
                  id="keywords"
                  options={keywords}
                  value={selectedKeywords}
                  onChange={(_, newValue) => setSelectedKeywords(newValue)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Keywords"
                      placeholder="Select keywords"
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        label={option}
                        {...getTagProps({ index })}
                        size="small"
                      />
                    ))
                  }
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  label="What do you want to communicate?"
                  multiline
                  rows={4}
                  fullWidth
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  required
                  placeholder="e.g., I want to introduce our water treatment solutions to a facility manager at a manufacturing plant who is looking to reduce energy costs and improve equipment lifespan."
                />
              </Grid>

              <Grid item xs={12}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <FormControlLabel
                    control={
                      <Switch
                        checked={showAdvanced}
                        onChange={(e) => setShowAdvanced(e.target.checked as any)}
                      />
                    }
                    label="Advanced Options"
                  />

                  <Box sx={{ display: "flex", gap: 2 }}>
                    <Button
                      variant="outlined"
                      startIcon={<RefreshIcon />}
                      onClick={handleResetClick}
                    >
                      Reset
                    </Button>

                    <Button
                      variant="contained"
                      startIcon={
                        isGenerating ? (
                          <CircularProgress size={20} color="inherit" />
                        ) : (
                          <SendIcon />
                        )
                      }
                      onClick={handleGenerateClick}
                      disabled={isGenerating || !recipient || !prompt}
                    >
                      {isGenerating ? "Generating..." : "Generate Email"}
                    </Button>
                  </Box>
                </Box>
              </Grid>

              {showAdvanced && (
                <>
                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }}>
                      <Chip label="Advanced Options" size="small" />
                    </Divider>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography gutterBottom>Creativity Level</Typography>
                    <Slider
                      value={creativity}
                      min={0.1}
                      max={1}
                      step={0.1}
                      onChange={(_, newValue) =>
                        setCreativity(newValue as number)
                      }
                      valueLabelDisplay="auto"
                      valueLabelFormat={(value) =>
                        value <= 0.3
                          ? "Conservative"
                          : value <= 0.7
                          ? "Balanced"
                          : "Creative"
                      }
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography gutterBottom>Email Length</Typography>
                    <Slider
                      value={emailLength}
                      min={1}
                      max={3}
                      step={1}
                      onChange={(_, newValue) =>
                        setEmailLength(newValue as number)
                      }
                      valueLabelDisplay="auto"
                      valueLabelFormat={(value) =>
                        value === 1 ? "Short" : value === 2 ? "Medium" : "Long"
                      }
                      marks={[
                        { value: 1, label: "Short" },
                        { value: 2, label: "Medium" },
                        { value: 3, label: "Long" },
                      ]}
                    />
                  </Grid>
                </>
              )}
            </Grid>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <LightbulbIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">Tips for Effective Emails</Typography>
              </Box>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="body2" paragraph>
                <strong>1. Be specific about the recipient's needs</strong> -
                The more details you provide about their industry and
                challenges, the more personalized the email will be.
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>2. Include relevant keywords</strong> - Select keywords
                that resonate with your recipient's industry and challenges.
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>3. Choose the right tone</strong> - Match your tone to
                your relationship with the recipient and the purpose of your
                email.
              </Typography>
              <Typography variant="body2">
                <strong>4. Keep it concise</strong> - Focus on the most
                important points you want to communicate.
              </Typography>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <SettingsIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Email Generation Settings</Typography>
              </Box>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="body2" paragraph>
                Current AI Model: <strong>DeepSeek-chat</strong>
              </Typography>
              <Typography variant="body2" paragraph>
                Default template includes: Introduction, value proposition,
                benefits, call to action, and signature.
              </Typography>
              <Typography variant="body2">
                All generated emails are saved to your template library for
                future use and editing.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EmailGenerator;
