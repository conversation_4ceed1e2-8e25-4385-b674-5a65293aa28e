import React from "react";
import { FC, ReactNode, ChangeEvent, MouseEvent } from "react";
import { TabPanel, a11yProps } from "../common/TabsNavigation";
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  Divider,
  TextField,
  IconButton,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  FilterList as FilterListIcon,
  Search as SearchIcon,
  Sort as SortIcon,
} from "@mui/icons-material";
import EmailPreview from "./EmailPreview";
import EmailGenerator from "./EmailGenerator";
import { IEmailTemplate } from "../../types/email";
import { sampleEmailTemplates } from "../../data/sampleEmailTemplates";

export const EmailDashboard = () => {
  const [tabValue, setTabValue] = React.useState(0);
  const [selectedTemplate, setSelectedTemplate] =
    React.useState(null);
  const [templateCategory, setTemplateCategory] = React.useState("all");
  const [searchTerm, setSearchTerm] = React.useState("");
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [templates, setTemplates] =
    React.useState(sampleEmailTemplates);

  const handleTabChange = (_event: any, newValue: number) => {
    setTabValue(newValue);
  };

  const handleTemplateSelect = (template: IEmailTemplate) => {
    setSelectedTemplate(template);
    setTabValue(1); // Switch to preview tab
  };

  const handleCategoryChange = (event: SelectChangeEvent) => {
    setTemplateCategory(event.target.value);
  };

  const handleSearchChange = (event: any) => {
    setSearchTerm(event.target.value);
  };

  const handleGenerateEmail = (prompt: string, recipient: string) => {
    setIsGenerating(true);

    // Simulate API call to generate email
    setTimeout(() => {
      const newTemplate: IEmailTemplate = {
        id: `generated-${Date.now()}`,
        name: `Generated Email for ${recipient}`,
        subject: `Proposal for ${recipient} - Mexel Water Treatment Solutions`,
        body: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; color: #333;">
            <p>Dear [Contact Name],</p>

            <p>Based on our analysis of your company's needs regarding ${prompt}, I'm pleased to present Mexel's innovative water treatment solutions that can address your specific challenges.</p>

            <p>Our Film-Forming Amine (FFA) technology has been proven to:</p>

            <ul>
              <li>Reduce energy consumption by up to 15%</li>
              <li>Extend equipment lifespan through superior corrosion protection</li>
              <li>Lower maintenance costs and minimize downtime</li>
              <li>Comply with environmental regulations while improving efficiency</li>
            </ul>

            <p>I'd welcome the opportunity to discuss how we can customize our solutions for your specific needs. Would you be available for a brief call next week?</p>

            <p>Best regards,<br>
            Zola Mahlaza<br>
            Mexel Energy Sustain<br>
            <a href="mailto:<EMAIL>"><EMAIL></a></p>
          </div>
        `,
        metadata: {
          type: "generated",
          industry: prompt.toLowerCase().includes("cooling")
            ? "cooling systems"
            : "water treatment",
          tone: "professional",
          keywords: prompt.split(" ").filter((word) => word.length > 3),
          recipient: recipient,
        },
      };

      setTemplates([newTemplate, ...templates]);
      setSelectedTemplate(newTemplate);
      setTabValue(1); // Switch to preview tab
      setIsGenerating(false);
    }, 2000);
  };

  // Filter templates by category and search term
  const filteredTemplates = templates.filter((template) => {
    const matchesCategory =
      templateCategory === "all" ||
      template.metadata?.type === templateCategory;
    const matchesSearch =
      searchTerm === "" ||
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.body.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesCategory && matchesSearch;
  });

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Email Generation & Templates
      </Typography>

      <Paper sx={{ p: 3, mb: 4 }}>
        <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 2 }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 2,
            }}
          >
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label="email dashboard tabs"
            >
              <Tab label="Template Library" {...a11yProps(0)} />
              <Tab
                label="Email Preview"
                {...a11yProps(1)}
                disabled={!selectedTemplate}
              />
              <Tab label="Generate New Email" {...a11yProps(2)} />
            </Tabs>

            <Box sx={{ display: "flex", gap: 2 }}>
              {tabValue === 0 && (
                <>
                  <TextField
                    size="small"
                    placeholder="Search templates..."
                    value={searchTerm}
                    onChange={handleSearchChange}
                    InputProps={{
                      startAdornment: (
                        <SearchIcon
                          fontSize="small"
                          sx={{ mr: 1, opacity: 0.5 }}
                        />
                      ),
                    }}
                    sx={{ width: 200 }}
                  />

                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel id="template-category-label">
                      Category
                    </InputLabel>
                    <Select
                      labelId="template-category-label"
                      id="template-category"
                      value={templateCategory}
                      label="Category"
                      onChange={handleCategoryChange}
                    >
                      <MenuItem value="all">All Templates</MenuItem>
                      <MenuItem value="outreach">Outreach</MenuItem>
                      <MenuItem value="follow-up">Follow-up</MenuItem>
                      <MenuItem value="tender">Tender Response</MenuItem>
                      <MenuItem value="generated">Generated</MenuItem>
                    </Select>
                  </FormControl>

                  <Tooltip title="Refresh Templates">
                    <IconButton size="small">
                      <RefreshIcon />
                    </IconButton>
                  </Tooltip>
                </>
              )}
            </Box>
          </Box>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            {filteredTemplates.length === 0 ? (
              <Grid item xs={12}>
                <Typography
                  variant="body1"
                  color="textSecondary"
                  align="center"
                  sx={{ py: 4 }}
                >
                  No templates found matching your criteria.
                </Typography>
              </Grid>
            ) : (
              filteredTemplates.map((template) => (
                <Grid item xs={12} md={6} lg={4} key={template.id}>
                  <Card
                    variant="outlined"
                    sx={{
                      height: "100%",
                      display: "flex",
                      flexDirection: "column",
                    }}
                  >
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" component="h3" gutterBottom>
                        {template.name}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="textSecondary"
                        gutterBottom
                      >
                        Subject: {template.subject}
                      </Typography>

                      <Box
                        sx={{
                          display: "flex",
                          flexWrap: "wrap",
                          gap: 0.5,
                          mt: 1,
                          mb: 2,
                        }}
                      >
                        {template.metadata?.type && (
                          <Chip
                            label={template.metadata.type}
                            size="small"
                            variant="outlined"
                            color="primary"
                          />
                        )}
                        {template.metadata?.industry && (
                          <Chip
                            label={template.metadata.industry}
                            size="small"
                            variant="outlined"
                          />
                        )}
                        {template.metadata?.tone && (
                          <Chip
                            label={template.metadata.tone}
                            size="small"
                            variant="outlined"
                          />
                        )}
                      </Box>

                      <Divider sx={{ my: 1 }} />

                      <Typography
                        variant="body2"
                        color="textSecondary"
                        sx={{
                          height: "80px",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          display: "-webkit-box",
                          WebkitLineClamp: 4,
                          WebkitBoxOrient: "vertical",
                        }}
                      >
                        {template.body.replace(/<[^>]*>?/gm, " ")}
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button
                        size="small"
                        onClick={() => handleTemplateSelect(template)}
                      >
                        Preview
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {selectedTemplate && (
            <EmailPreview
              template={selectedTemplate}
              onEdit={() => console.log("Edit template:", selectedTemplate)}
              onSend={() => console.log("Send template:", selectedTemplate)}
              onCopy={() => console.log("Copy template:", selectedTemplate)}
              showControls={true}
              showMetadata={true}
              previewMode="full"
            />
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <EmailGenerator
            onGenerate={handleGenerateEmail}
            isGenerating={isGenerating}
          />
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default EmailDashboard;
