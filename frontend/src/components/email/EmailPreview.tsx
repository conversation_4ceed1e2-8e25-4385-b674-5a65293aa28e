import React from "react";
import { TabPanel, a11yProps } from "../common/TabsNavigation";
import { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from "react";
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  TextField,
  Button,
  Chip,
  Divider,
  IconButton,
  Tooltip,
  Grid,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from "@mui/material";
import {
  Send as SendIcon,
  FileCopy as CopyIcon,
  Edit as EditIcon,
  Schedule as ScheduleIcon,
  Smartphone as MobileIcon,
  Computer as DesktopIcon,
  Close as CloseIcon,
} from "@mui/icons-material";
import { IEmailPreviewProps, IEmailTemplate } from "../../types/email";

const EmailPreview = ({
  template,
  onEdit,
  onSend,
  onCopy,
  showControls = true,
  showMetadata = true,
  recipientEmail = "",
  onRecipientChange,
  previewMode = "full",
}: IEmailPreviewProps) => {
  const [viewMode, setViewMode] = React.useState(0); // 0 = desktop, 1 = mobile
  const [recipient, setRecipient] = React.useState(recipientEmail);
  const [openScheduleDialog, setOpenScheduleDialog] = React.useState(false);
  const [scheduleDate, setScheduleDate] = React.useState("");
  const [scheduleTime, setScheduleTime] = React.useState("");
  const [selectedVariant, setSelectedVariant] = React.useState("default");

  const handleViewModeChange = (_event: any, newValue: number) => {
    setViewMode(newValue);
  };

  const handleRecipientChange = (event: any) => {
    const newValue = event.target.value;
    setRecipient(newValue);
    if (onRecipientChange) {
      onRecipientChange(newValue);
    }
  };

  // Get the current template based on selected variant
  const getCurrentTemplate = (): IEmailTemplate => {
    if (selectedVariant === "default" || !template.metadata?.variants) {
      return template;
    }

    const variant = template.metadata.variants.find(
      (v) => v.id === selectedVariant
    );
    if (!variant) return template;

    return {
      ...template,
      subject: variant.subject,
      body: variant.body,
    };
  };

  // Handle variant change
  const handleVariantChange = (event: SelectChangeEvent<string>) => {
    setSelectedVariant(event.target.value);
  };

  const handleCopyClick = () => {
    if (onCopy) {
      onCopy(template);
    }

    // Fallback copy to clipboard
    const tempElement = document.createElement("div");
    tempElement.innerHTML = template.body;
    const textContent = `Subject: ${template.subject}\n\n${tempElement.textContent}`;

    navigator.clipboard
      .writeText(textContent)
      .then(() => {
        alert("Email content copied to clipboard");
      })
      .catch((err) => {
        console.error("Failed to copy: ", err);
      });
  };

  const handleSendClick = () => {
    if (onSend) {
      onSend(template);
    }
  };

  const handleEditClick = () => {
    if (onEdit) {
      onEdit(template);
    }
  };

  const handleOpenScheduleDialog = () => {
    setOpenScheduleDialog(true);
  };

  const handleCloseScheduleDialog = () => {
    setOpenScheduleDialog(false);
  };

  const handleScheduleSend = () => {
    // In a real app, this would call an API to schedule the email
    alert(`Email scheduled for ${scheduleDate} at ${scheduleTime}`);
    handleCloseScheduleDialog();
  };

  // Get the current template with variant if selected
  const currentTemplate = getCurrentTemplate();

  return (
    <Box>
      {showMetadata && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            {currentTemplate.name}
          </Typography>

          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 2 }}>
            {currentTemplate.metadata?.type && (
              <Chip
                label={`Type: ${currentTemplate.metadata.type}`}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
            {currentTemplate.metadata?.industry && (
              <Chip
                label={`Industry: ${currentTemplate.metadata.industry}`}
                size="small"
                variant="outlined"
              />
            )}
            {currentTemplate.metadata?.tone && (
              <Chip
                label={`Tone: ${currentTemplate.metadata.tone}`}
                size="small"
                variant="outlined"
              />
            )}
            {currentTemplate.metadata?.keywords &&
              currentTemplate.metadata.keywords.map((keyword, index) => (
                <Chip
                  key={index}
                  label={keyword}
                  size="small"
                  variant="outlined"
                />
              ))}
          </Box>
        </Box>
      )}

      {previewMode === "full" && (
        <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 2 }}>
          <Tabs
            value={viewMode}
            onChange={handleViewModeChange}
            aria-label="preview mode tabs"
          >
            <Tab
              icon={<DesktopIcon />}
              iconPosition="start"
              label="Desktop"
              {...a11yProps(0)}
            />
            <Tab
              icon={<MobileIcon />}
              iconPosition="start"
              label="Mobile"
              {...a11yProps(1)}
            />
          </Tabs>
        </Box>
      )}

      {/* Variant Selector */}
      {template.metadata?.variants && template.metadata.variants.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <FormControl fullWidth size="small">
            <InputLabel id="variant-select-label">Email Variant</InputLabel>
            <Select
              labelId="variant-select-label"
              id="variant-select"
              value={selectedVariant}
              label="Email Variant"
              onChange={handleVariantChange}
            >
              <MenuItem value="default">{template.name} (Default)</MenuItem>
              {template.metadata.variants.map((variant) => (
                <MenuItem key={variant.id} value={variant.id}>
                  {variant.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      )}

      <Paper
        elevation={3}
        sx={{
          overflow: "hidden",
          width: previewMode === "mobile" || viewMode === 1 ? "380px" : "100%",
          mx: previewMode === "mobile" || viewMode === 1 ? "auto" : 0,
        }}
      >
        <Box
          sx={{ bgcolor: "#f5f5f5", p: 2, borderBottom: "1px solid #e0e0e0" }}
        >
          <TextField
            label="To"
            fullWidth
            size="small"
            value={recipient}
            onChange={handleRecipientChange}
            placeholder="<EMAIL>"
            sx={{ mb: 2 }}
          />

          <Typography variant="subtitle1" fontWeight="bold">
            {currentTemplate.subject}
          </Typography>
        </Box>

        <Box
          sx={{
            p: 2,
            maxHeight: "500px",
            overflowY: "auto",
            "& a": { color: "#1976d2", textDecoration: "none" },
          }}
          dangerouslySetInnerHTML={{ __html: currentTemplate.body }}
        />

        {showControls && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              p: 2,
              borderTop: "1px solid #e0e0e0",
              bgcolor: "#f5f5f5",
            }}
          >
            <Button
              startIcon={<CopyIcon />}
              onClick={handleCopyClick}
              sx={{ mr: 1 }}
            >
              Copy
            </Button>

            <Button
              startIcon={<EditIcon />}
              onClick={handleEditClick}
              sx={{ mr: 1 }}
            >
              Edit
            </Button>

            <Button
              startIcon={<ScheduleIcon />}
              onClick={handleOpenScheduleDialog}
              sx={{ mr: 1 }}
            >
              Schedule
            </Button>

            <Button
              variant="contained"
              startIcon={<SendIcon />}
              onClick={handleSendClick}
              disabled={!recipient}
            >
              Send
            </Button>
          </Box>
        )}
      </Paper>

      {/* Schedule Dialog */}
      <Dialog open={openScheduleDialog} onClose={handleCloseScheduleDialog}>
        <DialogTitle>Schedule Email</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                label="Recipient"
                fullWidth
                value={recipient}
                onChange={handleRecipientChange}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Subject"
                fullWidth
                value={currentTemplate.subject}
                InputProps={{
                  readOnly: true,
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Date"
                type="date"
                fullWidth
                value={scheduleDate}
                onChange={(e) => setScheduleDate(e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Time"
                type="time"
                fullWidth
                value={scheduleTime}
                onChange={(e) => setScheduleTime(e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
                required
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseScheduleDialog}>Cancel</Button>
          <Button
            onClick={handleScheduleSend}
            variant="contained"
            disabled={!recipient || !scheduleDate || !scheduleTime}
          >
            Schedule
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EmailPreview;
