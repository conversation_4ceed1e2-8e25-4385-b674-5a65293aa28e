import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  CircularProgress,
  Paper,
  Divider,
  Slider,
  Box,
  Chip,
} from "@mui/material";
import { AIService } from "../services/AIService";

export const AITool = () => {
  const [prompt, setPrompt] = useState("");
  const [systemPrompt, setSystemPrompt] = useState("");
  const [temperature, setTemperature] = useState(0.7);
  const [maxTokens, setMaxTokens] = useState(500);
  const [response, setResponse] = React.useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = React.useState(null);
  const [aiHealth, setAiHealth] = React.useState({
    status: "unknown",
    stats: undefined,
  });

  // Predefined prompts for water treatment industry
  const predefinedPrompts = [
    {
      title: "Cooling Tower Treatment",
      prompt:
        "Explain the importance of proper chemical treatment for cooling towers in industrial settings.",
      systemPrompt:
        "You are an expert in water treatment chemicals for industrial applications.",
    },
    {
      title: "Boiler Water Treatment",
      prompt:
        "Describe the key chemicals used in boiler water treatment and their functions.",
      systemPrompt:
        "You are an expert in water treatment chemicals for industrial applications.",
    },
    {
      title: "Corrosion Prevention",
      prompt:
        "What are the best practices for preventing corrosion in water systems using chemical treatments?",
      systemPrompt:
        "You are an expert in water treatment chemicals for industrial applications.",
    },
  ];

  const handleGenerateText = async () => {
    if (!prompt) {
      setError("Please enter a prompt");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await AIService.generateText({
        prompt,
        system_prompt: systemPrompt || undefined,
        temperature,
        max_tokens: maxTokens,
      });

      setResponse(result);
    } catch (err) {
      setError("Failed to generate text. Please try again.");
      console.error("Error generating text:", err);
    } finally {
      setLoading(false);
    }
  };

  const checkAIHealth = useCallback(async () => {
    try {
      const health = await AIService.getHealthStatus();
      setAiHealth({
        status: health.status as "healthy" | "unhealthy" | "unknown",
        stats: health.stats,
      });
    } catch (err) {
      setAiHealth({
        status: "unhealthy" as "healthy" | "unhealthy" | "unknown",
        stats: undefined,
      });
    }
  }, []);

  const handlePredefinedPrompt = (index: number) => {
    const selected = predefinedPrompts[index];
    setPrompt(selected.prompt);
    setSystemPrompt(selected.systemPrompt);
  };

  useEffect(() => {
    // Check AI health on component mount
    checkAIHealth();
  }, [checkAIHealth]);

  return (
    <Card sx={{ m: 2 }}>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          AI Tool - DeepSeek Powered
        </Typography>

        <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
          <Typography variant="body2" sx={{ mr: 1 }}>
            AI Service Status:
          </Typography>
          <Chip
            label={aiHealth.status}
            color={
              aiHealth.status === "healthy"
                ? "success"
                : aiHealth.status === "unhealthy"
                ? "error"
                : "default"
            }
            size="small"
            onClick={checkAIHealth}
          />
        </Box>

        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              Predefined Prompts for Water Treatment Industry
            </Typography>
            <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 2 }}>
              {predefinedPrompts.map((item, index) => (
                <Chip
                  key={index}
                  label={item.title}
                  onClick={() => handlePredefinedPrompt(index)}
                  clickable
                  color="primary"
                  variant="outlined"
                />
              ))}
            </Box>
          </Grid>

          <Grid item xs={12}>
            <TextField
              label="System Prompt (Optional)"
              multiline
              rows={2}
              fullWidth
              variant="outlined"
              value={systemPrompt}
              onChange={(e) => setSystemPrompt(e.target.value)}
              placeholder="Instructions for the AI model (e.g., 'You are an expert in water treatment chemicals')"
              margin="normal"
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              label="Prompt"
              multiline
              rows={4}
              fullWidth
              variant="outlined"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Enter your prompt here..."
              margin="normal"
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography gutterBottom>Temperature: {temperature}</Typography>
            <Slider
              value={temperature}
              onChange={(_, newValue) => setTemperature(newValue as number)}
              min={0}
              max={1}
              step={0.1}
              valueLabelDisplay="auto"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography gutterBottom>Max Tokens: {maxTokens}</Typography>
            <Slider
              value={maxTokens}
              onChange={(_, newValue) => setMaxTokens(newValue as number)}
              min={100}
              max={2000}
              step={100}
              valueLabelDisplay="auto"
            />
          </Grid>

          <Grid item xs={12}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleGenerateText}
              disabled={loading || !prompt}
              fullWidth
            >
              {loading ? <CircularProgress size={24} /> : "Generate Text"}
            </Button>
          </Grid>

          {error && (
            <Grid item xs={12}>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  bgcolor: "error.light",
                  color: "error.contrastText",
                }}
              >
                {error}
              </Paper>
            </Grid>
          )}

          {response && (
            <Grid item xs={12}>
              <Paper elevation={3} sx={{ p: 2, mt: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Generated Text:
                </Typography>
                <Typography variant="body1" sx={{ whiteSpace: "pre-wrap" }}>
                  {response.text}
                </Typography>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle2" color="textSecondary">
                  Model: {response.model || "Unknown"}
                </Typography>

                {response.source && (
                  <Typography variant="subtitle2" color="textSecondary">
                    Source: {response.source}
                  </Typography>
                )}

                {response.usage && (
                  <Typography variant="subtitle2" color="textSecondary">
                    Tokens: {response.usage.total_tokens || "Unknown"}
                    {response.usage.prompt_tokens &&
                      ` (Prompt: ${response.usage.prompt_tokens}, Completion: ${response.usage.completion_tokens})`}
                  </Typography>
                )}
              </Paper>
            </Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  );
};
