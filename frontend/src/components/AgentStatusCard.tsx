import { Box, Card, CardContent, LinearProgress, Typography } from '@mui/material';
import { AgentMetrics } from '@shared/types/index';

interface AgentStatusCardProps {
  agent: AgentMetrics;
}

export const AgentStatusCard = ({ agent  }: AgentStatusCardProps) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running': return 'success.main';
      case 'error': return 'error.main';
      default: return 'info.main';
    }
  };

  return (
    <Card sx={{ minWidth: 275, m: 1 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {agent.name}
        </Typography>
        <Typography color="textSecondary" gutterBottom>
          Role: {agent.role}
        </Typography>
        <Typography color={getStatusColor(agent.status)} variant="body2">
          Status: {agent.status}
        </Typography>
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2">Success Rate</Typography>
          <LinearProgress
            variant="determinate"
            value={agent.successRate * 100}
            color="success"
            sx={{ mb: 1 }}
          />
          <Typography variant="body2" color="success.main">
            Success Rate: {agent.successRate !== undefined ? agent.successRate : "N/A"}%
          </Typography>
          <Typography variant="body2">Error Rate</Typography>
          <LinearProgress
            variant="determinate"
            value={agent.errorRate * 100}
            color="error"
          />
          <Typography variant="body2" color="error.main">
            Error Rate: {agent.errorRate !== undefined ? agent.errorRate : "N/A"}%
          </Typography>
        </Box>
        <Typography variant="body2" color="text.secondary">
          Latency: {agent.latency !== undefined ? agent.latency : "N/A"} ms
        </Typography>
        <Typography variant="body2" sx={{ mt: 1 }}>
          Latency: {agent.latency.toFixed(2)}ms
        </Typography>
        <Typography variant="caption" color="textSecondary">
          Last Updated: {new Date(agent.lastCheckTime).toLocaleString()}
        </Typography>
      </CardContent>
    </Card>
  );
}
