import { Box, Card, CardContent, Chip, Grid, Typography } from '@mui/material';
import React, { useEffect } from 'react';
import { MockDataService } from '../services/MockDataService';

export const AnalyticsOverview = () => {
  const [data, setData] = React.useState(MockDataService.getAnalyticsData());

  useEffect(() => {
    const fetchData = async () => {
      // Initial data already set, just update periodically

      // Set up interval for updates
      const interval = setInterval(async () => {
        const updatedData = await MockDataService.getUpdatedAnalyticsData();
        setData(updatedData);
      }, 10000); // Update every 10 seconds

      return () => clearInterval(interval);
    };

    fetchData();
  }, []);
  const chartData = [
    { name: 'Leads', value: data.leadsGenerated },
    { name: 'Website Visits', value: data.websiteTraffic },
    { name: 'Social', value: data.socialEngagement },
    { name: 'Tenders', value: data.tenderOpportunities },
  ];

  return (
    <Card sx={{ m: 1 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Analytics Overview
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={8}>
            <Box sx={{ height: 300 }}>
              {/* @ts-ignore */}
              <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
            </Box>
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography variant="body2" gutterBottom>
              Conversion Rate: {(data.conversionRate * 100).toFixed(1)}%
            </Typography>
            <Typography variant="body2" gutterBottom>
              Average Tender Score: {data.averageTenderScore.toFixed(1)}
            </Typography>

            <Typography variant="body2" gutterBottom sx={{ mt: 2 }}>
              Top Keywords:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {data.topKeywords.map((keyword: string, index: number) => (
                <Chip key={index} label={keyword} size="small" />
              ))}
            </Box>

            <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mt: 2 }}>
              Last Updated: {new Date(data.lastUpdated).toLocaleString()}
            </Typography>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}
