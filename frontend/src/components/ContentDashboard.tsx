import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import PreviewIcon from "@mui/icons-material/Preview";
import PublishIcon from "@mui/icons-material/Publish";
import ScheduleIcon from "@mui/icons-material/Schedule";
import {
    Alert,
    Box,
    Button,
    Card,
    CardActions,
    CardContent,
    Checkbox,
    Chip,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Divider,
    FormControlLabel,
    Grid,
    MenuItem,
    IconButton as MuiIconButton,
    Tab,
    Tabs,
    TextField,
    Tooltip,
    Typography,
} from "@mui/material";
import AdapterDateFns from "@mui/x-date-pickers/AdapterDateFns";
import DatePicker from "@mui/x-date-pickers/DatePicker";
import LocalizationProvider from "@mui/x-date-pickers/LocalizationProvider";
import {
    ContentItem,
    ContentStatus,
    ContentType,
    ScheduleItem
} from "@shared/types/content";
import axios from "axios";
import React from "react";
import ReactMarkdown from "react-markdown";
import { TabPanel, a11yProps } from "./common/TabsNavigation";
import { EmailPreview } from "./EmailPreview";
import LinkedInMessageGenerator from "./LinkedInMessageGenerator";

// Content dashboard component
const ContentDashboard = () => {
  // State
  const [tabValue, setTabValue] = React.useState(0);
  const [content, setContent] = React.useState<any[]>([]);
  const [schedules, setSchedules] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState(null);
  const [openGenerateDialog, setOpenGenerateDialog] = React.useState(false);
  const [openScheduleDialog, setOpenScheduleDialog] = React.useState(false);
  const [openPreviewDialog, setOpenPreviewDialog] = React.useState(false);
  const [selectedContent, setSelectedContent] =
    React.useState(null);
  const [generationRequest, setGenerationRequest] =
    React.useState({
      type: ContentType.BLOG_POST,
      includeImagePrompt: true,
      length: "medium",
    });
  const [scheduleRequest, setScheduleRequest] =
    React.useState({
      type: ContentType.BLOG_POST,
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      length: "medium",
    });

  // Load content and schedules on component mount
  React.useEffect(() => {
    fetchContent();
    fetchSchedules();
  }, []);

  // Handle tab change
  const handleTabChange = (_event: any, newValue: number) => {
    setTabValue(newValue);
  };

  // Format date helper function
  const formatDate = (date: Date | string | null | undefined): string => {
    if (!date) return "N/A";
    try {
      return new Date(date).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (e) {
      return "Invalid Date";
    }
  };

  // Handle date change in schedule dialog
  const handleScheduleDateChange = (value: unknown) => {
    if (value instanceof Date || value === null) {
      setScheduleRequest((prev: any) => ({
        ...prev,
        scheduledDate: value as Date | null,
      }));
    } else if (typeof value === "string") {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        setScheduleRequest((prev: any) => ({ ...prev, scheduledDate: date }));
      }
    } else {
      // Optionally handle other types or log an error
      console.warn("DatePicker received an unexpected value type:", value);
      setScheduleRequest((prev: any) => ({ ...prev, scheduledDate: null }));
    }
  };

  // Get content type name
  const getContentTypeName = (type: ContentType): string => {
    switch (type) {
      case ContentType.BLOG_POST:
        return "Blog Post";
      case ContentType.LINKEDIN_POST:
        return "LinkedIn Post";
      case ContentType.INFOGRAPHIC:
        return "Infographic";
      case ContentType.EMAIL_NEWSLETTER:
        return "Email Newsletter";
      case ContentType.CASE_STUDY:
        return "Case Study";
      default:
        return "Content";
    }
  };

  // Get status color
  const getStatusColor = (
    status: ContentStatus | string
  ):
    | "default"
    | "primary"
    | "secondary"
    | "error"
    | "info"
    | "success"
    | "warning" => {
    switch (status) {
      case ContentStatus.DRAFT:
      case "scheduled":
        return "default";
      case ContentStatus.REVIEW:
      case "generated":
        return "info";
      case ContentStatus.APPROVED:
        return "success";
      case ContentStatus.PUBLISHED:
      case "published":
        return "primary";
      case ContentStatus.REJECTED:
      case "failed":
        return "error";
      default:
        return "default";
    }
  };

  // Fetch content from API
  const fetchContent = async () => {
    try {
      setLoading(true);
      const response = await axios.get<{ data: ContentItem[] }>("/api/content");
      setContent(
        response.data.data.map((item) => ({
          ...item,
          createdAt: new Date(item.createdAt),
          updatedAt: new Date(item.updatedAt),
          publishedAt: item.publishedAt
            ? new Date(item.publishedAt)
            : undefined,
        }))
      );
      setError(null);
    } catch (err) {
      setError("Failed to fetch content");
      console.error("Error fetching content:", err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch schedules from API
  const fetchSchedules = async () => {
    try {
      setLoading(true);
      const response = await axios.get<{ data: ScheduleItem[] }>(
        "/api/content/schedules"
      );
      setSchedules(
        response.data.data.map((item) => ({
          ...item,
          scheduledDate: item.scheduledDate
            ? new Date(item.scheduledDate)
            : new Date(), // Ensure Date is always valid
          createdAt: new Date(item.createdAt),
          updatedAt: new Date(item.updatedAt),
          publishedDate: item.publishedDate
            ? new Date(item.publishedDate)
            : undefined,
        }))
      );
      setError(null);
    } catch (err) {
      setError("Failed to fetch schedules");
      console.error("Error fetching schedules:", err);
    } finally {
      setLoading(false);
    }
  };

  // Generate content
  const generateContent = async () => {
    try {
      setLoading(true);
      const response = await axios.post<{ data: ContentItem }>(
        "/api/content/generate",
        generationRequest
      );
      const newItem = {
        ...response.data.data,
        createdAt: new Date(response.data.data.createdAt),
        updatedAt: new Date(response.data.data.updatedAt),
        publishedAt: response.data.data.publishedAt
          ? new Date(response.data.data.publishedAt)
          : undefined,
      };
      setContent([newItem, ...content] as any);
      setOpenGenerateDialog(false);
      setGenerationRequest({
        type: ContentType.BLOG_POST,
        includeImagePrompt: true,
        length: "medium",
      });
      setError(null);
    } catch (err) {
      setError("Failed to generate content");
      console.error("Error generating content:", err);
    } finally {
      setLoading(false);
    }
  };

  // Schedule content
  const scheduleContentGeneration = async () => {
    try {
      setLoading(true);
      const response = await axios.post<{ data: ScheduleItem }>(
        "/api/content/schedule",
        scheduleRequest
      );
      const newItem = {
        ...response.data.data,
        scheduledDate: response.data.data.scheduledDate
          ? new Date(response.data.data.scheduledDate)
          : new Date(), // Ensure Date is always valid
        createdAt: new Date(response.data.data.createdAt),
        updatedAt: new Date(response.data.data.updatedAt),
        publishedDate: response.data.data.publishedDate
          ? new Date(response.data.data.publishedDate)
          : undefined,
      };
      setSchedules([newItem, ...schedules] as any);
      setOpenScheduleDialog(false);
      setScheduleRequest({
        type: ContentType.BLOG_POST,
        scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        length: "medium",
      });
      setError(null);
    } catch (err) {
      setError("Failed to schedule content");
      console.error("Error scheduling content:", err);
    } finally {
      setLoading(false);
    }
  };

  // Update content status
  const updateContentStatus = async (id: string, status: ContentStatus) => {
    try {
      setLoading(true);
      // Assuming API returns the updated item or just a success message.
      // If it returns the updated item, use it directly.
      await axios.patch(`/api/content/${id}`, { status });

      // Update content in state
      setContent(
        content.map((item) =>
          item.id === id ? { ...item, status, updatedAt: new Date() } : item
        )
      );

      setError(null);
    } catch (err) {
      setError(`Failed to update content status to ${status}`);
      console.error("Error updating content status:", err);
    } finally {
      setLoading(false);
    }
  };

  // Delete content
  const deleteContent = async (id: string) => {
    if (!window.confirm("Are you sure you want to delete this content?")) {
      return;
    }

    try {
      setLoading(true);
      await axios.delete(`/api/content/${id}`);

      // Remove content from state
      setContent(content.filter((item) => item.id !== id));

      setError(null);
    } catch (err) {
      setError("Failed to delete content");
      console.error("Error deleting content:", err);
    } finally {
      setLoading(false);
    }
  };

  // Delete schedule
  const deleteSchedule = async (id: string) => {
    if (!window.confirm("Are you sure you want to delete this schedule?")) {
      return;
    }

    try {
      setLoading(true);
      await axios.delete(`/api/content/schedules/${id}`);

      // Remove schedule from state
      setSchedules(schedules.filter((item) => item.id !== id));

      setError(null);
    } catch (err) {
      setError("Failed to delete schedule");
      console.error("Error deleting schedule:", err);
    } finally {
      setLoading(false);
    }
  };

  // Open preview dialog
  const openPreview = (item: ContentItem) => {
    setSelectedContent(item);
    setOpenPreviewDialog(true);
  };

  // Ensure all IconButtons are MuiIconButton
  const handleDelete = async (id: string, type: "content" | "schedule") => {
    if (type === "content") {
      await deleteContent(id);
    } else {
      await deleteSchedule(id);
    }
  };

  return (
    <Box sx={{ width: "100%" }}>
      <Box
        sx={{
          borderBottom: 1,
          borderColor: "divider",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="content tabs"
        >
          <Tab label="Content Library" {...a11yProps(0)} />
          <Tab label="Content Schedule" {...a11yProps(1)} />
          <Tab label="LinkedIn Content" {...a11yProps(2)} />
          <Tab label="Email Templates" {...a11yProps(3)} />
        </Tabs>
        <Box>
          <MuiIconButton
            size="small"
            onClick={() => setOpenGenerateDialog(true)}
            sx={{ mr: 1 }}
          >
            <AddIcon />
          </MuiIconButton>
          <MuiIconButton
            size="small"
            onClick={() => setOpenScheduleDialog(true)}
          >
            <ScheduleIcon />
          </MuiIconButton>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <TabPanel value={tabValue} index={0}>
        {loading ? (
          <Box sx={{ display: "flex", justifyContent: "center", my: 4 }}>
            <CircularProgress />
          </Box>
        ) : content.length === 0 ? (
          <Alert severity="info">
            No content found. Generate some content to get started.
          </Alert>
        ) : (
          <Grid container spacing={3}>
            {content.map((item) => (
              <Grid item xs={12} key={item.id}>
                <Card>
                  <CardContent>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "flex-start",
                        mb: 1,
                      }}
                    >
                      <Typography variant="h6" component="h2">
                        {item.title}
                      </Typography>
                      <Chip
                        label={getContentTypeName(item.type)}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </Box>

                    {item.summary && (
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        paragraph
                      >
                        {item.summary}
                      </Typography>
                    )}

                    <Box
                      sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 2 }}
                    >
                      <Chip
                        label={item.status}
                        size="small"
                        color={getStatusColor(item.status)}
                      />

                      {item.keywords &&
                        item.keywords.map((keyword, index) => (
                          <Chip
                            key={index}
                            label={keyword}
                            size="small"
                            variant="outlined"
                          />
                        ))}
                    </Box>

                    <Typography variant="caption" color="text.secondary">
                      Created: {formatDate(item.createdAt)} | Updated:{" "}
                      {formatDate(item.updatedAt)}
                      {item.publishedAt &&
                        ` | Published: ${formatDate(item.publishedAt)}`}
                    </Typography>
                  </CardContent>
                  <CardActions
                    sx={{
                      justifyContent: "flex-end",
                      borderTop: "1px solid #eee",
                      pt: 1,
                    }}
                  >
                    <Tooltip title="Preview Content">
                      <MuiIconButton
                        size="small"
                        onClick={() => openPreview(item)}
                      >
                        <PreviewIcon />
                      </MuiIconButton>
                    </Tooltip>
                    <Tooltip title="Edit Content (Not Implemented)">
                      <MuiIconButton size="small" disabled>
                        <EditIcon />
                      </MuiIconButton>
                    </Tooltip>
                    <Tooltip title="Publish Content">
                      <MuiIconButton
                        size="small"
                        onClick={() =>
                          updateContentStatus(item.id, ContentStatus.PUBLISHED)
                        }
                        disabled={item.status === ContentStatus.PUBLISHED}
                      >
                        <PublishIcon />
                      </MuiIconButton>
                    </Tooltip>
                    <Tooltip title="Delete Content">
                      <MuiIconButton
                        size="small"
                        color="error"
                        onClick={() => handleDelete(item.id, "content")}
                      >
                        <DeleteIcon />
                      </MuiIconButton>
                    </Tooltip>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {loading ? (
          <Box sx={{ display: "flex", justifyContent: "center", my: 4 }}>
            <CircularProgress />
          </Box>
        ) : schedules.length === 0 ? (
          <Alert severity="info">
            No content scheduled. Schedule some content to get started.
          </Alert>
        ) : (
          <Grid container spacing={3}>
            {schedules.map((item) => (
              <Grid item xs={12} key={item.id}>
                <Card>
                  <CardContent>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "flex-start",
                        mb: 1,
                      }}
                    >
                      <Typography variant="h6" component="h2">
                        {item.topic || "Scheduled Content"}
                      </Typography>
                      <Chip
                        label={getContentTypeName(item.type)}
                        size="small"
                        color="secondary"
                        variant="outlined"
                      />
                    </Box>

                    <Typography variant="body2" color="text.secondary">
                      Scheduled for: {formatDate(item.scheduledDate)}
                    </Typography>

                    <Box
                      sx={{ display: "flex", flexWrap: "wrap", gap: 1, my: 1 }}
                    >
                      <Chip
                        label={item.status}
                        size="small"
                        color={getStatusColor(item.status)}
                      />
                      {item.keywords &&
                        item.keywords.map((keyword, index) => (
                          <Chip
                            key={index}
                            label={keyword}
                            size="small"
                            variant="outlined"
                          />
                        ))}
                    </Box>

                    <Typography variant="caption" color="text.secondary">
                      Created: {formatDate(item.createdAt)} | Updated:{" "}
                      {formatDate(item.updatedAt)}
                      {item.publishedDate &&
                        ` | Published: ${formatDate(item.publishedDate)}`}
                    </Typography>
                  </CardContent>
                  <CardActions
                    sx={{
                      justifyContent: "flex-end",
                      borderTop: "1px solid #eee",
                      pt: 1,
                    }}
                  >
                    <Tooltip title="Edit Schedule (Not Implemented)">
                      <MuiIconButton size="small" disabled>
                        <EditIcon />
                      </MuiIconButton>
                    </Tooltip>
                    <Tooltip title="Delete Schedule">
                      <MuiIconButton
                        size="small"
                        color="error"
                        onClick={() => handleDelete(item.id, "schedule")}
                      >
                        <DeleteIcon />
                      </MuiIconButton>
                    </Tooltip>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <LinkedInMessageGenerator />
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <Typography>Email Templates - Coming Soon</Typography>
        {/* Placeholder for Email Templates UI */}
      </TabPanel>

      {/* Generate Content Dialog */}
      <Dialog
        open={openGenerateDialog}
        onClose={() => setOpenGenerateDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Generate New Content
          <MuiIconButton
            aria-label="close"
            onClick={() => setOpenGenerateDialog(false)}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </MuiIconButton>
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                select
                label="Content Type"
                value={generationRequest.type}
                onChange={(e) =>
                  setGenerationRequest({
                    ...generationRequest,
                    type: e.target.value as ContentType,
                  })
                }
                fullWidth
                margin="normal"
              >
                {Object.values(ContentType).map((type) => (
                  <MenuItem key={type} value={type}>
                    {getContentTypeName(type)}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                select
                label="Length"
                value={generationRequest.length}
                onChange={(e) =>
                  setGenerationRequest({
                    ...generationRequest,
                    length: e.target.value as "short" | "medium" | "long",
                  })
                }
                fullWidth
                margin="normal"
              >
                <MenuItem value="short">Short</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="long">Long</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Topic / Title (Optional)"
                value={generationRequest.topic || ""}
                onChange={(e) =>
                  setGenerationRequest({
                    ...generationRequest,
                    topic: e.target.value,
                  })
                }
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Keywords (comma-separated, optional)"
                value={(generationRequest.keywords || []).join(", ")}
                onChange={(e) =>
                  setGenerationRequest({
                    ...generationRequest,
                    keywords: e.target.value
                      .split(",")
                      .map((kw) => kw.trim())
                      .filter((kw) => kw),
                  })
                }
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Target Audience (Optional)"
                value={generationRequest.targetAudience || ""}
                onChange={(e) =>
                  setGenerationRequest({
                    ...generationRequest,
                    targetAudience: e.target.value,
                  })
                }
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Tone (e.g., professional, casual, optional)"
                value={generationRequest.tone || ""}
                onChange={(e) =>
                  setGenerationRequest({
                    ...generationRequest,
                    tone: e.target.value,
                  })
                }
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={generationRequest.includeImagePrompt}
                    onChange={(e) =>
                      setGenerationRequest({
                        ...generationRequest,
                        includeImagePrompt: e.target.checked,
                      })
                    }
                  />
                }
                label="Include Image Prompt Suggestion"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Additional Instructions (Optional)"
                value={generationRequest.additionalInstructions || ""}
                onChange={(e) =>
                  setGenerationRequest({
                    ...generationRequest,
                    additionalInstructions: e.target.value,
                  })
                }
                fullWidth
                multiline
                rows={3}
                margin="normal"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={() => setOpenGenerateDialog(false)} color="inherit">
            Cancel
          </Button>
          <Button
            onClick={generateContent}
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <AddIcon />}
          >
            Generate Content
          </Button>
        </DialogActions>
      </Dialog>

      {/* Schedule Content Dialog */}
      <Dialog
        open={openScheduleDialog}
        onClose={() => setOpenScheduleDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Schedule Content Generation
          <MuiIconButton
            aria-label="close"
            onClick={() => setOpenScheduleDialog(false)}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </MuiIconButton>
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                select
                label="Content Type"
                value={scheduleRequest.type}
                onChange={(e) =>
                  setScheduleRequest({
                    ...scheduleRequest,
                    type: e.target.value as ContentType,
                  })
                }
                fullWidth
                margin="normal"
              >
                {Object.values(ContentType).map((type) => (
                  <MenuItem key={type} value={type}>
                    {getContentTypeName(type)}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Schedule Date"
                  value={scheduleRequest.scheduledDate}
                  onChange={handleScheduleDateChange}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Topic / Title (Optional)"
                value={scheduleRequest.topic || ""}
                onChange={(e) =>
                  setScheduleRequest({
                    ...scheduleRequest,
                    topic: e.target.value,
                  })
                }
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Keywords (comma-separated, optional)"
                value={(scheduleRequest.keywords || []).join(", ")}
                onChange={(e) =>
                  setScheduleRequest({
                    ...scheduleRequest,
                    keywords: e.target.value
                      .split(",")
                      .map((kw) => kw.trim())
                      .filter((kw) => kw),
                  })
                }
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                select
                label="Length"
                value={scheduleRequest.length}
                onChange={(e) =>
                  setScheduleRequest({
                    ...scheduleRequest,
                    length: e.target.value as "short" | "medium" | "long",
                  })
                }
                fullWidth
                margin="normal"
              >
                <MenuItem value="short">Short</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="long">Long</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Target Audience (Optional)"
                value={scheduleRequest.targetAudience || ""}
                onChange={(e) =>
                  setScheduleRequest({
                    ...scheduleRequest,
                    targetAudience: e.target.value,
                  })
                }
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Tone (e.g., professional, casual, optional)"
                value={scheduleRequest.tone || ""}
                onChange={(e) =>
                  setScheduleRequest({
                    ...scheduleRequest,
                    tone: e.target.value,
                  })
                }
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={scheduleRequest.includeImagePrompt}
                    onChange={(e) =>
                      setScheduleRequest({
                        ...scheduleRequest,
                        includeImagePrompt: e.target.checked,
                      })
                    }
                  />
                }
                label="Include Image Prompt Suggestion"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Additional Instructions (Optional)"
                value={scheduleRequest.additionalInstructions || ""}
                onChange={(e) =>
                  setScheduleRequest({
                    ...scheduleRequest,
                    additionalInstructions: e.target.value,
                  })
                }
                fullWidth
                multiline
                rows={3}
                margin="normal"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={() => setOpenScheduleDialog(false)} color="inherit">
            Cancel
          </Button>
          <Button
            onClick={scheduleContentGeneration}
            variant="contained"
            disabled={loading || !scheduleRequest.scheduledDate}
            startIcon={
              loading ? <CircularProgress size={20} /> : <ScheduleIcon />
            }
          >
            Schedule Content
          </Button>
        </DialogActions>
      </Dialog>

      {/* Preview Content Dialog */}
      <Dialog
        open={openPreviewDialog}
        onClose={() => setOpenPreviewDialog(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          {selectedContent?.title || "Content Preview"}
          <MuiIconButton
            aria-label="close"
            onClick={() => setOpenPreviewDialog(false)}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </MuiIconButton>
        </DialogTitle>
        <DialogContent dividers>
          {selectedContent ? (
            <Box>
              <Typography variant="h5" gutterBottom>
                {selectedContent.title}
              </Typography>
              <Chip
                label={getContentTypeName(selectedContent.type)}
                size="small"
                color="primary"
                variant="outlined"
                sx={{ mb: 1 }}
              />
              {selectedContent.imageUrl && (
                <Box
                  component="img"
                  src={selectedContent.imageUrl}
                  alt={selectedContent.title}
                  sx={{
                    maxWidth: "100%",
                    maxHeight: "400px",
                    display: "block",
                    my: 2,
                    borderRadius: 1,
                    boxShadow: 1,
                  }}
                />
              )}
              {selectedContent.imagePrompt && (
                <Typography variant="caption" color="text.secondary" paragraph>
                  Image Prompt: <em>{selectedContent.imagePrompt}</em>
                </Typography>
              )}
              <ReactMarkdown>{selectedContent.content}</ReactMarkdown>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2">Details:</Typography>
              <Typography variant="body2">
                Status: {selectedContent.status}
              </Typography>
              <Typography variant="body2">
                Keywords: {(selectedContent.keywords || []).join(", ")}
              </Typography>
              <Typography variant="body2">
                Created: {formatDate(selectedContent.createdAt)}
              </Typography>
              <Typography variant="body2">
                Last Updated: {formatDate(selectedContent.updatedAt)}
              </Typography>
              {selectedContent.publishedAt && (
                <Typography variant="body2">
                  Published: {formatDate(selectedContent.publishedAt)}
                </Typography>
              )}

              {selectedContent.type === ContentType.EMAIL_NEWSLETTER && (
                <Box mt={3}>
                  <Typography variant="h6">Email Preview:</Typography>
                  <EmailPreview
                    template={{
                      id: selectedContent?.id || "",
                      name: selectedContent?.title || "",
                      subject: selectedContent?.title || "",
                      body: selectedContent?.content || "",
                    }}
                    showControls={false}
                    previewMode="full"
                  />
                </Box>
              )}
            </Box>
          ) : (
            <Typography>No content selected for preview.</Typography>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button
            onClick={() => setOpenPreviewDialog(false)}
            color="primary"
            variant="outlined"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ContentDashboard;
