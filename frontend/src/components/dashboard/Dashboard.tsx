import React, { useState, useEffect } from "react";
import {
  FC,
  ReactNode,
  ChangeEvent,
  MouseEvent,
  SyntheticEvent,
} from "../../types/react";
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Button,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Tooltip,
  useTheme,
} from "@mui/material";
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Email as EmailIcon,
  LinkedIn as LinkedInIcon,
  Assignment as TaskIcon,
  Search as SearchIcon,
  Lightbulb as LightbulbIcon,
  MoreVert as MoreVertIcon,
  ArrowForward as ArrowForwardIcon,
} from "@mui/icons-material";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
} from "recharts";
import AgentStatusSection from "./sections/AgentStatusSection";
import SimpleAgentStatus from "./sections/SimpleAgentStatus";
// Recharts wrapper components - commented out due to TypeScript errors
// These components will be replaced with direct usage of Recharts components
/*
const BarWrapper = (props: any) => <Bar {...props} />;
const XAxisWrapper = (props: any) => <XAxis {...props} />;
const YAxisWrapper = (props: any) => <YAxis {...props} />;
const LineWrapper = (props: any) => <Line {...props} />;
const PieWrapper = (props: any) => <Pie {...props} />;
const TooltipWrapper = (props: any) => <Tooltip {...props} />;
const LegendWrapper = (props: any) => <Legend {...props} />;
*/

// Sample data for the dashboard
const analyticsData = {
  leadsGenerated: 143,
  websiteTraffic: 4786,
  socialEngagement: 2341,
  tenderOpportunities: 27,
  conversionRate: 3.2,
  averageTenderScore: 7.6,
  topKeywords: [
    "water treatment",
    "cooling tower",
    "boiler",
    "chemical dosing",
    "corrosion",
  ],
  lastUpdated: new Date().toISOString(),
};

const performanceData = [
  { name: "Jan", emails: 65, linkedin: 40, seo: 24 },
  { name: "Feb", emails: 59, linkedin: 45, seo: 30 },
  { name: "Mar", emails: 80, linkedin: 52, seo: 35 },
  { name: "Apr", emails: 81, linkedin: 56, seo: 40 },
  { name: "May", emails: 56, linkedin: 60, seo: 45 },
  { name: "Jun", emails: 55, linkedin: 50, seo: 50 },
  { name: "Jul", emails: 40, linkedin: 45, seo: 55 },
];

const recentTasks = [
  {
    id: 1,
    title: "Follow up with ABC Company",
    type: "email",
    dueDate: "2023-07-20",
    status: "pending",
  },
  {
    id: 2,
    title: 'Optimize website for "water treatment" keyword',
    type: "seo",
    dueDate: "2023-07-22",
    status: "in-progress",
  },
  {
    id: 3,
    title: "Create LinkedIn campaign for cooling systems",
    type: "linkedin",
    dueDate: "2023-07-25",
    status: "pending",
  },
  {
    id: 4,
    title: "Review AI recommendations for Q3",
    type: "ai",
    dueDate: "2023-07-18",
    status: "completed",
  },
];

const aiRecommendations = [
  {
    id: 1,
    title: "Increase email frequency to high-engagement leads",
    confidence: 85,
  },
  {
    id: 2,
    title: 'Focus SEO efforts on "industrial cooling" keywords',
    confidence: 92,
  },
  {
    id: 3,
    title: "Target manufacturing sector with LinkedIn messages",
    confidence: 78,
  },
];

export const Dashboard = () => {
  const theme = useTheme();
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard Overview
      </Typography>

      {isLoading ? (
        <LinearProgress sx={{ mb: 4 }} />
      ) : (
        <>
          {/* KPI Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Paper elevation={2} sx={{ p: 2, height: "100%" }}>
                <Typography variant="subtitle2" color="textSecondary">
                  Leads Generated
                </Typography>
                <Typography variant="h4">
                  {analyticsData.leadsGenerated}
                </Typography>
                <Box sx={{ display: "flex", alignItems: "center", mt: 1 }}>
                  <TrendingUpIcon color="success" fontSize="small" />
                  <Typography
                    variant="body2"
                    color="success.main"
                    sx={{ ml: 0.5 }}
                  >
                    +12% vs last month
                  </Typography>
                </Box>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper elevation={2} sx={{ p: 2, height: "100%" }}>
                <Typography variant="subtitle2" color="textSecondary">
                  Website Traffic
                </Typography>
                <Typography variant="h4">
                  {analyticsData.websiteTraffic.toLocaleString()}
                </Typography>
                <Box sx={{ display: "flex", alignItems: "center", mt: 1 }}>
                  <TrendingUpIcon color="success" fontSize="small" />
                  <Typography
                    variant="body2"
                    color="success.main"
                    sx={{ ml: 0.5 }}
                  >
                    +8.5% vs last month
                  </Typography>
                </Box>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper elevation={2} sx={{ p: 2, height: "100%" }}>
                <Typography variant="subtitle2" color="textSecondary">
                  Social Engagement
                </Typography>
                <Typography variant="h4">
                  {analyticsData.socialEngagement.toLocaleString()}
                </Typography>
                <Box sx={{ display: "flex", alignItems: "center", mt: 1 }}>
                  <TrendingUpIcon color="success" fontSize="small" />
                  <Typography
                    variant="body2"
                    color="success.main"
                    sx={{ ml: 0.5 }}
                  >
                    +15% vs last month
                  </Typography>
                </Box>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper elevation={2} sx={{ p: 2, height: "100%" }}>
                <Typography variant="subtitle2" color="textSecondary">
                  Conversion Rate
                </Typography>
                <Typography variant="h4">
                  {analyticsData.conversionRate}%
                </Typography>
                <Box sx={{ display: "flex", alignItems: "center", mt: 1 }}>
                  <TrendingDownIcon color="error" fontSize="small" />
                  <Typography
                    variant="body2"
                    color="error.main"
                    sx={{ ml: 0.5 }}
                  >
                    -0.5% vs last month
                  </Typography>
                </Box>
              </Paper>
            </Grid>
          </Grid>

          {/* Agent Status Section */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h5">Agent System</Typography>
              </Box>
              {/* Use the SimpleAgentStatus component instead of AgentStatusSection */}
              <Box sx={{ mb: 3 }}>
                <SimpleAgentStatus />
              </Box>
            </Grid>
          </Grid>

          {/* Main Content */}
          <Grid container spacing={3}>
            {/* Performance Chart */}
            <Grid item xs={12} md={8}>
              <Paper elevation={2} sx={{ p: 2 }}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mb: 2,
                  }}
                >
                  <Typography variant="h6">Performance Metrics</Typography>
                  <Box>
                    <Chip label="Last 7 Months" size="small" />
                  </Box>
                </Box>
                <Divider sx={{ mb: 2 }} />
                <Box
                  sx={{
                    height: 300,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    border: "1px dashed #ccc",
                    borderRadius: 1,
                  }}
                >
                  <Typography>Chart data loaded successfully.</Typography>
                </Box>
              </Paper>
            </Grid>

            {/* AI Recommendations */}
            <Grid item xs={12} md={4}>
              <Paper elevation={2} sx={{ p: 2, height: "100%" }}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mb: 2,
                  }}
                >
                  <Typography variant="h6">AI Recommendations</Typography>
                  <IconButton size="small">
                    <MoreVertIcon />
                  </IconButton>
                </Box>
                <Divider sx={{ mb: 2 }} />
                <List>
                  {aiRecommendations.map((rec) => (
                    <ListItem key={rec.id} sx={{ px: 0 }}>
                      <ListItemIcon>
                        <LightbulbIcon color="warning" />
                      </ListItemIcon>
                      <ListItemText
                        primary={rec.title}
                        secondary={`Confidence: ${rec.confidence}%`}
                      />
                    </ListItem>
                  ))}
                </List>
                <Button
                  variant="outlined"
                  endIcon={<ArrowForwardIcon />}
                  fullWidth
                  sx={{ mt: 2 }}
                >
                  View All Recommendations
                </Button>
              </Paper>
            </Grid>

            {/* Recent Tasks */}
            <Grid item xs={12} md={6}>
              <Paper elevation={2} sx={{ p: 2 }}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mb: 2,
                  }}
                >
                  <Typography variant="h6">Recent Tasks</Typography>
                  <Button size="small" endIcon={<ArrowForwardIcon />}>
                    View All
                  </Button>
                </Box>
                <Divider sx={{ mb: 2 }} />
                <List>
                  {recentTasks.map((task) => (
                    <ListItem
                      key={task.id}
                      secondaryAction={
                        <Chip
                          label={task.status}
                          size="small"
                          color={
                            task.status === "completed"
                              ? "success"
                              : task.status === "in-progress"
                              ? "primary"
                              : "default"
                          }
                        />
                      }
                    >
                      <ListItemIcon>
                        {task.type === "email" && <EmailIcon />}
                        {task.type === "seo" && <SearchIcon />}
                        {task.type === "linkedin" && <LinkedInIcon />}
                        {task.type === "ai" && <LightbulbIcon />}
                      </ListItemIcon>
                      <ListItemText
                        primary={task.title}
                        secondary={`Due: ${new Date(
                          task.dueDate
                        ).toLocaleDateString()}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Paper>
            </Grid>

            {/* Top Keywords */}
            <Grid item xs={12} md={6}>
              <Paper elevation={2} sx={{ p: 2 }}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mb: 2,
                  }}
                >
                  <Typography variant="h6">Top SEO Keywords</Typography>
                  <Button size="small" endIcon={<ArrowForwardIcon />}>
                    SEO Dashboard
                  </Button>
                </Box>
                <Divider sx={{ mb: 2 }} />
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 2 }}>
                  {analyticsData.topKeywords.map((keyword, index) => (
                    <Chip
                      key={index}
                      label={keyword}
                      color={index < 2 ? "primary" : "default"}
                      variant={index < 2 ? "filled" : "outlined"}
                    />
                  ))}
                </Box>
                <Typography variant="body2" color="textSecondary">
                  Last updated:{" "}
                  {new Date(analyticsData.lastUpdated).toLocaleString()}
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </>
      )}
    </Box>
  );
};

export default Dashboard;
