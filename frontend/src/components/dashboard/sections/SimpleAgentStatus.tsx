import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip
} from '@mui/material';
import axios from 'axios';

interface Agent {
  id: string;
  name: string;
  role: string;
  status: string;
  success_rate: number;
  error_rate: number;
  last_check_time: string;
}

const SimpleAgentStatus = () => {
  const [agents, setAgents] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);

  useEffect(() => {
    const fetchAgents = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await axios.get('http://localhost:3001/api/agents');
        
        if (response.data && response.data.data) {
          setAgents(response.data.data as any);
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching agents:', err);
        setError('Failed to load agents. Please try again later.');
        setLoading(false);
      }
    };
    
    fetchAgents();
  }, []);

  const getStatusColor = (status: string): 'success' | 'error' | 'warning' | 'default' => {
    switch (status.toLowerCase()) {
      case 'running':
        return 'success';
      case 'error':
        return 'error';
      case 'idle':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Paper elevation={2} sx={{ p: 3, height: '100%' }}>
      <Typography variant="h6" gutterBottom>Simple Agent Status</Typography>
      
      {agents.length === 0 ? (
        <Alert severity="info">No agents found.</Alert>
      ) : (
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Role</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Success Rate</TableCell>
                <TableCell align="right">Error Rate</TableCell>
                <TableCell>Last Check</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {agents.map((agent) => (
                <TableRow key={agent.id}>
                  <TableCell>{agent.name}</TableCell>
                  <TableCell>{agent.role}</TableCell>
                  <TableCell>
                    <Chip 
                      label={agent.status.toUpperCase()} 
                      color={getStatusColor(agent.status)} 
                      size="small" 
                    />
                  </TableCell>
                  <TableCell align="right">
                    {agent.success_rate ? `${agent.success_rate.toFixed(1)}%` : 'N/A'}
                  </TableCell>
                  <TableCell align="right">
                    {agent.error_rate ? `${agent.error_rate.toFixed(1)}%` : 'N/A'}
                  </TableCell>
                  <TableCell>
                    {agent.last_check_time ? new Date(agent.last_check_time).toLocaleTimeString() : 'Never'}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Paper>
  );
};

export default SimpleAgentStatus;
