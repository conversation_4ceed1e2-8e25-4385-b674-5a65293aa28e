import AccessTimeIcon from "@mui/icons-material/AccessTime";
import ErrorIcon from "@mui/icons-material/Error";
import InfoIcon from "@mui/icons-material/Info";
import WarningIcon from "@mui/icons-material/Warning";
import {
    Alert,
    Box,
    Card,
    CardContent,
    Chip,
    CircularProgress,
    Divider,
    Grid,
    LinearProgress,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    Paper,
    Tab,
    Tabs,
    Typography,
} from "@mui/material";
import axios from "axios";
import React, { useEffect, useState } from "react";

interface TabPanelProps {
  children?: any;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`agent-tabpanel-${index}`}
      aria-labelledby={`agent-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `agent-tab-${index}`,
    "aria-controls": `agent-tabpanel-${index}`,
  };
}

interface AgentDetailDashboardProps {
  agentId: string;
}

const API_URL = process.env.REACT_APP_API_URL || "http://localhost:3001";

const AgentDetailDashboard = ({ agentId,
 }: AgentDetailDashboardProps) => {
  const [value, setValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = React.useState(null);
  const [agent, setAgent] = React.useState(null);
  const [metrics, setMetrics] = React.useState(null);
  const [logs, setLogs] = React.useState<any[]>([]);

  const handleChange = (_event: any, newValue: number) => {
    setValue(newValue);
  };

  useEffect(() => {
    const fetchAgentDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch agent details
        const agentResponse = await axios.get(
          `${API_URL}/api/agents/${agentId}`
        );
        setAgent(agentResponse.data.data as any);

        // Fetch agent metrics (this endpoint might need to be implemented)
        try {
          const metricsResponse = await axios.get(
            `${API_URL}/api/agents/${agentId}/metrics`
          );
          setMetrics(metricsResponse.data.data as any);
        } catch (metricsError) {
          console.warn("Could not fetch agent metrics:", metricsError);
          // Don't set an error, just continue without metrics
        }

        // Fetch agent logs (this endpoint might need to be implemented)
        try {
          const logsResponse = await axios.get(
            `${API_URL}/api/agents/${agentId}/logs`
          );
          setLogs(logsResponse.data.data || []);
        } catch (logsError) {
          console.warn("Could not fetch agent logs:", logsError);
          // Don't set an error, just continue without logs
        }

        setLoading(false);
      } catch (err) {
        console.error("Error fetching agent details:", err);
        setError("Failed to load agent details. Please try again later.");
        setLoading(false);
      }
    };

    if (agentId) {
      fetchAgentDetails();
    }
  }, [agentId]);

  const renderAgentSpecificMetrics = () => {
    if (!agent) return null;

    switch (agent.role) {
      case "TENDER_MONITOR":
        return (
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Tender Processing
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      Tenders Processed:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {metrics?.tendersProcessed || 0}
                    </Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      New Opportunities:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {metrics?.newOpportunities || 0}
                    </Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      High Priority Tenders:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {metrics?.highPriorityTenders || 0}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Source Performance
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      eTenders Success Rate:
                    </Typography>
                    <Box sx={{ width: "100%" }}>
                      <LinearProgress
                        variant="determinate"
                        value={metrics?.sourcePerformance?.eTenders || 0}
                        color="success"
                        sx={{ height: 10, borderRadius: 5 }}
                      />
                      <Typography variant="body2" align="right">
                        {metrics?.sourcePerformance?.eTenders || 0}%
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      Eskom Success Rate:
                    </Typography>
                    <Box sx={{ width: "100%" }}>
                      <LinearProgress
                        variant="determinate"
                        value={metrics?.sourcePerformance?.eskom || 0}
                        color="success"
                        sx={{ height: 10, borderRadius: 5 }}
                      />
                      <Typography variant="body2" align="right">
                        {metrics?.sourcePerformance?.eskom || 0}%
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      case "OUTREACH_EMAIL_GENERATOR":
      case "EMAIL":
        return (
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Email Processing
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      Emails Generated:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {metrics?.emailsGenerated || 0}
                    </Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      Emails Sent:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {metrics?.emailsSent || 0}
                    </Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      Queue Size:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {metrics?.queueSize || 0}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Email Performance
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      Delivery Rate:
                    </Typography>
                    <Box sx={{ width: "100%" }}>
                      <LinearProgress
                        variant="determinate"
                        value={metrics?.deliveryRate || 0}
                        color="success"
                        sx={{ height: 10, borderRadius: 5 }}
                      />
                      <Typography variant="body2" align="right">
                        {metrics?.deliveryRate || 0}%
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      Template Usage:
                    </Typography>
                    <Box sx={{ width: "100%" }}>
                      <LinearProgress
                        variant="determinate"
                        value={metrics?.templateUsage || 0}
                        color="info"
                        sx={{ height: 10, borderRadius: 5 }}
                      />
                      <Typography variant="body2" align="right">
                        {metrics?.templateUsage || 0}%
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      case "ANALYTICS":
        return (
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Analytics Processing
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      Metrics Collected:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {metrics?.metricsCollected || 0}
                    </Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      Reports Generated:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {metrics?.reportsGenerated || 0}
                    </Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      Data Points Analyzed:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {metrics?.dataPointsAnalyzed || 0}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Report Categories
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      Performance Reports:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {metrics?.reportCategories?.performance || 0}
                    </Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      Tender Reports:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {metrics?.reportCategories?.tender || 0}
                    </Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 180 }}>
                      Email Reports:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {metrics?.reportCategories?.email || 0}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      default:
        return (
          <Alert severity="info">
            No specific metrics available for this agent type.
          </Alert>
        );
    }
  };

  // Get the icon type based on severity
  const getLogSeverityIconType = (severity: string) => {
    switch (severity.toLowerCase()) {
      case "error":
        return "error";
      case "warning":
        return "warning";
      case "info":
      default:
        return "info";
    }
  };

  // Render the actual icon based on the type
  const renderLogSeverityIcon = (severity: string) => {
    const iconType = getLogSeverityIconType(severity);

    if (iconType === "error") return <ErrorIcon color="error" />;
    if (iconType === "warning") return <WarningIcon color="warning" />;
    return <InfoIcon color="info" />;
  };

  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!agent) {
    return <Alert severity="warning">Agent not found or not available.</Alert>;
  }

  return (
    <Paper elevation={2} sx={{ p: 3, height: "100%" }}>
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="agent detail tabs"
        >
          <Tab label="Overview" {...a11yProps(0)} />
          <Tab label="Metrics" {...a11yProps(1)} />
          <Tab label="Logs" {...a11yProps(2)} />
        </Tabs>
      </Box>

      <TabPanel value={value} index={0}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h5" gutterBottom>
            {agent.name}
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Agent Information
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 120 }}>
                      ID:
                    </Typography>
                    <Typography variant="body1">{agent.id}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 120 }}>
                      Role:
                    </Typography>
                    <Chip label={agent.role} color="primary" size="small" />
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 120 }}>
                      Status:
                    </Typography>
                    <Chip
                      label={agent.status}
                      color={
                        agent.status.toLowerCase() === "running"
                          ? "success"
                          : agent.status.toLowerCase() === "error"
                          ? "error"
                          : "default"
                      }
                      size="small"
                    />
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 120 }}>
                      Last Check:
                    </Typography>
                    <Typography variant="body1">
                      {new Date(agent.last_check_time).toLocaleString()}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Performance
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 120 }}>
                      Success Rate:
                    </Typography>
                    <Box sx={{ width: "100%" }}>
                      <LinearProgress
                        variant="determinate"
                        value={agent.success_rate}
                        color="success"
                        sx={{ height: 10, borderRadius: 5 }}
                      />
                      <Typography variant="body2" align="right">
                        {agent.success_rate}%
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 120 }}>
                      Error Rate:
                    </Typography>
                    <Box sx={{ width: "100%" }}>
                      <LinearProgress
                        variant="determinate"
                        value={agent.error_rate}
                        color="error"
                        sx={{ height: 10, borderRadius: 5 }}
                      />
                      <Typography variant="body2" align="right">
                        {agent.error_rate}%
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 120 }}>
                      Latency:
                    </Typography>
                    <Typography variant="body1">{agent.latency}ms</Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </TabPanel>

      <TabPanel value={value} index={1}>
        <Typography variant="h6" gutterBottom>
          Agent-Specific Metrics
        </Typography>
        {renderAgentSpecificMetrics()}
      </TabPanel>

      <TabPanel value={value} index={2}>
        <Typography variant="h6" gutterBottom>
          Recent Logs
        </Typography>
        {logs.length > 0 ? (
          <List>
            {logs.map((log, index) => (
              <ListItem key={index} divider={index < logs.length - 1}>
                <ListItemIcon>{renderLogSeverityIcon(log.level)}</ListItemIcon>
                <ListItemText
                  primary={log.message}
                  secondary={
                    <React.Fragment>
                      <AccessTimeIcon
                        fontSize="small"
                        sx={{ verticalAlign: "middle", mr: 0.5 }}
                      />
                      {new Date(log.timestamp).toLocaleString()}
                    </React.Fragment>
                  }
                />
              </ListItem>
            ))}
          </List>
        ) : (
          <Alert severity="info">No logs available.</Alert>
        )}
      </TabPanel>
    </Paper>
  );
};

export default AgentDetailDashboard;
