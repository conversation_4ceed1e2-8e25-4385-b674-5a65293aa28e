import React from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import PersonIcon from '@mui/icons-material/Person';

interface LinkedInMessageSectionProps {
  sector: string;
}

const LinkedInMessageSection = ({ sector }: LinkedInMessageSectionProps) => {
  const [selectedRole, setSelectedRole] = React.useState('all');

  // In a real implementation, this would fetch data from an API
  // For now, we'll use mock data

  const templates = [
    {
      id: '1',
      name: 'Power Plant Manager Connection',
      role: 'manager',
      sector: 'power',
      messagePreview: 'I noticed your work in power plant operations and wanted to connect regarding our water treatment solutions...',
      successRate: 68
    },
    {
      id: '2',
      name: 'HVAC Engineer Outreach',
      role: 'engineer',
      sector: 'hvac',
      messagePreview: 'As a fellow professional in HVAC systems, I thought you might be interested in our innovative approach to water treatment...',
      successRate: 72
    },
    {
      id: '3',
      name: 'Mining Procurement Specialist',
      role: 'buyer',
      sector: 'mining',
      messagePreview: "I saw you're involved in procurement for mining operations and wanted to share how we've helped similar companies reduce costs...",
      successRate: 65
    },
    {
      id: '4',
      name: 'Oil & Gas Technician Connection',
      role: 'technician',
      sector: 'oil_gas',
      messagePreview: "Your experience with maintenance in oil & gas facilities caught my attention. I'd like to connect regarding our solutions...",
      successRate: 58,
    }
  ];

  // Filter templates based on sector and role
  let filteredTemplates = templates;

  if (sector !== 'all') {
    filteredTemplates = filteredTemplates.filter(template => template.sector === sector);
  }

  if (selectedRole !== 'all') {
    filteredTemplates = filteredTemplates.filter(template => template.role === selectedRole);
  }

  const handleRoleChange = (event: SelectChangeEvent) => {
    setSelectedRole(event.target.value);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'manager':
        return 'primary';
      case 'engineer':
        return 'secondary';
      case 'buyer':
        return 'success';
      case 'technician':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="body2" color="text.secondary">
          {filteredTemplates.length} LinkedIn templates
        </Typography>

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel id="linkedin-role-select-label">Role</InputLabel>
          <Select
            labelId="linkedin-role-select-label"
            id="linkedin-role-select"
            value={selectedRole}
            label="Role"
            onChange={handleRoleChange}
          >
            <MenuItem value="all">All Roles</MenuItem>
            <MenuItem value="manager">Managers</MenuItem>
            <MenuItem value="engineer">Engineers</MenuItem>
            <MenuItem value="buyer">Buyers</MenuItem>
            <MenuItem value="technician">Technicians</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {filteredTemplates.length === 0 ? (
        <Typography align="center">No templates available for the selected filters.</Typography>
      ) : (
        <List sx={{ maxHeight: 300, overflow: 'auto' }}>
          {filteredTemplates.map((template, index) => (
            <ListItem key={index} alignItems="flex-start" sx={{ mb: 1 }}>
              <ListItemAvatar>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <PersonIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Box display="flex" alignItems="center" gap={1}>
                    {template.name}
                    <Chip
                      label={template.role}
                      size="small"
                      color={getRoleColor(template.role) as any}
                    />
                  </Box>
                }
                secondary={
                  <>
                    <Typography component="span" variant="body2" color="text.primary">
                      {template.messagePreview}
                    </Typography>
                    <br />
                    <Typography component="span" variant="caption">
                      Success rate: {template.successRate}%
                    </Typography>
                  </>
                }
              />
            </ListItem>
          ))}
        </List>
      )}

      <Box display="flex" justifyContent="center" mt={2}>
        <Button variant="contained" color="primary" startIcon={<LinkedInIcon />}>
          Generate LinkedIn Message
        </Button>
      </Box>
    </Box>
  );
};

export default LinkedInMessageSection;
