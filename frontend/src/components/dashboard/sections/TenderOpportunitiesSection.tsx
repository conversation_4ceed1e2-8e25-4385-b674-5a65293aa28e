import React from 'react';
import { Box, Typography, List, ListItem, ListItemText, Chip, Button } from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';

interface TenderOpportunitiesSectionProps {
  sector: string;
}

const TenderOpportunitiesSection = ({ sector }: TenderOpportunitiesSectionProps) => {
  // In a real implementation, this would fetch data from an API
  // For now, we'll use mock data

  const tenders = [
    {
      id: 'T2023-001',
      title: 'Water Treatment System for Power Plant',
      deadline: '2023-06-15',
      value: 'R 2.5M',
      sector: 'power',
      priority: 'high'
    },
    {
      id: 'T2023-002',
      title: 'HVAC Water Treatment Services',
      deadline: '2023-06-20',
      value: 'R 1.2M',
      sector: 'hvac',
      priority: 'medium'
    },
    {
      id: 'T2023-003',
      title: 'Mining Process Water Treatment',
      deadline: '2023-06-25',
      value: 'R 3.8M',
      sector: 'mining',
      priority: 'high'
    },
    {
      id: 'T2023-004',
      title: 'Cooling Tower Chemical Supply',
      deadline: '2023-06-30',
      value: 'R 950K',
      sector: 'cooling',
      priority: 'medium'
    }
  ];

  // Filter tenders based on sector
  const filteredTenders = sector === 'all'
    ? tenders
    : tenders.filter(tender => tender.sector === sector);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        {filteredTenders.length} active tenders
      </Typography>

      {filteredTenders.length === 0 ? (
        <Typography align="center">No tenders available for the selected sector.</Typography>
      ) : (
        <List sx={{ maxHeight: 300, overflow: 'auto' }}>
          {filteredTenders.map((tender, index) => (
            <ListItem key={index} divider={index < filteredTenders.length - 1}>
              <ListItemText
                primary={
                  <Box display="flex" alignItems="center" gap={1}>
                    {tender.title}
                    <Chip
                      label={tender.priority}
                      color={getPriorityColor(tender.priority) as any}
                      size="small"
                    />
                  </Box>
                }
                secondary={
                  <>
                    <Typography component="span" variant="caption">
                      ID: {tender.id} • Value: {tender.value}
                    </Typography>
                    <br />
                    <Typography component="span" variant="caption" color="error">
                      Deadline: {tender.deadline}
                    </Typography>
                  </>
                }
              />
            </ListItem>
          ))}
        </List>
      )}

      <Box display="flex" justifyContent="center" mt={2}>
        <Button variant="outlined" startIcon={<NotificationsIcon />}>
          Set Alert
        </Button>
      </Box>
    </Box>
  );
};

export default TenderOpportunitiesSection;
