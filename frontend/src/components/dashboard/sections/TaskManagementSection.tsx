import React, { useState } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Chip,
  Button,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import FilterListIcon from '@mui/icons-material/FilterList';

interface TaskManagementSectionProps {
  sector: string;
}

const TaskManagementSection = ({ sector }: TaskManagementSectionProps) => {
  const [filter, setFilter] = React.useState('all');

  // In a real implementation, this would fetch data from an API
  // For now, we'll use mock data

  const tasks = [
    {
      id: '1',
      title: 'Follow up with Power Plant leads',
      dueDate: '2023-06-10',
      status: 'pending',
      priority: 'high',
      sector: 'power'
    },
    {
      id: '2',
      title: 'Update HVAC email templates',
      dueDate: '2023-06-12',
      status: 'pending',
      priority: 'medium',
      sector: 'hvac'
    },
    {
      id: '3',
      title: 'Prepare Mining sector report',
      dueDate: '2023-06-15',
      status: 'pending',
      priority: 'high',
      sector: 'mining'
    },
    {
      id: '4',
      title: 'Review Oil & Gas campaign results',
      dueDate: '2023-06-08',
      status: 'completed',
      priority: 'medium',
      sector: 'oil_gas'
    },
    {
      id: '5',
      title: 'Research Agriculture sector trends',
      dueDate: '2023-06-20',
      status: 'pending',
      priority: 'low',
      sector: 'agriculture'
    }
  ];

  // Filter tasks based on sector and status
  let filteredTasks = tasks;

  if (sector !== 'all') {
    filteredTasks = filteredTasks.filter(task => task.sector === sector);
  }

  if (filter !== 'all') {
    filteredTasks = filteredTasks.filter(task => task.status === filter);
  }

  const handleFilterChange = (event: SelectChangeEvent) => {
    setFilter(event.target.value);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="body2" color="text.secondary">
          {filteredTasks.length} tasks
        </Typography>

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel id="task-filter-label">Status</InputLabel>
          <Select
            labelId="task-filter-label"
            id="task-filter"
            value={filter}
            label="Status"
            onChange={handleFilterChange}
          >
            <MenuItem value="all">All</MenuItem>
            <MenuItem value="pending">Pending</MenuItem>
            <MenuItem value="completed">Completed</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {filteredTasks.length === 0 ? (
        <Typography align="center">No tasks available for the selected filters.</Typography>
      ) : (
        <List sx={{ maxHeight: 300, overflow: 'auto' }}>
          {filteredTasks.map((task, index) => (
            <ListItem
              key={index}
              dense
              secondaryAction={
                <Chip
                  label={task.priority}
                  size="small"
                  color={getPriorityColor(task.priority) as any}
                />
              }
            >
              <ListItemIcon>
                <Checkbox
                  edge="start"
                  checked={task.status === 'completed'}
                  tabIndex={-1}
                  disableRipple
                />
              </ListItemIcon>
              <ListItemText
                primary={task.title}
                secondary={`Due: ${task.dueDate}`}
                primaryTypographyProps={{
                  style: {
                    textDecoration: task.status === 'completed' ? 'line-through' : 'none'
                  }
                }}
              />
            </ListItem>
          ))}
        </List>
      )}

      <Box display="flex" justifyContent="center" mt={2}>
        <Button variant="contained" startIcon={<AddIcon />}>
          Add Task
        </Button>
      </Box>
    </Box>
  );
};

export default TaskManagementSection;
