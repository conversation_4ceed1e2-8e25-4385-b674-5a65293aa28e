import React from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Button
} from '@mui/material';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import WarningIcon from '@mui/icons-material/Warning';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

interface AIRecommendationsSectionProps {
  sector: string;
}

const AIRecommendationsSection = ({ sector }: AIRecommendationsSectionProps) => {
  // In a real implementation, this would fetch data from an API
  // For now, we'll use mock data

  const recommendations = [
    {
      id: '1',
      type: 'opportunity',
      title: 'Increase outreach to Power Plant Engineers',
      description: 'Engineers in the power sector have a 35% higher response rate than managers.',
      sector: 'power',
      priority: 'high'
    },
    {
      id: '2',
      type: 'insight',
      title: 'HVAC sector engagement increasing',
      description: 'Email open rates in the HVAC sector have increased by 18% in the last month.',
      sector: 'hvac',
      priority: 'medium'
    },
    {
      id: '3',
      type: 'action',
      title: 'Follow up with Mining leads',
      description: '12 leads from the mining sector have opened emails but not responded.',
      sector: 'mining',
      priority: 'high'
    },
    {
      id: '4',
      type: 'warning',
      title: 'Oil & Gas campaign underperforming',
      description: 'The latest campaign to Oil & Gas buyers has a 15% lower open rate than average.',
      sector: 'oil_gas',
      priority: 'medium'
    }
  ];

  // Filter recommendations based on sector
  const filteredRecommendations = sector === 'all'
    ? recommendations
    : recommendations.filter(rec => rec.sector === sector);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'opportunity':
        return <TrendingUpIcon color="success" />;
      case 'insight':
        return <LightbulbIcon color="primary" />;
      case 'action':
        return <CheckCircleIcon color="info" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      default:
        return <LightbulbIcon />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        AI-generated recommendations
      </Typography>

      {filteredRecommendations.length === 0 ? (
        <Typography align="center">No recommendations available for the selected sector.</Typography>
      ) : (
        <List sx={{ maxHeight: 300, overflow: 'auto' }}>
          {filteredRecommendations.map((rec, index) => (
            <ListItem key={index} alignItems="flex-start" sx={{ mb: 1 }}>
              <ListItemIcon>
                {getTypeIcon(rec.type)}
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box display="flex" alignItems="center" gap={1}>
                    {rec.title}
                    <Chip
                      label={rec.priority}
                      size="small"
                      color={getPriorityColor(rec.priority) as any}
                    />
                  </Box>
                }
                secondary={rec.description}
              />
            </ListItem>
          ))}
        </List>
      )}

      <Box display="flex" justifyContent="center" mt={2}>
        <Button variant="outlined" startIcon={<LightbulbIcon />}>
          Generate More Insights
        </Button>
      </Box>
    </Box>
  );
};

export default AIRecommendationsSection;
