import React, { useEffect } from "react";
import {
  <PERSON>,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Button,
  CircularProgress,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from "@mui/material";
import EmailIcon from "@mui/icons-material/Email";
import PersonIcon from "@mui/icons-material/Person";
import BusinessIcon from "@mui/icons-material/Business";
import EngineeringIcon from "@mui/icons-material/Engineering";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import BuildIcon from "@mui/icons-material/Build";

interface EmailTemplate {
  id: string;
  name: string;
  role: "manager" | "engineer" | "buyer" | "technician";
  sector: string;
  subject: string;
  createdAt: string;
  usageCount: number;
}

interface EmailGenerationSectionProps {
  sector: string;
}

const EmailGenerationSection = ({ sector }: EmailGenerationSectionProps) => {
  const [templates, setTemplates] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);
  const [selectedRole, setSelectedRole] = React.useState("all");

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        setError(null);

        // In a real implementation, this would be an API call
        // For now, we'll use mock data

        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 500 as any));

        // Mock templates data
        const mockTemplates: EmailTemplate[] = [
          {
            id: "1",
            name: "Power Plant Manager Template",
            role: "manager",
            sector: "power",
            subject:
              "Reduce Water Treatment Costs in Your Power Plant by up to 40%",
            createdAt: "2023-04-15",
            usageCount: 45,
          },
          {
            id: "2",
            name: "HVAC Engineer Template",
            role: "engineer",
            sector: "hvac",
            subject:
              "Advanced Water Treatment Technology for HVAC System Optimization",
            createdAt: "2023-04-16",
            usageCount: 32,
          },
          {
            id: "3",
            name: "Mining Procurement Template",
            role: "buyer",
            sector: "mining",
            subject:
              "Simplified Water Treatment Procurement for Mining Operations",
            createdAt: "2023-04-17",
            usageCount: 28,
          },
          {
            id: "4",
            name: "Oil & Gas Technician Template",
            role: "technician",
            sector: "oil_gas",
            subject:
              "Reduce Water Treatment Maintenance in Oil & Gas Facilities",
            createdAt: "2023-04-18",
            usageCount: 37,
          },
          {
            id: "5",
            name: "Agriculture Manager Template",
            role: "manager",
            sector: "agriculture",
            subject:
              "Efficient Water Treatment Solutions for Agricultural Operations",
            createdAt: "2023-04-19",
            usageCount: 19,
          },
          {
            id: "6",
            name: "Cooling Tower Engineer Template",
            role: "engineer",
            sector: "cooling",
            subject:
              "Technical Data: Film-Forming Amine Technology for Cooling Tower Optimization",
            createdAt: "2023-04-20",
            usageCount: 41,
          },
        ];

        // Filter templates based on sector
        let filteredTemplates = mockTemplates;

        if (sector !== "all") {
          filteredTemplates = mockTemplates.filter(
            (template) => template.sector === sector
          );
        }

        // Filter templates based on role
        if (selectedRole !== "all") {
          filteredTemplates = filteredTemplates.filter(
            (template) => template.role === selectedRole
          );
        }

        setTemplates(filteredTemplates);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching email templates:", error);
        setError("Failed to load templates. Please try again later.");
        setLoading(false);
      }
    };

    fetchTemplates();
  }, [sector, selectedRole]);

  const handleRoleChange = (event: SelectChangeEvent) => {
    setSelectedRole(event.target.value as any);
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "manager":
        return <PersonIcon />;
      case "engineer":
        return <EngineeringIcon />;
      case "buyer":
        return <ShoppingCartIcon />;
      case "technician":
        return <BuildIcon />;
      default:
        return <EmailIcon />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "manager":
        return "primary";
      case "engineer":
        return "secondary";
      case "buyer":
        return "success";
      case "technician":
        return "warning";
      default:
        return "default";
    }
  };

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100%"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100%"
      >
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Typography variant="body2" color="text.secondary">
          {templates.length} templates available
        </Typography>

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel id="role-select-label">Role</InputLabel>
          <Select
            labelId="role-select-label"
            id="role-select"
            value={selectedRole}
            label="Role"
            onChange={handleRoleChange}
          >
            <MenuItem value="all">All Roles</MenuItem>
            <MenuItem value="manager">Managers</MenuItem>
            <MenuItem value="engineer">Engineers</MenuItem>
            <MenuItem value="buyer">Buyers</MenuItem>
            <MenuItem value="technician">Technicians</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <Divider sx={{ mb: 2 }} />

      {templates.length === 0 ? (
        <Typography align="center">
          No templates available for the selected filters.
        </Typography>
      ) : (
        <List sx={{ maxHeight: 300, overflow: "auto" }}>
          {templates.map((template) => (
            <ListItem key={template.id} alignItems="flex-start" sx={{ mb: 1 }}>
              <ListItemAvatar>
                <Avatar>{getRoleIcon(template.role)}</Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Box display="flex" alignItems="center" gap={1}>
                    {template.name}
                    <Chip
                      label={template.role}
                      size="small"
                      color={getRoleColor(template.role) as any}
                    />
                  </Box>
                }
                secondary={
                  <>
                    <Typography
                      component="span"
                      variant="body2"
                      color="text.primary"
                    >
                      {template.subject}
                    </Typography>
                    <br />
                    <Typography component="span" variant="caption">
                      Used {template.usageCount} times • Created{" "}
                      {template.createdAt}
                    </Typography>
                  </>
                }
              />
            </ListItem>
          ))}
        </List>
      )}

      <Box display="flex" justifyContent="center" mt={2}>
        <Button variant="contained" startIcon={<EmailIcon />}>
          Generate New Email
        </Button>
      </Box>
    </Box>
  );
};

export default EmailGenerationSection;
