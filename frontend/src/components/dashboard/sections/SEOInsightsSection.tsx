import React from 'react';
import { Box, Typography, CircularProgress, List, ListItem, ListItemText, Chip } from '@mui/material';

interface SEOInsightsSectionProps {
  sector: string;
}

const SEOInsightsSection = ({ sector }: SEOInsightsSectionProps) => {
  // In a real implementation, this would fetch data from an API
  // For now, we'll use mock data

  const keywords = [
    { keyword: 'water treatment chemicals', position: 12, change: 3 },
    { keyword: 'film forming amine', position: 5, change: 7 },
    { keyword: 'cooling tower treatment', position: 18, change: -2 },
    { keyword: 'boiler water treatment', position: 8, change: 4 },
    { keyword: 'industrial water treatment', position: 15, change: 1 }
  ];

  return (
    <Box>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        Top performing keywords
      </Typography>

      <List sx={{ maxHeight: 300, overflow: 'auto' }}>
        {keywords.map((item, index) => (
          <ListItem key={index} divider={index < keywords.length - 1}>
            <ListItemText
              primary={item.keyword}
              secondary={`Position: ${item.position}`}
            />
            <Chip
              label={item.change > 0 ? `+${item.change}` : item.change}
              color={item.change > 0 ? 'success' : 'error'}
              size="small"
            />
          </ListItem>
        ))}
      </List>

      <Box mt={2} display="flex" justifyContent="center">
        <Typography variant="body2" color="text.secondary">
          Data from Google Search Console
        </Typography>
      </Box>
    </Box>
  );
};

export default SEOInsightsSection;
