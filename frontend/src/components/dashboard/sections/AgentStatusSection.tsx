import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import RefreshIcon from "@mui/icons-material/Refresh";
import StopIcon from "@mui/icons-material/Stop";
import VisibilityIcon from "@mui/icons-material/Visibility";
import {
    Alert,
    Box,
    Button,
    Chip,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    IconButton,
    Paper,
    Tab,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Tabs,
    Tooltip,
    Typography,
} from "@mui/material";
import axios from "axios";
import React, { useEffect } from "react";
import AgentPerformanceChart from "../charts/AgentPerformanceChart";
import AgentDetailDashboard from "./AgentDetailDashboard";
import AgentRealTimeStatus from "./AgentRealTimeStatus";

const API_URL = process.env.REACT_APP_API_URL || "http://localhost:3001";

const AgentStatusSection = () => {
  const [agents, setAgents] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);
  const [actionLoading, setActionLoading] = React.useState(false);
  const [selectedAgent, setSelectedAgent] = React.useState(null);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [tabValue, setValue] = React.useState(0);

  const handleTabChange = (_event: any, newValue: number) => {
    setValue(newValue);
  };

  const handleOpenDialog = (agentId: string) => {
    setSelectedAgent(agentId);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const fetchAgents = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.get(`${API_URL}/api/agents`);
      setAgents(response.data.data || []);
    } catch (err) {
      console.error("Error fetching agents:", err);
      setError("Failed to fetch agent data. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAgents();
    // Set up polling every 30 seconds
    const interval = setInterval(fetchAgents, 30000 as any);
    return () => clearInterval(interval);
  }, []);

  const handleRefresh = () => {
    fetchAgents();
  };

  const handleStartAgents = async () => {
    setActionLoading(true);
    try {
      await axios.post(`${API_URL}/api/agents/start`);
      // Refresh agent list after starting
      await fetchAgents();
    } catch (err) {
      console.error("Error starting agents:", err);
      setError("Failed to start agents. Please try again later.");
    } finally {
      setActionLoading(false);
    }
  };

  const handleStopAgents = async () => {
    setActionLoading(true);
    try {
      await axios.post(`${API_URL}/api/agents/stop`);
      // Refresh agent list after stopping
      await fetchAgents();
    } catch (err) {
      console.error("Error stopping agents:", err);
      setError("Failed to stop agents. Please try again later.");
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusColor = (
    status: string
  ): "success" | "error" | "warning" | "info" | "default" => {
    switch (status.toLowerCase()) {
      case "running":
        return "success";
      case "idle":
        return "info";
      case "error":
        return "error";
      case "initializing":
        return "warning";
      default:
        return "default";
    }
  };

  const renderStatusChip = (status: string) => {
    const color = getStatusColor(status);

    // Instead of using the icon prop, we'll just use the label
    return <Chip label={status.toUpperCase()} color={color} size="small" />;
  };

  return (
    <Paper elevation={2} sx={{ p: 3, height: "100%" }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Typography variant="h6">Agent System Status</Typography>
        <Box>
          <Tooltip title="Refresh">
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="contained"
            color="success"
            startIcon={<PlayArrowIcon />}
            onClick={handleStartAgents}
            disabled={loading || actionLoading}
            sx={{ ml: 1 }}
          >
            Start
          </Button>
          <Button
            variant="contained"
            color="error"
            startIcon={<StopIcon />}
            onClick={handleStopAgents}
            disabled={loading || actionLoading}
            sx={{ ml: 1 }}
          >
            Stop
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {agents.length === 0 ? (
            <Alert severity="info">
              No agents found. Please start the agent system.
            </Alert>
          ) : (
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Role</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell align="right">Success Rate</TableCell>
                    <TableCell align="right">Error Rate</TableCell>
                    <TableCell>Last Check</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {agents.map((agent) => (
                    <TableRow key={agent.id}>
                      <TableCell>{agent.name}</TableCell>
                      <TableCell>{agent.role}</TableCell>
                      <TableCell>{renderStatusChip(agent.status)}</TableCell>
                      <TableCell align="right">
                        {agent.success_rate
                          ? `${agent.success_rate.toFixed(1)}%`
                          : "N/A"}
                      </TableCell>
                      <TableCell align="right">
                        {agent.error_rate
                          ? `${agent.error_rate.toFixed(1)}%`
                          : "N/A"}
                      </TableCell>
                      <TableCell>
                        {agent.last_check_time
                          ? new Date(agent.last_check_time).toLocaleTimeString()
                          : "Never"}
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenDialog(agent.id)}
                            color="primary"
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          <Box sx={{ borderBottom: 1, borderColor: "divider", mt: 3 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label="agent dashboard tabs"
            >
              <Tab label="Overview" id="tab-0" aria-controls="tabpanel-0" />
              <Tab
                label="Real-Time Status"
                id="tab-1"
                aria-controls="tabpanel-1"
              />
              <Tab label="Performance" id="tab-2" aria-controls="tabpanel-2" />
            </Tabs>
          </Box>

          <Box
            role="tabpanel"
            hidden={tabValue !== 0}
            id="tabpanel-0"
            aria-labelledby="tab-0"
            sx={{ mt: 2 }}
          >
            {tabValue === 0 && (
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Paper
                    elevation={1}
                    sx={{ p: 2, bgcolor: "background.default" }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      System Health
                    </Typography>
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      {agents.every(
                        (a) => a.status.toLowerCase() !== "error"
                      ) ? (
                        <>
                          <CheckCircleIcon color="success" sx={{ mr: 1 }} />
                          <Typography>
                            All agents are functioning properly
                          </Typography>
                        </>
                      ) : (
                        <>
                          <ErrorIcon color="error" sx={{ mr: 1 }} />
                          <Typography>
                            {
                              agents.filter(
                                (a) => a.status.toLowerCase() === "error"
                              ).length
                            }{" "}
                            agent(s) in error state
                          </Typography>
                        </>
                      )}
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Paper
                    elevation={1}
                    sx={{ p: 2, bgcolor: "background.default" }}
                  >
                    <Typography variant="subtitle2" gutterBottom>
                      Agent Activity
                    </Typography>
                    <Typography>
                      {
                        agents.filter(
                          (a) => a.status.toLowerCase() === "running"
                        ).length
                      }{" "}
                      active agent(s)
                    </Typography>
                    <Typography>
                      Last system check: {new Date().toLocaleString()}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            )}
          </Box>

          <Box
            role="tabpanel"
            hidden={tabValue !== 1}
            id="tabpanel-1"
            aria-labelledby="tab-1"
            sx={{ mt: 2 }}
          >
            {tabValue === 1 && <AgentRealTimeStatus />}
          </Box>

          <Box
            role="tabpanel"
            hidden={tabValue !== 2}
            id="tabpanel-2"
            aria-labelledby="tab-2"
            sx={{ mt: 2 }}
          >
            {tabValue === 2 && (
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <AgentPerformanceChart metric="success_rate" height={300} />
                </Grid>
                <Grid item xs={12} md={6}>
                  <AgentPerformanceChart metric="error_rate" height={300} />
                </Grid>
                <Grid item xs={12}>
                  <AgentPerformanceChart metric="latency" height={300} />
                </Grid>
              </Grid>
            )}
          </Box>

          {/* Agent Detail Dialog */}
          <Dialog
            open={dialogOpen}
            onClose={handleCloseDialog}
            maxWidth="lg"
            fullWidth
          >
            <DialogTitle>Agent Details</DialogTitle>
            <DialogContent dividers>
              {selectedAgent && (
                <AgentDetailDashboard agentId={selectedAgent} />
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog}>Close</Button>
            </DialogActions>
          </Dialog>
        </>
      )}
    </Paper>
  );
};

export default AgentStatusSection;
