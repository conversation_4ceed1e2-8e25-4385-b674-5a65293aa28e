import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Bar<PERSON>hart,
  Bar
} from 'recharts';

interface MarketingMetricsSectionProps {
  sector: string;
  tabIndex: number;
}

const MarketingMetricsSection = ({ sector, tabIndex }: MarketingMetricsSectionProps) => {
  // In a real implementation, this would fetch data from an API
  // For now, we'll use mock data

  // Customer Acquisition Metrics
  const acquisitionData = [
    { month: 'Jan', leads: 45, qualified: 22, converted: 8 },
    { month: 'Feb', leads: 52, qualified: 25, converted: 10 },
    { month: 'Mar', leads: 58, qualified: 30, converted: 12 },
    { month: 'Apr', leads: 65, qualified: 35, converted: 15 },
    { month: 'May', leads: 72, qualified: 38, converted: 18 }
  ];

  // Content Performance Metrics
  const contentData = [
    { content: 'Water Treatment Blog', views: 1250, engagement: 320, leads: 18 },
    { content: 'FFA Technology Whitepaper', views: 850, engagement: 210, leads: 25 },
    { content: 'Case Study: Power Plant', views: 720, engagement: 180, leads: 22 },
    { content: 'HVAC Solutions Guide', views: 680, engagement: 150, leads: 15 },
    { content: 'Mining Water Treatment Video', views: 950, engagement: 280, leads: 20 }
  ];

  // Competitive Intelligence
  const competitorData = [
    { name: 'Mexel', marketShare: 22, growth: 15, sentiment: 85 },
    { name: 'Competitor A', marketShare: 28, growth: 8, sentiment: 72 },
    { name: 'Competitor B', marketShare: 18, growth: 12, sentiment: 68 },
    { name: 'Competitor C', marketShare: 15, growth: 5, sentiment: 75 },
    { name: 'Others', marketShare: 17, growth: 3, sentiment: 65 }
  ];

  // Keywords Performance
  const keywordsData = [
    { keyword: 'film forming amine', position: 3, traffic: 450, conversion: 5.2 },
    { keyword: 'water treatment chemicals', position: 8, traffic: 820, conversion: 3.8 },
    { keyword: 'cooling tower treatment', position: 5, traffic: 380, conversion: 4.5 },
    { keyword: 'boiler water treatment', position: 4, traffic: 520, conversion: 4.9 },
    { keyword: 'industrial water treatment', position: 12, traffic: 650, conversion: 2.7 }
  ];

  // Render Customer Acquisition Tab
  const renderCustomerAcquisitionTab = () => {
    return (
      <Box>
        <Box height={200} mb={2}>
          <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
        </Box>

        <Grid container spacing={2}>
          <Grid item xs={4}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h6" color="primary">
                {acquisitionData[acquisitionData.length - 1].leads}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                New Leads
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={4}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h6" color="secondary">
                {acquisitionData[acquisitionData.length - 1].qualified}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Qualified Leads
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={4}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h6" color="success.main">
                {acquisitionData[acquisitionData.length - 1].converted}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Conversions
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Render Content Performance Tab
  const renderContentPerformanceTab = () => {
    return (
      <Box>
        <TableContainer component={Paper} variant="outlined" sx={{ mb: 2 }}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Content</TableCell>
                <TableCell align="right">Views</TableCell>
                <TableCell align="right">Engagement</TableCell>
                <TableCell align="right">Leads</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {contentData.map((row) => (
                <TableRow key={row.content}>
                  <TableCell component="th" scope="row">
                    {row.content}
                  </TableCell>
                  <TableCell align="right">{row.views}</TableCell>
                  <TableCell align="right">{row.engagement}</TableCell>
                  <TableCell align="right">{row.leads}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        <Box height={150}>
          <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
        </Box>
      </Box>
    );
  };

  // Render Competitive Intelligence Tab
  const renderCompetitiveIntelligenceTab = () => {
    return (
      <Box>
        <Grid container spacing={2} mb={2}>
          <Grid item xs={6}>
            <Box height={150}>
              <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box height={150}>
              <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>
            </Box>
          </Grid>
        </Grid>

        <Typography variant="subtitle2" gutterBottom>
          Top Performing Keywords
        </Typography>

        <List dense>
          {keywordsData.map((item, index) => (
            <React.Fragment key={item.keyword}>
              <ListItem>
                <ListItemText
                  primary={item.keyword}
                  secondary={`Position: ${item.position} | Traffic: ${item.traffic} | Conv: ${item.conversion}%`}
                />
              </ListItem>
              {index < keywordsData.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>
      </Box>
    );
  };

  // Render the appropriate tab content
  const renderTabContent = () => {
    switch (tabIndex) {
      case 0:
        return renderCustomerAcquisitionTab();
      case 1:
        return renderContentPerformanceTab();
      case 2:
        return renderCompetitiveIntelligenceTab();
      default:
        return renderCustomerAcquisitionTab();
    }
  };

  return (
    <Box>
      {renderTabContent()}
    </Box>
  );
};

export default MarketingMetricsSection;
