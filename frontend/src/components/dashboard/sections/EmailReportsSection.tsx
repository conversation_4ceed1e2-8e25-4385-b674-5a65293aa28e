import React from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend
} from 'recharts';

interface EmailReportsSectionProps {
  sector: string;
}

const EmailReportsSection = ({ sector }: EmailReportsSectionProps) => {
  const [timeframe, setTimeframe] = React.useState('month');

  // In a real implementation, this would fetch data from an API
  // For now, we'll use mock data

  const campaigns = [
    {
      id: '1',
      name: 'Power Plant Outreach',
      sent: 120,
      delivered: 115,
      opened: 78,
      clicked: 32,
      replied: 15,
      sector: 'power',
      date: '2023-05-15'
    },
    {
      id: '2',
      name: 'HVAC Solutions',
      sent: 85,
      delivered: 82,
      opened: 45,
      clicked: 18,
      replied: 7,
      sector: 'hvac',
      date: '2023-05-20'
    },
    {
      id: '3',
      name: 'Mining Operations',
      sent: 65,
      delivered: 62,
      opened: 38,
      clicked: 15,
      replied: 6,
      sector: 'mining',
      date: '2023-05-25'
    },
    {
      id: '4',
      name: 'Oil & Gas Solutions',
      sent: 95,
      delivered: 92,
      opened: 52,
      clicked: 24,
      replied: 10,
      sector: 'oil_gas',
      date: '2023-05-30'
    }
  ];

  // Filter campaigns based on sector
  const filteredCampaigns = sector === 'all'
    ? campaigns
    : campaigns.filter(campaign => campaign.sector === sector);

  // Calculate totals and rates
  const totals = filteredCampaigns.reduce((acc, campaign) => {
    return {
      sent: acc.sent + campaign.sent,
      delivered: acc.delivered + campaign.delivered,
      opened: acc.opened + campaign.opened,
      clicked: acc.clicked + campaign.clicked,
      replied: acc.replied + campaign.replied
    };
  }, { sent: 0, delivered: 0, opened: 0, clicked: 0, replied: 0 });

  const rates = {
    deliveryRate: totals.sent > 0 ? (totals.delivered / totals.sent) * 100 : 0,
    openRate: totals.delivered > 0 ? (totals.opened / totals.delivered) * 100 : 0,
    clickRate: totals.opened > 0 ? (totals.clicked / totals.opened) * 100 : 0,
    replyRate: totals.delivered > 0 ? (totals.replied / totals.delivered) * 100 : 0
  };

  // Prepare chart data
  const chartData = [
    { name: 'Opened', value: totals.opened, color: '#8884d8' },
    { name: 'Clicked', value: totals.clicked, color: '#82ca9d' },
    { name: 'Replied', value: totals.replied, color: '#ffc658' },
    { name: 'No Action', value: totals.delivered - totals.opened, color: '#d0d0d0' }
  ];

  const handleTimeframeChange = (event: SelectChangeEvent) => {
    setTimeframe(event.target.value);
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="body2" color="text.secondary">
          Email performance
        </Typography>

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel id="timeframe-select-label">Timeframe</InputLabel>
          <Select
            labelId="timeframe-select-label"
            id="timeframe-select"
            value={timeframe}
            label="Timeframe"
            onChange={handleTimeframeChange}
          >
            <MenuItem value="week">Last Week</MenuItem>
            <MenuItem value="month">Last Month</MenuItem>
            <MenuItem value="quarter">Last Quarter</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <Box display="flex" mb={2}>
        <Box width="50%" pr={1}>
          {/* Due to TypeScript issues with recharts, we'll render a simplified version */}
          <Box sx={{
            height: 150,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: '1px dashed #ccc',
            borderRadius: 1
          }}>
            <Typography>
              Pie chart showing email engagement metrics
            </Typography>
          </Box>
        </Box>

        <Box width="50%" pl={1}>
          <Box mb={1}>
            <Typography variant="caption" color="text.secondary">Open Rate</Typography>
            <Typography variant="h6">{rates.openRate.toFixed(1)}%</Typography>
          </Box>
          <Box mb={1}>
            <Typography variant="caption" color="text.secondary">Click Rate</Typography>
            <Typography variant="h6">{rates.clickRate.toFixed(1)}%</Typography>
          </Box>
          <Box>
            <Typography variant="caption" color="text.secondary">Reply Rate</Typography>
            <Typography variant="h6">{rates.replyRate.toFixed(1)}%</Typography>
          </Box>
        </Box>
      </Box>

      <TableContainer component={Paper} variant="outlined">
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>Campaign</TableCell>
              <TableCell align="right">Sent</TableCell>
              <TableCell align="right">Opened</TableCell>
              <TableCell align="right">Clicked</TableCell>
              <TableCell align="right">Replied</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredCampaigns.map((campaign) => (
              <TableRow key={campaign.id}>
                <TableCell component="th" scope="row">
                  {campaign.name}
                </TableCell>
                <TableCell align="right">{campaign.sent}</TableCell>
                <TableCell align="right">
                  {campaign.opened} ({((campaign.opened / campaign.delivered) * 100).toFixed(1)}%)
                </TableCell>
                <TableCell align="right">
                  {campaign.clicked} ({((campaign.clicked / campaign.opened) * 100).toFixed(1)}%)
                </TableCell>
                <TableCell align="right">
                  {campaign.replied} ({((campaign.replied / campaign.delivered) * 100).toFixed(1)}%)
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default EmailReportsSection;
