import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import DeleteIcon from "@mui/icons-material/Delete";
import ErrorIcon from "@mui/icons-material/Error";
import InfoIcon from "@mui/icons-material/Info";
import NotificationsIcon from "@mui/icons-material/Notifications";
import WarningIcon from "@mui/icons-material/Warning";
import {
    Alert,
    Badge,
    Box,
    Chip,
    CircularProgress,
    Divider,
    IconButton,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    Paper,
    Tooltip,
    Typography,
} from "@mui/material";
import axios from "axios";
import React, { useCallback, useEffect } from "react";
import io from "socket.io-client";

interface AgentUpdate {
  agentId: string;
  status: string;
  message: string;
  timestamp: string;
  level: "info" | "warning" | "error" | "success";
}

const API_URL = process.env.REACT_APP_API_URL || "http://localhost:3001";
const SOCKET_URL = process.env.REACT_APP_SOCKET_URL || "http://localhost:3001";

const AgentRealTimeStatus = () => {
  const [agents, setAgents] = React.useState<any[]>([]);
  const [updates, setUpdates] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);
  const [socket, setSocket] = React.useState(null);
  const [connected, setConnected] = React.useState(false);

  // Fetch initial agent data
  useEffect(() => {
    const fetchAgents = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await axios.get(`${API_URL}/api/agents`);

        if (response.data && response.data.data) {
          setAgents(response.data.data as any);
        }

        setLoading(false);
      } catch (err) {
        console.error("Error fetching agents:", err);
        setError("Failed to load agents. Please try again later.");
        setLoading(false);
      }
    };

    fetchAgents();
  }, []);

  // Set up Socket.IO connection
  useEffect(() => {
    const newSocket = io(SOCKET_URL, {
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    newSocket.on("connect", () => {
      console.log("Connected to Socket.IO server");
      setConnected(true);
    });

    newSocket.on("disconnect", () => {
      console.log("Disconnected from Socket.IO server");
      setConnected(false);
    });

    newSocket.on("connect_error", (err: any) => {
      console.error("Socket.IO connection error:", err);
      setConnected(false);
    });

    setSocket(newSocket);

    return () => {
      newSocket.disconnect();
    };
  }, []);

  // Listen for agent updates
  useEffect(() => {
    if (!socket) return;

    // Listen for agent status updates
    socket.on("agent_status_update", (data: AgentUpdate) => {
      console.log("Received agent status update:", data);

      // Update the agent status in the agents array
      setAgents((prevAgents) =>
        prevAgents.map((agent) =>
          agent.id === data.agentId
            ? { ...agent, status: data.status, last_check_time: data.timestamp }
            : agent
        )
      );

      // Add the update to the updates array
      setUpdates((prevUpdates) => [data, ...prevUpdates].slice(0, 50)); // Keep only the last 50 updates
    });

    // Listen for agent metrics updates
    socket.on("agent_metrics_update", (data: any) => {
      console.log("Received agent metrics update:", data);

      // Update the agent metrics in the agents array
      setAgents((prevAgents) =>
        prevAgents.map((agent) =>
          agent.id === data.agentId
            ? {
                ...agent,
                success_rate: data.success_rate || agent.success_rate,
                error_rate: data.error_rate || agent.error_rate,
                latency: data.latency || agent.latency,
                last_check_time: data.timestamp || agent.last_check_time,
              }
            : agent
        )
      );
    });

    return () => {
      socket.off("agent_status_update");
      socket.off("agent_metrics_update");
    };
  }, [socket]);

  const clearUpdates = useCallback(() => {
    setUpdates([]);
  }, []);

  // Instead of returning React elements directly, we'll return string identifiers
  const getStatusIconType = (status: string) => {
    switch (status.toLowerCase()) {
      case "running":
        return "success";
      case "error":
        return "error";
      case "idle":
        return "info";
      default:
        return "info";
    }
  };

  // Render the actual icon based on the type
  const renderStatusIcon = (status: string) => {
    const iconType = getStatusIconType(status);

    if (iconType === "success") return <CheckCircleIcon color="success" />;
    if (iconType === "error") return <ErrorIcon color="error" />;
    return <InfoIcon color="info" />;
  };

  // Similar approach for update icons
  const getUpdateIconType = (level: string) => {
    switch (level) {
      case "success":
        return "success";
      case "error":
        return "error";
      case "warning":
        return "warning";
      case "info":
      default:
        return "info";
    }
  };

  // Render the actual icon based on the type
  const renderUpdateIcon = (level: string) => {
    const iconType = getUpdateIconType(level);

    if (iconType === "success") return <CheckCircleIcon color="success" />;
    if (iconType === "error") return <ErrorIcon color="error" />;
    if (iconType === "warning") return <WarningIcon color="warning" />;
    return <InfoIcon color="info" />;
  };

  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Paper elevation={2} sx={{ p: 3, height: "100%" }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Typography variant="h6">Real-Time Agent Status</Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Chip
            label={connected ? "Connected" : "Disconnected"}
            color={connected ? "success" : "error"}
            size="small"
            sx={{ mr: 1 }}
          />
          <Tooltip title="Clear all updates">
            <IconButton onClick={clearUpdates} size="small">
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", md: "row" },
          height: "calc(100% - 50px)",
        }}
      >
        <Box
          sx={{
            width: { xs: "100%", md: "40%" },
            mb: { xs: 2, md: 0 },
            mr: { xs: 0, md: 2 },
          }}
        >
          <Typography variant="subtitle1" gutterBottom>
            Agent Status
          </Typography>
          <List
            dense
            sx={{
              bgcolor: "background.paper",
              maxHeight: 400,
              overflow: "auto",
            }}
          >
            {agents.map((agent) => (
              <ListItem key={agent.id}>
                <ListItemIcon>{renderStatusIcon(agent.status)}</ListItemIcon>
                <ListItemText
                  primary={agent.name}
                  secondary={`${agent.role} - Last update: ${new Date(
                    agent.last_check_time
                  ).toLocaleTimeString()}`}
                />
                <Chip
                  label={agent.status}
                  color={
                    agent.status.toLowerCase() === "running"
                      ? "success"
                      : agent.status.toLowerCase() === "error"
                      ? "error"
                      : "default"
                  }
                  size="small"
                />
              </ListItem>
            ))}
          </List>
        </Box>

        <Divider
          orientation="vertical"
          flexItem
          sx={{ display: { xs: "none", md: "block" } }}
        />
        <Divider sx={{ display: { xs: "block", md: "none" }, my: 2 }} />

        <Box sx={{ width: { xs: "100%", md: "60%" }, ml: { xs: 0, md: 2 } }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 1,
            }}
          >
            <Typography variant="subtitle1">Recent Updates</Typography>
            <Badge badgeContent={updates.length} color="primary">
              <NotificationsIcon />
            </Badge>
          </Box>

          {updates.length === 0 ? (
            <Alert severity="info">No recent updates.</Alert>
          ) : (
            <List
              dense
              sx={{
                bgcolor: "background.paper",
                maxHeight: 400,
                overflow: "auto",
              }}
            >
              {updates.map((update, index) => (
                <ListItem key={index} divider={index < updates.length - 1}>
                  <ListItemIcon>{renderUpdateIcon(update.level)}</ListItemIcon>
                  <ListItemText
                    primary={update.message}
                    secondary={
                      <React.Fragment>
                        {`Agent: ${
                          agents.find((a) => a.id === update.agentId)?.name ||
                          update.agentId
                        }`}
                        <br />
                        {`${new Date(update.timestamp).toLocaleString()}`}
                      </React.Fragment>
                    }
                  />
                </ListItem>
              ))}
            </List>
          )}
        </Box>
      </Box>
    </Paper>
  );
};

export default AgentRealTimeStatus;
