import React from "react";
import {
  <PERSON>,
  Drawer,
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  useMediaQuery,
  Badge,
  Avatar,
  Menu,
  MenuItem,
  Tooltip,
  Button,
  Stack,
} from "@mui/material";
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  Email as EmailIcon,
  LinkedIn as LinkedInIcon,
  Analytics as AnalyticsIcon,
  Assignment as TaskIcon,
  Report as ReportIcon,
  Lightbulb as AIIcon,
  Notifications as NotificationsIcon,
  AccountCircle,
  Settings as SettingsIcon,
  Person as LeadIcon,
} from "@mui/icons-material";

interface DashboardLayoutProps {
  children: any;
  title: string;
}

export const DashboardLayout = ({ children, title }: DashboardLayoutProps) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [notificationAnchorEl, setNotificationAnchorEl] = React.useState(null);
  const [navMenuAnchorEl, setNavMenuAnchorEl] = React.useState(null);

  const handleProfileMenuOpen = (event: any) => {
    setAnchorEl(event.currentTarget);
  };

  const handleNotificationMenuOpen = (event: any) => {
    setNotificationAnchorEl(event.currentTarget);
  };

  const handleNavMenuOpen = (event: any) => {
    setNavMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setNotificationAnchorEl(null);
    setNavMenuAnchorEl(null);
  };

  // Define menu items with icon types instead of React elements
  const menuItems = [
    { text: "Dashboard", iconType: "dashboard", path: "/" },
    { text: "Email Generation", iconType: "email", path: "/email" },
    { text: "SEO Insights", iconType: "analytics", path: "/seo" },
    { text: "LinkedIn Messages", iconType: "linkedin", path: "/linkedin" },
    { text: "AI Recommendations", iconType: "ai", path: "/ai" },
    { text: "Task Management", iconType: "task", path: "/tasks" },
    { text: "Email Reports", iconType: "report", path: "/reports" },
    { text: "Lead Management", iconType: "lead", path: "/leads" },
    { text: "Agent Dashboard", iconType: "analytics", path: "/agents" },
  ];

  // Function to render the appropriate icon based on type
  const renderIcon = (iconType: string) => {
    switch (iconType) {
      case "dashboard":
        return <DashboardIcon />;
      case "email":
        return <EmailIcon />;
      case "analytics":
        return <AnalyticsIcon />;
      case "linkedin":
        return <LinkedInIcon />;
      case "ai":
        return <AIIcon />;
      case "task":
        return <TaskIcon />;
      case "report":
        return <ReportIcon />;
      case "lead":
        return <LeadIcon />;
      default:
        return <DashboardIcon />;
    }
  };

  return (
    <Box sx={{ display: "flex" }}>
      <AppBar position="fixed">
        <Toolbar>
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{
              display: { xs: "none", sm: "block" },
              mr: 4,
            }}
          >
            Mexel
          </Typography>

          {/* Desktop Navigation */}
          <Box sx={{ display: { xs: "none", md: "flex" }, flexGrow: 1 }}>
            {menuItems.map((item) => (
              <Button
                key={item.text}
                color="inherit"
                component="a"
                href={item.path}
                startIcon={renderIcon(item.iconType)}
                sx={{ mx: 0.5 }}
              >
                {item.text}
              </Button>
            ))}
          </Box>

          {/* Mobile Navigation Button */}
          <Box sx={{ display: { xs: "flex", md: "none" }, flexGrow: 1 }}>
            <Button
              color="inherit"
              onClick={handleNavMenuOpen}
              startIcon={<MenuIcon />}
            >
              {title}
            </Button>
          </Box>

          {/* Right side icons */}
          <Box sx={{ display: "flex" }}>
            <Tooltip title="Notifications">
              <IconButton
                size="large"
                aria-label="show 4 new notifications"
                color="inherit"
                onClick={handleNotificationMenuOpen}
              >
                <Badge badgeContent={4} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>

            <Tooltip title="Account">
              <IconButton
                size="large"
                edge="end"
                aria-label="account of current user"
                aria-haspopup="true"
                onClick={handleProfileMenuOpen}
                color="inherit"
              >
                <Avatar sx={{ width: 32, height: 32, bgcolor: "primary.dark" }}>
                  Z
                </Avatar>
              </IconButton>
            </Tooltip>

            <Tooltip title="Settings">
              <IconButton size="large" color="inherit" aria-label="settings">
                <SettingsIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: "100%",
          mt: "64px",
          backgroundColor: "#f5f5f5",
          minHeight: "calc(100vh - 64px)",
        }}
      >
        {children}
      </Box>

      <Menu
        anchorEl={anchorEl}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        keepMounted
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>Profile</MenuItem>
        <MenuItem onClick={handleMenuClose}>My account</MenuItem>
        <MenuItem onClick={handleMenuClose}>Logout</MenuItem>
      </Menu>

      <Menu
        anchorEl={notificationAnchorEl}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        keepMounted
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        open={Boolean(notificationAnchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <Typography variant="body2">
            New lead generated: ABC Company
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Typography variant="body2">Email campaign completed</Typography>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Typography variant="body2">New tender opportunity found</Typography>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Typography variant="body2">Task deadline approaching</Typography>
        </MenuItem>
      </Menu>

      {/* Mobile Navigation Menu */}
      <Menu
        anchorEl={navMenuAnchorEl}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        keepMounted
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        open={Boolean(navMenuAnchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            width: "250px",
            maxWidth: "100%",
          },
        }}
      >
        {menuItems.map((item) => (
          <MenuItem
            key={item.text}
            component="a"
            href={item.path}
            onClick={handleMenuClose}
            sx={{ py: 1 }}
          >
            <ListItemIcon>{renderIcon(item.iconType)}</ListItemIcon>
            <ListItemText primary={item.text} />
          </MenuItem>
        ))}
        <Divider />
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <SettingsIcon />
          </ListItemIcon>
          <ListItemText primary="Settings" />
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default DashboardLayout;
