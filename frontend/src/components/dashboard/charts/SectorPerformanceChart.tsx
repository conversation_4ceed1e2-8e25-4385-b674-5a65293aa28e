import React, { useEffect } from "react";
import { Box, CircularProgress, Typography } from "@mui/material";

interface SectorPerformanceChartProps {
  sector: string;
}

interface PerformanceData {
  name: string;
  leads: number;
  responses: number;
  conversions: number;
}

const SectorPerformanceChart = ({ sector }: SectorPerformanceChartProps) => {
  const [data, setData] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // In a real implementation, this would be an API call
        // For now, we'll use mock data

        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 500 as any));

        let mockData: PerformanceData[];

        if (sector === "all") {
          // Data for all sectors
          mockData = [
            {
              name: "Power Plants",
              leads: 120,
              responses: 65,
              conversions: 28,
            },
            { name: "HVAC", leads: 98, responses: 42, conversions: 18 },
            { name: "Mining", leads: 86, responses: 38, conversions: 15 },
            { name: "Oil & Gas", leads: 99, responses: 48, conversions: 22 },
            { name: "Agriculture", leads: 65, responses: 30, conversions: 12 },
            { name: "Cooling", leads: 110, responses: 58, conversions: 25 },
          ];
        } else {
          // Data for specific sector by role
          switch (sector) {
            case "power":
              mockData = [
                { name: "Managers", leads: 45, responses: 28, conversions: 12 },
                { name: "Engineers", leads: 38, responses: 20, conversions: 9 },
                { name: "Buyers", leads: 22, responses: 10, conversions: 4 },
                {
                  name: "Technicians",
                  leads: 15,
                  responses: 7,
                  conversions: 3,
                },
              ];
              break;
            case "hvac":
              mockData = [
                { name: "Managers", leads: 32, responses: 18, conversions: 8 },
                { name: "Engineers", leads: 28, responses: 14, conversions: 6 },
                { name: "Buyers", leads: 25, responses: 8, conversions: 3 },
                {
                  name: "Technicians",
                  leads: 13,
                  responses: 2,
                  conversions: 1,
                },
              ];
              break;
            case "mining":
              mockData = [
                { name: "Managers", leads: 28, responses: 15, conversions: 6 },
                { name: "Engineers", leads: 24, responses: 12, conversions: 5 },
                { name: "Buyers", leads: 20, responses: 8, conversions: 3 },
                {
                  name: "Technicians",
                  leads: 14,
                  responses: 3,
                  conversions: 1,
                },
              ];
              break;
            case "oil_gas":
              mockData = [
                { name: "Managers", leads: 35, responses: 20, conversions: 9 },
                { name: "Engineers", leads: 30, responses: 16, conversions: 7 },
                { name: "Buyers", leads: 22, responses: 9, conversions: 4 },
                {
                  name: "Technicians",
                  leads: 12,
                  responses: 3,
                  conversions: 2,
                },
              ];
              break;
            case "agriculture":
              mockData = [
                { name: "Managers", leads: 22, responses: 12, conversions: 5 },
                { name: "Engineers", leads: 18, responses: 9, conversions: 4 },
                { name: "Buyers", leads: 15, responses: 6, conversions: 2 },
                {
                  name: "Technicians",
                  leads: 10,
                  responses: 3,
                  conversions: 1,
                },
              ];
              break;
            case "cooling":
              mockData = [
                { name: "Managers", leads: 40, responses: 25, conversions: 11 },
                { name: "Engineers", leads: 35, responses: 20, conversions: 8 },
                { name: "Buyers", leads: 25, responses: 10, conversions: 4 },
                {
                  name: "Technicians",
                  leads: 10,
                  responses: 3,
                  conversions: 2,
                },
              ];
              break;
            default:
              mockData = [];
          }
        }

        setData(mockData);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching sector performance data:", error);
        setError("Failed to load data. Please try again later.");
        setLoading(false);
      }
    };

    fetchData();
  }, [sector]);

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100%"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100%"
      >
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  if (data.length === 0) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100%"
      >
        <Typography>No data available for this sector.</Typography>
      </Box>
    );
  }

  // Due to TypeScript issues with recharts, we'll render a simplified version
  return (
    <Box p={2}>
      <Typography variant="h6" gutterBottom>
        Performance for{" "}
        {sector === "all"
          ? "All Sectors"
          : sector.charAt(0).toUpperCase() + sector.slice(1)}
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        Bar chart showing performance metrics for leads, responses, and
        conversions.
      </Typography>
      <Box
        sx={{
          height: 300,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          border: "1px dashed #ccc",
          borderRadius: 1,
        }}
      >
        <Typography>
          Chart data loaded successfully. {data.length} data points available.
        </Typography>
      </Box>
    </Box>
  );
};

export default SectorPerformanceChart;
