import {
    AccountCircle as AccountIcon,
    SmartToy as AIIcon,
    Close as CloseIcon,
    CompareArrows as CompetitorsIcon,
    Edit as ContentIcon,
    Dashboard as DashboardIcon,
    Email as EmailIcon,
    Timeline as HistoricalIcon,
    Lightbulb as InsightsIcon,
    Search as KeywordsIcon,
    Menu as MenuIcon,
    Notifications as NotificationsIcon,
    Article as PagesIcon,
    Settings as SettingsIcon,
    Assignment as TasksIcon,
    BugReport as TechnicalIcon
} from '@mui/icons-material';
import {
    AppBar,
    Avatar,
    Badge,
    Box,
    Divider,
    Drawer,
    IconButton,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    Paper,
    Toolbar,
    Typography,
    useMediaQuery,
    useTheme
} from '@mui/material';
import BottomNavigation from "@mui/material/BottomNavigation";
import BottomNavigationAction from "@mui/material/BottomNavigationAction";
import React from 'react';

interface MobileLayoutProps {
  children: any;
  title: string;
  onNavigate: (index: number) => void;
  activeTab: number;
}

/**
 * Mobile Layout Component
 *
 * Provides a mobile-friendly layout for the SEO dashboard
 */
const MobileLayout = ({
  children,
  title,
  onNavigate,
  activeTab
}: MobileLayoutProps) => {
  // State for drawer
  const [drawerOpen, setDrawerOpen] = React.useState(false);

  // Theme and media query
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Toggle drawer
  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  // Handle navigation
  const handleNavigation = (index: number) => {
    onNavigate(index);
    setDrawerOpen(false);
  };

  // Navigation items
  const navigationItems = [
    { label: 'Overview', icon: <DashboardIcon />, index: 0 },
    { label: 'Keywords', icon: <KeywordsIcon />, index: 1 },
    { label: 'Pages', icon: <PagesIcon />, index: 2 },
    { label: 'Insights', icon: <InsightsIcon />, index: 3 },
    { label: 'Competitors', icon: <CompetitorsIcon />, index: 4 },
    { label: 'Technical SEO', icon: <TechnicalIcon />, index: 5 },
    { label: 'Content', icon: <ContentIcon />, index: 6 },
    { label: 'Historical', icon: <HistoricalIcon />, index: 7 },
    { label: 'AI Recommendations', icon: <AIIcon />, index: 8 },
    { label: 'Tasks', icon: <TasksIcon />, index: 9 },
    { label: 'Email Reports', icon: <EmailIcon />, index: 10 }
  ];

  // Bottom navigation items (limited to 5 for mobile)
  const bottomNavItems = [
    { label: 'Overview', icon: <DashboardIcon />, index: 0 },
    { label: 'Keywords', icon: <KeywordsIcon />, index: 1 },
    { label: 'Insights', icon: <InsightsIcon />, index: 3 },
    { label: 'Tasks', icon: <TasksIcon />, index: 9 },
    { label: 'More', icon: <MenuIcon />, index: -1 } // Special index for "More" button
  ];

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      {/* App Bar */}
      <AppBar position="static">
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            aria-label="menu"
            onClick={toggleDrawer}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {title}
          </Typography>
          <IconButton color="inherit">
            <Badge badgeContent={4} color="error">
              <NotificationsIcon />
            </Badge>
          </IconButton>
          <IconButton color="inherit">
            <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.dark' }}>
              U
            </Avatar>
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* Drawer */}
      <Drawer
        anchor="left"
        open={drawerOpen}
        onClose={toggleDrawer}
      >
        <Box
          sx={{ width: 250 }}
          role="presentation"
        >
          <Box sx={{ display: 'flex', alignItems: 'center', p: 2 }}>
            <Typography variant="h6" sx={{ flexGrow: 1 }}>
              SEO Dashboard
            </Typography>
            <IconButton onClick={toggleDrawer}>
              <CloseIcon />
            </IconButton>
          </Box>
          <Divider />
          <List>
            {navigationItems.map((item) => (
              <ListItem
                button
                key={item.index}
                selected={activeTab === item.index}
                onClick={() => handleNavigation(item.index)}
              >
                <ListItemIcon
                  sx={{
                    color: activeTab === item.index ? 'primary.main' : 'inherit'
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.label}
                  primaryTypographyProps={{
                    fontWeight: activeTab === item.index ? 'bold' : 'normal'
                  }}
                />
              </ListItem>
            ))}
          </List>
          <Divider />
          <List>
            <ListItem button>
              <ListItemIcon>
                <SettingsIcon />
              </ListItemIcon>
              <ListItemText primary="Settings" />
            </ListItem>
            <ListItem button>
              <ListItemIcon>
                <AccountIcon />
              </ListItemIcon>
              <ListItemText primary="Account" />
            </ListItem>
          </List>
        </Box>
      </Drawer>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 2,
          overflow: 'auto',
          pb: isMobile ? 8 : 2 // Add padding at bottom for mobile to account for bottom navigation
        }}
      >
        {children}
      </Box>

      {/* Bottom Navigation (Mobile Only) */}
      {isMobile && (
        <Paper
          sx={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            zIndex: 1100
          }}
          elevation={3}
        >
          <BottomNavigation
            value={
              activeTab === -1 ? 4 : // If "More" is selected
              bottomNavItems.findIndex(item => item.index === activeTab) !== -1 ?
              bottomNavItems.findIndex(item => item.index === activeTab) :
              4 // Default to "More" if current tab is not in bottom nav
            }
            onChange={(_event, newValue) => {
              if (newValue === 4) { // "More" button
                toggleDrawer();
              } else {
                handleNavigation(bottomNavItems[newValue].index);
              }
            }}
            showLabels
          >
            {bottomNavItems.map((item, index) => (
              <BottomNavigationAction
                key={index}
                label={item.label}
                icon={item.icon}
              />
            ))}
          </BottomNavigation>
        </Paper>
      )}
    </Box>
  );
};

export default MobileLayout;
