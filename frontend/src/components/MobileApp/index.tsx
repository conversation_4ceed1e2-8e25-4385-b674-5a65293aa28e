import React from 'react';
import { FC, ReactNode, ChangeEvent, MouseEvent } from 'react';

import { 
  ThemeProvider, 
  createTheme, 
  CssBaseline 
} from '@mui/material';
import MobileDashboard from './MobileDashboard';

/**
 * Mobile App Component
 * 
 * Entry point for the mobile app version of the SEO dashboard
 */
const MobileApp = () => {
  // Create a mobile-optimized theme
  const theme = createTheme({
    palette: {
      mode: 'light',
      primary: {
        main: '#1976d2',
      },
      secondary: {
        main: '#dc004e',
      },
    },
    typography: {
      // Slightly smaller font sizes for mobile
      h5: {
        fontSize: '1.4rem',
      },
      h6: {
        fontSize: '1.15rem',
      },
      body1: {
        fontSize: '0.95rem',
      },
      body2: {
        fontSize: '0.85rem',
      },
    },
    components: {
      // Optimize components for mobile
      MuiButton: {
        styleOverrides: {
          root: {
            textTransform: 'none',
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
          },
        },
      },
      MuiCardHeader: {
        styleOverrides: {
          root: {
            padding: '12px 16px',
          },
        },
      },
      MuiCardContent: {
        styleOverrides: {
          root: {
            padding: '12px 16px',
            '&:last-child': {
              paddingBottom: '12px',
            },
          },
        },
      },
      MuiListItem: {
        styleOverrides: {
          root: {
            paddingTop: '8px',
            paddingBottom: '8px',
          },
        },
      },
    },
  });

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <MobileDashboard />
    </ThemeProvider>
  );
};

export default MobileApp;
