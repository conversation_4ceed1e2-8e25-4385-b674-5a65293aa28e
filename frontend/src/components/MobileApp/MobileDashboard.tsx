import ErrorIcon from "@mui/icons-material/Error";
import InfoIcon from "@mui/icons-material/Info";
import LinkIcon from "@mui/icons-material/Link";
import RefreshIcon from "@mui/icons-material/Refresh";
import SpeedIcon from "@mui/icons-material/Speed";
import TrendingDownIcon from "@mui/icons-material/TrendingDown";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import WarningIcon from "@mui/icons-material/Warning";
import {
    Box,
    Button,
    Chip,
    CircularProgress,
    Grid,
    List,
    ListItem,
    ListItemIcon,
    ListItemSecondaryAction,
    ListItemText,
    Paper,
    Typography,
    useMediaQuery,
    useTheme
} from "@mui/material";
import React from "react";
import { getMockSEOData } from "../SEOInsightsDashboard/mockData";
import { SEOInsightsData } from "../SEOInsightsDashboard/types";
import MobileLayout from "./MobileLayout";

/**
 * Mobile Dashboard Component
 *
 * Mobile-optimized version of the SEO dashboard
 */
const MobileDashboard = () => {
  // State for active tab
  const [activeTab, setActiveTab] = React.useState(0);

  // State for data
  const [data, setData] = React.useState(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);

  // Theme and media query
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  // Fetch data
  React.useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // For demo purposes, use mock data
        const seoData = getMockSEOData();
        setData(seoData as SEOInsightsData);
        setError(null);
      } catch (err) {
        console.error("Error fetching data:", err);
        setError("Failed to load data. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle tab change
  const handleTabChange = (index: number) => {
    setActiveTab(index);
  };

  // Get tab title
  const getTabTitle = (): string => {
    switch (activeTab) {
      case 0:
        return "Overview";
      case 1:
        return "Keywords";
      case 2:
        return "Pages";
      case 3:
        return "Insights";
      case 4:
        return "Competitors";
      case 5:
        return "Technical SEO";
      case 6:
        return "Content Optimization";
      case 7:
        return "Historical Analysis";
      case 8:
        return "AI Recommendations";
      case 9:
        return "Tasks";
      case 10:
        return "Email Reports";
      default:
        return "SEO Dashboard";
    }
  };

  // Render overview tab
  const renderOverviewTab = () => {
    if (!data) return null;

    return (
      <Box>
        {/* Key Metrics */}
        <Typography variant="h6" gutterBottom>
          Key Metrics
        </Typography>
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6}>
            <Paper sx={{ p: 2, textAlign: "center", height: "100%" }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  mb: 1,
                }}
              >
                <VisibilityIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="subtitle2">Organic Traffic</Typography>
              </Box>
              <Typography variant="h4" color="primary.main">
                {data.overview.organicTraffic.toLocaleString()}
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {data.overview.organicTrafficChange > 0 ? (
                  <TrendingUpIcon fontSize="small" color="success" />
                ) : (
                  <TrendingDownIcon fontSize="small" color="error" />
                )}
                <Typography
                  variant="body2"
                  color={
                    data.overview.organicTrafficChange > 0
                      ? "success.main"
                      : "error.main"
                  }
                  sx={{ ml: 0.5 }}
                >
                  {Math.abs(data.overview.organicTrafficChange)}%
                </Typography>
              </Box>
            </Paper>
          </Grid>
          <Grid item xs={6}>
            <Paper sx={{ p: 2, textAlign: "center", height: "100%" }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  mb: 1,
                }}
              >
                <SearchIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="subtitle2">Keywords</Typography>
              </Box>
              <Typography variant="h4" color="primary.main">
                {data.overview.keywordsRanked.toLocaleString()}
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {data.overview.keywordsChange > 0 ? (
                  <TrendingUpIcon fontSize="small" color="success" />
                ) : (
                  <TrendingDownIcon fontSize="small" color="error" />
                )}
                <Typography
                  variant="body2"
                  color={
                    data.overview.keywordsChange > 0
                      ? "success.main"
                      : "error.main"
                  }
                  sx={{ ml: 0.5 }}
                >
                  {Math.abs(data.overview.keywordsChange)}%
                </Typography>
              </Box>
            </Paper>
          </Grid>
          <Grid item xs={6}>
            <Paper sx={{ p: 2, textAlign: "center", height: "100%" }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  mb: 1,
                }}
              >
                <LinkIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="subtitle2">Backlinks</Typography>
              </Box>
              <Typography variant="h4" color="primary.main">
                {data.overview.backlinks.toLocaleString()}
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {data.overview.backlinksChange > 0 ? (
                  <TrendingUpIcon fontSize="small" color="success" />
                ) : (
                  <TrendingDownIcon fontSize="small" color="error" />
                )}
                <Typography
                  variant="body2"
                  color={
                    data.overview.backlinksChange > 0
                      ? "success.main"
                      : "error.main"
                  }
                  sx={{ ml: 0.5 }}
                >
                  {Math.abs(data.overview.backlinksChange)}%
                </Typography>
              </Box>
            </Paper>
          </Grid>
          <Grid item xs={6}>
            <Paper sx={{ p: 2, textAlign: "center", height: "100%" }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  mb: 1,
                }}
              >
                <SpeedIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="subtitle2">Page Speed</Typography>
              </Box>
              <Typography variant="h4" color="primary.main">
                {data.overview.averagePageSpeed}
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {data.overview.pageSpeedChange > 0 ? (
                  <TrendingUpIcon fontSize="small" color="success" />
                ) : (
                  <TrendingDownIcon fontSize="small" color="error" />
                )}
                <Typography
                  variant="body2"
                  color={
                    data.overview.pageSpeedChange > 0
                      ? "success.main"
                      : "error.main"
                  }
                  sx={{ ml: 0.5 }}
                >
                  {Math.abs(data.overview.pageSpeedChange)}%
                </Typography>
              </Box>
            </Paper>
          </Grid>
        </Grid>

        {/* Top Keywords */}
        <Typography variant="h6" gutterBottom>
          Top Keywords
        </Typography>
        <Paper sx={{ mb: 3 }}>
          <List dense>
            {data.topKeywords.slice(0, 5).map((keyword, index) => (
              <ListItem key={index} divider={index < 4}>
                <ListItemText
                  primary={keyword.keyword}
                  secondary={`Position: ${
                    keyword.position
                  } | Volume: ${keyword.volume.toLocaleString()}`}
                />
                <ListItemSecondaryAction>
                  {keyword.change > 0 ? (
                    <Chip
                      icon={<ArrowUpIcon />}
                      label={`+${keyword.change}`}
                      size="small"
                      color="success"
                    />
                  ) : keyword.change < 0 ? (
                    <Chip
                      icon={<ArrowDownIcon />}
                      label={keyword.change}
                      size="small"
                      color="error"
                    />
                  ) : (
                    <Chip label="0" size="small" variant="outlined" />
                  )}
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </Paper>

        {/* Technical Issues */}
        <Typography variant="h6" gutterBottom>
          Technical Issues
        </Typography>
        <Paper>
          <List dense>
            {data.technicalIssues.slice(0, 5).map((issue, index) => (
              <ListItem key={index} divider={index < 4}>
                <ListItemIcon>
                  {issue.impact === "high" ? (
                    <ErrorIcon color="error" />
                  ) : issue.impact === "medium" ? (
                    <WarningIcon color="warning" />
                  ) : (
                    <InfoIcon color="info" />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={issue.title}
                  secondary={`Pages affected: ${issue.affectedPages}`}
                />
              </ListItem>
            ))}
          </List>
        </Paper>
      </Box>
    );
  };

  // Render content based on active tab
  const renderContent = () => {
    if (loading) {
      return (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "100%",
          }}
        >
          <CircularProgress />
        </Box>
      );
    }

    if (error) {
      return (
        <Box sx={{ textAlign: "center", p: 3 }}>
          <Typography color="error" paragraph>
            {error}
          </Typography>
          <Button
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={() => window.location.reload()}
          >
            Retry
          </Button>
        </Box>
      );
    }

    switch (activeTab) {
      case 0:
        return renderOverviewTab();
      default:
        return (
          <Box sx={{ textAlign: "center", p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {getTabTitle()}
            </Typography>
            <Typography color="text.secondary">
              This section is under development for the mobile app.
            </Typography>
          </Box>
        );
    }
  };

  return (
    <MobileLayout
      title={getTabTitle()}
      onNavigate={handleTabChange}
      activeTab={activeTab}
    >
      {renderContent()}
    </MobileLayout>
  );
};

export default MobileDashboard;
