import React from 'react';
import { render, screen } from '@testing-library/react';
import { ErrorBoundary } from './ErrorBoundary';

// Create a component that will intentionally throw an error
const ThrowError = () => {
  throw new Error('Test error');
  return <div>This should not render</div>;
};

// A simple component to use in testing
const TestComponent = () => <div>Test Component</div>;

describe('ErrorBoundary', () => {
  // Suppress console errors during this test since we expect errors
  const originalConsoleError = console.error;
  beforeEach(() => {
    console.error = jest.fn();
  });

  afterEach(() => {
    console.error = originalConsoleError;
  });

  it('renders children when there is no error', () => {
    render(
      <ErrorBoundary>
        <TestComponent />
      </ErrorBoundary>
    );

    expect(screen.getByText('Test Component')).toBeInTheDocument();
  });

  it('renders fallback UI when there is an error', () => {
    render(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>
    );

    expect(screen.getByText('Something went wrong.')).toBeInTheDocument();
  });

  it('renders custom fallbackRender when provided', () => {
    const customFallback = (error: Error) => <div>Custom Error: {error.message}</div>;

    render(
      <ErrorBoundary fallbackRender={customFallback}>
        <ThrowError />
      </ErrorBoundary>
    );

    expect(screen.getByText('Custom Error: Test error')).toBeInTheDocument();
  });
});
