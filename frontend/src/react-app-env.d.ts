/// <reference types="react-scripts" />

// Fix for JSX.IntrinsicElements and react/jsx-runtime not found errors
import React from "react";

declare module "react/jsx-runtime" {
    export const Fragment: React.ComponentType;
  export const jsx: typeof React.createElement;
  export const jsxs: typeof React.createElement;
  export const jsxDEV: typeof React.createElement;
}

// Fix for "Cannot find name 'process'" error in React components
declare var process: {
  env: {
    NODE_ENV: 'development' | 'production' | 'test';
    REACT_APP_API_URL?: string;
    REACT_APP_WEBSOCKET_URL?: string;
    PUBLIC_URL?: string;
    [key: string]: string | undefined;
  };
  version: string;
  platform: string;
};

declare global {
  namespace JSX {
    interface IntrinsicElements {
      div: React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLDivElement>,
        HTMLDivElement
      >;
      h1: React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLHeadingElement>,
        HTMLHeadingElement
      >;
      header: React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement>,
        HTMLElement
      >;
      span: React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLSpanElement>,
        HTMLSpanElement
      >;
      button: React.DetailedHTMLProps<
        React.ButtonHTMLAttributes<HTMLButtonElement>,
        HTMLButtonElement
      >;
      [elemName: string]: any;
    }
  }
}
