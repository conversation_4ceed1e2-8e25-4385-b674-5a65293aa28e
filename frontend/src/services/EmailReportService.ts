import React from 'react';
import { v4 as uuidv4 } from 'uuid';
import { 
  EmailReport, 
  ReportFrequency, 
  ReportFormat, 
  ReportSection, 
  ReportStatus,
  ReportHistoryItem,
  ReportSchedule,
  ReportCustomization
} from '../components/SEOInsightsDashboard/EmailReports/types';
import { BaseApiService } from './api/BaseApiService';

/**
 * Email Report Service
 * Service for managing email reports
 */
export class EmailReportService extends BaseApiService {
  private localStorageKey = 'seo_email_reports';
  private historyLocalStorageKey = 'seo_email_reports_history';

  constructor(apiKey?: string) {
    super('/api/email-reports', apiKey);
  }

  /**
   * Get all email reports
   * @returns Promise with all email reports
   */
  public async getReports(): Promise<EmailReport[]> {
    try {
      // Try to get reports from API
      return this.get<EmailReport[]>('/');
    } catch (error) {
      console.error('Error fetching reports from API, falling back to local storage:', error);
      // Fall back to local storage
      return this.getReportsFromLocalStorage();
    }
  }

  /**
   * Get a report by ID
   * @param id - Report ID
   * @returns Promise with the report
   */
  public async getReport(id: string): Promise<EmailReport> {
    try {
      // Try to get report from API
      return this.get<EmailReport>(`/${id}`);
    } catch (error) {
      console.error(`Error fetching report ${id} from API, falling back to local storage:`, error);
      // Fall back to local storage
      const reports = this.getReportsFromLocalStorage();
      const report = reports.find(r => r.id === id);
      if (!report) {
        throw new Error(`Report with ID ${id} not found`);
      }
      return report;
    }
  }

  /**
   * Create a new report
   * @param report - Report data
   * @returns Promise with the created report
   */
  public async createReport(report: Omit<EmailReport, 'id' | 'createdAt' | 'updatedAt' | 'status'>): Promise<EmailReport> {
    const now = new Date().toISOString();
    const newReport: EmailReport = {
      id: uuidv4(),
      ...report,
      status: ReportStatus.ACTIVE,
      createdAt: now,
      updatedAt: now,
      nextScheduledAt: this.calculateNextScheduledDate(report.schedule, report.frequency)
    };

    try {
      // Try to create report via API
      return this.post<EmailReport>('/', newReport);
    } catch (error) {
      console.error('Error creating report via API, falling back to local storage:', error);
      // Fall back to local storage
      const reports = this.getReportsFromLocalStorage();
      reports.push(newReport);
      this.saveReportsToLocalStorage(reports);
      return newReport;
    }
  }

  /**
   * Update a report
   * @param id - Report ID
   * @param updates - Report updates
   * @returns Promise with the updated report
   */
  public async updateReport(id: string, updates: Partial<EmailReport>): Promise<EmailReport> {
    try {
      // Try to update report via API
      return this.put<EmailReport>(`/${id}`, updates);
    } catch (error) {
      console.error(`Error updating report ${id} via API, falling back to local storage:`, error);
      // Fall back to local storage
      const reports = this.getReportsFromLocalStorage();
      const reportIndex = reports.findIndex(r => r.id === id);
      
      if (reportIndex === -1) {
        throw new Error(`Report with ID ${id} not found`);
      }
      
      // If frequency or schedule is updated, recalculate next scheduled date
      let nextScheduledAt = reports[reportIndex].nextScheduledAt;
      if (updates.frequency || updates.schedule) {
        const frequency = updates.frequency || reports[reportIndex].frequency;
        const schedule = updates.schedule || reports[reportIndex].schedule;
        nextScheduledAt = this.calculateNextScheduledDate(schedule, frequency);
      }
      
      const updatedReport: EmailReport = {
        ...reports[reportIndex],
        ...updates,
        nextScheduledAt,
        updatedAt: new Date().toISOString()
      };
      
      reports[reportIndex] = updatedReport;
      this.saveReportsToLocalStorage(reports);
      
      return updatedReport;
    }
  }

  /**
   * Delete a report
   * @param id - Report ID
   * @returns Promise with success status
   */
  public async deleteReport(id: string): Promise<boolean> {
    try {
      // Try to delete report via API
      await this.delete<void>(`/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting report ${id} via API, falling back to local storage:`, error);
      // Fall back to local storage
      const reports = this.getReportsFromLocalStorage();
      const filteredReports = reports.filter(r => r.id !== id);
      
      if (filteredReports.length === reports.length) {
        throw new Error(`Report with ID ${id} not found`);
      }
      
      this.saveReportsToLocalStorage(filteredReports);
      return true;
    }
  }

  /**
   * Send a report immediately
   * @param id - Report ID
   * @returns Promise with success status
   */
  public async sendReportNow(id: string): Promise<boolean> {
    try {
      // Try to send report via API
      await this.post<void>(`/${id}/send`, {});
      
      // Update report's lastSentAt
      const now = new Date().toISOString();
      await this.updateReport(id, { lastSentAt: now });
      
      // Add to history
      await this.addReportToHistory(id, now);
      
      return true;
    } catch (error) {
      console.error(`Error sending report ${id} via API:`, error);
      
      // Add failed attempt to history
      const now = new Date().toISOString();
      await this.addReportToHistory(id, now, 'failed', error instanceof Error ? error.message : 'Unknown error');
      
      throw error;
    }
  }

  /**
   * Download a report
   * @param id - Report ID
   * @param format - Report format
   * @returns Promise with download URL
   */
  public async downloadReport(id: string, format: ReportFormat): Promise<string> {
    try {
      // Try to download report via API
      const response = await this.post<{ downloadUrl: string }>(`/${id}/download`, { format });
      return response.downloadUrl;
    } catch (error) {
      console.error(`Error downloading report ${id} via API:`, error);
      throw error;
    }
  }

  /**
   * Get report history
   * @returns Promise with report history
   */
  public async getReportHistory(): Promise<ReportHistoryItem[]> {
    try {
      // Try to get history from API
      return this.get<ReportHistoryItem[]>('/history');
    } catch (error) {
      console.error('Error fetching report history from API, falling back to local storage:', error);
      // Fall back to local storage
      return this.getReportHistoryFromLocalStorage();
    }
  }

  /**
   * Add report to history
   * @param reportId - Report ID
   * @param sentAt - Date when report was sent
   * @param status - Status of the report sending
   * @param errorMessage - Error message if status is failed
   * @returns Promise with the created history item
   */
  private async addReportToHistory(
    reportId: string, 
    sentAt: string, 
    status: 'success' | 'failed' = 'success',
    errorMessage?: string
  ): Promise<ReportHistoryItem> {
    const report = await this.getReport(reportId);
    
    const historyItem: ReportHistoryItem = {
      id: uuidv4(),
      reportId,
      sentAt,
      recipients: report.recipients,
      status,
      errorMessage,
      downloadUrl: status === 'success' ? `/api/email-reports/history/${reportId}/${sentAt}` : undefined
    };
    
    try {
      // Try to add history via API
      return this.post<ReportHistoryItem>('/history', historyItem);
    } catch (error) {
      console.error('Error adding report history via API, falling back to local storage:', error);
      // Fall back to local storage
      const history = this.getReportHistoryFromLocalStorage();
      history.push(historyItem);
      this.saveReportHistoryToLocalStorage(history);
      return historyItem;
    }
  }

  /**
   * Calculate next scheduled date
   * @param schedule - Report schedule
   * @param frequency - Report frequency
   * @returns ISO date string of next scheduled date
   */
  private calculateNextScheduledDate(schedule: ReportSchedule, frequency: ReportFrequency): string {
    const now = new Date();
    const [hours, minutes] = schedule.time.split(':').map(Number);
    
    let nextDate = new Date(now);
    nextDate.setHours(hours, minutes, 0, 0);
    
    // If the scheduled time is in the past, move to the next occurrence
    if (nextDate <= now) {
      switch (frequency) {
        case ReportFrequency.DAILY:
          nextDate.setDate(nextDate.getDate() + 1);
          break;
          
        case ReportFrequency.WEEKLY:
          const dayOfWeek = schedule.dayOfWeek || 0;
          const daysUntilNextOccurrence = (dayOfWeek + 7 - nextDate.getDay()) % 7;
          nextDate.setDate(nextDate.getDate() + (daysUntilNextOccurrence === 0 ? 7 : daysUntilNextOccurrence));
          break;
          
        case ReportFrequency.BIWEEKLY:
          const biweeklyDayOfWeek = schedule.dayOfWeek || 0;
          const daysUntilBiweekly = (biweeklyDayOfWeek + 7 - nextDate.getDay()) % 7;
          nextDate.setDate(nextDate.getDate() + (daysUntilBiweekly === 0 ? 14 : daysUntilBiweekly));
          break;
          
        case ReportFrequency.MONTHLY:
          const dayOfMonth = schedule.dayOfMonth || 1;
          nextDate.setDate(1); // Go to first day of current month
          nextDate.setMonth(nextDate.getMonth() + 1); // Go to first day of next month
          nextDate.setDate(Math.min(dayOfMonth, this.getDaysInMonth(nextDate.getFullYear(), nextDate.getMonth()))); // Set to desired day of month
          break;
          
        case ReportFrequency.QUARTERLY:
          const quarterlyDayOfMonth = schedule.dayOfMonth || 1;
          const quarterlyMonth = schedule.month || 0;
          nextDate.setDate(1); // Go to first day of current month
          
          // Calculate months until next quarterly occurrence
          const currentMonth = nextDate.getMonth();
          const monthsInQuarter = 3;
          const currentQuarter = Math.floor(currentMonth / monthsInQuarter);
          const nextQuarterStartMonth = (currentQuarter + 1) * monthsInQuarter;
          const targetMonth = quarterlyMonth + nextQuarterStartMonth;
          const monthsToAdd = (targetMonth - currentMonth + 12) % 12;
          
          nextDate.setMonth(nextDate.getMonth() + monthsToAdd);
          nextDate.setDate(Math.min(quarterlyDayOfMonth, this.getDaysInMonth(nextDate.getFullYear(), nextDate.getMonth())));
          break;
      }
    }
    
    return nextDate.toISOString();
  }

  /**
   * Get days in month
   * @param year - Year
   * @param month - Month (0-11)
   * @returns Number of days in the month
   */
  private getDaysInMonth(year: number, month: number): number {
    return new Date(year, month + 1, 0).getDate();
  }

  /**
   * Get reports from local storage
   * @returns Array of reports
   */
  private getReportsFromLocalStorage(): EmailReport[] {
    const reportsJson = localStorage.getItem(this.localStorageKey);
    return reportsJson ? JSON.parse(reportsJson) : [];
  }

  /**
   * Save reports to local storage
   * @param reports - Reports to save
   */
  private saveReportsToLocalStorage(reports: EmailReport[]): void {
    localStorage.setItem(this.localStorageKey, JSON.stringify(reports));
  }

  /**
   * Get report history from local storage
   * @returns Array of report history items
   */
  private getReportHistoryFromLocalStorage(): ReportHistoryItem[] {
    const historyJson = localStorage.getItem(this.historyLocalStorageKey);
    return historyJson ? JSON.parse(historyJson) : [];
  }

  /**
   * Save report history to local storage
   * @param history - Report history to save
   */
  private saveReportHistoryToLocalStorage(history: ReportHistoryItem[]): void {
    localStorage.setItem(this.historyLocalStorageKey, JSON.stringify(history));
  }

  /**
   * Generate mock email reports for testing
   * @param count - Number of reports to generate
   * @returns Array of mock reports
   */
  public generateMockReports(count: number = 5): EmailReport[] {
    const reports: EmailReport[] = [];
    const now = new Date();
    const frequencies = Object.values(ReportFrequency);
    const formats = Object.values(ReportFormat);
    const sections = Object.values(ReportSection);
    const statuses = [ReportStatus.ACTIVE, ReportStatus.PAUSED];
    
    for (let i = 0; i < count; i++) {
      const frequency = frequencies[Math.floor(Math.random() * frequencies.length)];
      const format = formats[Math.floor(Math.random() * formats.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      
      // Generate random schedule based on frequency
      const schedule = this.generateMockSchedule(frequency);
      
      // Generate random sections (at least 2)
      const selectedSections: ReportSection[] = [];
      const shuffledSections = [...sections].sort(() => 0.5 - Math.random());
      const sectionCount = Math.max(2, Math.floor(Math.random() * sections.length));
      selectedSections.push(...shuffledSections.slice(0, sectionCount));
      
      const createdAt = new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString();
      const updatedAt = new Date(new Date(createdAt).getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString();
      
      const report: EmailReport = {
        id: uuidv4(),
        name: `SEO Report ${i + 1}`,
        description: `Automated SEO performance report ${i + 1}`,
        frequency,
        format,
        recipients: ['<EMAIL>', '<EMAIL>'],
        sections: selectedSections,
        schedule,
        status,
        createdAt,
        updatedAt,
        lastSentAt: Math.random() > 0.5 ? new Date(updatedAt).toISOString() : undefined,
        nextScheduledAt: this.calculateNextScheduledDate(schedule, frequency)
      };
      
      reports.push(report);
    }
    
    return reports;
  }

  /**
   * Generate mock schedule based on frequency
   * @param frequency - Report frequency
   * @returns Mock schedule
   */
  private generateMockSchedule(frequency: ReportFrequency): ReportSchedule {
    const now = new Date();
    const time = `${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`;
    
    const schedule: ReportSchedule = {
      time,
      timezone: 'America/New_York',
      startDate: now.toISOString()
    };
    
    switch (frequency) {
      case ReportFrequency.WEEKLY:
      case ReportFrequency.BIWEEKLY:
        schedule.dayOfWeek = Math.floor(Math.random() * 7); // 0-6, Sunday to Saturday
        break;
        
      case ReportFrequency.MONTHLY:
      case ReportFrequency.QUARTERLY:
        schedule.dayOfMonth = Math.floor(Math.random() * 28) + 1; // 1-28 (avoiding edge cases)
        break;
    }
    
    if (frequency === ReportFrequency.QUARTERLY) {
      schedule.month = Math.floor(Math.random() * 3) * 3; // 0, 3, 6, 9 (first month of each quarter)
    }
    
    return schedule;
  }

  /**
   * Generate mock report history for testing
   * @param reports - Reports to generate history for
   * @param count - Number of history items per report
   * @returns Array of mock history items
   */
  public generateMockReportHistory(reports: EmailReport[], count: number = 3): ReportHistoryItem[] {
    const history: ReportHistoryItem[] = [];
    const now = new Date();
    
    for (const report of reports) {
      for (let i = 0; i < count; i++) {
        const sentAt = new Date(now.getTime() - (i + 1) * 7 * 24 * 60 * 60 * 1000).toISOString();
        const status = Math.random() > 0.9 ? 'failed' : 'success';
        
        const historyItem: ReportHistoryItem = {
          id: uuidv4(),
          reportId: report.id,
          sentAt,
          recipients: report.recipients,
          status,
          errorMessage: status === 'failed' ? 'Failed to send email' : undefined,
          downloadUrl: status === 'success' ? `/api/email-reports/history/${report.id}/${sentAt}` : undefined,
          openRate: status === 'success' ? Math.floor(Math.random() * 100) : undefined,
          clickRate: status === 'success' ? Math.floor(Math.random() * 100) : undefined
        };
        
        history.push(historyItem);
      }
    }
    
    return history;
  }
}
