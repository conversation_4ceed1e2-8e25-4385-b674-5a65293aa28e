import React from 'react';
import axios from "axios";

// API response interfaces
interface ApiSuccessResponse<T> {
  status: "success";
  data: T;
  timestamp: string;
  requestId: string;
}

// User interfaces
export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
}

export interface LoginResponse {
  token: string;
  user: User;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  name: string;
  password: string;
  role?: string;
}

/**
 * Service for authentication
 */
export class AuthService {
  private baseUrl: string;
  private tokenKey = "auth_token";
  private userKey = "auth_user";

  constructor(baseUrl = "http://localhost:3001/api") {
    this.baseUrl = baseUrl;
  }

  /**
   * Login a user
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const response = await axios.post<ApiSuccessResponse<LoginResponse>>(
        `${this.baseUrl}/auth/login`,
        credentials
      );

      const { token, user } = response.data.data;

      // Store token and user in localStorage
      localStorage.setItem(this.tokenKey, token);
      localStorage.setItem(this.userKey, JSON.stringify(user));

      // Set default Authorization header for all future requests
      axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;

      return { token, user };
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    }
  }

  /**
   * Register a new user (admin only)
   */
  async register(data: RegisterData): Promise<User> {
    try {
      const token = this.getToken();

      if (!token) {
        throw new Error("Authentication required");
      }

      const response = await axios.post<ApiSuccessResponse<{ user: User }>>(
        `${this.baseUrl}/auth/register`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data.data.user;
    } catch (error) {
      console.error("Registration failed:", error);
      throw error;
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      const token = this.getToken();

      if (!token) {
        return null;
      }

      const response = await axios.get<ApiSuccessResponse<{ user: User }>>(
        `${this.baseUrl}/auth/me`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const user = response.data.data.user;

      // Update stored user
      localStorage.setItem(this.userKey, JSON.stringify(user));

      return user;
    } catch (error) {
      console.error("Failed to get current user:", error);
      this.logout();
      return null;
    }
  }

  /**
   * Logout the current user
   */
  logout(): void {
    // Remove token and user from localStorage
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);

    // Remove Authorization header
    delete axios.defaults.headers.common["Authorization"];
  }

  /**
   * Check if user is logged in
   */
  isLoggedIn(): boolean {
    return !!this.getToken();
  }

  /**
   * Get the stored token
   */
  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  /**
   * Get the stored user
   */
  getUser(): User | null {
    const userJson = localStorage.getItem(this.userKey);
    return userJson ? JSON.parse(userJson) : null;
  }

  /**
   * Check if user has a specific role
   */
  hasRole(role: string | string[]): boolean {
    const user = this.getUser();

    if (!user) {
      return false;
    }

    if (Array.isArray(role)) {
      return role.includes(user.role);
    }

    return user.role === role;
  }

  /**
   * Initialize the auth service
   * Sets up axios interceptors and restores the token if available
   */
  initialize(): void {
    const token = this.getToken();

    if (token) {
      // Set default Authorization header
      axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;

      // Verify the token is still valid
      this.getCurrentUser().catch(() => {
        this.logout();
      });
    }

    // Add response interceptor to handle 401 errors
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response && error.response.status === 401) {
          // Token expired or invalid, logout
          this.logout();
          window.location.href = "/login";
        }
        return Promise.reject(error);
      }
    );
  }
}
