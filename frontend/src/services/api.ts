import React from 'react';
// API service for communicating with the backend
import axios from "axios";

// Create a base axios instance with default configuration
const api = axios.create({
  baseURL: "http://localhost:8001/api/v1",
  headers: {
    "Content-Type": "application/json",
  },
});

// Add a request interceptor to add auth token if available
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("auth_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Basic error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error("API Error:", error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// API function to test the connection to the backend
export const testConnection = async (): Promise<{ message: string }> => {
  try {
    const response = await api.get("/");
    return response.data;
  } catch (error) {
    console.error("Failed to connect to backend:", error);
    throw error;
  }
};

// Export the API instance for use in other services
export default api;
