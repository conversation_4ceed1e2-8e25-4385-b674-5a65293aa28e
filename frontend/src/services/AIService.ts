import React from 'react';
import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';

export interface AIRequest {
  prompt: string;
  temperature?: number;
  top_p?: number;
  max_tokens?: number;
  system_prompt?: string;
}

export interface AIResponse {
  text: string;
  model?: string;
  source?: 'primary' | 'backup';
  usage?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
    evalCount?: number;
    evalDuration?: number;
  };
}

export class AIService {
  /**
   * Generate text using the AI service
   * @param request The request parameters
   * @returns The generated text and metadata
   */
  static async generateText(request: AIRequest): Promise<AIResponse> {
    try {
      const response = await axios.post<AIResponse>(
        `${API_URL}/api/generate`,
        request,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
      
      return response.data;
    } catch (error) {
      console.error('Error generating text with AI service:', error);
      throw new Error('Failed to generate text with AI service');
    }
  }

  /**
   * Get AI service health status
   * @returns The health status of the AI service
   */
  static async getHealthStatus(): Promise<{
    status: 'healthy' | 'unhealthy';
    stats?: Record<string, any>;
    error?: string;
  }> {
    try {
      const response = await axios.get(`${API_URL}/ai-health`);
      return response.data;
    } catch (error) {
      console.error('Error checking AI service health:', error);
      return {
        status: 'unhealthy',
        error: 'Failed to connect to AI service'
      };
    }
  }

  /**
   * Generate an email draft based on a lead profile
   * @param leadProfile The lead profile data
   * @returns The generated email draft
   */
  static async generateEmailDraft(leadProfile: any): Promise<string> {
    const systemPrompt = `You are a professional sales representative for a water treatment chemicals company. 
    Your task is to draft a personalized email to a potential client based on their profile and needs.
    The email should be professional, concise, and highlight how your company's solutions can address their specific needs.`;
    
    const prompt = `Generate a personalized email for the following potential client:
    
    Company: ${leadProfile.companyName}
    Contact: ${leadProfile.contactPerson}
    Role: ${leadProfile.role}
    Industry: ${leadProfile.industry}
    Needs: ${leadProfile.needs.join(', ')}
    
    The email should introduce our water treatment chemical solutions that would address their specific needs.`;
    
    try {
      const response = await this.generateText({
        prompt,
        system_prompt: systemPrompt,
        temperature: 0.7,
        max_tokens: 500
      });
      
      return response.text;
    } catch (error) {
      console.error('Error generating email draft:', error);
      throw new Error('Failed to generate email draft');
    }
  }

  /**
   * Analyze a tender opportunity for relevance to water treatment chemicals
   * @param tenderData The tender opportunity data
   * @returns Analysis of the tender opportunity
   */
  static async analyzeTenderOpportunity(tenderData: any): Promise<{
    relevanceScore: number;
    analysis: string;
    keywords: string[];
  }> {
    const systemPrompt = `You are an expert business analyst specializing in water treatment chemicals and industrial solutions.
    Analyze the tender opportunity and provide insights on its relevance to a water treatment chemicals company.`;
    
    const prompt = `Analyze the following tender opportunity for its relevance to a water treatment chemicals company:
    
    Title: ${tenderData.title}
    Description: ${tenderData.description}
    Source: ${tenderData.source}
    Closing Date: ${tenderData.closing_date}
    
    Provide a relevance score (0-10), a brief analysis, and extract key keywords.`;
    
    try {
      const response = await this.generateText({
        prompt,
        system_prompt: systemPrompt,
        temperature: 0.7,
        max_tokens: 500
      });
      
      // Parse the response to extract structured data
      // This is a simple implementation - in a real app, you might want to use a more robust approach
      const lines = response.text.split('\n');
      let relevanceScore = 0;
      let analysis = '';
      let keywords: string[] = [];
      
      for (const line of lines) {
        if (line.toLowerCase().includes('relevance score')) {
          const match = line.match(/\d+(\.\d+)?/);
          if (match) {
            relevanceScore = parseFloat(match[0]);
          }
        } else if (line.toLowerCase().includes('keywords')) {
          const keywordText = line.split(':')[1];
          if (keywordText) {
            keywords = keywordText.split(',').map(k => k.trim());
          }
        } else if (line.trim() && !line.toLowerCase().includes('analysis')) {
          analysis += line + ' ';
        }
      }
      
      return {
        relevanceScore,
        analysis: analysis.trim(),
        keywords
      };
    } catch (error) {
      console.error('Error analyzing tender opportunity:', error);
      throw new Error('Failed to analyze tender opportunity');
    }
  }
}
