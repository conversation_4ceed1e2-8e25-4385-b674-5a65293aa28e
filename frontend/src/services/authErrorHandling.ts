import React from 'react';
/**
 * Unified authentication error handling system for the Mexel frontend
 * This module provides a consistent way to handle authentication errors
 * throughout the application.
 */

import { create } from "zustand";

// Authentication error types
export enum AuthErrorType {
  INVALID_CREDENTIALS = "INVALID_CREDENTIALS",
  SESSION_EXPIRED = "SESSION_EXPIRED",
  UNAUTHORIZED = "UNAUTHORIZED",
  FORBIDDEN = "FORBIDDEN",
  NETWORK_ERROR = "NETWORK_ERROR",
  UNKNOWN = "UNKNOWN",
}

// Authentication error messages
const AUTH_ERROR_MESSAGES = {
  [AuthErrorType.INVALID_CREDENTIALS]: "Invalid username or password.",
  [AuthErrorType.SESSION_EXPIRED]:
    "Your session has expired. Please log in again.",
  [AuthErrorType.UNAUTHORIZED]:
    "You are not authorized to access this resource.",
  [AuthErrorType.FORBIDDEN]:
    "You do not have permission to access this resource.",
  [AuthErrorType.NETWORK_ERROR]:
    "Network error. Please check your connection and try again.",
  [AuthErrorType.UNKNOWN]:
    "An unknown authentication error occurred. Please try again later.",
};

// Authentication error store
interface AuthErrorState {
  error: AuthErrorType | null;
  message: string | null;
  setError: (error: AuthErrorType | null) => void;
  clearError: () => void;
}

export const useAuthErrorStore = create<AuthErrorState>((set) => ({
  error: null,
  message: null,
  setError: (error) =>
    set({
      error,
      message: error ? AUTH_ERROR_MESSAGES[error] : null,
    }),
  clearError: () => set({ error: null, message: null }),
}));

// Handlers for different error scenarios
export const handleLoginError = (error: any): AuthErrorType => {
  console.error("Login error:", error);

  if (!error.response) {
    return AuthErrorType.NETWORK_ERROR;
  }

  const { status } = error.response;

  switch (status) {
    case 401:
      return AuthErrorType.INVALID_CREDENTIALS;
    case 403:
      return AuthErrorType.FORBIDDEN;
    default:
      return AuthErrorType.UNKNOWN;
  }
};

export const handleAuthenticationError = (error: any): AuthErrorType => {
  console.error("Authentication error:", error);

  if (!error.response) {
    return AuthErrorType.NETWORK_ERROR;
  }

  const { status } = error.response;

  switch (status) {
    case 401:
      return AuthErrorType.SESSION_EXPIRED;
    case 403:
      return AuthErrorType.UNAUTHORIZED;
    default:
      return AuthErrorType.UNKNOWN;
  }
};

// Helper for HTTP requests that handles auth errors automatically
export const withAuthErrorHandling = async <T>(
  apiCall: () => Promise<T>,
  onAuthError?: (errorType: AuthErrorType) => void
): Promise<T> => {
  try {
    return await apiCall();
  } catch (error: any) {
    const errorType = handleAuthenticationError(error);
    useAuthErrorStore.getState().setError(errorType);

    if (onAuthError) {
      onAuthError(errorType);
    }

    throw error;
  }
};

// Method to display a standard auth error message in components
export const getAuthErrorMessage = (error: AuthErrorType | null): string => {
  if (!error) return "";
  return (
    AUTH_ERROR_MESSAGES[error] || AUTH_ERROR_MESSAGES[AuthErrorType.UNKNOWN]
  );
};

// Utility to check if a specific error should trigger a redirect to login
export const shouldRedirectToLogin = (errorType: AuthErrorType): boolean => {
  return [AuthErrorType.SESSION_EXPIRED, AuthErrorType.UNAUTHORIZED].includes(
    errorType
  );
};

export default {
  AuthErrorType,
  useAuthErrorStore,
  handleLoginError,
  handleAuthenticationError,
  withAuthErrorHandling,
  getAuthErrorMessage,
  shouldRedirectToLogin,
};
