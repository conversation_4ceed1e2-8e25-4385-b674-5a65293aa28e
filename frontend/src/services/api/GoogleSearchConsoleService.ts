import React from 'react';
import { BaseApiService } from './BaseApiService';

// Define interfaces for Google Search Console API responses
export interface SearchAnalyticsQueryRequest {
  startDate: string;
  endDate: string;
  dimensions?: Array<'query' | 'page' | 'country' | 'device' | 'searchAppearance'>;
  rowLimit?: number;
  startRow?: number;
  searchType?: 'web' | 'image' | 'video' | 'news' | 'googleNews';
  dimensionFilterGroups?: Array<{
    filters: Array<{
      dimension: 'query' | 'page' | 'country' | 'device' | 'searchAppearance';
      operator?: 'equals' | 'contains' | 'notEquals' | 'notContains';
      expression: string;
    }>;
  }>;
}

export interface SearchAnalyticsQueryResponse {
  rows?: Array<{
    keys: string[];
    clicks: number;
    impressions: number;
    ctr: number;
    position: number;
  }>;
  responseAggregationType?: 'byPage' | 'byProperty';
}

export interface SiteVerificationInfo {
  siteUrl: string;
  siteVerificationPermission: 'SITE_VERIFICATION_ALLOWED' | 'SITE_VERIFICATION_DENIED';
}

export interface SitesList {
  siteEntry: Array<{
    siteUrl: string;
    permissionLevel: 'siteOwner' | 'siteFullUser' | 'siteRestrictedUser';
  }>;
}

/**
 * Google Search Console API Service
 *
 * Provides methods to interact with the Google Search Console API
 */
export class GoogleSearchConsoleService extends BaseApiService {
  private siteUrl: string;

  constructor(apiKey: string, siteUrl: string) {
    super('https://www.googleapis.com/webmasters/v3', apiKey);
    this.siteUrl = encodeURIComponent(siteUrl);
  }

  /**
   * Get default start date (30 days ago) in YYYY-MM-DD format
   * @returns Default start date
   */
  private getDefaultStartDate(): string {
    const date = new Date();
    date.setDate(date.getDate() - 30);
    return date.toISOString().split('T')[0];
  }

  /**
   * Get default end date (today) in YYYY-MM-DD format
   * @returns Default end date
   */
  private getDefaultEndDate(): string {
    return new Date().toISOString().split('T')[0];
  }

  /**
   * Get a list of sites in the Google Search Console account
   * @returns Promise with the list of sites
   */
  public async getSites(): Promise<SitesList> {
    return this.get<SitesList>('/sites');
  }

  /**
   * Get site verification information
   * @returns Promise with site verification information
   */
  public async getSiteVerification(): Promise<SiteVerificationInfo> {
    return this.get<SiteVerificationInfo>(`/sites/${this.siteUrl}/verificationDetails`);
  }

  /**
   * Query search analytics data
   * @param request - The search analytics query request
   * @returns Promise with search analytics data
   */
  public async querySearchAnalytics(request: SearchAnalyticsQueryRequest): Promise<SearchAnalyticsQueryResponse> {
    return this.post<SearchAnalyticsQueryResponse>(
      `/sites/${this.siteUrl}/searchAnalytics/query`,
      request
    );
  }

  /**
   * Get top queries (keywords)
   * @param startDate - Start date in YYYY-MM-DD format
   * @param endDate - End date in YYYY-MM-DD format
   * @param rowLimit - Maximum number of rows to return
   * @returns Promise with top queries data
   */
  public async getTopQueries(startDate: string, endDate: string, rowLimit: number = 100): Promise<SearchAnalyticsQueryResponse> {
    return this.querySearchAnalytics({
      startDate,
      endDate,
      dimensions: ['query'],
      rowLimit
    });
  }

  /**
   * Get top pages
   * @param startDate - Start date in YYYY-MM-DD format
   * @param endDate - End date in YYYY-MM-DD format
   * @param rowLimit - Maximum number of rows to return
   * @returns Promise with top pages data
   */
  public async getTopPages(startDate: string, endDate: string, rowLimit: number = 100): Promise<SearchAnalyticsQueryResponse> {
    return this.querySearchAnalytics({
      startDate,
      endDate,
      dimensions: ['page'],
      rowLimit
    });
  }

  /**
   * Get performance data by country
   * @param startDate - Start date in YYYY-MM-DD format
   * @param endDate - End date in YYYY-MM-DD format
   * @param rowLimit - Maximum number of rows to return
   * @returns Promise with performance data by country
   */
  public async getPerformanceByCountry(startDate: string, endDate: string, rowLimit: number = 100): Promise<SearchAnalyticsQueryResponse> {
    return this.querySearchAnalytics({
      startDate,
      endDate,
      dimensions: ['country'],
      rowLimit
    });
  }

  /**
   * Get performance data by device
   * @param startDate - Start date in YYYY-MM-DD format
   * @param endDate - End date in YYYY-MM-DD format
   * @returns Promise with performance data by device
   */
  public async getPerformanceByDevice(startDate: string, endDate: string): Promise<SearchAnalyticsQueryResponse> {
    return this.querySearchAnalytics({
      startDate,
      endDate,
      dimensions: ['device']
    });
  }

  /**
   * Get performance trend data
   * @param startDate - Start date in YYYY-MM-DD format
   * @param endDate - End date in YYYY-MM-DD format
   * @returns Promise with performance trend data
   */
  public async getPerformanceTrend(startDate: string, endDate: string): Promise<SearchAnalyticsQueryResponse> {
    // For trend data, we don't specify dimensions to get daily aggregated data
    return this.querySearchAnalytics({
      startDate,
      endDate
    });
  }

  /**
   * Get keyword position distribution
   * @param startDate - Start date in YYYY-MM-DD format (defaults to 30 days ago)
   * @param endDate - End date in YYYY-MM-DD format (defaults to today)
   * @returns Promise with keyword position distribution data
   */
  public async getKeywordPositionDistribution(
    startDate: string = this.getDefaultStartDate(),
    endDate: string = this.getDefaultEndDate()
  ): Promise<{
    position: string;
    count: number;
  }[]> {
    const response = await this.getTopQueries(startDate, endDate, 1000);

    // Initialize position ranges
    const positionRanges = [
      { position: '1-3', count: 0 },
      { position: '4-10', count: 0 },
      { position: '11-20', count: 0 },
      { position: '21-50', count: 0 },
      { position: '51-100', count: 0 }
    ];

    // Count keywords in each position range
    if (response.rows) {
      response.rows.forEach(row => {
        const position = row.position;
        if (position <= 3) {
          positionRanges[0].count++;
        } else if (position <= 10) {
          positionRanges[1].count++;
        } else if (position <= 20) {
          positionRanges[2].count++;
        } else if (position <= 50) {
          positionRanges[3].count++;
        } else {
          positionRanges[4].count++;
        }
      });
    }

    return positionRanges;
  }
}
