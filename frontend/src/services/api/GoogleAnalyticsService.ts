import React from 'react';
import { BaseApiService } from './BaseApiService';

// Define interfaces for Google Analytics API responses
export interface AnalyticsQueryRequest {
  viewId: string;
  startDate: string;
  endDate: string;
  metrics: Array<{
    expression: string;
    alias?: string;
  }>;
  dimensions?: Array<{
    name: string;
  }>;
  orderBys?: Array<{
    fieldName: string;
    sortOrder?: 'ASCENDING' | 'DESCENDING';
  }>;
  pageSize?: number;
  pageToken?: string;
  dateRanges?: Array<{
    startDate: string;
    endDate: string;
  }>;
  dimensionFilterClauses?: Array<{
    filters: Array<{
      dimensionName: string;
      operator: string;
      expressions: string[];
    }>;
  }>;
}

export interface AnalyticsQueryResponse {
  columnHeader: {
    dimensions: string[];
    metricHeader: {
      metricHeaderEntries: Array<{
        name: string;
        type: string;
      }>;
    };
  };
  data: {
    rows?: Array<{
      dimensions: string[];
      metrics: Array<{
        values: string[];
      }>;
    }>;
    totals: Array<{
      values: string[];
    }>;
    rowCount: number;
    minimums?: Array<{
      values: string[];
    }>;
    maximums?: Array<{
      values: string[];
    }>;
  };
  nextPageToken?: string;
}

export interface AccountSummary {
  id: string;
  name: string;
  webProperties: Array<{
    id: string;
    name: string;
    profiles: Array<{
      id: string;
      name: string;
    }>;
  }>;
}

export interface AccountSummaries {
  items: AccountSummary[];
}

/**
 * Google Analytics API Service
 *
 * Provides methods to interact with the Google Analytics API (GA4)
 */
export class GoogleAnalyticsService extends BaseApiService {
  private propertyId: string;

  constructor(apiKey: string, propertyId: string) {
    super('https://analyticsdata.googleapis.com/v1beta', apiKey);
    this.propertyId = propertyId;
  }

  // Method to get the API key
  getApiKey(): string | undefined {
    return this.api.defaults.headers.common['Authorization'] ?
      (this.api.defaults.headers.common['Authorization'] as string).split(' ')[1] :
      undefined;
  }

  /**
   * Run a report query
   * @param request - The analytics query request
   * @returns Promise with analytics data
   */
  public async runReport(request: any): Promise<any> {
    return this.post<any>(
      `/properties/${this.propertyId}:runReport`,
      request
    );
  }

  /**
   * Get account summaries
   * @returns Promise with account summaries
   */
  public async getAccountSummaries(): Promise<AccountSummaries> {
    // This endpoint is from the Analytics Management API
    // Create a new instance of BaseApiService for the management API
    const token = this.getApiKey() || '';
    const managementApi = new BaseApiService('https://www.googleapis.com/analytics/v3', token);

    // Use the public get method
    return managementApi.get<AccountSummaries>('/management/accountSummaries');
  }

  /**
   * Get website traffic data
   * @param startDate - Start date in YYYY-MM-DD format
   * @param endDate - End date in YYYY-MM-DD format
   * @returns Promise with website traffic data
   */
  public async getWebsiteTraffic(startDate: string, endDate: string): Promise<any> {
    return this.runReport({
      dateRanges: [
        {
          startDate,
          endDate
        }
      ],
      dimensions: [
        {
          name: 'date'
        }
      ],
      metrics: [
        {
          name: 'sessions'
        },
        {
          name: 'totalUsers'
        },
        {
          name: 'screenPageViews'
        }
      ]
    });
  }

  /**
   * Get traffic sources data
   * @param startDate - Start date in YYYY-MM-DD format
   * @param endDate - End date in YYYY-MM-DD format
   * @returns Promise with traffic sources data
   */
  public async getTrafficSources(startDate: string, endDate: string): Promise<any> {
    return this.runReport({
      dateRanges: [
        {
          startDate,
          endDate
        }
      ],
      dimensions: [
        {
          name: 'sessionSource'
        }
      ],
      metrics: [
        {
          name: 'sessions'
        },
        {
          name: 'totalUsers'
        }
      ],
      orderBys: [
        {
          metric: {
            metricName: 'sessions'
          },
          desc: true
        }
      ],
      limit: 10
    });
  }

  /**
   * Get top pages data
   * @param startDate - Start date in YYYY-MM-DD format
   * @param endDate - End date in YYYY-MM-DD format
   * @returns Promise with top pages data
   */
  public async getTopPages(startDate: string, endDate: string): Promise<any> {
    return this.runReport({
      dateRanges: [
        {
          startDate,
          endDate
        }
      ],
      dimensions: [
        {
          name: 'pagePath'
        },
        {
          name: 'pageTitle'
        }
      ],
      metrics: [
        {
          name: 'screenPageViews'
        },
        {
          name: 'averageSessionDuration'
        },
        {
          name: 'bounceRate'
        }
      ],
      orderBys: [
        {
          metric: {
            metricName: 'screenPageViews'
          },
          desc: true
        }
      ],
      limit: 20
    });
  }

  /**
   * Get user engagement metrics
   * @param startDate - Start date in YYYY-MM-DD format
   * @param endDate - End date in YYYY-MM-DD format
   * @returns Promise with user engagement metrics
   */
  public async getUserEngagement(startDate: string, endDate: string): Promise<any> {
    return this.runReport({
      dateRanges: [
        {
          startDate,
          endDate
        }
      ],
      metrics: [
        {
          name: 'averageSessionDuration'
        },
        {
          name: 'bounceRate'
        },
        {
          name: 'screenPageViewsPerSession'
        },
        {
          name: 'engagedSessionsPerUser'
        }
      ]
    });
  }

  /**
   * Get device category breakdown
   * @param startDate - Start date in YYYY-MM-DD format
   * @param endDate - End date in YYYY-MM-DD format
   * @returns Promise with device category breakdown
   */
  public async getDeviceCategories(startDate: string, endDate: string): Promise<any> {
    return this.runReport({
      dateRanges: [
        {
          startDate,
          endDate
        }
      ],
      dimensions: [
        {
          name: 'deviceCategory'
        }
      ],
      metrics: [
        {
          name: 'sessions'
        },
        {
          name: 'totalUsers'
        },
        {
          name: 'bounceRate'
        }
      ]
    });
  }
}
