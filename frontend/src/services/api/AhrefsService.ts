import React from 'react';
import { BaseApiService } from './BaseApiService';

// Define interfaces for Ahrefs API responses
export interface AhrefsDomainRating {
  domain: string;
  domain_rating: number;
  ahrefs_rank: number;
  ahrefs_rank_delta: number;
  referring_domains: number;
  referring_domains_delta: number;
  referring_pages: number;
  referring_pages_delta: number;
  dofollow_referring_domains: number;
  dofollow_referring_pages: number;
  linked_domains: number;
  linked_domains_delta: number;
  linked_pages: number;
  linked_pages_delta: number;
}

export interface AhrefsOrganicKeywords {
  domain: string;
  keyword: string;
  position: number;
  position_history: Array<{
    date: string;
    position: number;
  }>;
  search_volume: number;
  traffic: number;
  traffic_percentage: number;
  cpc: number;
  url: string;
  serp_features: string[];
  keyword_difficulty: number;
}

export interface AhrefsBacklinks {
  url_from: string;
  url_to: string;
  domain_from: string;
  domain_to: string;
  link_type: string;
  anchor: string;
  page_from_title: string;
  first_seen: string;
  last_seen: string;
  domain_from_rating: number;
  domain_to_rating: number;
  url_from_rating: number;
  url_to_rating: number;
  link_attributes: string[];
  is_dofollow: boolean;
  is_image: boolean;
  is_sponsored: boolean;
  is_ugc: boolean;
  is_nofollow: boolean;
}

export interface AhrefsContentExplorer {
  title: string;
  url: string;
  domain_rating: number;
  ahrefs_rank: number;
  referring_domains: number;
  referring_pages: number;
  organic_traffic: number;
  organic_keywords: number;
  published: string;
  first_seen: string;
  language: string;
  word_count: number;
  traffic_value: number;
}

export interface AhrefsCompetitors {
  domain: string;
  competitor_domain: string;
  intersections: number;
  competitor_keywords: number;
  domain_keywords: number;
  keywords_unique_to_competitor: number;
  keywords_unique_to_domain: number;
  competitor_domain_rating: number;
  competitor_organic_traffic: number;
}

/**
 * Ahrefs API Service
 * 
 * Provides methods to interact with the Ahrefs API
 */
export class AhrefsService extends BaseApiService {
  private domain: string;

  constructor(apiKey: string, domain: string) {
    super('https://api.ahrefs.com/v1', apiKey);
    this.domain = domain;
  }

  /**
   * Get domain rating data
   * @returns Promise with domain rating data
   */
  public async getDomainRating(): Promise<AhrefsDomainRating> {
    return this.get<AhrefsDomainRating>('/domain-rating', {
      params: {
        token: this.apiKey,
        target: this.domain,
        output: 'json'
      }
    });
  }

  /**
   * Get organic keywords data
   * @param limit - Maximum number of keywords to return
   * @param offset - Offset for pagination
   * @returns Promise with organic keywords data
   */
  public async getOrganicKeywords(limit: number = 100, offset: number = 0): Promise<AhrefsOrganicKeywords[]> {
    return this.get<AhrefsOrganicKeywords[]>('/organic-keywords', {
      params: {
        token: this.apiKey,
        target: this.domain,
        limit,
        offset,
        order_by: 'traffic:desc',
        output: 'json'
      }
    });
  }

  /**
   * Get backlinks data
   * @param limit - Maximum number of backlinks to return
   * @param offset - Offset for pagination
   * @returns Promise with backlinks data
   */
  public async getBacklinks(limit: number = 100, offset: number = 0): Promise<AhrefsBacklinks[]> {
    return this.get<AhrefsBacklinks[]>('/backlinks', {
      params: {
        token: this.apiKey,
        target: this.domain,
        limit,
        offset,
        order_by: 'domain_rating:desc',
        output: 'json'
      }
    });
  }

  /**
   * Get content explorer data
   * @param query - Search query
   * @param limit - Maximum number of results to return
   * @param offset - Offset for pagination
   * @returns Promise with content explorer data
   */
  public async getContentExplorer(query: string, limit: number = 100, offset: number = 0): Promise<AhrefsContentExplorer[]> {
    return this.get<AhrefsContentExplorer[]>('/content-explorer', {
      params: {
        token: this.apiKey,
        query,
        limit,
        offset,
        order_by: 'organic_traffic:desc',
        output: 'json'
      }
    });
  }

  /**
   * Get competitors data
   * @param limit - Maximum number of competitors to return
   * @param offset - Offset for pagination
   * @returns Promise with competitors data
   */
  public async getCompetitors(limit: number = 10, offset: number = 0): Promise<AhrefsCompetitors[]> {
    return this.get<AhrefsCompetitors[]>('/competing-domains', {
      params: {
        token: this.apiKey,
        target: this.domain,
        limit,
        offset,
        order_by: 'intersections:desc',
        output: 'json'
      }
    });
  }

  /**
   * Get keyword position distribution
   * @returns Promise with keyword position distribution data
   */
  public async getKeywordPositionDistribution(): Promise<{
    position: string;
    count: number;
  }[]> {
    const keywords = await this.getOrganicKeywords(1000);
    
    // Initialize position ranges
    const positionRanges = [
      { position: '1-3', count: 0 },
      { position: '4-10', count: 0 },
      { position: '11-20', count: 0 },
      { position: '21-50', count: 0 },
      { position: '51-100', count: 0 }
    ];
    
    // Count keywords in each position range
    keywords.forEach(keyword => {
      const position = keyword.position;
      if (position <= 3) {
        positionRanges[0].count++;
      } else if (position <= 10) {
        positionRanges[1].count++;
      } else if (position <= 20) {
        positionRanges[2].count++;
      } else if (position <= 50) {
        positionRanges[3].count++;
      } else {
        positionRanges[4].count++;
      }
    });
    
    return positionRanges;
  }

  /**
   * Get domain authority (Ahrefs Domain Rating)
   * @returns Promise with domain authority
   */
  public async getDomainAuthority(): Promise<number> {
    const rating = await this.getDomainRating();
    return rating.domain_rating;
  }

  /**
   * Get organic traffic
   * @returns Promise with organic traffic
   */
  public async getOrganicTraffic(): Promise<number> {
    const keywords = await this.getOrganicKeywords(1);
    let totalTraffic = 0;
    
    keywords.forEach(keyword => {
      totalTraffic += keyword.traffic;
    });
    
    return totalTraffic;
  }
}
