import { BaseApiService } from './BaseApiService';

// Define interfaces for SEMrush API responses
export interface DomainOverviewResponse {
  domain: string;
  rank: number;
  organic_keywords: number;
  organic_traffic: number;
  organic_traffic_cost: number;
  adwords_keywords: number;
  adwords_traffic: number;
  adwords_traffic_cost: number;
  traffic_trend: Array<{
    date: string;
    organic_traffic: number;
    paid_traffic: number;
  }>;
}

export interface KeywordOverviewResponse {
  keyword: string;
  search_volume: number;
  cpc: number;
  competition: number;
  results: number;
  trend: number[];
  related_keywords: Array<{
    keyword: string;
    search_volume: number;
    cpc: number;
    competition: number;
  }>;
}

export interface OrganicKeywordsResponse {
  keywords: Array<{
    keyword: string;
    position: number;
    previous_position: number;
    position_difference: number;
    search_volume: number;
    cpc: number;
    url: string;
    traffic: number;
    traffic_cost: number;
    competition: number;
    results: number;
    trends: number[];
  }>;
}

export interface BacklinksOverviewResponse {
  total: number;
  referring_domains: number;
  referring_ips: number;
  follow_links: number;
  nofollow_links: number;
  government_links: number;
  educational_links: number;
  text_links: number;
  image_links: number;
  form_links: number;
  frame_links: number;
}

export interface CompetitorsResponse {
  competitors: Array<{
    domain: string;
    common_keywords: number;
    se_keywords: number;
    se_traffic: number;
    se_traffic_price: number;
    adwords_keywords: number;
    adwords_traffic: number;
    adwords_traffic_price: number;
  }>;
}

/**
 * SEMrush API Service
 *
 * Provides methods to interact with the SEMrush API
 */
export class SEMrushService extends BaseApiService {
  private domain: string;
  private database: string;

  constructor(apiKey: string, domain: string, database: string = 'us') {
    super('https://api.semrush.com', apiKey);
    this.domain = domain;
    this.database = database;
  }

  /**
   * Get domain overview data
   * @returns Promise with domain overview data
   */
  public async getDomainOverview(): Promise<DomainOverviewResponse> {
    return this.get<DomainOverviewResponse>('/analytics/api/v1/domains/overview', {
      params: {
        key: this.apiKey,
        domain: this.domain,
        database: this.database,
        display_limit: 10,
        export_columns: 'Dn,Rk,Or,Ot,Oc,Ad,At,Ac',
        display_date: 'yesterday'
      }
    });
  }

  /**
   * Get keyword overview data
   * @param keyword - The keyword to get data for
   * @returns Promise with keyword overview data
   */
  public async getKeywordOverview(keyword: string): Promise<KeywordOverviewResponse> {
    return this.get<KeywordOverviewResponse>('/analytics/api/v1/keywords/overview', {
      params: {
        key: this.apiKey,
        phrase: keyword,
        database: this.database,
        export_columns: 'Ph,Nq,Cp,Co,Nr,Td'
      }
    });
  }

  /**
   * Get organic keywords data
   * @param limit - Maximum number of keywords to return
   * @returns Promise with organic keywords data
   */
  public async getOrganicKeywords(limit: number = 100): Promise<OrganicKeywordsResponse> {
    return this.get<OrganicKeywordsResponse>('/analytics/api/v1/domains/organic', {
      params: {
        key: this.apiKey,
        domain: this.domain,
        database: this.database,
        display_limit: limit,
        export_columns: 'Ph,Po,Pp,Pd,Nq,Cp,Ur,Tr,Tc,Co,Nr,Td',
        display_sort: 'tr_desc'
      }
    });
  }

  /**
   * Get backlinks overview data
   * @returns Promise with backlinks overview data
   */
  public async getBacklinksOverview(): Promise<BacklinksOverviewResponse> {
    return this.get<BacklinksOverviewResponse>('/analytics/api/v1/backlinks/overview', {
      params: {
        key: this.apiKey,
        target: this.domain,
        export_columns: 'target,total,domains,ips,follow,nofollow,government,educational,text,image,form,frame'
      }
    });
  }

  /**
   * Get competitors data
   * @param limit - Maximum number of competitors to return
   * @returns Promise with competitors data
   */
  public async getCompetitors(limit: number = 10): Promise<CompetitorsResponse> {
    return this.get<CompetitorsResponse>('/analytics/api/v1/domains/competitors', {
      params: {
        key: this.apiKey,
        domain: this.domain,
        database: this.database,
        display_limit: limit,
        export_columns: 'Dn,Np,Ok,Ot,Op,Ak,At,Ap',
        display_sort: 'np_desc'
      }
    });
  }

  /**
   * Get keyword position distribution
   * @returns Promise with keyword position distribution data
   */
  public async getKeywordPositionDistribution(): Promise<{
    position: string;
    count: number;
  }[]> {
    const response = await this.getOrganicKeywords(1000);

    // Initialize position ranges
    const positionRanges = [
      { position: '1-3', count: 0 },
      { position: '4-10', count: 0 },
      { position: '11-20', count: 0 },
      { position: '21-50', count: 0 },
      { position: '51-100', count: 0 }
    ];

    // Count keywords in each position range
    if (response.keywords) {
      response.keywords.forEach(keyword => {
        const position = keyword.position;
        if (position <= 3) {
          positionRanges[0].count++;
        } else if (position <= 10) {
          positionRanges[1].count++;
        } else if (position <= 20) {
          positionRanges[2].count++;
        } else if (position <= 50) {
          positionRanges[3].count++;
        } else {
          positionRanges[4].count++;
        }
      });
    }

    return positionRanges;
  }

  /**
   * Get domain authority (SEMrush Rank)
   * @returns Promise with domain authority
   */
  public async getDomainAuthority(): Promise<number> {
    const overview = await this.getDomainOverview();
    return overview.rank;
  }
}
