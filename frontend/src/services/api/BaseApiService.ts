import React from 'react';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * Base API Service
 *
 * Provides common functionality for all API services
 */
export class BaseApiService {
  protected api: AxiosInstance;
  protected baseUrl: string;
  protected apiKey?: string;

  constructor(baseUrl: string, apiKey?: string) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;

    this.api = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        ...(this.apiKey && { 'Authorization': `Bearer ${this.apiKey}` })
      }
    });

    // Add request interceptor for logging and modifying requests
    this.api.interceptors.request.use(
      (config) => {
        // Log request (in development only)
        if (process.env.NODE_ENV === 'development') {
          console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging and error handling
    this.api.interceptors.response.use(
      (response) => {
        // Log response (in development only)
        if (process.env.NODE_ENV === 'development') {
          console.log(`API Response: ${response.status} ${response.config.url}`);
        }
        return response;
      },
      (error) => {
        // Log error (in development only)
        if (process.env.NODE_ENV === 'development') {
          console.error('API Error:', error.response || error.message);
        }

        // Handle specific error cases
        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          switch (error.response.status) {
            case 401:
              // Handle unauthorized (e.g., redirect to login)
              console.error('API Error: Unauthorized. Please check your API credentials.');
              break;
            case 403:
              // Handle forbidden
              console.error('API Error: Forbidden. You do not have permission to access this resource.');
              break;
            case 404:
              // Handle not found
              console.error('API Error: Resource not found.');
              break;
            case 429:
              // Handle rate limiting
              console.error('API Error: Rate limit exceeded. Please try again later.');
              break;
            default:
              // Handle other errors
              console.error(`API Error: ${error.response.status} - ${error.response.data.message || 'Unknown error'}`);
          }
        } else if (error.request) {
          // The request was made but no response was received
          console.error('API Error: No response received from server. Please check your network connection.');
        } else {
          // Something happened in setting up the request that triggered an Error
          console.error('API Error:', error.message);
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Make a GET request
   * @param url - The URL to request
   * @param config - Optional Axios request config
   * @returns Promise with the response data
   */
  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.api.get<T>(url, config);
    return response.data;
  }

  /**
   * Make a POST request
   * @param url - The URL to request
   * @param data - The data to send
   * @param config - Optional Axios request config
   * @returns Promise with the response data
   */
  public async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.api.post<T>(url, data, config);
    return response.data;
  }

  /**
   * Make a PUT request
   * @param url - The URL to request
   * @param data - The data to send
   * @param config - Optional Axios request config
   * @returns Promise with the response data
   */
  public async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.api.put<T>(url, data, config);
    return response.data;
  }

  /**
   * Make a DELETE request
   * @param url - The URL to request
   * @param config - Optional Axios request config
   * @returns Promise with the response data
   */
  public async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.api.delete<T>(url, config);
    return response.data;
  }

  /**
   * Check if the API is available
   * @returns Promise with boolean indicating if the API is available
   */
  public async isAvailable(): Promise<boolean> {
    try {
      await this.api.head('');
      return true;
    } catch (error) {
      return false;
    }
  }
}
