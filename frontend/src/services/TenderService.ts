import React from 'react';
import axios from 'axios';
import { Tender, TenderStatus } from '../types/tender';
import { AuthService } from './AuthService';

// API response interfaces
interface ApiSuccessResponse<T> {
  status: 'success';
  data: T;
  timestamp: string;
  requestId: string;
}

interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

interface TenderFilters {
  status?: TenderStatus;
  category?: string;
  minConfidence?: number;
  search?: string;
  issuer?: string;
  source?: string;
  fromDate?: Date;
  toDate?: Date;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface TenderStats {
  totalCount: number;
  statusCounts: Record<string, number>;
  categoryCounts: Record<string, number>;
  sourceCounts: Record<string, number>;
  averageValue: number;
  upcomingClosingCount: number;
}

/**
 * Service for interacting with the Tender API
 */
export class TenderService {
  private baseUrl: string;
  private authService: AuthService;

  constructor(baseUrl = 'http://localhost:3001/api') {
    this.baseUrl = baseUrl;
    this.authService = new AuthService(baseUrl);
  }

  /**
   * Get tenders with optional filtering and pagination
   */
  async getTenders(
    filters: TenderFilters = {},
    page = 1,
    limit = 10
  ): Promise<{ tenders: Tender[], pagination: PaginatedResponse<Tender>['pagination'] }> {
    try {
      // Build query parameters
      const params = new URLSearchParams();

      // Add pagination params
      params.append('page', page.toString());
      params.append('limit', limit.toString());

      // Add filter params if they exist
      if (filters.status) params.append('status', filters.status);
      if (filters.category) params.append('category', filters.category);
      if (filters.minConfidence) params.append('minConfidence', filters.minConfidence.toString());
      if (filters.search) params.append('search', filters.search);
      if (filters.issuer) params.append('issuer', filters.issuer);
      if (filters.source) params.append('source', filters.source);
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);

      // Add date filters if they exist
      if (filters.fromDate) params.append('fromDate', filters.fromDate.toISOString());
      if (filters.toDate) params.append('toDate', filters.toDate.toISOString());

      // Make the API request
      const response = await axios.get<ApiSuccessResponse<{
        tenders: Tender[],
        pagination: PaginatedResponse<Tender>['pagination']
      }>>(`${this.baseUrl}/tenders?${params.toString()}`);

      return response.data.data;
    } catch (error) {
      console.error('Error fetching tenders:', error);
      throw error;
    }
  }

  /**
   * Get a tender by ID
   */
  async getTender(id: string): Promise<Tender> {
    try {
      const response = await axios.get<ApiSuccessResponse<Tender>>(`${this.baseUrl}/tenders/${id}`);
      return response.data.data;
    } catch (error) {
      console.error(`Error fetching tender ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update a tender's status
   */
  async updateTenderStatus(id: string, status: TenderStatus): Promise<void> {
    try {
      // Check if user is authenticated
      if (!this.authService.isLoggedIn()) {
        throw new Error('Authentication required');
      }

      const token = this.authService.getToken();

      await axios.patch<ApiSuccessResponse<{ message: string }>>(
        `${this.baseUrl}/tenders/${id}/status`,
        { status },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
    } catch (error) {
      console.error(`Error updating tender ${id} status:`, error);
      throw error;
    }
  }

  /**
   * Get all unique tender categories
   */
  async getCategories(): Promise<string[]> {
    try {
      const response = await axios.get<ApiSuccessResponse<string[]>>(`${this.baseUrl}/tenders/categories`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching tender categories:', error);
      throw error;
    }
  }

  /**
   * Get all unique tender issuers
   */
  async getIssuers(): Promise<string[]> {
    try {
      const response = await axios.get<ApiSuccessResponse<string[]>>(`${this.baseUrl}/tenders/issuers`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching tender issuers:', error);
      throw error;
    }
  }

  /**
   * Get all unique tender sources
   */
  async getSources(): Promise<string[]> {
    try {
      const response = await axios.get<ApiSuccessResponse<string[]>>(`${this.baseUrl}/tenders/sources`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching tender sources:', error);
      throw error;
    }
  }

  /**
   * Get tender statistics
   */
  async getStats(): Promise<TenderStats> {
    try {
      const response = await axios.get<ApiSuccessResponse<TenderStats>>(`${this.baseUrl}/tenders/stats`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching tender statistics:', error);
      throw error;
    }
  }

  /**
   * Refresh tenders by triggering a scraping process
   */
  async refreshTenders(): Promise<{ message: string, count: number }> {
    try {
      // Check if user is authenticated and has admin or editor role
      if (!this.authService.isLoggedIn()) {
        throw new Error('Authentication required');
      }

      if (!this.authService.hasRole(['admin', 'editor'])) {
        throw new Error('Insufficient permissions');
      }

      const token = this.authService.getToken();

      const response = await axios.post<ApiSuccessResponse<{ message: string, count: number }>>(
        `${this.baseUrl}/tenders/refresh`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error refreshing tenders:', error);
      throw error;
    }
  }
}
