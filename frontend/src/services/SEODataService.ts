import React from 'react';
import { GoogleSearchConsoleService } from './api/GoogleSearchConsoleService';
import { GoogleAnalyticsService } from './api/GoogleAnalyticsService';
import { SEMrushService } from './api/SEMrushService';
import { AhrefsService } from './api/AhrefsService';
import {
  SEOInsightsData,
  SEOKeyword,
  SEOPageMetrics,
  CompetitorData,
  HistoricalDataPeriod,
  HistoricalMetrics,
  HistoricalDataPoint
} from '../components/SEOInsightsDashboard/types';
import { getMockSEOData } from '../components/SEOInsightsDashboard/mockData';

/**
 * SEO Data Service Configuration
 */
export interface SEODataServiceConfig {
  useMockData?: boolean;
  googleSearchConsole?: {
    apiKey: string;
    siteUrl: string;
  };
  googleAnalytics?: {
    apiKey: string;
    propertyId: string;
  };
  semrush?: {
    apiKey: string;
    domain: string;
    database?: string;
  };
  ahrefs?: {
    apiKey: string;
    domain: string;
  };
}

/**
 * SEO Data Service
 *
 * Provides a unified interface to access SEO data from various sources
 */
export class SEODataService {
  private config: SEODataServiceConfig;
  private googleSearchConsoleService?: GoogleSearchConsoleService;
  private googleAnalyticsService?: GoogleAnalyticsService;
  private semrushService?: SEMrushService;
  private ahrefsService?: AhrefsService;

  constructor(config: SEODataServiceConfig) {
    this.config = config;

    // Initialize services based on provided configuration
    if (config.googleSearchConsole) {
      this.googleSearchConsoleService = new GoogleSearchConsoleService(
        config.googleSearchConsole.apiKey,
        config.googleSearchConsole.siteUrl
      );
    }

    if (config.googleAnalytics) {
      this.googleAnalyticsService = new GoogleAnalyticsService(
        config.googleAnalytics.apiKey,
        config.googleAnalytics.propertyId
      );
    }

    if (config.semrush) {
      this.semrushService = new SEMrushService(
        config.semrush.apiKey,
        config.semrush.domain,
        config.semrush.database
      );
    }

    if (config.ahrefs) {
      this.ahrefsService = new AhrefsService(
        config.ahrefs.apiKey,
        config.ahrefs.domain
      );
    }
  }

  /**
   * Get all SEO data for the dashboard
   * @returns Promise with all SEO data
   */
  public async getAllSEOData(): Promise<SEOInsightsData> {
    // If mock data is enabled, return mock data
    if (this.config.useMockData) {
      return getMockSEOData();
    }

    try {
      // Get data from all available sources
      const [
        domainAuthority,
        organicTraffic,
        organicKeywords,
        backlinks,
        topKeywords,
        topPages,
        trafficTrend,
        keywordPositions,
        competitors,
        historicalData
      ] = await Promise.all([
        this.getDomainAuthority(),
        this.getOrganicTraffic(),
        this.getOrganicKeywordsCount(),
        this.getBacklinksCount(),
        this.getTopKeywords(),
        this.getTopPages(),
        this.getTrafficTrend(),
        this.getKeywordPositionDistribution(),
        this.getCompetitors(),
        this.getHistoricalData()
      ]);

      // Use mock data for parts we don't have real data for yet
      const mockData = getMockSEOData();

      // Return combined data with all required properties for SEOInsightsData
      return {
        overview: mockData.overview, // Use mock data for overview
        domainAuthority,
        organicTraffic,
        organicKeywords,
        backlinks,
        topKeywords,
        topPages,
        trafficTrend,
        keywordPositions,
        insights: mockData.insights, // Use mock data for insights
        competitors,
        technicalIssues: mockData.technicalIssues, // Use mock data for technical issues
        contentSuggestions: mockData.contentSuggestions, // Use mock data for content suggestions
        keywordOpportunities: mockData.keywordOpportunities, // Use mock data for keyword opportunities
        seoHealth: mockData.seoHealth, // Use mock data for SEO health
        historicalData,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching SEO data:', error);
      // Fallback to mock data if there's an error
      return getMockSEOData();
    }
  }

  /**
   * Get historical data for SEO metrics
   * @returns Promise with historical data
   */
  private async getHistoricalData(): Promise<{
    availablePeriods: HistoricalDataPeriod[];
    metrics: HistoricalDataPoint<HistoricalMetrics>[];
    keywordTrends?: any[];
    pageTrends?: any[];
  }> {
    try {
      // Generate periods for the last 6 months
      const periods = this.generateHistoricalPeriods();

      // Get historical metrics for each period
      const metrics = await this.getHistoricalMetrics(periods);

      // Get keyword trends
      const keywordTrends = await this.getKeywordTrends(periods);

      // Get page trends
      const pageTrends = await this.getPageTrends(periods);

      return {
        availablePeriods: periods,
        metrics,
        keywordTrends,
        pageTrends
      };
    } catch (error) {
      console.error('Error fetching historical data:', error);
      // Fallback to mock data
      const mockData = getMockSEOData();
      return mockData.historicalData!;
    }
  }

  /**
   * Generate historical periods (last 6 months)
   * @returns Array of historical periods
   */
  private generateHistoricalPeriods(): HistoricalDataPeriod[] {
    const periods: HistoricalDataPeriod[] = [];
    const now = new Date();

    // Current month
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    // Generate periods for the last 6 months
    for (let i = 0; i < 6; i++) {
      const monthIndex = currentMonth - i;
      const year = monthIndex < 0 ? currentYear - 1 : currentYear;
      const month = monthIndex < 0 ? 12 + monthIndex : monthIndex;

      const startDate = new Date(year, month, 1);
      const endDate = new Date(year, month + 1, 0);

      periods.push({
        id: `${year}-${(month + 1).toString().padStart(2, '0')}`,
        label: startDate.toLocaleString('default', { month: 'long', year: 'numeric' }),
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0]
      });
    }

    // Reverse to have oldest first
    return periods.reverse();
  }

  /**
   * Get historical metrics for each period
   * @param periods - Array of historical periods
   * @returns Promise with historical metrics
   */
  private async getHistoricalMetrics(periods: HistoricalDataPeriod[]): Promise<HistoricalDataPoint<HistoricalMetrics>[]> {
    const metrics: HistoricalDataPoint<HistoricalMetrics>[] = [];

    // For each period, get metrics
    for (const period of periods) {
      try {
        if (this.googleSearchConsoleService) {
          // Get data from Google Search Console for this period
          const [
            keywordData,
            performanceData
          ] = await Promise.all([
            this.googleSearchConsoleService.getTopQueries(period.startDate, period.endDate, 1000),
            this.googleSearchConsoleService.getPerformanceTrend(period.startDate, period.endDate)
          ]);

          // Calculate metrics
          let totalClicks = 0;
          let totalImpressions = 0;
          let totalPosition = 0;
          let keywordCount = 0;
          let topPositionCount = 0;
          let firstPageCount = 0;

          if (keywordData.rows) {
            keywordCount = keywordData.rows.length;

            keywordData.rows.forEach(row => {
              totalPosition += row.position;
              if (row.position <= 3) topPositionCount++;
              if (row.position <= 10) firstPageCount++;
            });
          }

          if (performanceData.rows) {
            performanceData.rows.forEach(row => {
              totalClicks += row.clicks;
              totalImpressions += row.impressions;
            });
          }

          // Calculate average position
          const averagePosition = keywordCount > 0 ? totalPosition / keywordCount : 0;

          // Calculate CTR
          const clickThroughRate = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;

          // Get keyword position distribution
          const keywordPositionDistribution = [
            { position: '1-3', count: topPositionCount },
            { position: '4-10', count: firstPageCount - topPositionCount },
            { position: '11-20', count: 0 },
            { position: '21-50', count: 0 },
            { position: '51-100', count: 0 }
          ];

          if (keywordData.rows) {
            keywordData.rows.forEach(row => {
              const position = row.position;
              if (position > 10 && position <= 20) {
                keywordPositionDistribution[2].count++;
              } else if (position > 20 && position <= 50) {
                keywordPositionDistribution[3].count++;
              } else if (position > 50) {
                keywordPositionDistribution[4].count++;
              }
            });
          }

          // Get domain authority and backlinks (these might not change monthly)
          const domainAuthority = await this.getDomainAuthority();
          const backlinks = await this.getBacklinksCount();

          metrics.push({
            period: period.id,
            data: {
              domainAuthority,
              organicTraffic: totalClicks,
              organicKeywords: keywordCount,
              backlinks,
              keywordPositionDistribution,
              averagePosition,
              topPositionCount,
              firstPageCount,
              clickThroughRate,
              impressions: totalImpressions,
              clicks: totalClicks
            }
          });
        } else if (this.semrushService) {
          // Implement SEMrush historical data fetching
          // This would require multiple API calls with different date ranges
          // For simplicity, we'll use mock data for now
          const mockData = getMockSEOData();
          const mockMetric = mockData.historicalData?.metrics.find(m => m.period === period.id);

          if (mockMetric) {
            metrics.push(mockMetric);
          }
        } else {
          // Fallback to mock data
          const mockData = getMockSEOData();
          const mockMetric = mockData.historicalData?.metrics.find(m => m.period === period.id);

          if (mockMetric) {
            metrics.push(mockMetric);
          }
        }
      } catch (error) {
        console.error(`Error fetching historical metrics for period ${period.id}:`, error);
        // Fallback to mock data for this period
        const mockData = getMockSEOData();
        const mockMetric = mockData.historicalData?.metrics.find(m => m.period === period.id);

        if (mockMetric) {
          metrics.push(mockMetric);
        }
      }
    }

    return metrics;
  }

  /**
   * Get keyword trends
   * @param periods - Array of historical periods
   * @returns Promise with keyword trends
   */
  private async getKeywordTrends(periods: HistoricalDataPeriod[]): Promise<any[]> {
    try {
      // Get top keywords
      const topKeywords = await this.getTopKeywords();
      const keywordTrends: any[] = [];

      // For each top keyword, get historical positions
      for (const keyword of topKeywords.slice(0, 5)) { // Limit to top 5 keywords
        const positions: { period: string; position: number }[] = [];

        // For each period, get position for this keyword
        for (const period of periods) {
          if (this.googleSearchConsoleService) {
            try {
              // Query for this specific keyword in this period
              const response = await this.googleSearchConsoleService.querySearchAnalytics({
                startDate: period.startDate,
                endDate: period.endDate,
                dimensions: ['query'],
                dimensionFilterGroups: [{
                  filters: [{
                    dimension: 'query',
                    operator: 'equals',
                    expression: keyword.keyword
                  }]
                }]
              });

              if (response.rows && response.rows.length > 0) {
                positions.push({
                  period: period.id,
                  position: Math.round(response.rows[0].position)
                });
              } else {
                // No data for this keyword in this period
                positions.push({
                  period: period.id,
                  position: 100 // Default to position 100 (not ranking)
                });
              }
            } catch (error) {
              console.error(`Error fetching position for keyword ${keyword.keyword} in period ${period.id}:`, error);
              // Fallback to mock data
              positions.push({
                period: period.id,
                position: Math.round(50 + Math.random() * 50) // Random position between 50-100
              });
            }
          } else {
            // Fallback to mock data
            positions.push({
              period: period.id,
              position: Math.round(50 + Math.random() * 50) // Random position between 50-100
            });
          }
        }

        keywordTrends.push({
          keyword: keyword.keyword,
          positions
        });
      }

      return keywordTrends;
    } catch (error) {
      console.error('Error fetching keyword trends:', error);
      // Fallback to mock data
      const mockData = getMockSEOData();
      return mockData.historicalData?.keywordTrends || [];
    }
  }

  /**
   * Get page trends
   * @param periods - Array of historical periods
   * @returns Promise with page trends
   */
  private async getPageTrends(periods: HistoricalDataPeriod[]): Promise<any[]> {
    try {
      // Get top pages
      const topPages = await this.getTopPages();
      const pageTrends: any[] = [];

      // For each top page, get historical metrics
      for (const page of topPages.slice(0, 3)) { // Limit to top 3 pages
        const metrics: { period: string; organicTraffic: number; position: number }[] = [];

        // For each period, get metrics for this page
        for (const period of periods) {
          if (this.googleSearchConsoleService) {
            try {
              // Query for this specific page in this period
              const response = await this.googleSearchConsoleService.querySearchAnalytics({
                startDate: period.startDate,
                endDate: period.endDate,
                dimensions: ['page'],
                dimensionFilterGroups: [{
                  filters: [{
                    dimension: 'page',
                    operator: 'equals',
                    expression: page.url
                  }]
                }]
              });

              if (response.rows && response.rows.length > 0) {
                metrics.push({
                  period: period.id,
                  organicTraffic: Math.round(response.rows[0].clicks),
                  position: Math.round(response.rows[0].position)
                });
              } else {
                // No data for this page in this period
                metrics.push({
                  period: period.id,
                  organicTraffic: 0,
                  position: 100 // Default to position 100 (not ranking)
                });
              }
            } catch (error) {
              console.error(`Error fetching metrics for page ${page.url} in period ${period.id}:`, error);
              // Fallback to mock data
              metrics.push({
                period: period.id,
                organicTraffic: Math.round(Math.random() * 100),
                position: Math.round(10 + Math.random() * 40) // Random position between 10-50
              });
            }
          } else {
            // Fallback to mock data
            metrics.push({
              period: period.id,
              organicTraffic: Math.round(Math.random() * 100),
              position: Math.round(10 + Math.random() * 40) // Random position between 10-50
            });
          }
        }

        pageTrends.push({
          url: page.url,
          metrics
        });
      }

      return pageTrends;
    } catch (error) {
      console.error('Error fetching page trends:', error);
      // Fallback to mock data
      const mockData = getMockSEOData();
      return mockData.historicalData?.pageTrends || [];
    }
  }

  /**
   * Get domain authority
   * @returns Promise with domain authority
   */
  private async getDomainAuthority(): Promise<number> {
    try {
      if (this.semrushService) {
        return await this.semrushService.getDomainAuthority();
      } else if (this.ahrefsService) {
        return await this.ahrefsService.getDomainAuthority();
      } else {
        // Fallback to mock data
        return getMockSEOData().domainAuthority;
      }
    } catch (error) {
      console.error('Error fetching domain authority:', error);
      return getMockSEOData().domainAuthority;
    }
  }

  /**
   * Get organic traffic
   * @returns Promise with organic traffic
   */
  private async getOrganicTraffic(): Promise<number> {
    try {
      if (this.googleSearchConsoleService) {
        // Calculate date range for last 30 days
        const endDate = new Date().toISOString().split('T')[0];
        const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        const response = await this.googleSearchConsoleService.getPerformanceTrend(startDate, endDate);

        // Sum up clicks for the period
        let totalClicks = 0;
        if (response.rows) {
          response.rows.forEach(row => {
            totalClicks += row.clicks;
          });
        }

        return totalClicks;
      } else if (this.semrushService) {
        const overview = await this.semrushService.getDomainOverview();
        return overview.organic_traffic;
      } else if (this.ahrefsService) {
        return await this.ahrefsService.getOrganicTraffic();
      } else {
        // Fallback to mock data
        return getMockSEOData().organicTraffic;
      }
    } catch (error) {
      console.error('Error fetching organic traffic:', error);
      return getMockSEOData().organicTraffic;
    }
  }

  /**
   * Get organic keywords count
   * @returns Promise with organic keywords count
   */
  private async getOrganicKeywordsCount(): Promise<number> {
    try {
      if (this.semrushService) {
        const overview = await this.semrushService.getDomainOverview();
        return overview.organic_keywords;
      } else if (this.googleSearchConsoleService) {
        // Calculate date range for last 30 days
        const endDate = new Date().toISOString().split('T')[0];
        const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        const response = await this.googleSearchConsoleService.getTopQueries(startDate, endDate, 1000);

        return response.rows ? response.rows.length : 0;
      } else {
        // Fallback to mock data
        return getMockSEOData().organicKeywords;
      }
    } catch (error) {
      console.error('Error fetching organic keywords count:', error);
      return getMockSEOData().organicKeywords;
    }
  }

  /**
   * Get backlinks count
   * @returns Promise with backlinks count
   */
  private async getBacklinksCount(): Promise<number> {
    try {
      if (this.semrushService) {
        const backlinks = await this.semrushService.getBacklinksOverview();
        return backlinks.total;
      } else if (this.ahrefsService) {
        const rating = await this.ahrefsService.getDomainRating();
        return rating.referring_pages;
      } else {
        // Fallback to mock data
        return getMockSEOData().backlinks;
      }
    } catch (error) {
      console.error('Error fetching backlinks count:', error);
      return getMockSEOData().backlinks;
    }
  }

  /**
   * Get top keywords
   * @returns Promise with top keywords
   */
  private async getTopKeywords(): Promise<SEOKeyword[]> {
    try {
      if (this.googleSearchConsoleService) {
        // Calculate date range for last 30 days
        const endDate = new Date().toISOString().split('T')[0];
        const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        // Get previous period for comparison
        const previousEndDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        const previousStartDate = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        const [currentResponse, previousResponse] = await Promise.all([
          this.googleSearchConsoleService.getTopQueries(startDate, endDate, 100),
          this.googleSearchConsoleService.getTopQueries(previousStartDate, previousEndDate, 1000)
        ]);

        if (!currentResponse.rows) {
          throw new Error('No keyword data available');
        }

        // Create a map of previous positions
        const previousPositions = new Map<string, number>();
        if (previousResponse.rows) {
          previousResponse.rows.forEach(row => {
            previousPositions.set(row.keys[0], row.position);
          });
        }

        // Transform the data
        return currentResponse.rows.map(row => {
          const keyword = row.keys[0];
          const position = Math.round(row.position);
          const previousPosition = previousPositions.has(keyword)
            ? Math.round(previousPositions.get(keyword)!)
            : position;

          return {
            keyword,
            volume: Math.round(row.impressions),
            position,
            previousPosition,
            change: previousPosition - position,
            difficulty: Math.round(Math.random() * 100), // Not available from GSC
            traffic: Math.round(row.clicks),
            intent: this.determineKeywordIntent(keyword),
            cpc: Math.random() * 5, // Not available from GSC
            competition: Math.random() // Not available from GSC
          };
        });
      } else if (this.semrushService) {
        const response = await this.semrushService.getOrganicKeywords(100);

        return response.keywords.map(keyword => ({
          keyword: keyword.keyword,
          volume: keyword.search_volume,
          position: keyword.position,
          previousPosition: keyword.previous_position,
          change: keyword.position_difference,
          difficulty: Math.round(Math.random() * 100), // Not directly available
          traffic: keyword.traffic,
          intent: this.determineKeywordIntent(keyword.keyword),
          cpc: keyword.cpc,
          competition: keyword.competition
        }));
      } else {
        // Fallback to mock data
        return getMockSEOData().topKeywords;
      }
    } catch (error) {
      console.error('Error fetching top keywords:', error);
      return getMockSEOData().topKeywords;
    }
  }

  /**
   * Get top pages
   * @returns Promise with top pages
   */
  private async getTopPages(): Promise<SEOPageMetrics[]> {
    try {
      if (this.googleSearchConsoleService && this.googleAnalyticsService) {
        // Calculate date range for last 30 days
        const endDate = new Date().toISOString().split('T')[0];
        const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        const [gscResponse, gaResponse] = await Promise.all([
          this.googleSearchConsoleService.getTopPages(startDate, endDate, 20),
          this.googleAnalyticsService.getTopPages(startDate, endDate)
        ]);

        if (!gscResponse.rows) {
          throw new Error('No page data available');
        }

        // Create a map of GA data
        interface GAPageData {
          pageTitle: string;
          pageViews: number;
          avgSessionDuration: number;
          bounceRate: number;
        }
        
        const gaData = new Map<string, GAPageData>();
        if (gaResponse.rows) {
          gaResponse.rows.forEach(row => {
            gaData.set(row.dimensions[0], {
              pageTitle: row.dimensions[1],
              pageViews: parseInt(row.metrics[0].values[0]),
              avgSessionDuration: parseFloat(row.metrics[0].values[1]),
              bounceRate: parseFloat(row.metrics[0].values[2])
            });
          });
        }

        // Transform the data
        return gscResponse.rows.map(row => {
          const url = row.keys[0];
          const gaPageData = gaData.get(url);

          return {
            url,
            title: gaPageData ? gaPageData.pageTitle : url.split('/').pop() || url,
            visits: gaPageData ? gaPageData.pageViews : Math.round(row.impressions),
            bounceRate: gaPageData ? Math.round(gaPageData.bounceRate) : Math.round(Math.random() * 100),
            avgTimeOnPage: gaPageData ? Math.round(gaPageData.avgSessionDuration) : Math.round(Math.random() * 300),
            organicTraffic: Math.round(row.clicks),
            keywordsRanked: Math.round(Math.random() * 30), // Not directly available
            conversionRate: Math.random() * 5, // Not directly available
            pageSpeed: {
              mobile: 60 + Math.round(Math.random() * 30),
              desktop: 70 + Math.round(Math.random() * 25)
            },
            wordCount: 800 + Math.round(Math.random() * 1500)
          };
        });
      } else {
        // Fallback to mock data
        return getMockSEOData().topPages;
      }
    } catch (error) {
      console.error('Error fetching top pages:', error);
      return getMockSEOData().topPages;
    }
  }

  /**
   * Get traffic trend
   * @returns Promise with traffic trend
   */
  private async getTrafficTrend(): Promise<{
    date: string;
    organicTraffic: number;
    directTraffic: number;
    referralTraffic: number;
  }[]> {
    try {
      if (this.googleSearchConsoleService && this.googleAnalyticsService) {
        // Calculate date range for last 7 months
        const endDate = new Date().toISOString().split('T')[0];
        const startDate = new Date(Date.now() - 7 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        const [gscResponse, gaResponse] = await Promise.all([
          this.googleSearchConsoleService.getPerformanceTrend(startDate, endDate),
          this.googleAnalyticsService.getWebsiteTraffic(startDate, endDate)
        ]);

        // Process the data by month
        interface MonthlyTrafficData {
          organicTraffic: number;
          directTraffic: number;
          referralTraffic: number;
        }
        
        const monthlyData = new Map<string, MonthlyTrafficData>();

        // We already defined MonthlyTrafficData above
        
        // Process GSC data (organic traffic)
        if (gscResponse.rows) {
          gscResponse.rows.forEach(row => {
            const date = new Date(row.keys[0]);
            const month = date.toLocaleString('default', { month: 'short' });

            if (!monthlyData.has(month)) {
              monthlyData.set(month, {
                organicTraffic: 0,
                directTraffic: 0,
                referralTraffic: 0
              } as MonthlyTrafficData);
            }

            const data = monthlyData.get(month)!;
            data.organicTraffic += row.clicks;
          });
        }

        // Process GA data (direct and referral traffic)
        if (gaResponse.rows) {
          gaResponse.rows.forEach(row => {
            const date = new Date(row.dimensions[0]);
            const month = date.toLocaleString('default', { month: 'short' });

            if (!monthlyData.has(month)) {
              monthlyData.set(month, {
                organicTraffic: 0,
                directTraffic: 0,
                referralTraffic: 0
              });
            }

            const data = monthlyData.get(month)!;
            // Assume 30% of sessions are direct and 20% are referral (simplified)
            data.directTraffic += parseInt(row.metrics[0].values[0]) * 0.3;
            data.referralTraffic += parseInt(row.metrics[0].values[0]) * 0.2;
          });
        }

        // Convert map to array and sort by month
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const result = Array.from(monthlyData.entries())
          .map(([month, data]) => ({
            date: month,
            organicTraffic: Math.round(data.organicTraffic),
            directTraffic: Math.round(data.directTraffic),
            referralTraffic: Math.round(data.referralTraffic)
          }))
          .sort((a, b) => months.indexOf(a.date) - months.indexOf(b.date));

        return result;
      } else if (this.semrushService) {
        const overview = await this.semrushService.getDomainOverview();

        if (overview.traffic_trend) {
          return overview.traffic_trend.map(item => ({
            date: new Date(item.date).toLocaleString('default', { month: 'short' }),
            organicTraffic: item.organic_traffic,
            directTraffic: Math.round(item.organic_traffic * 0.3), // Estimate
            referralTraffic: Math.round(item.organic_traffic * 0.2) // Estimate
          }));
        }
      }

      // Fallback to mock data
      return getMockSEOData().trafficTrend;
    } catch (error) {
      console.error('Error fetching traffic trend:', error);
      return getMockSEOData().trafficTrend;
    }
  }

  /**
   * Get keyword position distribution
   * @returns Promise with keyword position distribution
   */
  private async getKeywordPositionDistribution(): Promise<{
    position: string;
    count: number;
  }[]> {
    try {
      if (this.googleSearchConsoleService) {
        return await this.googleSearchConsoleService.getKeywordPositionDistribution();
      } else if (this.semrushService) {
        return await this.semrushService.getKeywordPositionDistribution();
      } else if (this.ahrefsService) {
        return await this.ahrefsService.getKeywordPositionDistribution();
      } else {
        // Fallback to mock data
        return getMockSEOData().keywordPositions;
      }
    } catch (error) {
      console.error('Error fetching keyword position distribution:', error);
      return getMockSEOData().keywordPositions;
    }
  }

  /**
   * Get competitors
   * @returns Promise with competitors data
   */
  private async getCompetitors(): Promise<CompetitorData[]> {
    try {
      if (this.semrushService) {
        const response = await this.semrushService.getCompetitors();

        return await Promise.all(response.competitors.map(async competitor => {
          // Get additional data for each competitor
          const topKeywords = await this.getCompetitorTopKeywords(competitor.domain);

          return {
            domain: competitor.domain,
            domainAuthority: Math.round(Math.random() * 100), // Not directly available
            organicTraffic: competitor.se_traffic,
            organicKeywords: competitor.se_keywords,
            backlinks: Math.round(Math.random() * 1000), // Not directly available
            sharedKeywords: competitor.common_keywords,
            topKeywords,
            trafficTrend: Array.from({ length: 7 }, (_, i) => ({
              date: new Date(Date.now() - (6 - i) * 30 * 24 * 60 * 60 * 1000)
                .toLocaleString('default', { month: 'short' }),
              traffic: Math.round(competitor.se_traffic * (0.9 + i * 0.02)) // Simulate trend
            }))
          };
        }));
      } else {
        // Fallback to mock data
        return getMockSEOData().competitors;
      }
    } catch (error) {
      console.error('Error fetching competitors:', error);
      return getMockSEOData().competitors;
    }
  }

  /**
   * Get competitor top keywords
   * @param domain - Competitor domain
   * @returns Promise with competitor top keywords
   */
  private async getCompetitorTopKeywords(domain: string): Promise<SEOKeyword[]> {
    // This would require additional API calls to get competitor keywords
    // For simplicity, we'll return mock data
    return [
      {
        keyword: 'industrial water treatment',
        volume: 2400,
        position: 4,
        previousPosition: 5,
        change: 1,
        difficulty: 68,
        traffic: 320
      },
      {
        keyword: 'cooling tower chemicals',
        volume: 880,
        position: 3,
        previousPosition: 3,
        change: 0,
        difficulty: 58,
        traffic: 210
      }
    ];
  }

  /**
   * Determine keyword intent based on the keyword text
   * @param keyword - The keyword to analyze
   * @returns The determined intent
   */
  private determineKeywordIntent(keyword: string): 'informational' | 'navigational' | 'transactional' | 'commercial' {
    const keyword_lower = keyword.toLowerCase();

    // Informational keywords
    if (keyword_lower.includes('how') ||
        keyword_lower.includes('what') ||
        keyword_lower.includes('why') ||
        keyword_lower.includes('guide') ||
        keyword_lower.includes('tutorial')) {
      return 'informational';
    }

    // Navigational keywords
    if (keyword_lower.includes('login') ||
        keyword_lower.includes('sign in') ||
        keyword_lower.includes('account') ||
        keyword_lower.includes('website')) {
      return 'navigational';
    }

    // Transactional keywords
    if (keyword_lower.includes('buy') ||
        keyword_lower.includes('purchase') ||
        keyword_lower.includes('order') ||
        keyword_lower.includes('cheap') ||
        keyword_lower.includes('price') ||
        keyword_lower.includes('discount')) {
      return 'transactional';
    }

    // Commercial keywords
    if (keyword_lower.includes('best') ||
        keyword_lower.includes('top') ||
        keyword_lower.includes('review') ||
        keyword_lower.includes('comparison') ||
        keyword_lower.includes('vs')) {
      return 'commercial';
    }

    // Default to informational
    return 'informational';
  }
}
