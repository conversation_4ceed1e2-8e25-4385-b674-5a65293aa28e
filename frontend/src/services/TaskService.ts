import React from 'react';
import { v4 as uuidv4 } from 'uuid';
import { 
  Task, 
  TaskStatus, 
  TaskCategory, 
  TaskPriority, 
  TaskComment, 
  TaskAttachment 
} from '../components/SEOInsightsDashboard/TaskManagement/types';
import { BaseApiService } from './api/BaseApiService';

/**
 * Task Service
 * Service for managing SEO tasks
 */
export class TaskService extends BaseApiService {
  private localStorageKey = 'seo_tasks';

  constructor(apiKey?: string) {
    super('/api/tasks', apiKey);
  }

  /**
   * Get all tasks
   * @returns Promise with all tasks
   */
  public async getTasks(): Promise<Task[]> {
    try {
      // Try to get tasks from API
      return this.get<Task[]>('/');
    } catch (error) {
      console.error('Error fetching tasks from API, falling back to local storage:', error);
      // Fall back to local storage
      return this.getTasksFromLocalStorage();
    }
  }

  /**
   * Get a task by ID
   * @param id - Task ID
   * @returns Promise with the task
   */
  public async getTask(id: string): Promise<Task> {
    try {
      // Try to get task from API
      return this.get<Task>(`/${id}`);
    } catch (error) {
      console.error(`Error fetching task ${id} from API, falling back to local storage:`, error);
      // Fall back to local storage
      const tasks = this.getTasksFromLocalStorage();
      const task = tasks.find(t => t.id === id);
      if (!task) {
        throw new Error(`Task with ID ${id} not found`);
      }
      return task;
    }
  }

  /**
   * Create a new task
   * @param task - Task data
   * @returns Promise with the created task
   */
  public async createTask(task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> {
    const now = new Date().toISOString();
    const newTask: Task = {
      id: uuidv4(),
      ...task,
      createdAt: now,
      updatedAt: now
    };

    try {
      // Try to create task via API
      return this.post<Task>('/', newTask);
    } catch (error) {
      console.error('Error creating task via API, falling back to local storage:', error);
      // Fall back to local storage
      const tasks = this.getTasksFromLocalStorage();
      tasks.push(newTask);
      this.saveTasksToLocalStorage(tasks);
      return newTask;
    }
  }

  /**
   * Update a task
   * @param id - Task ID
   * @param updates - Task updates
   * @returns Promise with the updated task
   */
  public async updateTask(id: string, updates: Partial<Task>): Promise<Task> {
    try {
      // Try to update task via API
      return this.put<Task>(`/${id}`, updates);
    } catch (error) {
      console.error(`Error updating task ${id} via API, falling back to local storage:`, error);
      // Fall back to local storage
      const tasks = this.getTasksFromLocalStorage();
      const taskIndex = tasks.findIndex(t => t.id === id);
      
      if (taskIndex === -1) {
        throw new Error(`Task with ID ${id} not found`);
      }
      
      const updatedTask: Task = {
        ...tasks[taskIndex],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      tasks[taskIndex] = updatedTask;
      this.saveTasksToLocalStorage(tasks);
      
      return updatedTask;
    }
  }

  /**
   * Delete a task
   * @param id - Task ID
   * @returns Promise with success status
   */
  public async deleteTask(id: string): Promise<boolean> {
    try {
      // Try to delete task via API
      await this.delete<void>(`/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting task ${id} via API, falling back to local storage:`, error);
      // Fall back to local storage
      const tasks = this.getTasksFromLocalStorage();
      const filteredTasks = tasks.filter(t => t.id !== id);
      
      if (filteredTasks.length === tasks.length) {
        throw new Error(`Task with ID ${id} not found`);
      }
      
      this.saveTasksToLocalStorage(filteredTasks);
      return true;
    }
  }

  /**
   * Change task status
   * @param id - Task ID
   * @param status - New status
   * @returns Promise with the updated task
   */
  public async changeTaskStatus(id: string, status: TaskStatus): Promise<Task> {
    const updates: Partial<Task> = { 
      status, 
      updatedAt: new Date().toISOString() 
    };
    
    // If status is completed, add completedAt timestamp
    if (status === TaskStatus.COMPLETED) {
      updates.completedAt = new Date().toISOString();
    } else {
      // If status is not completed, remove completedAt timestamp
      updates.completedAt = undefined;
    }
    
    return this.updateTask(id, updates);
  }

  /**
   * Assign task to user
   * @param id - Task ID
   * @param assignee - Assignee name or ID
   * @returns Promise with the updated task
   */
  public async assignTask(id: string, assignee: string): Promise<Task> {
    return this.updateTask(id, { 
      assignee, 
      updatedAt: new Date().toISOString() 
    });
  }

  /**
   * Add comment to task
   * @param taskId - Task ID
   * @param comment - Comment data
   * @returns Promise with the updated task
   */
  public async addComment(taskId: string, comment: Omit<TaskComment, 'id' | 'createdAt'>): Promise<Task> {
    const task = await this.getTask(taskId);
    
    const newComment: TaskComment = {
      id: uuidv4(),
      ...comment,
      createdAt: new Date().toISOString()
    };
    
    const comments = task.comments || [];
    
    return this.updateTask(taskId, {
      comments: [...comments, newComment],
      updatedAt: new Date().toISOString()
    });
  }

  /**
   * Add attachment to task
   * @param taskId - Task ID
   * @param attachment - Attachment data
   * @returns Promise with the updated task
   */
  public async addAttachment(taskId: string, attachment: Omit<TaskAttachment, 'id' | 'uploadedAt'>): Promise<Task> {
    const task = await this.getTask(taskId);
    
    const newAttachment: TaskAttachment = {
      id: uuidv4(),
      ...attachment,
      uploadedAt: new Date().toISOString()
    };
    
    const attachments = task.attachments || [];
    
    return this.updateTask(taskId, {
      attachments: [...attachments, newAttachment],
      updatedAt: new Date().toISOString()
    });
  }

  /**
   * Delete attachment from task
   * @param taskId - Task ID
   * @param attachmentId - Attachment ID
   * @returns Promise with the updated task
   */
  public async deleteAttachment(taskId: string, attachmentId: string): Promise<Task> {
    const task = await this.getTask(taskId);
    
    if (!task.attachments) {
      throw new Error(`Task ${taskId} has no attachments`);
    }
    
    const filteredAttachments = task.attachments.filter(a => a.id !== attachmentId);
    
    if (filteredAttachments.length === task.attachments.length) {
      throw new Error(`Attachment ${attachmentId} not found in task ${taskId}`);
    }
    
    return this.updateTask(taskId, {
      attachments: filteredAttachments,
      updatedAt: new Date().toISOString()
    });
  }

  /**
   * Get tasks from local storage
   * @returns Array of tasks
   */
  private getTasksFromLocalStorage(): Task[] {
    const tasksJson = localStorage.getItem(this.localStorageKey);
    return tasksJson ? JSON.parse(tasksJson) : [];
  }

  /**
   * Save tasks to local storage
   * @param tasks - Tasks to save
   */
  private saveTasksToLocalStorage(tasks: Task[]): void {
    localStorage.setItem(this.localStorageKey, JSON.stringify(tasks));
  }

  /**
   * Generate mock tasks for testing
   * @param count - Number of tasks to generate
   * @returns Array of mock tasks
   */
  public generateMockTasks(count: number = 10): Task[] {
    const tasks: Task[] = [];
    const now = new Date();
    const statuses = Object.values(TaskStatus);
    const categories = Object.values(TaskCategory);
    const priorities = Object.values(TaskPriority);
    const assignees = ['John Doe', 'Jane Smith', 'Bob Johnson', 'Alice Williams'];
    
    for (let i = 0; i < count; i++) {
      const createdAt = new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString();
      const updatedAt = new Date(new Date(createdAt).getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString();
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      
      const task: Task = {
        id: uuidv4(),
        title: `SEO Task ${i + 1}`,
        description: `This is a mock SEO task for testing purposes. Task number ${i + 1}.`,
        category: categories[Math.floor(Math.random() * categories.length)],
        priority: priorities[Math.floor(Math.random() * priorities.length)],
        status,
        dueDate: new Date(now.getTime() + (Math.random() * 14 - 7) * 24 * 60 * 60 * 1000).toISOString(),
        assignee: Math.random() > 0.3 ? assignees[Math.floor(Math.random() * assignees.length)] : undefined,
        tags: ['SEO', 'Task', `Tag ${i % 5}`],
        createdAt,
        updatedAt,
        completedAt: status === TaskStatus.COMPLETED ? updatedAt : undefined,
        estimatedHours: Math.floor(Math.random() * 8) + 1,
        actualHours: status === TaskStatus.COMPLETED ? Math.floor(Math.random() * 10) + 1 : undefined,
        relatedUrls: Math.random() > 0.5 ? [`https://www.mexelenergysustain.com/page-${i + 1}`] : undefined
      };
      
      tasks.push(task);
    }
    
    return tasks;
  }
}
