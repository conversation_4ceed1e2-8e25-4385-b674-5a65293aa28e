import React from 'react';
// Mock data service for development and testing
export class MockDataService {
  // Mock LinkedIn data
  static getLinkedInTemplates() {
    return [
      {
        id: 1,
        title: "Connection Request",
        content:
          "Hi {{name}}, I noticed your work in {{industry}} and would love to connect. I'm working on solutions for {{problem}} and thought we might have some interesting discussions.",
        tags: ["connection", "networking", "introduction"],
      },
      {
        id: 2,
        title: "Follow-up After Meeting",
        content:
          "Hi {{name}}, it was great meeting you at {{event}}. I'd love to continue our conversation about {{topic}}. Let me know if you'd like to schedule a call next week.",
        tags: ["follow-up", "meeting", "networking"],
      },
      {
        id: 3,
        title: "Industry Insight Share",
        content:
          "Hi {{name}}, I just read an interesting article about {{topic}} in the {{industry}} sector and thought you might find it valuable. Here's the link: {{link}}. Would love to hear your thoughts!",
        tags: ["insight", "sharing", "industry"],
      },
      {
        id: 4,
        title: "Product Introduction",
        content:
          "Hi {{name}}, I noticed you're working on {{problem}} at {{company}}. We've developed a solution that helps companies like yours improve {{metric}} by {{percentage}}. Would you be interested in a quick demo?",
        tags: ["product", "sales", "introduction"],
      },
      {
        id: 5,
        title: "Event Invitation",
        content:
          "Hi {{name}}, I wanted to personally invite you to our upcoming webinar on {{topic}}. Given your experience in {{industry}}, I think you'd find it valuable. It's on {{date}} at {{time}}. Let me know if you can make it!",
        tags: ["event", "invitation", "webinar"],
      },
    ];
  }

  // Mock LinkedIn keywords
  static getLinkedInKeywords() {
    return [
      "water treatment",
      "cooling towers",
      "industrial chemicals",
      "water management",
      "energy efficiency",
      "sustainability",
      "plant operations",
      "facility management",
      "maintenance",
      "engineering",
      "industrial solutions",
      "chemical engineering",
      "water quality",
      "corrosion prevention",
      "scale inhibition",
      "biocide treatment",
      "water conservation",
      "environmental compliance",
      "cost reduction",
      "operational efficiency",
    ];
  }

  // Mock LinkedIn message types
  static getLinkedInMessageTypes() {
    return [
      { value: "connection", label: "Connection Request" },
      { value: "follow-up", label: "Follow-up Message" },
      { value: "introduction", label: "Product Introduction" },
      { value: "insight", label: "Industry Insight" },
      { value: "event", label: "Event Invitation" },
      { value: "content", label: "Content Share" },
      { value: "testimonial", label: "Testimonial Request" },
      { value: "partnership", label: "Partnership Proposal" },
    ];
  }

  // Mock industries
  static getIndustries() {
    return [
      { value: "manufacturing", label: "Manufacturing" },
      { value: "energy", label: "Energy & Utilities" },
      { value: "chemical", label: "Chemical Processing" },
      { value: "food", label: "Food & Beverage" },
      { value: "pharmaceutical", label: "Pharmaceutical" },
      { value: "hospitality", label: "Hospitality" },
      { value: "healthcare", label: "Healthcare" },
      { value: "commercial", label: "Commercial Buildings" },
      { value: "mining", label: "Mining" },
      { value: "oil-gas", label: "Oil & Gas" },
    ];
  }

  // Mock job titles
  static getJobTitles() {
    return [
      { value: "plant-manager", label: "Plant Manager" },
      { value: "facility-manager", label: "Facility Manager" },
      { value: "operations-director", label: "Operations Director" },
      { value: "maintenance-manager", label: "Maintenance Manager" },
      { value: "chief-engineer", label: "Chief Engineer" },
      { value: "sustainability-director", label: "Sustainability Director" },
      { value: "ehs-manager", label: "EHS Manager" },
      { value: "technical-director", label: "Technical Director" },
      { value: "procurement-manager", label: "Procurement Manager" },
      { value: "cto", label: "CTO" },
    ];
  }

  // Mock analytics data
  static getAnalyticsData() {
    return {
      leadsGenerated: Math.floor(Math.random() * 500) + 50,
      websiteTraffic: Math.floor(Math.random() * 5000) + 500,
      socialEngagement: Math.floor(Math.random() * 1000) + 100,
      tenderOpportunities: Math.floor(Math.random() * 100) + 10,
      conversionRate: Math.random() * 0.1 + 0.01,
      averageTenderScore: Math.random() * 50 + 50,
      topKeywords: [
        "water treatment",
        "industrial chemicals",
        "cooling towers",
        "sustainability",
        "energy efficiency",
      ],
      lastUpdated: new Date().toISOString(),
    };
  }

  // Mock updated analytics data
  static getUpdatedAnalyticsData() {
    // Simulate data update
    return Promise.resolve(this.getAnalyticsData());
  }
}
