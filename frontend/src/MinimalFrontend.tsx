import React, { useState } from "react";
import {
  Box,
  Typography,
  Container,
  Paper,
  Divider,
  Tabs,
  Tab,
} from "@mui/material";
import ApiTester from "./ApiTester";
import WebSocketTester from "./WebSocketTester";

interface TabPanelProps {
  children?: any;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

/**
 * A minimal test component to verify the frontend is working
 */
const MinimalFrontend = () => {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: any, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Mexel Frontend
        </Typography>
        <Typography variant="subtitle1" gutterBottom>
          The frontend is now working!
        </Typography>
        <Box mt={3}>
          <Typography>
            This is a minimal test component to verify that the React
            application is running correctly.
          </Typography>
          <Typography mt={2}>
            Once this is confirmed working, we can gradually add back the more
            complex components.
          </Typography>
        </Box>

        <Box
          mt={3}
          p={2}
          sx={{ bgcolor: "info.main", color: "white", borderRadius: 1 }}
        >
          <Typography variant="h6">Backend Server Instructions</Typography>
          <Typography>
            To test API connectivity, make sure the backend server is running:
          </Typography>
          <Typography
            component="div"
            sx={{
              mt: 1,
              fontFamily: "monospace",
              bgcolor: "rgba(0,0,0,0.2)",
              p: 2,
              borderRadius: 1,
            }}
          >
            cd /Users/<USER>/Desktop/Mexel/backend-python
            <br />
            python3 run.py
          </Typography>
          <Typography mt={1}>
            The backend server should run on port 3001. Once it's running, you
            can test the API connections below.
          </Typography>
        </Box>

        <Divider sx={{ my: 4 }} />

        {/* Testing Tabs */}
        <Box sx={{ width: "100%" }}>
          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label="connectivity testing tabs"
            >
              <Tab label="REST API Testing" />
              <Tab label="WebSocket Testing" />
            </Tabs>
          </Box>
          <TabPanel value={tabValue} index={0}>
            <ApiTester />
          </TabPanel>
          <TabPanel value={tabValue} index={1}>
            <WebSocketTester />
          </TabPanel>
        </Box>
      </Paper>
    </Container>
  );
};

export default MinimalFrontend;
