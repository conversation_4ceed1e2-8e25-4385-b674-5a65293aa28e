import {
    CircularProgress,
    createTheme,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Styled<PERSON><PERSON><PERSON><PERSON>rovider,
    ThemeProvider,
} from "@mui/material";
import React, { Suspense } from "react";
import {
    Navigate,
    Route,
    BrowserRouter as Router,
    Routes,
} from "react-router-dom";
import MinimalFrontend from "./MinimalFrontend";
import DashboardLayout from "./components/DashboardLayout";
import ErrorBoundary from "./components/ErrorBoundary";
import ProtectedRoute from "./components/ProtectedRoute";
import { MainLayout } from "./components/layout/MainLayout"; // Added import

// Define route components with proper lazy loading
const MinimalDashboard = React.lazy(
  () => import("./components/MinimalDashboard")
);
const EmailDashboard = React.lazy(() => import("./components/EmailDashboard"));
const SEODashboard = React.lazy(() => import("./components/SEODashboard"));
const LinkedInDashboard = React.lazy(
  () => import("./components/LinkedInDashboard")
);
const AIRecommendations = React.lazy(
  () => import("./components/ai/AIRecommendations")
);
const TaskManagement = React.lazy(
  () => import("./components/tasks/TaskManagement")
);
const EmailReports = React.lazy(
  () => import("./components/reports/EmailReports")
);
const AgentDashboard = React.lazy(
  () => import("./components/agent/AgentDashboard")
);
const TestAgentDashboard = React.lazy(() => import("./TestAgentDashboard"));
const AgentMonitorExample = React.lazy(
  () => import("./components/agent/AgentMonitorExample")
);
const AgentMonitorDebug = React.lazy(
  () => import("./components/agent/AgentMonitorDebug")
);
const AgentMonitorFixed = React.lazy(
  () => import("./components/agent/AgentMonitorFixed")
);
const LeadDashboard = React.lazy(
  () => import("./components/lead/LeadDashboard")
);
const MinimalTest = React.lazy(() => import("./MinimalTest"));

// Create theme instance
export const theme = createTheme({
  palette: {
    mode: "light",
    primary: {
      main: "#1976d2",
    },
    secondary: {
      main: "#f50057",
    },
    background: {
      default: "#f5f5f5",
      paper: "#ffffff",
    },
    error: {
      main: "#f44336",
    },
    warning: {
      main: "#ff9800",
    },
    info: {
      main: "#2196f3",
    },
    success: {
      main: "#4caf50",
    },
    text: {
      primary: "rgba(0, 0, 0, 0.87)",
      secondary: "rgba(0, 0, 0, 0.6)",
    },
  },
  typography: {
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',
    h1: {
      fontSize: "2.5rem",
      fontWeight: 500,
    },
    h2: {
      fontSize: "2rem",
      fontWeight: 500,
    },
    h3: {
      fontSize: "1.75rem",
      fontWeight: 500,
    },
    h4: {
      fontSize: "1.5rem",
      fontWeight: 500,
    },
    h5: {
      fontSize: "1.25rem",
      fontWeight: 500,
    },
    h6: {
      fontSize: "1rem",
      fontWeight: 500,
    },
    body1: {
      fontSize: "1rem",
    },
    body2: {
      fontSize: "0.875rem",
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          scrollbarWidth: "thin",
          "&::-webkit-scrollbar": {
            width: "8px",
            height: "8px",
          },
          "&::-webkit-scrollbar-track": {
            background: "#f1f1f1",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#888",
            borderRadius: "4px",
          },
          "&::-webkit-scrollbar-thumb:hover": {
            background: "#555",
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: "none",
          borderRadius: "8px",
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          padding: "12px 16px",
        },
        head: {
          fontWeight: 600,
          backgroundColor: "rgba(0, 0, 0, 0.04)",
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: "16px",
        },
      },
    },
  },
});

// Layout wrapper components for routes

// Wrapper component for dashboard layouts
const DashboardWrapper = ({
  title,
  children,
}: {
  title: string;
  children: any;
}) => <DashboardLayout title={title}>{children}</DashboardLayout>;

const App = () => {
  return (
    <StyledEngineProvider injectFirst>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <ErrorBoundary>
          <Router>
            <MainLayout>
              <Suspense
                fallback={
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      height: "100vh",
                    }}
                  >
                    <CircularProgress />
                  </div>
                }
              >
                <Routes>
                  <Route path="/minimal" element={<MinimalFrontend />} />
                  <Route
                    path="/dashboard/minimal"
                    element={
                      <ProtectedRoute>
                        <MinimalDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/email"
                    element={
                      <ProtectedRoute>
                        <EmailDashboard title="Email Campaign Dashboard" />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/seo"
                    element={
                      <ProtectedRoute>
                        <SEODashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/linkedin"
                    element={
                      <ProtectedRoute>
                        <LinkedInDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/ai/recommendations"
                    element={
                      <ProtectedRoute>
                        <AIRecommendations />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/tasks"
                    element={
                      <ProtectedRoute>
                        <TaskManagement />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/reports/email"
                    element={
                      <ProtectedRoute>
                        <EmailReports />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/agent-dashboard"
                    element={
                      <ProtectedRoute>
                        <AgentDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/test-agent-dashboard"
                    element={
                      <ProtectedRoute>
                        <TestAgentDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/agent-monitor-example"
                    element={
                      <ProtectedRoute>
                        <AgentMonitorExample />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/agent-monitor-debug"
                    element={
                      <ProtectedRoute>
                        <AgentMonitorDebug />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/agent-monitor-fixed"
                    element={
                      <ProtectedRoute>
                        <AgentMonitorFixed />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/lead-dashboard"
                    element={
                      <ProtectedRoute>
                        <LeadDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route path="/minimal-test" element={<MinimalTest />} />
                  <Route
                    path="/"
                    element={<Navigate to="/dashboard/minimal" />}
                  />
                </Routes>
              </Suspense>
            </MainLayout>
          </Router>
        </ErrorBoundary>
      </ThemeProvider>
    </StyledEngineProvider>
  );
};

export default App;
