import React from 'react';

type Props = {
  children: React.ReactNode;
  fallbackRender?: (error: Error, errorInfo: React.ErrorInfo) => React.ReactNode;
};

type State = {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
};

export class ErrorBoundary extends React.Component<Props, State> {
  readonly state: State;
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  public static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  public componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
    // Log error to error reporting service
    console.error('Uncaught error:', error, errorInfo);
    this.setState({
      errorInfo,
    });
  }

  public render(): React.ReactNode {
    const { hasError, error, errorInfo } = this.state;
    const fallbackRender = this.props.fallbackRender;
    const children = this.props.children;

    if (hasError) {
      if (fallbackRender && error && errorInfo) {
        return fallbackRender(error, errorInfo);
      }

      // Default fallback UI
      return (
        <div role="alert" className="error-boundary">
          <h2>Something went wrong</h2>
          {error && (
            <details style={{ whiteSpace: 'pre-wrap' }}>
              <summary>{error.toString()}</summary>
              <br />
              {errorInfo?.componentStack}
            </details>
          )}
        </div>
      );
    }

    return children;
  }
}
