import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>ton,
    Chip,
    CircularProgress,
    Divider,
    List,
    ListItem,
    ListItemText,
    Paper,
    TextField,
    Typography,
} from "@mui/material";
import React, { useEffect } from "react";
import webSocketService from "./api/webSocketService";

/**
 * WebSocketTester - A component to test Socket.IO connectivity with the backend
 */
const WebSocketTester = () => {
  const [connected, setConnected] = React.useState(false);
  const [connecting, setConnecting] = React.useState(false);
  const [connectionError, setConnectionError] = React.useState("");
  const [message, setMessage] = React.useState("");
  const [messages, setMessages] = React.useState<string[]>([]);
  const [serverStatus, setServerStatus] = React.useState(null);
  const [lastActivity, setLastActivity] = React.useState(null);
  const [serverUrl, setServerUrl] = React.useState("http://localhost:3001");
  const [advanced, setAdvanced] = React.useState(false);

  useEffect(() => {
    // Set up WebSocket event listeners
    const welcomeHandler = (data: any) => {
      addMessage("Server", `Welcome: ${data.message}`);
    };

    const responseHandler = (data: any) => {
      addMessage("Server", data.data || JSON.stringify(data));
    };

    const connectionHandler = (status: string) => {
      setConnected(status === "connected");
      setConnecting(status === "connecting");
      if (status === "connected") {
        addMessage("System", "Connected to WebSocket server");
      } else if (status === "disconnected") {
        addMessage("System", "Disconnected from WebSocket server");
        setConnectionError("");
      }
    };

    // Register handlers
    const unsubWelcome = webSocketService.onMessage("welcome", welcomeHandler);
    const unsubResponse = webSocketService.onMessage(
      "response",
      responseHandler
    );
    const unsubConnection =
      webSocketService.onConnectionChange(connectionHandler);

    // Clean up when component unmounts
    return () => {
      unsubWelcome();
      unsubResponse();
      unsubConnection();
      webSocketService.disconnect();
    };
  }, []);

  // Fetch server status
  const fetchServerStatus = async () => {
    try {
      const status = await webSocketService.getServerStatus();
      setServerStatus(status);
    } catch (error: any) {
      console.error("Failed to fetch server status:", error);
      setConnectionError(`Failed to fetch server status: ${error.message}`);
    }
  };

  // Connect to the Socket.IO server
  const connectSocket = () => {
    setConnecting(true);
    setConnectionError("");

    webSocketService
      .connect(serverUrl)
      .then(() => {
        // Connection successful
        fetchServerStatus();
      })
      .catch((error: any) => {
        setConnectionError(
          `Connection error: ${
            error.message || "Failed to connect to socket server"
          }`
        );
        setConnecting(false);
      });
  };

  // Attempt to reconnect with retry logic
  const reconnectSocket = () => {
    setConnecting(true);
    setConnectionError("Attempting to reconnect...");

    let attempts = 0;
    const maxAttempts = 3;
    const attemptReconnect = () => {
      webSocketService
        .connect(serverUrl)
        .then(() => {
          // Connection successful
          fetchServerStatus();
          addMessage("System", "Successfully reconnected");
        })
        .catch((error: any) => {
          attempts++;
          if (attempts < maxAttempts) {
            const delay = attempts * 2000; // Exponential backoff
            addMessage(
              "System",
              `Reconnection attempt ${attempts} failed. Retrying in ${
                delay / 1000
              }s...`
            );
            setTimeout(attemptReconnect, delay);
          } else {
            setConnectionError(
              `Failed to reconnect after ${maxAttempts} attempts. Please try again manually.`
            );
            setConnecting(false);
          }
        });
    };

    attemptReconnect();
  };

  // Disconnect from the Socket.IO server
  const disconnectSocket = () => {
    webSocketService.disconnect();
  };

  // Send a message to the server
  const sendMessage = () => {
    if (!connected || !message.trim()) {
      return;
    }

    webSocketService.sendMessage(message);
    addMessage("You", message);
    setMessage("");
  };

  // Add a message to the messages list
  const addMessage = (from: string, text: string) => {
    const now = new Date();
    setLastActivity(now);
    setMessages((prevMessages) => [
      ...prevMessages,
      { from, text, timestamp: now },
    ]);
  };

  // Send an echo request
  const sendEcho = () => {
    if (!connected) return;

    const echoData = {
      timestamp: new Date().toISOString(),
      text: message || "Echo test",
    };

    webSocketService.sendEcho(echoData);
    addMessage("You", `Echo request: ${JSON.stringify(echoData)}`);

    // Set up a one-time listener for the response
    const unsubscribe = webSocketService.onMessage(
      "echo_response",
      (data: any) => {
        addMessage("Server", `Echo response: ${JSON.stringify(data)}`);
        unsubscribe(); // Remove listener after first response
      }
    );
  };

  // Send a ping request
  const sendPing = () => {
    if (!connected) return;

    webSocketService.sendPing();
    addMessage("You", "Ping");

    // Set up a one-time listener for the response
    const unsubscribe = webSocketService.onMessage("pong", (data: any) => {
      addMessage("Server", `Pong: ${JSON.stringify(data)}`);
      unsubscribe(); // Remove listener after first response
    });
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h5" gutterBottom>
        WebSocket Connection Test
      </Typography>
      <Alert severity="info" sx={{ mb: 2 }}>
        This component tests WebSocket connectivity with the backend using
        Socket.IO. The backend server should be running on port 3001 with
        Socket.IO support.
      </Alert>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography variant="h6">Connection Status</Typography>
          <Button size="small" onClick={() => setAdvanced((prev) => !prev)}>
            {advanced ? "Hide Advanced" : "Advanced Settings"}
          </Button>
        </Box>

        {advanced && (
          <Box
            sx={{ mb: 3, p: 2, bgcolor: "rgba(0,0,0,0.03)", borderRadius: 1 }}
          >
            <Typography variant="subtitle2" gutterBottom>
              Server URL
            </Typography>
            <TextField
              fullWidth
              size="small"
              value={serverUrl}
              onChange={(e) => setServerUrl(e.target.value)}
              placeholder="http://localhost:3001"
              disabled={connected}
              margin="dense"
              helperText="Enter the WebSocket server URL"
            />
            <Box sx={{ mt: 1, display: "flex", justifyContent: "flex-end" }}>
              <Button
                size="small"
                onClick={() => {
                  webSocketService.setDebugMode(
                    !webSocketService.getDebugMode()
                  );
                  addMessage(
                    "System",
                    `Debug mode ${
                      webSocketService.getDebugMode() ? "enabled" : "disabled"
                    }`
                  );
                }}
              >
                Toggle Debug Mode
              </Button>
            </Box>
          </Box>
        )}

        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <Chip
            label={connected ? "Connected" : "Disconnected"}
            color={connected ? "success" : "error"}
            sx={{ mr: 2 }}
          />
          {connectionError && (
            <Typography color="error" variant="body2">
              {connectionError}
            </Typography>
          )}
        </Box>

        <Box sx={{ display: "flex", gap: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={connectSocket}
            disabled={connected || connecting}
          >
            Connect
          </Button>
          <Button
            variant="outlined"
            color="secondary"
            onClick={disconnectSocket}
            disabled={!connected || connecting}
          >
            Disconnect
          </Button>
          <Button
            variant="contained"
            color="warning"
            onClick={reconnectSocket}
            disabled={connected || connecting}
          >
            Reconnect
          </Button>
          <Button
            variant="outlined"
            onClick={fetchServerStatus}
            disabled={!connected}
            sx={{ ml: 2 }}
          >
            Check Server Status
          </Button>
          {connecting && <CircularProgress size={24} sx={{ ml: 2 }} />}
        </Box>

        {serverStatus && (
          <Box
            sx={{
              mt: 2,
              p: 2,
              bgcolor: "rgba(0, 200, 0, 0.1)",
              borderRadius: 1,
            }}
          >
            <Typography variant="subtitle2" gutterBottom>
              Server Status:
            </Typography>
            <Typography
              variant="body2"
              component="pre"
              sx={{ whiteSpace: "pre-wrap" }}
            >
              {JSON.stringify(serverStatus, null, 2)}
            </Typography>
          </Box>
        )}
      </Paper>

      <Paper
        sx={{
          p: 3,
          mb: 3,
          opacity: connected ? 1 : 0.6,
          pointerEvents: connected ? "auto" : "none",
        }}
      >
        <Typography variant="h6" gutterBottom>
          WebSocket Actions
        </Typography>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          <Box sx={{ display: "flex", gap: 2 }}>
            <TextField
              fullWidth
              label="Message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === "Enter") sendMessage();
              }}
              disabled={!connected}
              placeholder="Type a message to send"
            />
          </Box>

          <Box sx={{ display: "flex", gap: 2 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={sendMessage}
              disabled={!connected || !message.trim()}
            >
              Send Message
            </Button>

            <Button
              variant="contained"
              color="secondary"
              onClick={() => sendEcho()}
              disabled={!connected}
            >
              Send Echo
            </Button>

            <Button
              variant="outlined"
              onClick={() => sendPing()}
              disabled={!connected}
            >
              Send Ping
            </Button>
          </Box>
        </Box>
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="h6" gutterBottom>
            Message History
          </Typography>
          {lastActivity && (
            <Typography variant="body2" color="text.secondary">
              Last activity: {lastActivity.toLocaleTimeString()}
            </Typography>
          )}
        </Box>
        <Divider />
        <List
          sx={{
            height: "300px",
            overflowY: "auto",
            bgcolor: "background.paper",
            borderRadius: 1,
          }}
        >
          {messages.length === 0 ? (
            <ListItem>
              <ListItemText
                primary="No messages yet"
                secondary="Connect to the WebSocket server and send a message"
              />
            </ListItem>
          ) : (
            messages.map((msg, index) => (
              <ListItem key={index} divider={index < messages.length - 1}>
                <ListItemText
                  primary={
                    <Box
                      sx={{ display: "flex", justifyContent: "space-between" }}
                    >
                      <Typography
                        component="span"
                        variant="body1"
                        sx={{
                          fontWeight: "bold",
                          color:
                            msg.from === "You"
                              ? "primary.main"
                              : msg.from === "Server"
                              ? "success.main"
                              : "text.primary",
                        }}
                      >
                        {msg.from}
                      </Typography>
                      <Typography
                        component="span"
                        variant="body2"
                        color="text.secondary"
                      >
                        {msg.timestamp.toLocaleTimeString()}
                      </Typography>
                    </Box>
                  }
                  secondary={msg.text}
                />
              </ListItem>
            ))
          )}
        </List>
      </Paper>
    </Box>
  );
};

export default WebSocketTester;
