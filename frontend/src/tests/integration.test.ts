import { expect, test, type Page } from './test-utils';

test.describe('Mexel Frontend Integration Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3000');
  });

  test('basic navigation and layout', async ({ page }) => {
    // Check main components
    await expect(page.locator('nav')).toBeVisible();
    await expect(page.locator('main')).toBeVisible();

    // Verify navigation links exist
    const navLinks = await page.locator('nav a').all();
    expect(navLinks.length).toBeGreaterThan(0);
  });

  test('connection status indicator', async ({ page }) => {
    const statusIndicator = page.locator('[data-testid="connection-status"]');
    await expect(statusIndicator).toBeVisible();
  });

  test('AI chat interface', async ({ page }: { page: Page }) => {
    // Navigate to chat interface
    await page.click('text=Chat');

    // Check chat components
    await expect(page.locator('[data-testid="chat-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="send-button"]')).toBeVisible();

    // Test message sending
    await page.fill('[data-testid="chat-input"]', 'Test message about water treatment');
    await page.click('[data-testid="send-button"]');

    // Should see message in chat
    await expect(page.locator('.message-list')).toContainText('Test message about water treatment');
  });

  test('data persistence', async ({ page }: { page: Page }) => {
    // Navigate to form
    await page.click('text=New Entry');

    // Fill out form
    await page.fill('[data-testid="title-input"]', 'Test Entry');
    await page.fill('[data-testid="description-input"]', 'Test Description');
    await page.click('[data-testid="submit-button"]');

    // Should show success message
    await expect(page.locator('.success-message')).toBeVisible();

    // Navigate to list view
    await page.click('text=Entries');

    // Should see new entry in list
    await expect(page.locator('.entry-list')).toContainText('Test Entry');
  });

  test('LinkedIn post generation', async ({ page }: { page: Page }) => {
    // Navigate to LinkedIn post generator
    await page.click('text=LinkedIn');

    // Fill post details
    await page.fill('[data-testid="topic-input"]', 'Water treatment efficiency');
    await page.selectOption('[data-testid="audience-select"]', 'Industry professionals');
    await page.click('[data-testid="generate-button"]');

    // Should show generated post
    await expect(page.locator('[data-testid="generated-post"]')).toBeVisible();
    const count = await page.locator('[data-testid="generated-post"]').count();
    expect(count).toBeGreaterThan(0);
  });

  test('data visualization', async ({ page }: { page: Page }) => {
    // Navigate to dashboard
    await page.click('text=Dashboard');

    // Check for charts
    await expect(page.locator('.chart-container')).toBeVisible();
    const charts = await page.locator('.recharts-wrapper').all();
    expect(charts.length).toBeGreaterThan(0);
  });
});
