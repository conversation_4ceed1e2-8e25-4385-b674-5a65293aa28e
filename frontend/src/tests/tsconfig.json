{"extends": "../../../tsconfig.json", "compilerOptions": {"typeRoots": ["../../../node_modules/@types", "../../node_modules/@types", "../../../types", "../types"], "types": ["node"], "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["../../../frontend/src/*"], "@shared/*": ["../../../shared/src/*"]}}, "include": ["./**/*", "../../../types/jest.d.ts", "../../../types/playwright.d.ts"], "exclude": []}