import { test, expect } from '@playwright/test';

test.describe('Basic functionality', () => {
  test('should load the homepage', async ({ page }) => {
    await page.goto('http://localhost:3000');

    // Check page title
    await expect(page).toHaveTitle(/Mexel/);

    // Check main content exists
    await expect(page.locator('main')).toBeVisible();

    // Check navigation exists
    await expect(page.locator('nav')).toBeVisible();
  });

  test('should have proper meta tags', async ({ page }) => {
    await page.goto('http://localhost:3000');

    // Check viewport meta
    const viewport = await page.locator('meta[name="viewport"]');
    await expect(viewport).toHaveAttribute('content', 'width=device-width, initial-scale=1');

    // Check description meta
    const description = await page.locator('meta[name="description"]');
    await expect(description).toBeTruthy();
  });
});
