import React from 'react';
import { test, expect } from "@playwright/test";

test.describe("Navigation Tests", () => {
  test("homepage loads and shows the title", async ({ page }) => {
    await page.goto("/");

    // Wait for the document to be loaded
    await page.waitForLoadState("domcontentloaded");

    // Check if the page has loaded with the correct title
    const pageTitle = await page.title();
    expect(pageTitle).toBeTruthy();
  });

  test("homepage contains key elements", async ({ page }) => {
    await page.goto("/");

    // Wait for the main content to be loaded
    await page.waitForLoadState("domcontentloaded");

    // These are example assertions - adjust based on your actual UI elements
    // Example: Header
    const header = page.getByRole("banner");
    await expect(header).toBeVisible();
  });
});
