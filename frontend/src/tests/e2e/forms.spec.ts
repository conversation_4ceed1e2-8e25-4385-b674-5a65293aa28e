import React from 'react';
import { test, expect } from "@playwright/test";

test.describe("Form Validation Tests", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("http://localhost:3000");
  });

  test("tender submission form validation", async ({ page }) => {
    // Navigate to tender form
    await page.click("text=New Tender");

    // Try to submit empty form
    await page.click('[data-testid="submit-tender"]');

    // Should show validation errors
    await expect(page.locator('[data-testid="title-error"]')).toBeVisible();
    await expect(
      page.locator('[data-testid="description-error"]')
    ).toBeVisible();

    // Fill form with valid data
    await page.fill('[data-testid="title-input"]', "Test Tender");
    await page.fill('[data-testid="description-input"]', "Test Description");
    await page.fill('[data-testid="budget-input"]', "100000");
    await page.fill('[data-testid="deadline-input"]', "2025-12-31");

    // Submit form
    await page.click('[data-testid="submit-tender"]');

    // Should show success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  });

  test("customer data form validation", async ({ page }) => {
    // Navigate to customer form
    await page.click("text=Add Customer");

    // Try to submit with invalid email
    await page.fill('[data-testid="name-input"]', "Test Customer");
    await page.fill('[data-testid="email-input"]', "invalid-email");
    await page.click('[data-testid="submit-customer"]');

    // Should show email validation error
    await expect(page.locator('[data-testid="email-error"]')).toBeVisible();

    // Fix email and submit
    await page.fill('[data-testid="email-input"]', "<EMAIL>");
    await page.click('[data-testid="submit-customer"]');

    // Should show success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  });
});
