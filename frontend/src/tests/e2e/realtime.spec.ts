import React from 'react';
import { test, expect } from "@playwright/test";

test.describe("Real-time Updates Tests", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("http://localhost:3000");
  });

  test("updates tender status in real-time", async ({ page }) => {
    // Navigate to tender monitoring
    await page.click("text=Monitor");

    // Wait for WebSocket connection
    await expect(page.locator('[data-testid="connection-status"]')).toHaveText(
      /connected/i
    );

    // Find a tender to watch
    await expect(page.locator(".tender-item")).toBeVisible();

    // Simulate status update via WebSocket
    await page.evaluate(() => {
      // This simulates receiving a WebSocket message
      window.postMessage(
        {
          type: "TENDER_UPDATE",
          data: {
            id: 1,
            status: "IN_PROGRESS",
          },
        },
        "*"
      );
    });

    // Should show updated status
    await expect(page.locator('[data-testid="tender-1-status"]')).toHaveText(
      "IN_PROGRESS"
    );
  });

  test("shows real-time notifications", async ({ page }) => {
    // Navigate to dashboard
    await page.click("text=Dashboard");

    // Wait for notification system
    await expect(
      page.locator('[data-testid="notification-center"]')
    ).toBeVisible();

    // Simulate notification via WebSocket
    await page.evaluate(() => {
      window.postMessage(
        {
          type: "NEW_NOTIFICATION",
          data: {
            id: "test-1",
            message: "New tender matching your criteria",
          },
        },
        "*"
      );
    });

    // Should show notification
    await expect(
      page.locator('[data-testid="notification-message"]')
    ).toBeVisible();
    await expect(
      page.locator('[data-testid="notification-message"]')
    ).toContainText("New tender");
  });

  test("updates analytics in real-time", async ({ page }) => {
    // Navigate to analytics
    await page.click("text=Analytics");

    // Wait for initial chart
    await expect(page.locator('[data-testid="analytics-chart"]')).toBeVisible();

    // Simulate data update via WebSocket
    await page.evaluate(() => {
      window.postMessage(
        {
          type: "ANALYTICS_UPDATE",
          data: {
            timestamp: new Date().toISOString(),
            value: 150,
          },
        },
        "*"
      );
    });

    // Should update chart
    await expect(page.locator('[data-testid="chart-value"]')).toContainText(
      "150"
    );
  });
});
