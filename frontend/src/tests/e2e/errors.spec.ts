import { expect, test } from "@playwright/test";

// If <PERSON><PERSON> is not installed, you need to install it first:
// npm install --save-dev @playwright/test
// or comment out this import and use an alternative testing framework

// import { test, expect } from '@playwright/test';

test.describe("Error Handling Tests", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("http://localhost:3000");
  });

  test("handles API errors gracefully", async ({ page }) => {
    // Navigate to tenders page
    await page.click("text=Tenders");

    // Force API error by modifying request
    await page.route("**/api/v1/tenders", (route) => {
      route.fulfill({
        status: 500,
        body: JSON.stringify({ error: "Internal Server Error" }),
      });
    });

    // Try to load tenders
    await page.click('[data-testid="refresh-tenders"]');

    // Should show error message
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="error-message"]')).toContainText(
      "Error loading tenders"
    );
  });

  test("handles authentication errors", async ({ page }) => {
    // Navigate to protected page
    await page.click("text=Dashboard");

    // Force 401 error
    await page.route("**/api/v1/dashboard-data", (route) => {
      route.fulfill({
        status: 401,
        body: JSON.stringify({ error: "Unauthorized" }),
      });
    });

    // Try to load dashboard data
    await page.click('[data-testid="refresh-dashboard"]');

    // Should redirect to login
    await expect(page).toHaveURL(/.*login/);
  });

  test("handles network errors", async ({ page }) => {
    // Navigate to analysis page
    await page.click("text=Analysis");

    // Simulate offline state
    await page.context().setOffline(true);

    // Try to load data
    await page.click('[data-testid="load-analysis"]');

    // Should show network error
    await expect(page.locator('[data-testid="network-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="network-error"]')).toContainText(
      "network"
    );

    // Restore online state
    await page.context().setOffline(false);
  });
});
