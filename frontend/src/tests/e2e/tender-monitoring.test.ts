import { test, expect } from '@playwright/test';
import { TenderStatus } from '../../types/tender';

test.describe('Tender Monitoring System', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // Login first
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'admin123');
    await page.click('[data-testid="login-button"]');
  });

  test('dashboard shows tender statistics and list', async ({ page }) => {
    await page.click('[data-testid="tenders-nav"]');

    // Check that the tender list exists
    await expect(page.locator('[data-testid="tender-list"]')).toBeVisible();

    // Verify statistics panel shows key metrics
    const statsPanel = page.locator('[data-testid="tender-stats"]');
    await expect(statsPanel.locator('text=Total Tenders')).toBeVisible();
    await expect(statsPanel.locator('text=High Priority')).toBeVisible();
  });

  test('can filter and sort tenders', async ({ page }) => {
    await page.click('[data-testid="tenders-nav"]');

    // Apply status filter
    await page.click('[data-testid="filter-button"]');
    await page.selectOption('[data-testid="status-filter"]', 'open');

    // Apply priority filter
    await page.selectOption('[data-testid="priority-filter"]', 'high');

    // Apply sector filter
    await page.selectOption('[data-testid="sector-filter"]', 'power');

    await page.click('[data-testid="apply-filters"]');

    // Verify filtered results
    const tenderList = page.locator('[data-testid="tender-list"]');
    await expect(tenderList).toBeVisible();

    // Sort by value
    await page.click('[data-testid="sort-value"]');

    // Verify sorting worked (values should be in descending order)
    const values = await page.locator('[data-testid="tender-value"]').allInnerTexts();
    const numericValues = values.map(v => parseInt(v.replace(/[^0-9]/g, '')));
    expect(numericValues).toEqual([...numericValues].sort((a, b) => b - a));
  });

  test('can submit a new tender', async ({ page }) => {
    await page.click('[data-testid="tenders-nav"]');
    await page.click('[data-testid="new-tender"]');

    // Fill in tender details
    await page.fill('[data-testid="tender-title"]', 'Water Treatment Plant Upgrade');
    await page.fill(
      '[data-testid="tender-description"]',
      'Complete upgrade of municipal water treatment facility including chemical dosing systems'
    );
    await page.fill('[data-testid="tender-value"]', '5000000');
    await page.fill('[data-testid="tender-organization"]', 'City Municipality');
    await page.fill('[data-testid="tender-location"]', 'Cape Town');
    await page.fill('[data-testid="tender-deadline"]', '2024-12-31');

    // Select sector and priority
    await page.selectOption('[data-testid="tender-sector"]', 'water');
    await page.selectOption('[data-testid="tender-priority"]', 'high');

    // Submit the tender
    await page.click('[data-testid="submit-tender"]');

    // Verify success message
    await expect(page.locator('text=Tender created successfully')).toBeVisible();

    // Verify tender appears in the list
    await page.click('[data-testid="tenders-nav"]');
    await expect(page.locator('text=Water Treatment Plant Upgrade')).toBeVisible();
  });

  test('calculates and displays tender scores', async ({ page }) => {
    await page.click('[data-testid="tenders-nav"]');
    await page.click('[data-testid="new-tender"]');

    // Create a highly relevant tender
    await page.fill('[data-testid="tender-title"]', 'Chemical Processing Equipment');
    await page.fill(
      '[data-testid="tender-description"]',
      'Industrial chemical processing equipment for water treatment'
    );
    await page.fill('[data-testid="tender-value"]', '1000000');
    await page.fill('[data-testid="tender-deadline"]', '2024-12-31');
    await page.click('[data-testid="submit-tender"]');

    // Verify the tender score calculation
    await expect(page.locator('[data-testid="tender-score"]')).toBeVisible();
    const scoreText = await page.locator('[data-testid="tender-score"]').innerText();
    const score = parseFloat(scoreText);
    expect(score).toBeGreaterThan(7); // High score for relevant tender
  });

  test('supports tender workflow status changes', async ({ context }) => {
    const page = await context.newPage();
    await page.goto('/');
    await page.click('[data-testid="tenders-nav"]');

    // Find a tender
    await page.click('[data-testid="tender-actions"]');

    // Change status to in review
    await page.selectOption('[data-testid="change-status"]', TenderStatus.IN_REVIEW);
    await page.click('[data-testid="update-status"]');

    // Open tender in new tab to verify status change
    const secondPage = await context.newPage();
    await secondPage.goto('/');
    await secondPage.click('[data-testid="tenders-nav"]');

    // Verify status update appears
    await expect(
      secondPage.locator(`[data-testid="status-badge"]:has-text("${TenderStatus.IN_REVIEW}")`)
    ).toBeVisible();
  });

  test('validates required fields on tender submission', async ({ page }) => {
    await page.click('[data-testid="tenders-nav"]');
    await page.click('[data-testid="new-tender"]');

    // Try to submit without required fields
    await page.click('[data-testid="submit-tender"]');

    // Check validation messages
    await expect(page.locator('text=Title is required')).toBeVisible();
    await expect(page.locator('text=Value must be provided')).toBeVisible();
    await expect(page.locator('text=Deadline is required')).toBeVisible();

    // Fill only some fields
    await page.fill('[data-testid="tender-title"]', 'Test Tender');
    await page.click('[data-testid="submit-tender"]');

    // Verify remaining validation messages
    await expect(page.locator('text=Value must be provided')).toBeVisible();
    await expect(page.locator('text=Deadline is required')).toBeVisible();
  });

  test('handles invalid input values', async ({ page }) => {
    await page.click('[data-testid="tenders-nav"]');
    await page.click('[data-testid="new-tender"]');

    // Test invalid value
    await page.fill('[data-testid="tender-value"]', '-5000');
    await expect(page.locator('text=Value must be positive')).toBeVisible();

    // Test past deadline
    const pastDate = new Date();
    pastDate.setDate(pastDate.getDate() - 1);
    await page.fill('[data-testid="tender-deadline"]', pastDate.toISOString().split('T')[0]);
    await expect(page.locator('text=Deadline must be in the future')).toBeVisible();

    // Test extremely large values
    await page.fill('[data-testid="tender-value"]', '999999999999999');
    await expect(page.locator('text=Value exceeds maximum allowed')).toBeVisible();
  });

  test('handles server errors gracefully', async ({ page }) => {
    await page.click('[data-testid="tenders-nav"]');

    // Force error by submitting malformed data
    await page.evaluate(() => {
      window.fetch = async () => {
        throw new Error('Network error');
      };
    });

    await page.click('[data-testid="refresh-tenders"]');

    // Verify error message
    await expect(page.locator('text=Failed to load tenders')).toBeVisible();
    await expect(page.locator('text=Please try again later')).toBeVisible();
  });

  test('maintains data consistency across updates', async ({ context }) => {
    const page1 = await context.newPage();
    const page2 = await context.newPage();

    // Login on both pages
    for (const page of [page1, page2]) {
      await page.goto('/');
      await page.fill('[data-testid="email"]', '<EMAIL>');
      await page.fill('[data-testid="password"]', 'admin123');
      await page.click('[data-testid="login-button"]');
      await page.click('[data-testid="tenders-nav"]');
    }

    // Create tender on first page
    await page1.click('[data-testid="new-tender"]');
    await page1.fill('[data-testid="tender-title"]', 'Concurrent Update Test');
    await page1.fill('[data-testid="tender-value"]', '1000000');
    await page1.fill('[data-testid="tender-deadline"]', '2024-12-31');
    await page1.click('[data-testid="submit-tender"]');

    // Try to update simultaneously from both pages
    const tenderId = await page1.evaluate(() => {
      const element = document.querySelector('[data-testid="tender-item"]');
      if (!element) throw new Error('Tender item not found');
      return element.getAttribute('data-tender-id');
    });

    // Update on page 1
    await page1.click(`[data-tender-id="${tenderId}"]`);
    await page1.fill('[data-testid="tender-value"]', '1100000');

    // Update on page 2
    await page2.click(`[data-tender-id="${tenderId}"]`);
    await page2.fill('[data-testid="tender-value"]', '1200000');

    // Submit both updates
    await Promise.all([
      page1.click('[data-testid="update-tender"]'),
      page2.click('[data-testid="update-tender"]')
    ]);

    // Verify conflict resolution
    await expect(page1.locator('text=Tender has been modified by another user')).toBeVisible();
    await expect(page2.locator(`[data-tender-id="${tenderId}"] [data-testid="tender-value"]`))
      .toHaveText('1200000');
  });

  test('handles file upload limits and types', async ({ page }) => {
    await page.click('[data-testid="tenders-nav"]');
    await page.click('[data-testid="new-tender"]');

    // Try to upload oversized file
    const bigFile = await page.evaluate(() => {
      const file = new File(['x'.repeat(11 * 1024 * 1024)], 'big.pdf', { type: 'application/pdf' });
      return file;
    });

    await page.setInputFiles('[data-testid="tender-documents"]', bigFile);
    await expect(page.locator('text=File size exceeds 10MB limit')).toBeVisible();

    // Try to upload invalid file type
    const invalidFile = await page.evaluate(() => {
      const file = new File(['test'], 'test.exe', { type: 'application/x-msdownload' });
      return file;
    });

    await page.setInputFiles('[data-testid="tender-documents"]', invalidFile);
    await expect(page.locator('text=Invalid file type. Allowed: PDF, DOC, DOCX')).toBeVisible();
  });
});
