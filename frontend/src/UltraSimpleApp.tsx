import React, { useState, useEffect } from 'react';

// A super simple app with no Material UI dependencies
const UltraSimpleApp: React.FC = () => {
  const [message, setMessage] = useState<string>("Loading...");
  const [backendStatus, setBackendStatus] = useState<string>("Checking...");
  const [backendData, setBackendData] = useState<string | null>(null);

  // Check backend status
useEffect(() => {
  const checkBackend = async () => {
    try {
      const response = await fetch('http://localhost:3002/health');
      if (response.ok) {
        const data = await response.json();
        setBackendStatus(`Connected: ${data.message || 'API is operational'}`);
      } else {
        setBackendStatus(`Error: Backend returned ${response.status}`);
      }
    } catch (error) {
      setBackendStatus(`Error: Cannot connect to backend at http://localhost:3002`);
    }
  };

    checkBackend();
    setMessage("Frontend server is running!");
  }, []);

  // Simple function to test API
  const testBackendData = async () => {
    try {
      const response = await fetch('http://localhost:3002/api/example');
      if (response.ok) {
        const data = await response.json();
        setBackendData(JSON.stringify(data, null, 2));
      } else {
        setBackendData(`Error fetching data: ${response.status}`);
      }
    } catch (error) {
      setBackendData(`Network error: Cannot connect to backend`);
    }
  };

  return (
    <div style={{
      fontFamily: 'Arial, sans-serif',
      maxWidth: '800px',
      margin: '0 auto',
      padding: '20px',
      backgroundColor: '#f5f5f5'
    }}>
      <div style={{
        backgroundColor: '#fff',
        padding: '20px',
        borderRadius: '5px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{ color: '#333' }}>Mexel Ultra Simple Frontend</h1>
        <p style={{ fontSize: '18px', color: '#4CAF50' }}>{message}</p>
        
        <div style={{
          backgroundColor: '#e1f5fe',
          padding: '15px',
          borderRadius: '4px',
          marginTop: '20px'
        }}>
          <h2 style={{ margin: '0 0 10px 0', color: '#0277bd' }}>Backend Status</h2>
          <p style={{ 
            color: backendStatus.includes('Error') ? '#d32f2f' : '#0277bd' 
          }}>{backendStatus}</p>
        </div>

        <div style={{ marginTop: '20px' }}>
          <button 
            onClick={testBackendData}
            style={{
              backgroundColor: '#2196f3',
              color: 'white',
              border: 'none',
              padding: '10px 15px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            Test Backend Data
          </button>
          
          {backendData && (
            <div style={{
              marginTop: '15px',
              padding: '15px',
              backgroundColor: '#f1f8e9',
              borderRadius: '4px',
              border: '1px solid #dcedc8'
            }}>
              <h3 style={{ margin: '0 0 10px 0', color: '#558b2f' }}>Backend Response:</h3>
              <pre style={{
                backgroundColor: '#fff',
                padding: '10px',
                borderRadius: '4px',
                overflow: 'auto'
              }}>
                {backendData}
              </pre>
            </div>
          )}
        </div>

        <div style={{ marginTop: '30px', paddingTop: '20px', borderTop: '1px solid #eee' }}>
          <h3>Instructions</h3>
          <p>Make sure the backend is running with:</p>
          <pre style={{
            backgroundColor: '#f5f5f5',
            padding: '10px',
            borderRadius: '4px',
            overflow: 'auto'
          }}>
            cd /Users/<USER>/Desktop/Mexel/backend-python
            python3 run.py --port 3002
          </pre>
        </div>
      </div>
    </div>
  );
};

export default UltraSimpleApp;