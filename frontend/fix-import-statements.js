const fs = require('fs');
const path = require('path');

// Function to fix import statements
function fixImportStatements(filePath) {
  console.log(`Fixing import statements in ${filePath}`);
  
  try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Fix double import statements
    content = content.replace(
      /import { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from '\.\.\/types\/react'; from 'react';/g,
      "import { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from '../types/react';"
    );
    
    // Fix relative paths
    content = content.replace(
      /from '\.\.\/types\/react'/g,
      "from '../types/react'"
    );
    
    content = content.replace(
      /from '\.\.\/\.\.\/types\/react'/g,
      "from '../../types/react'"
    );
    
    content = content.replace(
      /from '\.\.\/\.\.\/\.\.\/types\/react'/g,
      "from '../../../types/react'"
    );
    
    content = content.replace(
      /from '\.\.\/\.\.\/\.\.\/\.\.\/types\/react'/g,
      "from '../../../../types/react'"
    );
    
    // Write the file
    fs.writeFileSync(filePath, content, 'utf8');
    
    console.log(`Fixed import statements in ${filePath}`);
  } catch (error) {
    console.error(`Error fixing import statements in ${filePath}:`, error);
  }
}

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      processDirectory(filePath);
    } else if (stats.isFile() && (filePath.endsWith('.tsx') || filePath.endsWith('.ts'))) {
      // Check if the file has import statement issues
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes("from '../types/react'; from 'react';") ||
          content.includes("from '../../types/react'; from 'react';") ||
          content.includes("from '../../../types/react'; from 'react';") ||
          content.includes("from '../../../../types/react'; from 'react';")) {
        fixImportStatements(filePath);
      }
    }
  }
}

// Start processing from the src directory
processDirectory('./src');

console.log('Done fixing import statements!');
