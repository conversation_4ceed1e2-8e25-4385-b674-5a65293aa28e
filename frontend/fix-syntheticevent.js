/**
 * <PERSON><PERSON><PERSON> to fix SyntheticEvent type issues across the codebase
 * 
 * This script:
 * 1. Finds all files that use SyntheticEvent without the React namespace
 * 2. Replaces SyntheticEvent with React.SyntheticEvent
 */

const fs = require('fs');
const path = require('path');

// Function to fix SyntheticEvent issues in a file
function fixSyntheticEventIssues(filePath) {
  console.log(`Fixing SyntheticEvent issues in ${filePath}`);
  
  try {
    // Read the file content
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Check if the file imports SyntheticEvent directly
    const importsSyntheticEvent = content.includes('SyntheticEvent') && 
                                 (content.includes('import { SyntheticEvent }') || 
                                  content.includes('import {SyntheticEvent}') ||
                                  content.match(/import.*?{.*?SyntheticEvent.*?}.*?from ['"]react['"]/));
    
    if (importsSyntheticEvent) {
      // Remove SyntheticEvent from imports
      content = content.replace(/import\s*{([^}]*)SyntheticEvent,([^}]*)}\s*from\s*['"]react['"]/g, 'import {$1$2} from \'react\'');
      content = content.replace(/import\s*{([^}]*),\s*SyntheticEvent([^}]*)}\s*from\s*['"]react['"]/g, 'import {$1$2} from \'react\'');
      content = content.replace(/import\s*{([^}]*),\s*SyntheticEvent\s*}\s*from\s*['"]react['"]/g, 'import {$1} from \'react\'');
      content = content.replace(/import\s*{\s*SyntheticEvent\s*}\s*from\s*['"]react['"]/g, '');
      content = content.replace(/import\s*{\s*SyntheticEvent,([^}]*)}\s*from\s*['"]react['"]/g, 'import {$1} from \'react\'');
      
      // Replace SyntheticEvent with React.SyntheticEvent
      content = content.replace(/([^.])SyntheticEvent([<,\s)])/g, '$1React.SyntheticEvent$2');
    }
    
    // Write the updated content back to the file
    fs.writeFileSync(filePath, content);
    console.log(`Fixed SyntheticEvent issues in ${filePath}`);
  } catch (error) {
    console.error(`Error fixing SyntheticEvent issues in ${filePath}:`, error);
  }
}

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      processDirectory(filePath);
    } else if (stats.isFile() && (filePath.endsWith('.tsx') || filePath.endsWith('.ts'))) {
      // Check if the file has SyntheticEvent issues
      const content = fs.readFileSync(filePath, 'utf8');
      
      if (content.includes('SyntheticEvent') && !content.includes('React.SyntheticEvent')) {
        fixSyntheticEventIssues(filePath);
      }
    }
  }
}

// Create a backup directory
const backupDir = path.join(__dirname, 'backups');
if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir);
}

// Backup the src directory
const srcDir = path.join(__dirname, 'src');
const backupSrcDir = path.join(backupDir, 'src-' + new Date().toISOString().replace(/:/g, '-'));
fs.mkdirSync(backupSrcDir, { recursive: true });

function copyDirectory(source, destination) {
  const files = fs.readdirSync(source);
  
  for (const file of files) {
    const sourcePath = path.join(source, file);
    const destPath = path.join(destination, file);
    const stats = fs.statSync(sourcePath);
    
    if (stats.isDirectory()) {
      fs.mkdirSync(destPath, { recursive: true });
      copyDirectory(sourcePath, destPath);
    } else {
      fs.copyFileSync(sourcePath, destPath);
    }
  }
}

console.log(`Backing up src directory to ${backupSrcDir}`);
copyDirectory(srcDir, backupSrcDir);

// Start processing from the src directory
console.log('Starting to fix SyntheticEvent issues...');
processDirectory(srcDir);
console.log('Done fixing SyntheticEvent issues!');
