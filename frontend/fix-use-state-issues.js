const fs = require("fs");
const path = require("path");
const glob = require("glob");

// Function to fix component declarations
function fixReactUseStateIssues(filePath) {
  console.log(`Fixing React.React.useState issues in ${filePath}`);

  let content = fs.readFileSync(filePath, "utf8");
  let modified = false;

  // Fix 1: Replace React.React.useState with React.useState
  const reactReactUseStateRegex = /React\.React\.useState/g;
  content = content.replace(reactReactUseStateRegex, (match) => {
    modified = true;
    return "React.useState";
  });

  // Save the modified file
  if (modified) {
    fs.writeFileSync(filePath, content, "utf8");
    console.log(`Fixed React.React.useState issues in ${filePath}`);
  } else {
    console.log(`No React.React.useState issues to fix in ${filePath}`);
  }
}

// Find all TypeScript files in the src directory
const files = glob.sync(
  "/Users/<USER>/Desktop/Mexel/frontend/src/**/*.{ts,tsx}"
);

console.log(`Found ${files.length} TypeScript files to process.`);

// Fix React.React.useState issues in each file
let fixedFilesCount = 0;
files.forEach((file) => {
  try {
    fixReactUseStateIssues(file);
    fixedFilesCount++;
  } catch (error) {
    console.error(`Error processing file ${file}:`, error);
  }
});

console.log(
  `Done fixing React.React.useState issues! Processed ${fixedFilesCount} files.`
);
