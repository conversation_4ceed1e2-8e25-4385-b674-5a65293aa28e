/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset and base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

:root {
    --primary-color: #1976d2;
    --primary-light: #4791db;
    --primary-dark: #115293;
    --secondary-color: #f50057;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;
    --background-color: #f5f5f5;
    --card-color: #ffffff;
    --text-primary: #333333;
    --text-secondary: #666666;
    --border-color: #e0e0e0;
    --sidebar-width: 240px;
    --header-height: 64px;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.5;
}

/* Layout */
#app {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.app-container {
    display: flex;
    flex: 1;
}

/* Header */
.app-header {
    height: var(--header-height);
    background-color: var(--card-color);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-name {
    font-weight: 500;
}

.avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--card-color);
    box-shadow: var(--shadow-sm);
    padding: 20px 0;
    height: calc(100vh - var(--header-height));
    position: sticky;
    top: var(--header-height);
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: 4px;
}

.nav-item a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    text-decoration: none;
    color: var(--text-primary);
    transition: background-color 0.2s;
}

.nav-item a:hover {
    background-color: rgba(25, 118, 210, 0.08);
}

.nav-item.active a {
    background-color: rgba(25, 118, 210, 0.12);
    color: var(--primary-color);
    font-weight: 500;
    border-right: 3px solid var(--primary-color);
}

.nav-item .icon {
    margin-right: 12px;
    font-size: 1.2rem;
}

/* Main content */
.content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

/* Cards */
.card {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-weight: 600;
    font-size: 1.1rem;
}

.card-content {
    padding: 20px;
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

/* KPI Cards */
.kpi-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.kpi-card {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    padding: 20px;
}

.kpi-title {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.kpi-value {
    font-size: 1.8rem;
    font-weight: 600;
}

/* Grid layout */
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 20px;
}

.full-width {
    grid-column: 1 / -1;
}

/* Tables */
.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Status badges */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
}

.badge.new { background-color: var(--info-color); }
.badge.contacted { background-color: #673ab7; }
.badge.qualified { background-color: var(--success-color); }
.badge.proposal { background-color: var(--warning-color); }
.badge.negotiation { background-color: #009688; }

/* API Status */
.api-status {
    padding: 10px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.api-status.connected {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.api-status.disconnected {
    background-color: #ffebee;
    color: #c62828;
}

/* Buttons */
.button {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.button.primary {
    background-color: var(--primary-color);
    color: white;
}

.button.primary:hover {
    background-color: var(--primary-dark);
}

/* Tender details */
.tender-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.tender-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.tender-reference {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 16px;
}

.tender-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 20px;
}

.tender-meta-item {
    display: flex;
    flex-direction: column;
}

.tender-meta-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.tender-meta-value {
    font-weight: 500;
}

.tender-description {
    margin-bottom: 20px;
    line-height: 1.6;
}

.tender-section {
    margin-bottom: 20px;
}

.tender-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--primary-color);
}

.tender-requirements {
    list-style-type: none;
    padding: 0;
}

.tender-requirements li {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
}

.tender-requirements li:before {
    content: "✓";
    color: var(--success-color);
    margin-right: 8px;
    font-weight: bold;
}

.tender-keywords {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px;
}

.keyword-tag {
    background-color: rgba(25, 118, 210, 0.1);
    color: var(--primary-color);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 500;
}

.relevance-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.relevance-score {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

.relevance-bar {
    height: 8px;
    width: 100px;
    background-color: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.relevance-fill {
    height: 100%;
    background-color: var(--primary-color);
}

/* Sectors list */
.sectors-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.sector-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-radius: 4px;
    background-color: rgba(25, 118, 210, 0.05);
    transition: background-color 0.2s;
}

.sector-item:hover {
    background-color: rgba(25, 118, 210, 0.1);
}

.sector-name {
    font-weight: 500;
}

.sector-count {
    background-color: var(--primary-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Chart container */
.chart-container {
    height: 300px;
    width: 100%;
}

/* Button styles */
.button.secondary {
    background-color: #f5f5f5;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.button.secondary:hover {
    background-color: #e0e0e0;
}

/* Loading state */
.loading {
    text-align: center;
    padding: 20px;
    color: var(--text-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        position: static;
    }

    .nav-menu {
        display: flex;
        overflow-x: auto;
    }

    .nav-item {
        margin-bottom: 0;
        margin-right: 4px;
    }

    .grid-container {
        grid-template-columns: 1fr;
    }

    .tender-meta {
        flex-direction: column;
        gap: 8px;
    }
}
