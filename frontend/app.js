// Configuration
const API_URL = 'http://localhost:3001';

// DOM Elements
const apiStatusEl = document.getElementById('api-status');
const refreshButton = document.getElementById('refresh-button');
const tendersCountEl = document.getElementById('tenders-count');
const avgRelevanceEl = document.getElementById('avg-relevance');
const closingSoonEl = document.getElementById('closing-soon');
const systemStatusEl = document.getElementById('system-status');
const tendersTableEl = document.getElementById('tenders-table');
const productsTableEl = document.getElementById('products-table');
const sectorsListEl = document.getElementById('sectors-list');
const tenderDetailsCardEl = document.getElementById('tender-details-card');
const tenderDetailsEl = document.getElementById('tender-details');
const closeDetailsBtn = document.getElementById('close-details');

// API Functions
async function fetchAPI(endpoint) {
    try {
        const response = await fetch(`${API_URL}${endpoint}`);
        if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error(`Error fetching ${endpoint}:`, error);
        throw error;
    }
}

// Check API connection
async function checkAPIConnection() {
    try {
        const result = await fetchAPI('/api/health');
        apiStatusEl.className = 'api-status connected';
        apiStatusEl.textContent = `Connected to API (Port 3001) - Server time: ${new Date(result.data.timestamp).toLocaleTimeString()}`;
        return true;
    } catch (error) {
        apiStatusEl.className = 'api-status disconnected';
        apiStatusEl.textContent = `Disconnected from API (Port 3001). Error: ${error.message}`;
        return false;
    }
}

// Load Tenders Data
async function loadTenders() {
    try {
        const result = await fetchAPI('/api/tenders');
        const tenders = result.data;

        // Update KPIs
        tendersCountEl.textContent = tenders.length;

        // Calculate average relevance score
        const totalRelevance = tenders.reduce((sum, tender) => sum + tender.relevanceScore, 0);
        const avgRelevance = Math.round(totalRelevance / tenders.length);
        avgRelevanceEl.textContent = `${avgRelevance}%`;

        // Calculate closing soon (next 7 days)
        const today = new Date();
        const nextWeek = new Date();
        nextWeek.setDate(today.getDate() + 7);

        const closingSoon = tenders.filter(tender => {
            const closingDate = new Date(tender.closingDate);
            return closingDate >= today && closingDate <= nextWeek;
        });

        closingSoonEl.textContent = closingSoon.length;

        // Clear table
        tendersTableEl.innerHTML = '';

        // Add rows
        tenders.forEach(tender => {
            const row = document.createElement('tr');
            row.dataset.tenderId = tender.id;
            row.style.cursor = 'pointer';

            const referenceCell = document.createElement('td');
            referenceCell.textContent = tender.reference;

            const titleCell = document.createElement('td');
            titleCell.textContent = tender.title;

            const issuerCell = document.createElement('td');
            issuerCell.textContent = tender.issuer;

            const sectorCell = document.createElement('td');
            sectorCell.textContent = tender.sector;

            const closingDateCell = document.createElement('td');
            closingDateCell.textContent = tender.closingDate;

            const relevanceCell = document.createElement('td');
            relevanceCell.textContent = `${tender.relevanceScore}%`;
            relevanceCell.style.color =
                tender.relevanceScore >= 90 ? 'var(--success-color)' :
                tender.relevanceScore >= 70 ? 'var(--warning-color)' :
                'var(--text-secondary)';

            row.appendChild(referenceCell);
            row.appendChild(titleCell);
            row.appendChild(issuerCell);
            row.appendChild(sectorCell);
            row.appendChild(closingDateCell);
            row.appendChild(relevanceCell);

            // Add click event to show tender details
            row.addEventListener('click', () => showTenderDetails(tender));

            tendersTableEl.appendChild(row);
        });

        // Load sectors data
        loadSectors(tenders);

    } catch (error) {
        tendersTableEl.innerHTML = `<tr><td colspan="6" class="loading">Error: ${error.message}</td></tr>`;
        tendersCountEl.textContent = '-';
        avgRelevanceEl.textContent = '-';
        closingSoonEl.textContent = '-';
    }
}

// Show tender details
function showTenderDetails(tender) {
    // Show the details card
    tenderDetailsCardEl.style.display = 'block';

    // Format the details
    const detailsHTML = `
        <div class="tender-detail-header">
            <div>
                <div class="tender-title">${tender.title}</div>
                <div class="tender-reference">Reference: ${tender.reference}</div>
            </div>
            <div class="relevance-indicator">
                <div class="relevance-score">${tender.relevanceScore}%</div>
                <div class="relevance-bar">
                    <div class="relevance-fill" style="width: ${tender.relevanceScore}%"></div>
                </div>
            </div>
        </div>

        <div class="tender-meta">
            <div class="tender-meta-item">
                <span class="tender-meta-label">Issuer</span>
                <span class="tender-meta-value">${tender.issuer}</span>
            </div>
            <div class="tender-meta-item">
                <span class="tender-meta-label">Sector</span>
                <span class="tender-meta-value">${tender.sector}</span>
            </div>
            <div class="tender-meta-item">
                <span class="tender-meta-label">Location</span>
                <span class="tender-meta-value">${tender.location}</span>
            </div>
            <div class="tender-meta-item">
                <span class="tender-meta-label">Publication Date</span>
                <span class="tender-meta-value">${tender.publicationDate}</span>
            </div>
            <div class="tender-meta-item">
                <span class="tender-meta-label">Closing Date</span>
                <span class="tender-meta-value">${tender.closingDate}</span>
            </div>
            <div class="tender-meta-item">
                <span class="tender-meta-label">Status</span>
                <span class="tender-meta-value">${tender.status}</span>
            </div>
        </div>

        <div class="tender-section">
            <div class="tender-section-title">Description</div>
            <div class="tender-description">${tender.description}</div>
        </div>

        <div class="tender-section">
            <div class="tender-section-title">Requirements</div>
            <ul class="tender-requirements">
                ${tender.requirements.map(req => `<li>${req}</li>`).join('')}
            </ul>
        </div>

        <div class="tender-section">
            <div class="tender-section-title">Keywords</div>
            <div class="tender-keywords">
                ${tender.keywords.map(keyword => `<div class="keyword-tag">${keyword}</div>`).join('')}
            </div>
        </div>
    `;

    tenderDetailsEl.innerHTML = detailsHTML;

    // Scroll to the details
    tenderDetailsCardEl.scrollIntoView({ behavior: 'smooth' });
}

// Load sectors data
function loadSectors(tenders) {
    // Count tenders by sector
    const sectorCounts = {};
    tenders.forEach(tender => {
        if (!sectorCounts[tender.sector]) {
            sectorCounts[tender.sector] = 0;
        }
        sectorCounts[tender.sector]++;
    });

    // Sort sectors by count (descending)
    const sortedSectors = Object.entries(sectorCounts)
        .sort((a, b) => b[1] - a[1])
        .map(([sector, count]) => ({ sector, count }));

    // Clear sectors list
    sectorsListEl.innerHTML = '';

    // Add sectors
    sortedSectors.forEach(({ sector, count }) => {
        const sectorItem = document.createElement('div');
        sectorItem.className = 'sector-item';

        const sectorName = document.createElement('div');
        sectorName.className = 'sector-name';
        sectorName.textContent = sector;

        const sectorCount = document.createElement('div');
        sectorCount.className = 'sector-count';
        sectorCount.textContent = count;

        sectorItem.appendChild(sectorName);
        sectorItem.appendChild(sectorCount);

        sectorsListEl.appendChild(sectorItem);
    });
}

// Load system status
async function loadSystemStatus() {
    try {
        const result = await fetchAPI('/api/system-status');
        const status = result.data;

        systemStatusEl.textContent = status.status;
        systemStatusEl.style.color =
            status.status === 'Operational' ? 'var(--success-color)' :
            'var(--warning-color)';

    } catch (error) {
        systemStatusEl.textContent = 'Unknown';
        systemStatusEl.style.color = 'var(--text-secondary)';
    }
}

// Load Products Data
async function loadProducts() {
    try {
        const result = await fetchAPI('/api/products');
        const products = result.data;

        // Clear table
        productsTableEl.innerHTML = '';

        // Add rows
        products.forEach(product => {
            const row = document.createElement('tr');

            const nameCell = document.createElement('td');
            nameCell.textContent = product.name;

            const categoryCell = document.createElement('td');
            categoryCell.textContent = product.category;

            // Effectiveness based on growth
            const effectivenessCell = document.createElement('td');
            const effectiveness = product.growth > 0 ? 'High' : product.growth === 0 ? 'Medium' : 'Standard';
            effectivenessCell.textContent = effectiveness;
            effectivenessCell.style.color =
                effectiveness === 'High' ? 'var(--success-color)' :
                effectiveness === 'Medium' ? 'var(--warning-color)' : 'var(--info-color)';

            row.appendChild(nameCell);
            row.appendChild(categoryCell);
            row.appendChild(effectivenessCell);

            productsTableEl.appendChild(row);
        });
    } catch (error) {
        productsTableEl.innerHTML = `<tr><td colspan="3" class="loading">Error: ${error.message}</td></tr>`;
    }
}

// Load all data
async function loadAllData() {
    const connected = await checkAPIConnection();

    if (connected) {
        await Promise.all([
            loadTenders(),
            loadSystemStatus(),
            loadProducts()
        ]);
    } else {
        // Reset all data if API is not connected
        tendersCountEl.textContent = '-';
        avgRelevanceEl.textContent = '-';
        closingSoonEl.textContent = '-';
        systemStatusEl.textContent = 'Disconnected';
        systemStatusEl.style.color = 'var(--error-color)';

        tendersTableEl.innerHTML = '<tr><td colspan="6" class="loading">API disconnected</td></tr>';
        productsTableEl.innerHTML = '<tr><td colspan="3" class="loading">API disconnected</td></tr>';
        sectorsListEl.innerHTML = '<div class="loading">API disconnected</div>';
    }
}

// Navigation
document.querySelectorAll('.nav-item a').forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();

        // Remove active class from all nav items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to clicked nav item
        e.currentTarget.parentElement.classList.add('active');

        // Handle navigation
        const target = e.currentTarget.getAttribute('href').substring(1);
        console.log(`Navigating to: ${target}`);

        // Implement basic tab functionality
        switch(target) {
            case 'dashboard':
                // Already on dashboard, just refresh data
                loadAllData();
                break;

            case 'tenders':
                // Redirect to a tenders page or show a modal with all tenders
                alert('Tenders page would be shown here in a full implementation');
                break;

            case 'sectors':
                // Redirect to a sectors page or show a modal with sector analysis
                alert('Sectors analysis page would be shown here in a full implementation');
                break;

            case 'products':
                // Redirect to a products page or show a modal with all products
                alert('Products page would be shown here in a full implementation');
                break;

            case 'system':
                // Redirect to a system page or show a modal with system status
                fetch(`${API_URL}/api/system-status`)
                    .then(response => response.json())
                    .then(data => {
                        alert(`System Status: ${data.data.status}\nRunning Agents: ${data.data.runningAgents}/${data.data.agentCount}\nLast Updated: ${new Date(data.data.lastUpdated).toLocaleString()}`);
                    })
                    .catch(error => {
                        alert(`Error fetching system status: ${error.message}`);
                    });
                break;
        }
    });
});

// Event Listeners
refreshButton.addEventListener('click', () => {
    loadAllData();
});

// Close tender details
if (closeDetailsBtn) {
    closeDetailsBtn.addEventListener('click', () => {
        tenderDetailsCardEl.style.display = 'none';
    });
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    loadAllData();

    // Refresh data every 30 seconds
    setInterval(loadAllData, 30000);
});
