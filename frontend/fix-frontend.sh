#!/bin/bash

# <PERSON><PERSON>t to fix frontend issues

echo "Fixing frontend issues..."

# Create a temporary package.json with the correct dependencies
echo "Creating temporary package.json..."
cat > package.json.new << 'EOL'
{
  "name": "@mexel/frontend",
  "version": "1.0.0",
  "main": "index.js",
  "scripts": {
    "start": "CSP_DISABLED=true react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "keywords": [],
  "author": "",
  "license": "ISC",
  "description": "",
  "dependencies": {
    "@emotion/react": "^11.11.1",
    "@emotion/styled": "^11.11.0",
    "@mui/icons-material": "^5.14.18",
    "@mui/material": "^5.14.18",
    "axios": "^1.9.0",
    "dotenv-expand": "^10.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "^5.0.1",
    "recharts": "^2.9.0",
    "socket.io-client": "^4.7.2"
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "devDependencies": {
    "@types/jest": "^29.5.5",
    "@types/node": "^18.18.0",
    "@types/react": "^18.2.23",
    "@types/react-dom": "^18.2.8",
    "nth-check": "^2.1.1",
    "postcss": "^8.4.31",
    "typescript": "~4.9.5"
  },
  "overrides": {
    "nth-check": "^2.1.1",
    "postcss": "^8.4.31"
  }
}
EOL

# Replace the package.json
echo "Replacing package.json..."
mv package.json.new package.json

# Create a simple test HTML page to verify the API
echo "Creating test HTML page..."
mkdir -p public
cat > public/test.html << 'EOL'
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mexel API Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #2c3e50;
    }
    .tender {
      border: 1px solid #ddd;
      padding: 15px;
      margin-bottom: 15px;
      border-radius: 5px;
    }
    .tender h3 {
      margin-top: 0;
      color: #3498db;
    }
    .tender p {
      margin: 5px 0;
    }
    .tender .meta {
      font-size: 0.9em;
      color: #7f8c8d;
    }
    .tender .score {
      font-weight: bold;
      color: #27ae60;
    }
    button {
      background-color: #3498db;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 5px;
      cursor: pointer;
      margin-bottom: 20px;
    }
    button:hover {
      background-color: #2980b9;
    }
    #loading {
      display: none;
      color: #7f8c8d;
    }
  </style>
</head>
<body>
  <h1>Mexel API Test</h1>
  <button id="fetchTenders">Fetch Tenders</button>
  <span id="loading">Loading...</span>
  <div id="tenders"></div>

  <script>
    document.getElementById('fetchTenders').addEventListener('click', async () => {
      const loadingEl = document.getElementById('loading');
      const tendersEl = document.getElementById('tenders');
      
      loadingEl.style.display = 'inline';
      tendersEl.innerHTML = '';
      
      try {
        const response = await fetch('http://localhost:3001/api/tenders');
        const tenders = await response.json();
        
        loadingEl.style.display = 'none';
        
        if (tenders.length === 0) {
          tendersEl.innerHTML = '<p>No tenders found</p>';
          return;
        }
        
        tenders.forEach(tender => {
          const tenderEl = document.createElement('div');
          tenderEl.className = 'tender';
          
          const closingDate = tender.closingDate ? new Date(tender.closingDate).toLocaleDateString() : 'Unknown';
          const publishDate = tender.publishDate ? new Date(tender.publishDate).toLocaleDateString() : 'Unknown';
          
          tenderEl.innerHTML = `
            <h3>${tender.title}</h3>
            <p>${tender.description}</p>
            <p class="meta">Source: ${tender.issuer || tender.source || 'Unknown'}</p>
            <p class="meta">Reference: ${tender.reference || 'N/A'}</p>
            <p class="meta">Published: ${publishDate}</p>
            <p class="meta">Closing: ${closingDate}</p>
            ${tender.confidence ? `<p class="score">Relevance Score: ${Math.round(tender.confidence)}%</p>` : ''}
            <p><a href="${tender.url}" target="_blank">View Tender</a></p>
          `;
          
          tendersEl.appendChild(tenderEl);
        });
      } catch (error) {
        loadingEl.style.display = 'none';
        tendersEl.innerHTML = `<p>Error fetching tenders: ${error.message}</p>`;
        console.error('Error fetching tenders:', error);
      }
    });
  </script>
</body>
</html>
EOL

echo "Frontend files updated successfully!"
echo ""
echo "To start the frontend, run:"
echo "cd frontend && yarn start"
echo ""
echo "To test the API connection, open http://localhost:3000/test.html after starting both backend and frontend"
