{"name": "frontend", "version": "1.0.0", "description": "Mexel Energy Frontend Application", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.15", "@mui/lab": "^5.0.0-alpha.169", "@mui/material": "^5.15.15", "@mui/system": "^5.15.15", "@mui/x-date-pickers": "^6.19.7", "axios": "^1.6.7", "chart.js": "^4.4.2", "date-fns": "^2.16.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.2.0", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "recharts": "^2.15.3", "shared": "workspace:*", "socket.io-client": "^4.7.5"}, "scripts": {"preinstall": "node -e \"if(process.env.npm_execpath.indexOf('yarn')===-1) throw new Error('Use yarn instead of npm')\"", "start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint --ext .js,.jsx,.ts,.tsx src/", "lint:fix": "eslint --ext .js,.jsx,.ts,.tsx src/ --fix", "check:ts": "tsc --noEmit", "fix:typescript": "node ../scripts/frontend/typescript/fix-all-typescript.js", "fix:imports": "node ../scripts/frontend/imports/fix-imports.js", "fix:ui": "node ../scripts/frontend/ui-libraries/fix-ui-libraries.js", "fix:all": "node ../scripts/frontend/fix-all-frontend.js", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:show-report": "playwright show-report", "clean": "rm -rf node_modules build coverage .eslintcache"}, "proxy": "http://localhost:3001", "devDependencies": {"@playwright/test": "^1.36.0", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-color": "^3.0.13", "create-react-app": "^5.1.0", "eslint-config-react-app": "^7.0.1"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"testPathIgnorePatterns": ["<rootDir>/src/tests/e2e/"]}, "engines": {"node": ">=18.0.0", "yarn": ">=3.0.0", "npm": "please-use-yarn"}}