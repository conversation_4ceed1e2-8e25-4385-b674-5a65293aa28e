#!/usr/bin/env node

/**
 * Consolidated TypeScript Fixer for Mexel Frontend
 * 
 * This script consolidates multiple TypeScript fix scripts into a single,
 * configurable utility that can fix common TypeScript and React issues.
 * 
 * Usage:
 *   node typescript-fixer.js [options]
 * 
 * Options:
 *   --all            Run all fixes (default)
 *   --react-types    Fix React type issues (FC, ReactNode, etc.)
 *   --components     Fix component declarations
 *   --usestate       Fix useState issues
 *   --imports        Fix import issues
 *   --namespaces     Fix namespace issues
 *   --tabpanel       Fix TabPanel component issues
 *   --recharts       Fix Recharts component issues
 *   --path <path>    Specify a custom path (default: src/**/*.{ts,tsx})
 *   --dry-run        Show what would be fixed without making changes
 *   --help           Show this help message
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  all: args.length === 0 || args.includes('--all'),
  reactTypes: args.includes('--react-types'),
  components: args.includes('--components'),
  usestate: args.includes('--usestate'),
  imports: args.includes('--imports'),
  namespaces: args.includes('--namespaces'),
  tabpanel: args.includes('--tabpanel'),
  recharts: args.includes('--recharts'),
  dryRun: args.includes('--dry-run'),
  help: args.includes('--help'),
  path: 'src/**/*.{ts,tsx}'
};

// Check for custom path
const pathIndex = args.indexOf('--path');
if (pathIndex !== -1 && args.length > pathIndex + 1) {
  options.path = args[pathIndex + 1];
}

// Show help
if (options.help) {
  console.log(`
Consolidated TypeScript Fixer for Mexel Frontend

Usage:
  node typescript-fixer.js [options]

Options:
  --all            Run all fixes (default)
  --react-types    Fix React type issues (FC, ReactNode, etc.)
  --components     Fix component declarations
  --usestate       Fix useState issues
  --imports        Fix import issues
  --namespaces     Fix namespace issues
  --tabpanel       Fix TabPanel component issues
  --recharts       Fix Recharts component issues
  --path <path>    Specify a custom path (default: src/**/*.{ts,tsx})
  --dry-run        Show what would be fixed without making changes
  --help           Show this help message
  `);
  process.exit(0);
}

console.log('Mexel TypeScript Fixer');
console.log('=====================');
console.log(`Mode: ${options.dryRun ? 'Dry Run (no changes)' : 'Live (making changes)'}`);
console.log(`Path: ${options.path}`);
console.log('Fixes enabled:');
console.log(`- React Types: ${options.all || options.reactTypes ? 'Yes' : 'No'}`);
console.log(`- Component Declarations: ${options.all || options.components ? 'Yes' : 'No'}`);
console.log(`- useState Issues: ${options.all || options.usestate ? 'Yes' : 'No'}`);
console.log(`- Import Issues: ${options.all || options.imports ? 'Yes' : 'No'}`);
console.log(`- Namespace Issues: ${options.all || options.namespaces ? 'Yes' : 'No'}`);
console.log(`- TabPanel Issues: ${options.all || options.tabpanel ? 'Yes' : 'No'}`);
console.log(`- Recharts Issues: ${options.all || options.recharts ? 'Yes' : 'No'}`);
console.log('');

// Statistics
let filesProcessed = 0;
let filesModified = 0;
let fixesApplied = 0;

/**
 * Fix function for React type issues
 */
function fixReactTypes(content) {
  let modified = false;
  let fixCount = 0;
  let newContent = content;

  // Fix React.FC and FC usage
  if (newContent.includes("React.FC") || newContent.includes(": FC")) {
    newContent = newContent.replace(/React\.FC<([^>]*)>/g, "($1)");
    newContent = newContent.replace(/: FC<([^>]*)>/g, "($1)");
    newContent = newContent.replace(/: FC( |=)/g, " ");
    newContent = newContent.replace(/React\.FC( |=)/g, "");
    modified = true;
    fixCount++;
  }

  // Fix ReactNode usage
  if (newContent.includes("ReactNode") || newContent.includes("React.ReactNode")) {
    newContent = newContent.replace(/: ReactNode/g, ": any");
    newContent = newContent.replace(/: React\.ReactNode/g, ": any");
    modified = true;
    fixCount++;
  }

  // Fix SyntheticEvent usage
  if (newContent.includes("SyntheticEvent") || newContent.includes("React.SyntheticEvent")) {
    newContent = newContent.replace(/: SyntheticEvent/g, ": any");
    newContent = newContent.replace(/: React\.SyntheticEvent/g, ": any");
    modified = true;
    fixCount++;
  }

  // Fix ChangeEvent usage
  if (newContent.includes("ChangeEvent") || newContent.includes("React.ChangeEvent")) {
    newContent = newContent.replace(/: ChangeEvent<([^>]*)>/g, ": any");
    newContent = newContent.replace(/: React\.ChangeEvent<([^>]*)>/g, ": any");
    modified = true;
    fixCount++;
  }

  // Fix MouseEvent usage
  if (newContent.includes("MouseEvent") || newContent.includes("React.MouseEvent")) {
    newContent = newContent.replace(/: MouseEvent<([^>]*)>/g, ": any");
    newContent = newContent.replace(/: React\.MouseEvent<([^>]*)>/g, ": any");
    modified = true;
    fixCount++;
  }

  // Fix FormEvent usage
  if (newContent.includes("FormEvent") || newContent.includes("React.FormEvent")) {
    newContent = newContent.replace(/: FormEvent/g, ": any");
    newContent = newContent.replace(/: React\.FormEvent/g, ": any");
    modified = true;
    fixCount++;
  }

  return { content: newContent, modified, fixCount };
}

/**
 * Fix function for component declarations
 */
function fixComponentDeclarations(content) {
  let modified = false;
  let fixCount = 0;
  let newContent = content;

  // Fix 1: Fix component declarations with syntax errors
  // Pattern: const ComponentName(PropsType) = ({ ... }) => {
  // Replace with: const ComponentName = ({ ... }: PropsType) => {
  const componentDeclarationRegex = /const\s+(\w+)(\s*):?\s*\((\w+)\)\s*=\s*\(\{\s*([^}]*)\s*\}\)\s*=>/g;
  newContent = newContent.replace(
    componentDeclarationRegex,
    (match, componentName, colon, propsType, params) => {
      modified = true;
      fixCount++;
      return `const ${componentName} = ({ ${params} }: ${propsType}) =>`;
    }
  );

  // Fix 2: Fix export component declarations with syntax errors
  // Pattern: export const ComponentName(PropsType) = ({ ... }) => {
  // Replace with: export const ComponentName = ({ ... }: PropsType) => {
  const exportComponentDeclarationRegex = /export\s+const\s+(\w+)(\s*):?\s*\((\w+)\)\s*=\s*\(\{\s*([^}]*)\s*\}\)\s*=>/g;
  newContent = newContent.replace(
    exportComponentDeclarationRegex,
    (match, componentName, colon, propsType, params) => {
      modified = true;
      fixCount++;
      return `export const ${componentName} = ({ ${params} }: ${propsType}) =>`;
    }
  );

  // Fix 3: Fix component declarations with syntax errors (single line)
  // Pattern: const ComponentName(PropsType) = ({ prop1, prop2 }) => {
  // Replace with: const ComponentName = ({ prop1, prop2 }: PropsType) => {
  const singleLineComponentDeclarationRegex = /const\s+(\w+)(\s*):?\s*\((\w+)\)\s*=\s*\(\{([^}]*)\}\)\s*=>/g;
  newContent = newContent.replace(
    singleLineComponentDeclarationRegex,
    (match, componentName, colon, propsType, params) => {
      modified = true;
      fixCount++;
      return `const ${componentName} = ({${params}}: ${propsType}) =>`;
    }
  );

  // Fix 4: Fix export component declarations with syntax errors (single line)
  // Pattern: export const ComponentName(PropsType) = ({ prop1, prop2 }) => {
  // Replace with: export const ComponentName = ({ prop1, prop2 }: PropsType) => {
  const singleLineExportComponentDeclarationRegex = /export\s+const\s+(\w+)(\s*):?\s*\((\w+)\)\s*=\s*\(\{([^}]*)\}\)\s*=>/g;
  newContent = newContent.replace(
    singleLineExportComponentDeclarationRegex,
    (match, componentName, colon, propsType, params) => {
      modified = true;
      fixCount++;
      return `export const ${componentName} = ({${params}}: ${propsType}) =>`;
    }
  );

  // Fix 5: Fix component declarations with syntax errors (no props)
  // Pattern: const ComponentName: = () => {
  // Replace with: const ComponentName = () => {
  const noPropsComponentDeclarationRegex = /const\s+(\w+)\s*:\s*=\s*\(\)\s*=>/g;
  newContent = newContent.replace(
    noPropsComponentDeclarationRegex,
    (match, componentName) => {
      modified = true;
      fixCount++;
      return `const ${componentName} = () =>`;
    }
  );

  // Fix 6: Fix export component declarations with syntax errors (no props)
  // Pattern: export const ComponentName: = () => {
  // Replace with: export const ComponentName = () => {
  const noPropsExportComponentDeclarationRegex = /export\s+const\s+(\w+)\s*:\s*=\s*\(\)\s*=>/g;
  newContent = newContent.replace(
    noPropsExportComponentDeclarationRegex,
    (match, componentName) => {
      modified = true;
      fixCount++;
      return `export const ${componentName} = () =>`;
    }
  );

  // Fix 7: Fix TenderOpportunities syntax error
  // Pattern: export const TenderOpportunities(TenderOpportunitiesProps) = ({
  // Replace with: export const TenderOpportunities = ({ ... }: TenderOpportunitiesProps) => {
  const tenderOpportunitiesRegex = /export\s+const\s+TenderOpportunities\((\w+)\)\s*=\s*\(\{/g;
  newContent = newContent.replace(tenderOpportunitiesRegex, (match, propsType) => {
    modified = true;
    fixCount++;
    return `export const TenderOpportunities = ({`;
  });

  return { content: newContent, modified, fixCount };
}

/**
 * Fix function for useState issues
 */
function fixUseStateIssues(content) {
  let modified = false;
  let fixCount = 0;
  let newContent = content;

  // Fix 1: Replace React.React.useState with React.useState
  if (newContent.includes("React.React.")) {
    newContent = newContent.replace(/React\.React\./g, "React.");
    modified = true;
    fixCount++;
  }

  // Fix 2: Fix useState with never[] by adding explicit typing
  if (newContent.match(/useState\(\[\]\)/g)) {
    newContent = newContent.replace(/useState\(\[\]\)/g, "useState<any[]>([])");
    modified = true;
    fixCount++;
  }

  // Fix 3: Fix useState(null) by adding explicit typing
  if (newContent.includes("useState(null)")) {
    newContent = newContent.replace(/useState\(null\)/g, "useState<any>(null)");
    modified = true;
    fixCount++;
  }

  // Fix 4: Fix useState with object literals
  if (newContent.match(/useState\((\s)*{/g)) {
    newContent = newContent.replace(/useState\((\s)*{/g, "useState<any>({");
    modified = true;
    fixCount++;
  }

  // Fix 5: Fix useState generic type parameter issues
  if (newContent.includes("useState<")) {
    newContent = newContent.replace(/useState<[^>]*>/g, "React.useState");
    modified = true;
    fixCount++;
  }

  // Fix 6: Fix useState with arrays that have initialization data
  const arrayStateMatches = newContent.match(/useState\(\[[^\]]+\]\)/g);
  if (arrayStateMatches) {
    for (const match of arrayStateMatches) {
      newContent = newContent.replace(
        match,
        match.replace("useState(", "useState<any[]>(")
      );
    }
    modified = true;
    fixCount++;
  }

  // Fix 7: Fix setError with string issues
  if (newContent.includes("setError(") && newContent.includes("useState<null>(null)")) {
    newContent = newContent.replace(
      /useState<null>\(null\)/g,
      "useState<string | null>(null)"
    );
    modified = true;
    fixCount++;
  }

  return { content: newContent, modified, fixCount };
}

/**
 * Fix function for import issues
 */
function fixImportIssues(content) {
  let modified = false;
  let fixCount = 0;
  let newContent = content;

  // Fix 1: Add React import if missing
  if (!newContent.includes("import React")) {
    newContent = `import React from 'react';\n${newContent}`;
    modified = true;
    fixCount++;
  }

  // Fix 2: Fix import path issues for TabsNavigation
  if (newContent.includes("./components/common/TabsNavigation")) {
    newContent = newContent.replace(
      /import \{ TabPanel, a11yProps \} from '\.\/components\/common\/TabsNavigation'/g,
      "import { TabPanel, a11yProps } from './common/TabsNavigation'"
    );
    modified = true;
    fixCount++;
  }

  // Fix 3: Add useState import if using useState but not imported
  if (newContent.includes('useState(') && !newContent.includes('import { useState')) {
    // Add useState import
    if (newContent.includes('import React from \'react\';')) {
      newContent = newContent.replace(
        'import React from \'react\';',
        'import React, { useState, useEffect } from \'react\';'
      );
      modified = true;
      fixCount++;
    } else if (newContent.includes('import React,')) {
      // Check if useState is already in the import
      if (!newContent.includes('useState')) {
        newContent = newContent.replace(
          /import React, {([^}]*)}/,
          'import React, {$1, useState, useEffect}'
        );
        modified = true;
        fixCount++;
      }
    }
  }

  return { content: newContent, modified, fixCount };
}

/**
 * Fix function for namespace issues
 */
function fixNamespaceIssues(content) {
  let modified = false;
  let fixCount = 0;
  let newContent = content;

  // Fix 1: Replace React.React namespace
  if (newContent.includes("React.React.")) {
    newContent = newContent.replace(/React\.React\./g, "React.");
    modified = true;
    fixCount++;
  }

  return { content: newContent, modified, fixCount };
}

/**
 * Fix function for TabPanel issues
 */
function fixTabPanelIssues(content, filePath) {
  let modified = false;
  let fixCount = 0;
  let newContent = content;

  // Fix TabPanel declaration in TabsNavigation.tsx
  if (filePath.includes('TabsNavigation.tsx') || filePath.includes('TabPanel.tsx')) {
    const tabPanelRegex = /export\s+const\s+TabPanel\(\{\s*children\?:\s*any;\s*index:\s*number;\s*value:\s*number;\s*\}\)/g;
    if (tabPanelRegex.test(newContent)) {
      newContent = newContent.replace(
        tabPanelRegex,
        "export const TabPanel = ({ children, index, value }: { children?: any; index: number; value: number; })"
      );
      modified = true;
      fixCount++;
    }
  }

  return { content: newContent, modified, fixCount };
}

/**
 * Fix function for Recharts issues
 */
function fixRechartsIssues(content) {
  let modified = false;
  let fixCount = 0;
  let newContent = content;

  // Fix 1: Fix recharts components
  if (
    newContent.includes("<LineChart") ||
    newContent.includes("<BarChart") ||
    newContent.includes("<PieChart") ||
    newContent.includes("<RadarChart")
  ) {
    // Replace recharts components with a simple Box
    newContent = newContent.replace(
      /<ResponsiveContainer[^>]*>[\s\S]*?<\/ResponsiveContainer>/g,
      `<Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>`
    );

    // Add Typography import if it's missing
    if (!newContent.includes("Typography")) {
      newContent = newContent.replace(
        /import \{([^}]*)\} from '@mui\/material';/,
        "import {$1, Typography} from '@mui/material';"
      );
    }

    modified = true;
    fixCount++;
  }

  return { content: newContent, modified, fixCount };
}

/**
 * Main function to fix TypeScript issues in a file
 */
function fixTypeScriptIssues(filePath) {
  try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    let totalFixes = 0;
    
    // Apply fixes based on options
    if (options.all || options.imports) {
      const result = fixImportIssues(content);
      content = result.content;
      modified = modified || result.modified;
      totalFixes += result.fixCount;
    }
    
    if (options.all || options.reactTypes) {
      const result = fixReactTypes(content);
      content = result.content;
      modified = modified || result.modified;
      totalFixes += result.fixCount;
    }
    
    if (options.all || options.components) {
      const result = fixComponentDeclarations(content);
      content = result.content;
      modified = modified || result.modified;
      totalFixes += result.fixCount;
    }
    
    if (options.all || options.usestate) {
      const result = fixUseStateIssues(content);
      content = result.content;
      modified = modified || result.modified;
      totalFixes += result.fixCount;
    }
    
    if (options.all || options.namespaces) {
      const result = fixNamespaceIssues(content);
      content = result.content;
      modified = modified || result.modified;
      totalFixes += result.fixCount;
    }
    
    if (options.all || options.tabpanel) {
      const result = fixTabPanelIssues(content, filePath);
      content = result.content;
      modified = modified || result.modified;
      totalFixes += result.fixCount;
    }
    
    if (options.all || options.recharts) {
      const result = fixRechartsIssues(content);
      content = result.content;
      modified = modified || result.modified;
      totalFixes += result.fixCount;
    }
    
    // Save the modified file if changes were made and not in dry run mode
    if (modified && !options.dryRun) {
      fs.writeFileSync(filePath, content, 'utf8');
      filesModified++;
    }
    
    if (modified) {
      console.log(`[FIXED] ${filePath} (${totalFixes} fixes applied)`);
      fixesApplied += totalFixes;
    } else {
      console.log(`[UNCHANGED] ${filePath}`);
    }
    
    filesProcessed++;
  } catch (error) {
    console.error(`[ERROR] Failed to process ${filePath}:`, error.message);
  }
}

// Get all files that match the pattern
const files = glob.sync(options.path, { cwd: process.cwd(), absolute: true });

console.log(`Found ${files.length} files to process.\n`);

// Process each file
files.forEach(fixTypeScriptIssues);

// Print summary
console.log('\nSummary:');
console.log(`Files processed: ${filesProcessed}`);
console.log(`Files modified: ${filesModified}`);
console.log(`Total fixes applied: ${fixesApplied}`);

if (options.dryRun) {
  console.log('\nThis was a dry run. No files were modified.');
}