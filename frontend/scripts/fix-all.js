#!/usr/bin/env node

/**
 * Consolidated Fix Script for Mexel Frontend
 * 
 * This script serves as the main entry point for all fix scripts,
 * allowing for selection of specific fix categories or running all fixes.
 * 
 * Usage:
 *   node fix-all.js [options]
 * 
 * Options:
 *   --typescript     Fix TypeScript issues
 *   --react          Fix React component issues
 *   --imports        Fix import issues
 *   --dependencies   Fix dependency issues
 *   --all            Run all fixes (default)
 *   --dry-run        Show what would be fixed without making changes
 *   --path <path>    Specify a custom path
 *   --help           Show this help message
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  typescript: args.includes('--typescript'),
  react: args.includes('--react'),
  imports: args.includes('--imports'),
  dependencies: args.includes('--dependencies'),
  all: args.length === 0 || args.includes('--all'),
  dryRun: args.includes('--dry-run'),
  help: args.includes('--help'),
  path: 'src'
};

// Check for custom path
const pathIndex = args.indexOf('--path');
if (pathIndex !== -1 && args.length > pathIndex + 1) {
  options.path = args[pathIndex + 1];
}

// Show help
if (options.help) {
  console.log(`
Consolidated Fix Script for Mexel Frontend

Usage:
  node fix-all.js [options]

Options:
  --typescript     Fix TypeScript issues
  --react          Fix React component issues
  --imports        Fix import issues
  --dependencies   Fix dependency issues
  --all            Run all fixes (default)
  --dry-run        Show what would be fixed without making changes
  --path <path>    Specify a custom path
  --help           Show this help message
  `);
  process.exit(0);
}

console.log('Mexel Frontend Fix Script');
console.log('=========================');
console.log(`Mode: ${options.dryRun ? 'Dry Run (no changes)' : 'Live (making changes)'}`);
console.log(`Path: ${options.path}`);
console.log('Fixes enabled:');
console.log(`- TypeScript: ${options.all || options.typescript ? 'Yes' : 'No'}`);
console.log(`- React: ${options.all || options.react ? 'Yes' : 'No'}`);
console.log(`- Imports: ${options.all || options.imports ? 'Yes' : 'No'}`);
console.log(`- Dependencies: ${options.all || options.dependencies ? 'Yes' : 'No'}`);
console.log('');

// Script paths
const SCRIPTS = {
  typescript: path.resolve(__dirname, 'fixes/typescript-fixer.js'),
  react: path.resolve(__dirname, 'fixes/react-fixer.js'),
  imports: path.resolve(__dirname, 'fixes/import-fixer.js'),
  dependencies: path.resolve(__dirname, 'fix-dependencies.sh')
};

// Ensure scripts exist
Object.entries(SCRIPTS).forEach(([key, scriptPath]) => {
  if ((options.all || options[key]) && !fs.existsSync(scriptPath)) {
    console.warn(`Warning: ${scriptPath} not found. Skipping ${key} fixes.`);
    options[key] = false;
    if (options.all) {
      console.warn(`Note: Running with --all, but ${key} fixes will be skipped.`);
    }
  }
});

// Build args for each script
function buildArgs(scriptType) {
  const baseArgs = options.dryRun ? ['--dry-run'] : [];
  
  if (options.path !== 'src') {
    baseArgs.push('--path', options.path);
  }
  
  switch (scriptType) {
    case 'typescript':
      return [...baseArgs];
    case 'react':
      return [...baseArgs];
    case 'imports':
      return [...baseArgs];
    default:
      return baseArgs;
  }
}

// Function to run a script and return a promise
function runScript(scriptPath, args = []) {
  return new Promise((resolve, reject) => {
    console.log(`\nRunning: ${path.basename(scriptPath)}`);
    
    // For shell scripts
    if (scriptPath.endsWith('.sh')) {
      try {
        execSync(`sh ${scriptPath} ${args.join(' ')}`, { 
          stdio: 'inherit',
          cwd: process.cwd() 
        });
        resolve();
      } catch (error) {
        console.error(`Error running ${scriptPath}:`, error.message);
        reject(error);
      }
      return;
    }
    
    // For node scripts
    const child = spawn('node', [scriptPath, ...args], { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    child.on('close', code => {
      if (code === 0) {
        resolve();
      } else {
        console.error(`${path.basename(scriptPath)} exited with code ${code}`);
        reject(new Error(`Script exited with code ${code}`));
      }
    });
    
    child.on('error', error => {
      console.error(`Failed to start ${scriptPath}:`, error.message);
      reject(error);
    });
  });
}

// Run all selected scripts in sequence
async function runScripts() {
  try {
    // TypeScript fixes
    if (options.all || options.typescript) {
      await runScript(SCRIPTS.typescript, buildArgs('typescript'));
    }
    
    // React fixes
    if (options.all || options.react) {
      // Check if the script exists first
      if (fs.existsSync(SCRIPTS.react)) {
        await runScript(SCRIPTS.react, buildArgs('react'));
      } else {
        console.log('React fixer script not found. Running TypeScript fixer with React options instead.');
        await runScript(SCRIPTS.typescript, [...buildArgs('typescript'), '--react-types', '--components']);
      }
    }
    
    // Import fixes
    if (options.all || options.imports) {
      // Check if the script exists first
      if (fs.existsSync(SCRIPTS.imports)) {
        await runScript(SCRIPTS.imports, buildArgs('imports'));
      } else {
        console.log('Import fixer script not found. Running TypeScript fixer with import options instead.');
        await runScript(SCRIPTS.typescript, [...buildArgs('typescript'), '--imports']);
      }
    }
    
    // Dependency fixes
    if (options.all || options.dependencies) {
      await runScript(SCRIPTS.dependencies, options.dryRun ? ['--dry-run'] : []);
    }
    
    console.log('\nAll fixes completed successfully!');
  } catch (error) {
    console.error('\nFix process failed:', error.message);
    process.exit(1);
  }
}

// Start running scripts
runScripts();