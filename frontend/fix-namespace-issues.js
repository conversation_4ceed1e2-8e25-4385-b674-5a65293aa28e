const fs = require('fs');
const path = require('path');

// Function to fix namespace issues
function fixNamespaceIssues(filePath) {
  console.log(`Fixing namespace issues in ${filePath}`);
  
  try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Fix import statements
    if (!content.includes("import { FC, ReactNode") && 
        (content.includes(": FC") || content.includes(": ReactNode"))) {
      
      // Add import for React types if not already present
      if (content.includes("import React from 'react';")) {
        content = content.replace(
          "import React from 'react';",
          "import React from 'react';\nimport { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from '../types/react';"
        );
      } else if (content.includes("import React,")) {
        content = content.replace(
          /import React,\s*{([^}]*)}/,
          "import React, {$1} from 'react';\nimport { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from '../types/react';"
        );
      } else if (content.includes("import { React")) {
        content = content.replace(
          /import { React([^}]*) } from 'react';/,
          "import { React$1 } from 'react';\nimport { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from '../types/react';"
        );
      } else if (!content.includes("import") && !content.includes("from 'react'")) {
        // If no React import at all, add it at the top
        content = "import React from 'react';\nimport { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from '../types/react';\n\n" + content;
      }
    }
    
    // Write the file
    fs.writeFileSync(filePath, content, 'utf8');
    
    console.log(`Fixed namespace issues in ${filePath}`);
  } catch (error) {
    console.error(`Error fixing namespace issues in ${filePath}:`, error);
  }
}

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      processDirectory(filePath);
    } else if (stats.isFile() && (filePath.endsWith('.tsx') || filePath.endsWith('.ts'))) {
      // Check if the file has namespace issues
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes("TS2709: Cannot use namespace") || 
          content.includes(": FC") || 
          content.includes(": ReactNode") ||
          content.includes(": ChangeEvent") ||
          content.includes(": MouseEvent") ||
          content.includes(": SyntheticEvent")) {
        fixNamespaceIssues(filePath);
      }
    }
  }
}

// Start processing from the src directory
processDirectory('./src');

console.log('Done fixing namespace issues!');
