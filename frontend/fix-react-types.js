const fs = require('fs');
const path = require('path');

// Function to fix React types
function fixReactTypes(filePath) {
  console.log(`Fixing React types in ${filePath}`);
  
  try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Remove duplicate imports
    const duplicateImports = [
      /import\s+{\s*FC,\s*ReactNode\s*}\s+from\s+['"][^'"]+['"];?\n/g,
      /import\s+{\s*FC,\s*ReactNode,\s*ChangeEvent,\s*MouseEvent,\s*SyntheticEvent\s*}\s+from\s+['"][^'"]+['"];?\n/g,
      /import\s+{\s*FC,\s*ReactNode,\s*SyntheticEvent\s*}\s+from\s+['"][^'"]+['"];?\n/g
    ];
    
    for (const pattern of duplicateImports) {
      content = content.replace(pattern, '');
    }
    
    // Replace React.FC with FC
    content = content.replace(/React\.FC/g, 'FC');
    
    // Replace React.ReactNode with ReactNode
    content = content.replace(/React\.ReactNode/g, 'ReactNode');
    
    // Replace React.ChangeEvent with ChangeEvent
    content = content.replace(/React\.ChangeEvent/g, 'ChangeEvent');
    
    // Replace React.MouseEvent with MouseEvent
    content = content.replace(/React\.MouseEvent/g, 'MouseEvent');
    
    // Replace React.SyntheticEvent with SyntheticEvent
    content = content.replace(/React\.SyntheticEvent/g, 'SyntheticEvent');
    
    // Add imports if they don't exist
    if (content.includes('FC') || 
        content.includes('ReactNode') || 
        content.includes('ChangeEvent') || 
        content.includes('MouseEvent') || 
        content.includes('SyntheticEvent')) {
      
      // Check if we already have the import
      if (!content.includes('import { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent }')) {
        // Add the import at the top, after any existing React import
        if (content.includes('import React')) {
          content = content.replace(
            /import React[^;]*;/,
            'import React from \'react\';\nimport { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from \'react\';'
          );
        } else {
          // No React import, add both
          content = 'import React from \'react\';\nimport { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from \'react\';\n\n' + content;
        }
      }
    }
    
    // Fix TabPanel issues
    if (content.includes('<TabPanel') && !content.includes('import { TabPanel }')) {
      if (content.includes('import { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from \'react\';')) {
        content = content.replace(
          'import { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from \'react\';',
          'import { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from \'react\';\nimport { TabPanel } from \'./TabsNavigation\';'
        );
      } else if (content.includes('import React from \'react\';')) {
        content = content.replace(
          'import React from \'react\';',
          'import React from \'react\';\nimport { TabPanel } from \'./TabsNavigation\';'
        );
      }
    }
    
    // Fix PlayArrow issues
    if (content.includes('<PlayArrow') && !content.includes('import PlayArrow')) {
      if (content.includes('import { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from \'react\';')) {
        content = content.replace(
          'import { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from \'react\';',
          'import { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from \'react\';\nimport PlayArrow from \'@mui/icons-material/PlayArrow\';'
        );
      } else if (content.includes('import React from \'react\';')) {
        content = content.replace(
          'import React from \'react\';',
          'import React from \'react\';\nimport PlayArrow from \'@mui/icons-material/PlayArrow\';'
        );
      }
    }
    
    // Clean up empty lines
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    // Write the file
    fs.writeFileSync(filePath, content, 'utf8');
    
    console.log(`Fixed React types in ${filePath}`);
  } catch (error) {
    console.error(`Error fixing React types in ${filePath}:`, error);
  }
}

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      processDirectory(filePath);
    } else if (stats.isFile() && (filePath.endsWith('.tsx') || filePath.endsWith('.ts'))) {
      // Check if the file has React type issues
      const content = fs.readFileSync(filePath, 'utf8');
      
      if (content.includes('React.FC') || 
          content.includes('React.ReactNode') || 
          content.includes('React.ChangeEvent') || 
          content.includes('React.MouseEvent') || 
          content.includes('React.SyntheticEvent') ||
          (content.includes('<TabPanel') && !content.includes('import { TabPanel }')) ||
          (content.includes('<PlayArrow') && !content.includes('import PlayArrow'))) {
        fixReactTypes(filePath);
      }
    }
  }
}

// Start processing from the src directory
processDirectory('./src');

console.log('Done fixing React types!');
