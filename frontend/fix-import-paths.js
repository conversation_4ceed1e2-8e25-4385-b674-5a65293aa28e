const fs = require('fs');
const path = require('path');

// Function to calculate the relative path to the types directory
function calculateRelativePath(filePath) {
  const fileDir = path.dirname(filePath);
  const typesDir = path.join(process.cwd(), 'src', 'types');
  
  // Calculate the relative path from fileDir to typesDir
  const relativePath = path.relative(fileDir, typesDir);
  
  // Convert to posix path (forward slashes)
  const posixPath = relativePath.replace(/\\/g, '/');
  
  // Ensure the path starts with './'
  return posixPath.startsWith('.') ? posixPath : `./${posixPath}`;
}

// Function to fix import paths
function fixImportPaths(filePath) {
  console.log(`Fixing import paths in ${filePath}`);
  
  try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Calculate the correct relative path
    const relativePath = calculateRelativePath(filePath);
    
    // Fix import paths
    if (content.includes("from '../types/react'") || 
        content.includes("from '../../types/react'") || 
        content.includes("from '../../../types/react'") || 
        content.includes("from '../../../../types/react'")) {
      
      // Replace all occurrences of incorrect paths
      content = content.replace(/from '\.\.\/types\/react'/g, `from '${relativePath}/react'`);
      content = content.replace(/from '\.\.\/\.\.\/types\/react'/g, `from '${relativePath}/react'`);
      content = content.replace(/from '\.\.\/\.\.\/\.\.\/types\/react'/g, `from '${relativePath}/react'`);
      content = content.replace(/from '\.\.\/\.\.\/\.\.\/\.\.\/types\/react'/g, `from '${relativePath}/react'`);
      
      // Write the file
      fs.writeFileSync(filePath, content, 'utf8');
      
      console.log(`Fixed import paths in ${filePath}`);
    }
  } catch (error) {
    console.error(`Error fixing import paths in ${filePath}:`, error);
  }
}

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      processDirectory(filePath);
    } else if (stats.isFile() && (filePath.endsWith('.tsx') || filePath.endsWith('.ts'))) {
      // Check if the file has import path issues
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes("from '../types/react'") || 
          content.includes("from '../../types/react'") || 
          content.includes("from '../../../types/react'") || 
          content.includes("from '../../../../types/react'")) {
        fixImportPaths(filePath);
      }
    }
  }
}

// Start processing from the src directory
processDirectory('./src');

console.log('Done fixing import paths!');
