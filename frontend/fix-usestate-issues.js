const fs = require('fs');
const path = require('path');

// Function to fix useState issues
function fixUseStateIssues(filePath) {
  console.log(`Fixing useState issues in ${filePath}`);
  
  try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Add React import if not present
    if (!content.includes('import React') && !content.includes('from \'react\'')) {
      content = `import React from 'react';\n${content}`;
    }
    
    // Fix useState not found
    if (content.includes('useState<') && !content.includes('import { useState')) {
      // Add useState import
      if (content.includes('import React from \'react\';')) {
        content = content.replace(
          'import React from \'react\';',
          'import React, { useState, useEffect } from \'react\';'
        );
      } else if (content.includes('import React,')) {
        // Check if useState is already in the import
        if (!content.includes('useState')) {
          content = content.replace(
            /import React, {([^}]*)}/,
            'import React, {$1, useState, useEffect}'
          );
        }
      }
    }
    
    // Fix useState type parameter issues
    content = content.replace(/useState<([^>]+)>\(([^)]+)\)/g, 'React.useState($2)');
    
    // Write the file
    fs.writeFileSync(filePath, content, 'utf8');
    
    console.log(`Fixed useState issues in ${filePath}`);
  } catch (error) {
    console.error(`Error fixing useState issues in ${filePath}:`, error);
  }
}

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      processDirectory(filePath);
    } else if (stats.isFile() && (filePath.endsWith('.tsx') || filePath.endsWith('.ts'))) {
      // Check if the file has useState issues
      const content = fs.readFileSync(filePath, 'utf8');
      
      if (content.includes('useState<') || 
          (content.includes('useState(') && !content.includes('import { useState'))) {
        fixUseStateIssues(filePath);
      }
    }
  }
}

// Start processing from the src directory
processDirectory('./src');

console.log('Done fixing useState issues!');
