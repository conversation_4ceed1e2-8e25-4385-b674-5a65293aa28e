#!/usr/bin/env node

/**
 * Comprehensive TypeScript Language Service Crash Fix Script
 *
 * This script fixes the most common TypeScript issues in React components:
 * 1. Fixes React.React.useState() duplication issues
 * 2. Adds proper typing to useState hooks to fix never[] issues
 * 3. Fixes type issues with setters in useStates
 * 4. Corrects MUI Chip icon typing issues
 * 5. Adds missing type annotations to parameters
 */

const fs = require("fs");
const path = require("path");
const glob = require("glob");

// Utility function to display progress
const logProgress = (message) => {
  console.log(`[Fix Script] ${message}`);
};

// 1. Find all component files
logProgress("Scanning for component files...");
const componentFiles = glob.sync("src/components/**/*.{tsx,ts}", {
  cwd: process.cwd(),
});
logProgress(`Found ${componentFiles.length} component files to process`);

// Track statistics
let stats = {
  filesProcessed: 0,
  filesModified: 0,
  fixedReactReact: 0,
  fixedNeverArrays: 0,
  fixedSetStateActions: 0,
  fixedChipIcons: 0,
  fixedImplicitAny: 0,
  fixedNullableSetters: 0,
};

// Process each component file
componentFiles.forEach((file) => {
  const filePath = path.resolve(process.cwd(), file);
  stats.filesProcessed++;

  try {
    let content = fs.readFileSync(filePath, "utf8");
    let modified = false;

    // 1. Fix React.React.useState issue (common issue in many files)
    if (content.includes("React.React.")) {
      content = content.replace(/React\.React\./g, "React.");
      modified = true;
      stats.fixedReactReact++;
    }

    // 2. Fix never[] issues by adding proper typing to useState
    if (content.includes("useState([])")) {
      content = content.replace(/useState\(\[\]\)/g, "useState<any[]>([])");
      modified = true;
      stats.fixedNeverArrays++;
    }

    // 3. Fix useState(null) typing issues
    if (content.includes("useState(null)")) {
      content = content.replace(
        /useState\(null\)/g,
        "useState<string | null>(null)"
      );
      modified = true;
      stats.fixedNullableSetters++;
    }

    // 4. Fix useState<never[]> typing issues
    if (content.includes("useState<never[]>")) {
      content = content.replace(/useState<never\[\]>/g, "useState<any[]>");
      modified = true;
      stats.fixedNeverArrays++;
    }

    // 5. Fix MUI Chip icon issues by wrapping icon generators in React.createElement
    const chipIconPattern = /icon={get[A-Za-z]+Icon\(\)}/g;
    if (chipIconPattern.test(content)) {
      content = content.replace(chipIconPattern, (match) => {
        // Extract the icon function call, e.g. getStatusIcon()
        const functionCall = match.match(/get[A-Za-z]+Icon\(\)/)[0];
        return `icon={React.createElement(${functionCall})}`;
      });
      modified = true;
      stats.fixedChipIcons++;
    }

    // 6. Fix implicit any types by adding explicit type annotations
    const implicitAnyPattern = /\(\s*([a-zA-Z0-9_]+)\s*\)\s*=>/g;
    if (implicitAnyPattern.test(content)) {
      content = content.replace(implicitAnyPattern, (match, paramName) => {
        // A simple fix that adds a type annotation to the parameter
        return `(${paramName}: any) =>`;
      });
      modified = true;
      stats.fixedImplicitAny++;
    }

    // 7. Fix parameterized map functions with implicit any
    const mapImplicitAnyPattern = /\.map\(\s*\(\s*([a-zA-Z0-9_]+)\s*\)\s*=>/g;
    if (mapImplicitAnyPattern.test(content)) {
      content = content.replace(mapImplicitAnyPattern, (match, paramName) => {
        return `.map((${paramName}: any) =>`;
      });
      modified = true;
      stats.fixedImplicitAny++;
    }

    // 8. Fix parameterized map functions with multiple parameters
    const mapMultiParamPattern =
      /\.map\(\s*\(\s*([a-zA-Z0-9_]+)\s*,\s*([a-zA-Z0-9_]+)\s*\)\s*=>/g;
    if (mapMultiParamPattern.test(content)) {
      content = content.replace(
        mapMultiParamPattern,
        (match, param1, param2) => {
          return `.map((${param1}: any, ${param2}: number) =>`;
        }
      );
      modified = true;
      stats.fixedImplicitAny++;
    }

    // 9. Fix setStateAction issues with primitive types (common for string, number, boolean values)
    const setStatePattern =
      /set([A-Z][a-zA-Z0-9_]+)\s*\(\s*([a-zA-Z0-9_'"]+)\s*\)/g;
    const setStateCallsMatches = [...content.matchAll(setStatePattern)];
    for (const match of setStateCallsMatches) {
      const [fullMatch, stateName, setValue] = match;

      // Don't replace if already a function parameter
      if (setValue.includes("=>")) continue;

      // Check if this is likely a primitive value being passed to a non-primitive state
      const stateNameLower = stateName.toLowerCase();
      if (
        // Only apply this fix if we have state hooks that commonly get type errors
        (stateNameLower.includes("error") ||
          stateNameLower.includes("selected") ||
          stateNameLower.includes("data") ||
          stateNameLower.includes("response")) &&
        // Only when passing primitives (strings, numbers, booleans)
        (setValue.startsWith('"') ||
          setValue.startsWith("'") ||
          ["true", "false", "null"].includes(setValue) ||
          !isNaN(parseInt(setValue)))
      ) {
        // Replace with a function form to avoid setState type issues
        const replacement = `set${stateName}(() => ${setValue})`;
        content = content.replace(fullMatch, replacement);
        modified = true;
        stats.fixedSetStateActions++;
      }
    }

    // Save changes if modified
    if (modified) {
      fs.writeFileSync(filePath, content, "utf8");
      stats.filesModified++;
      logProgress(`Fixed issues in ${file}`);
    }
  } catch (error) {
    console.error(`Error processing ${file}:`, error);
  }
});

// Print statistics
console.log("\n=== Fix Script Statistics ===");
console.log(`Files Processed: ${stats.filesProcessed}`);
console.log(`Files Modified: ${stats.filesModified}`);
console.log(`React.React duplications fixed: ${stats.fixedReactReact}`);
console.log(`never[] array types fixed: ${stats.fixedNeverArrays}`);
console.log(`SetStateAction type issues fixed: ${stats.fixedSetStateActions}`);
console.log(`MUI Chip icon issues fixed: ${stats.fixedChipIcons}`);
console.log(`Implicit any types fixed: ${stats.fixedImplicitAny}`);
console.log(`Nullable state setters fixed: ${stats.fixedNullableSetters}`);
console.log("================================");
