#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to standardize on Yarn by replacing npm references in various files
 */

const fs = require("fs");
const path = require("path");
const { promisify } = require("util");
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const glob = promisify(require("glob"));

// Files to ignore (already updated)
const ignoredFiles = [
  "fix-frontend.sh",
  "fix-dependencies.sh",
  "FRONTEND_FIXES_GUIDE.md",
  "package-lock.json",
];

// Function to check if a file should be processed
function shouldProcessFile(filePath) {
  for (const ignore of ignoredFiles) {
    if (filePath.includes(ignore)) {
      return false;
    }
  }
  return true;
}

// Function to replace npm commands with yarn commands
function replaceNpmWithYarn(content) {
  // Replace npm commands with yarn equivalents
  content = content.replace(/yarn install/g, "yarn install");
  content = content.replace(/yarn add /g, "yarn add ");
  content = content.replace(/yarn remove/g, "yarn remove");
  content = content.replace(/yarn /g, "yarn ");
  content = content.replace(/yarn test/g, "yarn test");
  content = content.replace(/yarn start/g, "yarn start");

  // Replace npx commands with yarn equivalents
  content = content.replace(/yarn tsc/g, "yarn tsc");
  content = content.replace(/yarn webpack/g, "yarn webpack");
  content = content.replace(/yarn eslint/g, "yarn eslint");

  return content;
}

// Main function to process files
async function processFiles() {
  console.log("Searching for files with npm references...");

  // Find all files that might contain npm references
  const markdownFiles = await glob(
    "/Users/<USER>/Desktop/Mexel/**/*.md"
  );
  const shellScripts = await glob(
    "/Users/<USER>/Desktop/Mexel/**/*.sh"
  );
  const dockerfiles = await glob(
    "/Users/<USER>/Desktop/Mexel/**/Dockerfile"
  );
  const jsFiles = await glob("/Users/<USER>/Desktop/Mexel/**/*.js");

  const allFiles = [
    ...markdownFiles,
    ...shellScripts,
    ...dockerfiles,
    ...jsFiles,
  ];

  let modifiedCount = 0;

  // Process each file
  for (const filePath of allFiles) {
    if (!shouldProcessFile(filePath)) {
      console.log(`Skipping ${filePath} (already processed)`);
      continue;
    }

    try {
      const content = await readFile(filePath, "utf8");

      // Check if file contains npm references
      if (content.includes("npm ") || content.includes("npx ")) {
        console.log(`Found npm references in ${filePath}`);

        // Replace npm with yarn
        const updatedContent = replaceNpmWithYarn(content);

        // Only write if content changed
        if (content !== updatedContent) {
          await writeFile(filePath, updatedContent, "utf8");
          console.log(`✅ Updated ${filePath} to use yarn`);
          modifiedCount++;
        }
      }
    } catch (error) {
      console.error(`Error processing ${filePath}:`, error.message);
    }
  }

  console.log(`\nTotal files updated: ${modifiedCount}`);
}

// Run the script
processFiles().catch((error) => {
  console.error("Error:", error);
  process.exit(1);
});
