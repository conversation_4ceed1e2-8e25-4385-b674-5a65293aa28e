#!/bin/bash

# Script to fix React hook imports in TypeScript files

# Create a backup directory
mkdir -p ./backups

# Function to add React hook imports
fix_react_hooks() {
  echo "Fixing React hook imports in $1"
  # Backup the file
  cp "$1" "./backups/$(basename "$1").bak"
  
  # Check if the file uses useState or useEffect without importing them
  if grep -q "useState(" "$1" || grep -q "useEffect(" "$1"; then
    # Check if React is already imported
    if grep -q "import React" "$1"; then
      # Add hooks to existing React import
      sed -i '' 's/import React/import React, { useState, useEffect }/g' "$1"
    elif grep -q "import { React" "$1"; then
      # Add hooks to existing React import with destructuring
      sed -i '' 's/import { React/import { React, useState, useEffect/g' "$1"
    else
      # Add new import for React hooks
      sed -i '' '1s/^/import { useState, useEffect } from '"'"'react'"'"';\n/' "$1"
    fi
  fi
}

# Function to fix FC and ReactNode imports
fix_react_types() {
  echo "Fixing React type imports in $1"
  # Backup the file
  cp "$1" "./backups/$(basename "$1").bak"
  
  # Check if the file uses FC or ReactNode
  if grep -q "FC" "$1" || grep -q "ReactNode" "$1" || grep -q "ChangeEvent" "$1" || grep -q "MouseEvent" "$1" || grep -q "SyntheticEvent" "$1"; then
    # Add import for React types
    if ! grep -q "import { FC, ReactNode" "$1"; then
      # Add import at the top of the file
      sed -i '' '1s/^/import { FC, ReactNode, ChangeEvent, MouseEvent, SyntheticEvent } from '"'"'../types/react'"'"';\n/' "$1"
    fi
  fi
}

# Find all TypeScript files that use React hooks
find ./src -type f -name "*.tsx" -exec grep -l "useState\|useEffect" {} \; | while read -r file; do
  fix_react_hooks "$file"
done

# Find all TypeScript files that use React types
find ./src -type f -name "*.tsx" -exec grep -l "FC\|ReactNode\|ChangeEvent\|MouseEvent\|SyntheticEvent" {} \; | while read -r file; do
  fix_react_types "$file"
done

echo "Done fixing React hook and type imports!"
