const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Function to fix TabsNavigation import paths
function fixTabsNavigationImports(filePath) {
  console.log(`Fixing TabsNavigation imports in ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix 1: Fix import paths for TabsNavigation
  if (content.includes("import { TabPanel, a11yProps } from './common/TabsNavigation';")) {
    // Determine the correct relative path based on the file location
    const relativePath = getRelativePathToCommon(filePath);
    content = content.replace(
      "import { TabPanel, a11yProps } from './common/TabsNavigation';",
      `import { TabPanel, a11yProps } from '${relativePath}/TabsNavigation';`
    );
    modified = true;
  }

  // Fix 2: Fix component declarations with syntax errors
  if (content.includes("(DataTabProps)")) {
    content = content.replace(
      /const (\w+)\(DataTabProps\) = \(\{ ([^}]+) \}\) =>/g,
      "const $1 = ({ $2 }: DataTabProps) =>"
    );
    modified = true;
  }

  // Save the modified file
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed TabsNavigation imports in ${filePath}`);
  } else {
    console.log(`No TabsNavigation imports to fix in ${filePath}`);
  }
}

// Function to determine the correct relative path to the common directory
function getRelativePathToCommon(filePath) {
  const fileDir = path.dirname(filePath);
  const commonDir = '/Users/<USER>/Desktop/Mexel/frontend/src/components/common';
  
  // Calculate the relative path from fileDir to commonDir
  const relativePath = path.relative(fileDir, commonDir);
  
  // If the relative path is empty, it means we're already in the common directory
  if (!relativePath) {
    return '.';
  }
  
  // Make sure the path starts with ./ or ../
  return relativePath.startsWith('.') ? relativePath : `./${relativePath}`;
}

// Find all TypeScript files in the src directory
const files = glob.sync('/Users/<USER>/Desktop/Mexel/frontend/src/**/*.{ts,tsx}');

// Fix TabsNavigation imports in each file
files.forEach(fixTabsNavigationImports);

console.log('Done fixing TabsNavigation imports!');
