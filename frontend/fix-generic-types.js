const fs = require('fs');
const path = require('path');

// Function to fix generic type issues
function fixGenericTypes(filePath) {
  console.log(`Fixing generic types in ${filePath}`);
  
  try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Fix MouseEvent<HTMLElement>
    content = content.replace(/MouseEvent<HTMLElement>/g, 'MouseEvent<HTMLElement>');
    
    // Fix ChangeEvent<HTMLInputElement>
    content = content.replace(/ChangeEvent<HTMLInputElement>/g, 'ChangeEvent<HTMLInputElement>');
    
    // Fix ChangeEvent<HTMLSelectElement>
    content = content.replace(/ChangeEvent<HTMLSelectElement>/g, 'ChangeEvent<HTMLSelectElement>');
    
    // Fix ChangeEvent<HTMLTextAreaElement>
    content = content.replace(/ChangeEvent<HTMLTextAreaElement>/g, 'ChangeEvent<HTMLTextAreaElement>');
    
    // Fix FormEvent<HTMLFormElement>
    content = content.replace(/FormEvent<HTMLFormElement>/g, 'FormEvent<HTMLFormElement>');
    
    // Fix KeyboardEvent<HTMLElement>
    content = content.replace(/KeyboardEvent<HTMLElement>/g, 'KeyboardEvent<HTMLElement>');
    
    // Fix FocusEvent<HTMLElement>
    content = content.replace(/FocusEvent<HTMLElement>/g, 'FocusEvent<HTMLElement>');
    
    // Write the file
    fs.writeFileSync(filePath, content, 'utf8');
    
    console.log(`Fixed generic types in ${filePath}`);
  } catch (error) {
    console.error(`Error fixing generic types in ${filePath}:`, error);
  }
}

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      processDirectory(filePath);
    } else if (stats.isFile() && (filePath.endsWith('.tsx') || filePath.endsWith('.ts'))) {
      fixGenericTypes(filePath);
    }
  }
}

// Start processing from the src directory
processDirectory('./src');

console.log('Done fixing generic types!');
