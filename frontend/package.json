{"name": "@mexel/frontend", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@mui/icons-material": "5.15.15", "@mui/material": "5.15.15", "@mui/system": "5.15.15", "react": "18.2.0", "react-dom": "18.2.0", "react-scripts": "5.0.1"}, "devDependencies": {"@playwright/test": "1.52.0", "@types/node": "18.19.14", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "jest-watch-typeahead": "2.2.2", "playwright": "1.52.0", "typescript": "4.9.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "jest --config jest.config.ts", "test:watch": "jest --watch --config jest.config.ts", "test:ci": "jest --ci --coverage --config jest.config.ts", "test:e2e": "yarn playwright test", "test:e2e:install": "yarn playwright install", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "description": "", "main": ".eslintrc.js", "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "jest": {"testPathIgnorePatterns": ["<rootDir>/src/tests/integration.test.ts", "<rootDir>/src/tests/e2e/"]}, "packageManager": "yarn@3.6.3"}