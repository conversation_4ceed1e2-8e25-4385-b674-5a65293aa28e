#!/usr/bin/env node

/**
 * Fix React useState Type Issues
 *
 * This script focuses on fixing the three most common React useState-related issues:
 * 1. Duplicate React namespace references (React.React.useState)
 * 2. Missing types in useState hooks that lead to never[] errors
 * 3. SetStateAction type incompatibilities
 */

const fs = require("fs");
const path = require("path");
const glob = require("glob");

console.log("Starting React useState type fixes...");

// Find all component files
const componentFiles = glob.sync("src/components/**/*.{tsx,ts}", {
  cwd: process.cwd(),
});
console.log(`Found ${componentFiles.length} component files to scan...`);

let fixStats = {
  filesModified: 0,
  reactReactFixed: 0,
  neverArraysFixed: 0,
  nullableStatesFixed: 0,
  setStateActionFixed: 0,
};

componentFiles.forEach((file) => {
  try {
    const filePath = path.resolve(process.cwd(), file);
    let content = fs.readFileSync(filePath, "utf8");
    let modified = false;

    // 1. Fix React.React.useState duplication
    if (content.includes("React.React.")) {
      const originalContent = content;
      content = content.replace(/React\.React\./g, "React.");
      if (originalContent !== content) {
        modified = true;
        fixStats.reactReactFixed++;
      }
    }

    // 2. Fix useState with empty arrays (causes never[] errors)
    if (content.includes("useState([])")) {
      const originalContent = content;
      content = content.replace(/useState\(\[\]\)/g, "useState<any[]>([])");
      if (originalContent !== content) {
        modified = true;
        fixStats.neverArraysFixed++;
      }
    }

    // 3. Fix useState with null (causes type errors with string setters)
    if (content.includes("useState(null)")) {
      const originalContent = content;
      content = content.replace(
        /const\s+\[([a-zA-Z0-9_]+),\s*set([A-Za-z0-9_]+)\]\s*=\s*(?:React\.)?useState\(null\)/g,
        "const [$1, set$2] = React.useState<string | null>(null)"
      );
      if (originalContent !== content) {
        modified = true;
        fixStats.nullableStatesFixed++;
      }
    }

    // 4. Fix incompatible setState calls
    // This looks for error setters that are passed strings directly
    const errorSetterPattern = /setError\((['"].*?['"])\)/g;
    if (errorSetterPattern.test(content)) {
      const originalContent = content;
      content = content.replace(errorSetterPattern, (match, errorMessage) => {
        return `setError(() => ${errorMessage})`;
      });
      if (originalContent !== content) {
        modified = true;
        fixStats.setStateActionFixed++;
      }
    }

    // 5. Fix other setState pattern issues where a primitive is passed to non-primitive state
    // This is more general and may need refinement based on specific codebase patterns
    const primitiveSetterPattern =
      /set(Selected[A-Za-z0-9_]+|Data|Response)\(([^(=][^=]*?)\)/g;
    if (primitiveSetterPattern.test(content)) {
      const originalContent = content;
      content = content.replace(
        primitiveSetterPattern,
        (match, stateVar, value) => {
          // Only transform if it's likely a primitive value, not an object or function
          if (
            value.trim().startsWith('"') ||
            value.trim().startsWith("'") ||
            ["true", "false", "null"].includes(value.trim()) ||
            !isNaN(parseInt(value.trim()))
          ) {
            return `set${stateVar}(() => ${value})`;
          }
          return match; // Return unchanged if not a simple primitive
        }
      );
      if (originalContent !== content) {
        modified = true;
        fixStats.setStateActionFixed++;
      }
    }

    // Save changes if modified
    if (modified) {
      fs.writeFileSync(filePath, content, "utf8");
      fixStats.filesModified++;
      console.log(`Fixed useState issues in ${file}`);
    }
  } catch (error) {
    console.error(`Error processing ${file}:`, error);
  }
});

console.log("\n=== React useState Fix Results ===");
console.log(`Files modified: ${fixStats.filesModified}`);
console.log(`React.React duplications fixed: ${fixStats.reactReactFixed}`);
console.log(`never[] array types fixed: ${fixStats.neverArraysFixed}`);
console.log(`Nullable states fixed: ${fixStats.nullableStatesFixed}`);
console.log(
  `SetStateAction type issues fixed: ${fixStats.setStateActionFixed}`
);
console.log("===================================");
