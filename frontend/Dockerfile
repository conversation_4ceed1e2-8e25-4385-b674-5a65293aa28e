# syntax=docker/dockerfile:1.4
# Stage 1: Development
FROM node:18-alpine as development

# Set working directory
WORKDIR /app

# First copy only package.json
COPY package.json ./

# Enable Corepack for Yarn management
RUN corepack enable

# Install Yarn using corepack (which handles modern Yarn versions correctly)
RUN corepack prepare yarn@3.6.3 --activate

# Create a simple .yarnrc.yml file for this container
RUN echo "nodeLinker: node-modules" > ./.yarnrc.yml

# Disable global cache to avoid permission issues in Docker
RUN yarn config set enableGlobalCache false

# Create an empty yarn.lock if it doesn't exist
RUN [ -f "./yarn.lock" ] || touch yarn.lock

# Copy all files - we'll override .yarnrc.yml later
COPY . .

# Then explicitly override .yarnrc.yml with a simplified version
RUN echo "nodeLinker: node-modules" > ./.yarnrc.yml

# Install dependencies
RUN yarn install

# Stage 2: Build
FROM development as build

# Build the app
RUN yarn build

# Stage 3: Production with Nginx
FROM nginx:alpine as production

# Copy the build output
COPY --from=build /app/build /usr/share/nginx/html

# Add custom nginx config
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]

# Development command
FROM development as dev
CMD ["yarn", "start"]
