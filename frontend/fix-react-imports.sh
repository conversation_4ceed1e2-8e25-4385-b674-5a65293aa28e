#!/bin/bash

# Script to fix React imports in TypeScript files

# Create a backup directory
mkdir -p ./backups

# Function to fix duplicate React imports
fix_duplicate_imports() {
  echo "Fixing duplicate React imports in $1"
  # Backup the file
  cp "$1" "./backups/$(basename "$1").bak"
  
  # Remove duplicate import lines for useState and useEffect
  sed -i '' '/^import { useState, useEffect } from '"'"'react'"'"';/d' "$1"
  
  # Add the import at the top if it doesn't exist
  if ! grep -q "import React" "$1" && ! grep -q "import { React" "$1"; then
    sed -i '' '1s/^/import React from '"'"'react'"'"';\n/' "$1"
  fi
}

# Function to fix React type imports
fix_react_type_imports() {
  echo "Fixing React type imports in $1"
  # Backup the file
  cp "$1" "./backups/$(basename "$1").bak"
  
  # Remove any import from '/react'
  sed -i '' 's|from '"'"'/react'"'"'|from "../types/react"|g' "$1"
  
  # Fix duplicate imports from '../types/react'
  awk '!seen[$0]++' "$1" > "$1.tmp" && mv "$1.tmp" "$1"
}

# Find all TypeScript files with duplicate React imports
find ./src -type f -name "*.tsx" -exec grep -l "import { useState, useEffect } from 'react';" {} \; | while read -r file; do
  fix_duplicate_imports "$file"
done

# Find all TypeScript files with React type imports from '/react'
find ./src -type f -name "*.tsx" -exec grep -l "from '/react'" {} \; | while read -r file; do
  fix_react_type_imports "$file"
done

echo "Done fixing React imports!"
