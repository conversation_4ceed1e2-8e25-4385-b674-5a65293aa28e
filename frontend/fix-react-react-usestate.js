const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Function to fix React.React.useState calls
function fixReactReactUseState(filePath) {
  console.log(`Fixing React.React.useState calls in ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix 1: Fix React.React.useState calls
  // Pattern: React.React.useState(...)
  // Replace with: React.useState(...)
  const reactReactUseStateRegex = /React\.React\.(useState|useEffect|useRef|useCallback|useMemo|useContext|useReducer|useImperativeHandle|useLayoutEffect|useDebugValue)/g;
  content = content.replace(reactReactUseStateRegex, (match, hookName) => {
    modified = true;
    return `React.${hookName}`;
  });

  // Save the modified file
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed React.React.useState calls in ${filePath}`);
  } else {
    console.log(`No React.React.useState calls to fix in ${filePath}`);
  }
}

// Find all TypeScript files in the src directory
const files = glob.sync('/Users/<USER>/Desktop/Mexel/frontend/src/**/*.{ts,tsx}');

// Fix React.React.useState calls in each file
files.forEach(fixReactReactUseState);

console.log('Done fixing React.React.useState calls!');
