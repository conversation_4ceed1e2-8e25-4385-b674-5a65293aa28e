#!/bin/bash

# <PERSON>ript to fix frontend dependencies

echo "Fixing frontend dependencies..."

# Remove node_modules and package-lock.json
echo "Removing node_modules and package-lock.json..."
rm -rf node_modules
rm -f package-lock.json

# Remove any nested frontend directory
echo "Removing nested frontend directory..."
rm -rf frontend

# Install dependencies
echo "Installing dependencies..."
yarn install

echo "Dependencies fixed successfully!"
