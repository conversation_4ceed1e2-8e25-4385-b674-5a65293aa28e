const fs = require('fs');
const path = require('path');

// Function to fix React namespace issues
function fixReactNamespaceIssues(filePath) {
  console.log(`Fixing React namespace issues in ${filePath}`);

  try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');

    // Check if the file imports React
    const hasReactImport = content.includes('import React') || content.includes('import * as React');

    // If the file doesn't import React, add it
    if (!hasReactImport && (
        content.includes(': FC') ||
        content.includes(': ReactNode') ||
        content.includes(': ChangeEvent') ||
        content.includes(': MouseEvent') ||
        content.includes(': SyntheticEvent'))) {
      content = 'import React from \'react\';\n' + content;
    }

    // Fix FC type issues
    content = content.replace(/: FC(?![a-zA-Z<])/g, ': React.FC');
    content = content.replace(/: FC</g, ': React.FC<');

    // Fix ReactNode type issues
    content = content.replace(/: ReactNode(?![a-zA-Z<])/g, ': React.ReactNode');
    content = content.replace(/\?: ReactNode(?![a-zA-Z<])/g, '?: React.ReactNode');

    // Fix ChangeEvent type issues
    content = content.replace(/: ChangeEvent</g, ': React.ChangeEvent<');
    content = content.replace(/event: ChangeEvent(?![a-zA-Z<])/g, 'event: React.ChangeEvent');

    // Fix MouseEvent type issues
    content = content.replace(/: MouseEvent</g, ': React.MouseEvent<');
    content = content.replace(/event: MouseEvent(?![a-zA-Z<])/g, 'event: React.MouseEvent');

    // Fix SyntheticEvent type issues
    content = content.replace(/: SyntheticEvent(?![a-zA-Z<])/g, ': React.SyntheticEvent');
    content = content.replace(/: SyntheticEvent</g, ': React.SyntheticEvent<');
    content = content.replace(/event: SyntheticEvent(?![a-zA-Z<])/g, 'event: React.SyntheticEvent');

    // Fix useState and useEffect calls
    content = content.replace(/useState\(/g, 'React.useState(');
    content = content.replace(/useEffect\(/g, 'React.useEffect(');
    content = content.replace(/useContext\(/g, 'React.useContext(');
    content = content.replace(/useReducer\(/g, 'React.useReducer(');
    content = content.replace(/useCallback\(/g, 'React.useCallback(');
    content = content.replace(/useMemo\(/g, 'React.useMemo(');
    content = content.replace(/useRef\(/g, 'React.useRef(');

    // Write the file
    fs.writeFileSync(filePath, content, 'utf8');

    console.log(`Fixed React namespace issues in ${filePath}`);
  } catch (error) {
    console.error(`Error fixing React namespace issues in ${filePath}:`, error);
  }
}

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);

  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);

    if (stats.isDirectory()) {
      processDirectory(filePath);
    } else if (stats.isFile() && (filePath.endsWith('.tsx') || filePath.endsWith('.ts'))) {
      // Check if the file has React namespace issues
      const content = fs.readFileSync(filePath, 'utf8');

      if (content.includes(': FC') ||
          content.includes(': ReactNode') ||
          content.includes(': ChangeEvent') ||
          content.includes(': MouseEvent') ||
          content.includes(': SyntheticEvent') ||
          content.includes('useState(') ||
          content.includes('useEffect(') ||
          content.includes('useContext(') ||
          content.includes('useReducer(') ||
          content.includes('useCallback(') ||
          content.includes('useMemo(') ||
          content.includes('useRef(')) {
        fixReactNamespaceIssues(filePath);
      }
    }
  }
}

// Start processing from the src directory
processDirectory('./src');

console.log('Done fixing React namespace issues!');
