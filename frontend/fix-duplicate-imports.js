const fs = require('fs');
const path = require('path');

// Function to fix duplicate imports
function fixDuplicateImports(filePath) {
  console.log(`Fixing duplicate imports in ${filePath}`);

  try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');

    // Check for duplicate React imports
    const reactImports = content.match(/import\s+React\s+from\s+['"]react['"];?/g);
    if (reactImports && reactImports.length > 1) {
      // Keep only the first React import
      const firstImport = reactImports[0];
      for (let i = 1; i < reactImports.length; i++) {
        content = content.replace(reactImports[i], '');
      }
    }

    // Check for duplicate FC, ReactNode imports
    const fcImports = content.match(/import\s+{\s*FC\s*(?:,\s*[^}]*)?\s*}\s+from\s+['"]react['"];?/g);
    if (fcImports && fcImports.length > 1) {
      // Combine all FC imports into one
      let combinedImport = 'import { ';
      const importedTypes = new Set();

      for (const imp of fcImports) {
        const typeMatch = imp.match(/import\s+{\s*([^}]*)\s*}\s+from\s+['"]react['"];?/);
        if (typeMatch && typeMatch[1]) {
          const types = typeMatch[1].split(',').map(t => t.trim()).filter(t => t);
          types.forEach(t => importedTypes.add(t));
        }
      }

      combinedImport += Array.from(importedTypes).join(', ');
      combinedImport += ' } from "react";';

      // Replace the first import with the combined one
      content = content.replace(fcImports[0], combinedImport);

      // Remove the rest of the imports
      for (let i = 1; i < fcImports.length; i++) {
        content = content.replace(fcImports[i], '');
      }
    }

    // Check for duplicate imports from types/react
    const typesReactImports = content.match(/import\s+{\s*[^}]*\s*}\s+from\s+['"]\.\.\/?\.\.\/?\.\.\/?\.\.\/?\/?types\/react['"];?/g);
    if (typesReactImports && typesReactImports.length > 0) {
      // Remove all imports from types/react as we're using React directly
      for (const imp of typesReactImports) {
        content = content.replace(imp, '');
      }
    }

    // Fix React import and hooks
    if (content.includes('import React from \'react\';') &&
        content.includes('import { useState, useEffect } from \'react\';')) {
      // Combine the imports
      content = content.replace(
        /import React from 'react';\nimport { useState, useEffect } from 'react';/g,
        "import React, { useState, useEffect } from 'react';"
      );
    }

    // Clean up empty lines
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

    // Write the file
    fs.writeFileSync(filePath, content, 'utf8');

    console.log(`Fixed duplicate imports in ${filePath}`);
  } catch (error) {
    console.error(`Error fixing duplicate imports in ${filePath}:`, error);
  }
}

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);

  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);

    if (stats.isDirectory()) {
      processDirectory(filePath);
    } else if (stats.isFile() && (filePath.endsWith('.tsx') || filePath.endsWith('.ts'))) {
      // Check if the file has duplicate imports
      const content = fs.readFileSync(filePath, 'utf8');

      // Check for duplicate React imports
      const reactImports = content.match(/import\s+React\s+from\s+['"]react['"];?/g);

      // Check for duplicate FC, ReactNode imports
      const fcImports = content.match(/import\s+{\s*FC\s*(?:,\s*[^}]*)?\s*}\s+from\s+['"]react['"];?/g);

      // Check for duplicate imports from types/react
      const typesReactImports = content.match(/import\s+{\s*[^}]*\s*}\s+from\s+['"]\.\.\/?\.\.\/?\.\.\/?\.\.\/?\/?types\/react['"];?/g);

      // Check for separate React and hooks imports
      const hasReactImport = content.includes('import React from \'react\';');
      const hasHooksImport = content.includes('import { useState, useEffect } from \'react\';');

      if ((reactImports && reactImports.length > 1) ||
          (fcImports && fcImports.length > 1) ||
          (typesReactImports && typesReactImports.length > 0) ||
          (hasReactImport && hasHooksImport)) {
        fixDuplicateImports(filePath);
      }
    }
  }
}

// Start processing from the src directory
processDirectory('./src');

console.log('Done fixing duplicate imports!');
