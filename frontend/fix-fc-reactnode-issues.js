const fs = require('fs');
const path = require('path');

// Function to fix FC and ReactNode type issues
function fixFCReactNodeIssues(filePath) {
  console.log(`Fixing FC and ReactNode type issues in ${filePath}`);
  
  try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Fix FC type issues
    content = content.replace(/: FC(?![a-zA-Z])/g, ': React.FC');
    content = content.replace(/: FC</g, ': React.FC<');
    
    // Fix ReactNode type issues
    content = content.replace(/: ReactNode(?![a-zA-Z])/g, ': React.ReactNode');
    content = content.replace(/\?: ReactNode(?![a-zA-Z])/g, '?: React.ReactNode');
    
    // Fix ChangeEvent type issues
    content = content.replace(/: ChangeEvent</g, ': React.ChangeEvent<');
    
    // Fix MouseEvent type issues
    content = content.replace(/: MouseEvent</g, ': React.MouseEvent<');
    
    // Fix SyntheticEvent type issues
    content = content.replace(/: SyntheticEvent(?![a-zA-Z])/g, ': React.SyntheticEvent');
    content = content.replace(/: SyntheticEvent</g, ': React.SyntheticEvent<');
    
    // Write the file
    fs.writeFileSync(filePath, content, 'utf8');
    
    console.log(`Fixed FC and ReactNode type issues in ${filePath}`);
  } catch (error) {
    console.error(`Error fixing FC and ReactNode type issues in ${filePath}:`, error);
  }
}

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      processDirectory(filePath);
    } else if (stats.isFile() && (filePath.endsWith('.tsx') || filePath.endsWith('.ts'))) {
      // Check if the file has FC or ReactNode type issues
      const content = fs.readFileSync(filePath, 'utf8');
      
      if (content.includes(': FC') || 
          content.includes(': ReactNode') || 
          content.includes('?: ReactNode') ||
          content.includes(': ChangeEvent<') ||
          content.includes(': MouseEvent<') ||
          content.includes(': SyntheticEvent')) {
        fixFCReactNodeIssues(filePath);
      }
    }
  }
}

// Start processing from the src directory
processDirectory('./src');

console.log('Done fixing FC and ReactNode type issues!');
