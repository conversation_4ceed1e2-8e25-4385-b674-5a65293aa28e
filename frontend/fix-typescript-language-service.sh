#!/bin/bash

# Comprehensive TypeScript Language Service Fix Script
# This script addresses common TypeScript issues that cause language service crashes
# including React.React.useState duplications, never[] type issues, and more.

echo "=== TypeScript Language Service Crash Recovery ==="
echo "Running comprehensive fixes for TypeScript issues..."

# Get the directory of the script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 1. Create a backup of the current state
BACKUP_DIR="ts-fixes-backup-$(date +%Y%m%d-%H%M%S)"
echo "Creating backup in ./$BACKUP_DIR"
mkdir -p "$BACKUP_DIR"
cp tsconfig.json "$BACKUP_DIR/"
cp -r .vscode "$BACKUP_DIR/" 2>/dev/null || echo "No .vscode directory found"

# 2. Kill any running TypeScript servers
echo "Stopping TypeScript language service..."
pkill -f tsserver || true

# 3. Clear TypeScript server logs
echo "Clearing TypeScript server logs..."
rm -f ~/.config/Code/logs/*/tsserver.log* 2>/dev/null
rm -f ~/.vscode/extensions/typescript-language-features/tsserver.log* 2>/dev/null

# 4. Ensure the correct typescript version is installed
echo "Ensuring correct TypeScript version..."
if ! yarn list typescript | grep -q "4\.9\.5"; then
  echo "Installing TypeScript 4.9.5..."
  yarn install --save-dev typescript@4.9.5
fi

# 5. Update or create proper .vscode/settings.json
echo "Updating VS Code settings for TypeScript performance..."
mkdir -p .vscode
cat > .vscode/settings.json << EOF
{
  "typescript.tsserver.maxTsServerMemory": 8192,
  "typescript.tsserver.useSeparateSyntaxServer": true,
  "typescript.tsserver.experimental.enableProjectDiagnostics": false,
  "typescript.suggest.enabled": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.tsserver.watchOptions": {
    "watchFile": "useFsEvents",
    "watchDirectory": "useFsEvents",
    "fallbackPolling": "dynamicPriority"
  },
  "typescript.disableAutomaticTypeAcquisition": false,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "javascript.suggestionActions.enabled": false
}
EOF

# 6. Update tsconfig.json with optimized settings
echo "Updating tsconfig.json with optimized settings..."
# Backup original
cp tsconfig.json tsconfig.json.bak

# 7. Using node to properly modify JSON with comments
node -e '
const fs = require("fs");
const path = require("path");
const tsconfig = fs.readFileSync("tsconfig.json", "utf8");

try {
  // Using regex to handle JSON with comments
  let updatedConfig = tsconfig;
  
  // Ensure isolatedModules is true
  if (!tsconfig.includes("isolatedModules")) {
    updatedConfig = updatedConfig.replace(/"compilerOptions"\s*:\s*{/, "\"compilerOptions\": {\n    \"isolatedModules\": true,");
  } else if (tsconfig.includes("isolatedModules\"\\s*:\\s*false")) {
    updatedConfig = updatedConfig.replace(/"isolatedModules"\s*:\s*false/, "\"isolatedModules\": true");
  }
  
  // Add ignoreDeprecations
  if (!tsconfig.includes("ignoreDeprecations")) {
    updatedConfig = updatedConfig.replace(/"compilerOptions"\s*:\s*{/, "\"compilerOptions\": {\n    \"ignoreDeprecations\": \"5.0\",");
  }
  
  // Add useUnknownInCatchVariables: false to avoid type errors
  if (!tsconfig.includes("useUnknownInCatchVariables")) {
    updatedConfig = updatedConfig.replace(/"compilerOptions"\s*:\s*{/, "\"compilerOptions\": {\n    \"useUnknownInCatchVariables\": false,");
  }
  
  fs.writeFileSync("tsconfig.json", updatedConfig);
  console.log("tsconfig.json updated successfully");
} catch (error) {
  console.error("Error updating tsconfig.json:", error);
  process.exit(1);
}'

# 8. Run our specialized TypeScript fix scripts
echo "Running specific TypeScript fix scripts..."

echo "Fixing React.React.useState duplications..."
node fix-react-usestate-types.js

echo "Fixing MUI Chip icon type issues..."
node fix-mui-chip-icons.js

echo "Running comprehensive TypeScript type fixes..."
node fix-typescript-crashes.js

# 9. Clear the TypeScript build info
echo "Clearing TypeScript build cache..."
rm -f tsconfig.tsbuildinfo

# 10. Run the TypeScript compiler to check the results
echo "Running TypeScript compiler to check for remaining errors..."
echo "(This may take a while)"
yarn tsc --noEmit

# 11. Final instructions
echo 
echo "=== Fix Process Completed ==="
echo 
echo "Next steps:"
echo "1. Restart VS Code with problematic extensions disabled:"
echo "   - GitHub.copilot-chat"
echo "   - VisualStudioExptTeam.vscodeintellicode"
echo "   - VisualStudioExptTeam.intellicode-api-usage-examples"
echo "2. Remaining errors should be significantly reduced"
echo "3. If TypeScript server crashes persist, consider:"
echo "   - Increasing memory in .vscode/settings.json"
echo "   - Restarting your computer to clear system memory"
echo "   - Updating VS Code to the latest version"
echo 
echo "Remember to restart VS Code after this script completes!"
