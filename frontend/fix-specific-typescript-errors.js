const fs = require("fs");
const path = require("path");
const glob = require("glob");

console.log("Starting TypeScript error fixing process...");

// Get all component files
const files = glob.sync("src/components/**/*.tsx", { cwd: process.cwd() });
console.log(`Found ${files.length} component files to process.`);

let fixedFiles = 0;

// Process each file
files.forEach((file) => {
  try {
    const filePath = path.resolve(process.cwd(), file);
    let content = fs.readFileSync(filePath, "utf8");
    let modified = false;

    // Fix 1: Fix React import
    if (!content.includes("import React")) {
      content = "import React from 'react';\n" + content;
      modified = true;
    }

    // Fix 2: Replace React.React (double React) usage
    if (content.includes("React.React.")) {
      content = content.replace(/React\.React\./g, "React.");
      modified = true;
    }

    // Fix 3: Fix useState with never[] by adding explicit typing
    const reactStateMatches = content.match(/useState\(\[\]\)/g);
    if (reactStateMatches) {
      content = content.replace(/useState\(\[\]\)/g, "useState<any[]>([])");
      modified = true;
    }

    // Fix 4: Fix useState(null) by adding explicit typing
    if (content.includes("useState(null)")) {
      content = content.replace(/useState\(null\)/g, "useState<any>(null)");
      modified = true;
    }

    // Fix 5: Fix setError with string issues
    if (
      content.includes("setError(") &&
      content.includes("useState<null>(null)")
    ) {
      content = content.replace(
        /useState<null>\(null\)/g,
        "useState<string | null>(null)"
      );
      modified = true;
    }

    // Fix 6: Fix useState with object literals
    if (content.match(/useState\((\s)*{/g)) {
      content = content.replace(/useState\((\s)*{/g, "useState<any>({");
      modified = true;
    }

    // Fix 7: Fix useState with arrays that have initialization data
    const arrayStateMatches = content.match(/useState\(\[[^\]]+\]\)/g);
    if (arrayStateMatches) {
      for (const match of arrayStateMatches) {
        content = content.replace(
          match,
          match.replace("useState(", "useState<any[]>(")
        );
      }
      modified = true;
    }

    // Fix 8: Fix spreads with never types
    if (
      content.includes("...") &&
      (content.includes("never[]") || content.includes(": never"))
    ) {
      content = content.replace(/\.\.\.([\w\d]+)/g, (match, varName) => {
        // Check if this is a spreaded variable with a never type
        if (
          content.includes(`${varName}: never[]`) ||
          content.includes(`${varName}: never`)
        ) {
          return `...(${varName} as any)`;
        }
        return match;
      });
      modified = true;
    }

    // Only write the file if we made changes
    if (modified) {
      fs.writeFileSync(filePath, content, "utf8");
      console.log(`Fixed TypeScript errors in ${file}`);
      fixedFiles++;
    }
  } catch (error) {
    console.error(`Error processing file ${file}:`, error);
  }
});

console.log(`TypeScript error fixing complete! Fixed ${fixedFiles} files.`);
console.log(
  'You may still need to manually fix some type errors. Run "yarn tsc --noEmit" to check remaining errors.'
);
