const fs = require("fs");
const path = require("path");

console.log("Starting to fix TypeScript type assertion errors...");

// List of files that have errors
const problemFiles = [
  "src/components/agent/AgentMonitorDebug.tsx",
  "src/components/agent/AgentMonitorExample.tsx",
  "src/components/agent/AgentMonitorFixed.tsx",
  "src/components/AgentMonitor.tsx",
  "src/components/ContentDashboard.tsx",
  "src/components/dashboard/charts/AgentPerformanceChart.tsx",
  "src/components/dashboard/sections/AgentRealTimeStatus.tsx",
  "src/components/EmailAnalyticsDashboard.tsx",
  "src/components/lead/LeadProcessingMonitor.tsx",
  "src/components/LinkedInMessageGenerator.tsx",
  "src/components/SEOInsightsDashboard/index.tsx",
  "src/components/TenderDashboard.tsx",
];

let fixedFiles = 0;

// Manual fixes for specific files
function fixAgentMonitorDebug(content) {
  return content.replace(/setTimeout\(\( as any\) =>/, "setTimeout(() =>");
}

function fixAgentMonitorExample(content) {
  return content.replace(/setTimeout\(\( as any\) =>/, "setTimeout(() =>");
}

function fixAgentMonitorFixed(content) {
  return content.replace(/setTimeout\(\( as any\) =>/, "setTimeout(() =>");
}

function fixAgentMonitor(content) {
  return content.replace(
    /setAgents\(\(prev: AgentMetrics\[\] as any\) =>/,
    "setAgents((prev: AgentMetrics[]) =>"
  );
}

function fixContentDashboard(content) {
  let modified = content;
  modified = modified.replace(
    /setScheduleRequest\(\(prev as any\) =>/g,
    "setScheduleRequest((prev) =>"
  );
  modified = modified.replace(
    /content\.filter\(\(item as any\) =>/g,
    "content.filter((item) =>"
  );
  modified = modified.replace(
    /schedules\.filter\(\(item as any\) =>/g,
    "schedules.filter((item) =>"
  );
  return modified;
}

function fixAgentPerformanceChart(content) {
  let modified = content;
  modified = modified.replace(/now\.getHours\( as any\)/g, "now.getHours()");
  modified = modified.replace(/now\.getDate\( as any\)/g, "now.getDate()");
  return modified;
}

function fixAgentRealTimeStatus(content) {
  let modified = content;
  modified = modified.replace(/\(prevAgents as any\) =>/g, "(prevAgents) =>");
  modified = modified.replace(/\(prevUpdates as any\) =>/g, "(prevUpdates) =>");
  return modified;
}

function fixEmailAnalyticsDashboard(content) {
  return content.replace(/setMockData\( as any\);/g, "setMockData(mockData);");
}

function fixLeadProcessingMonitor(content) {
  return content.replace(/\(prev as any\) =>/g, "(prev) =>");
}

function fixLinkedInMessageGenerator(content) {
  let modified = content;
  modified = modified.replace(
    /setTimeout\(\( as any\) =>/g,
    "setTimeout(() =>"
  );
  modified = modified.replace(
    /keywords\.filter\(\(keyword as any\) =>/g,
    "keywords.filter((keyword) =>"
  );
  return modified;
}

function fixSEOInsightsDashboard(content) {
  let modified = content;
  modified = modified.replace(
    /\(prevRecommendations as any\) =>/g,
    "(prevRecommendations) =>"
  );
  modified = modified.replace(/\(prevReports as any\) =>/g, "(prevReports) =>");
  modified = modified.replace(/\(prevConfig as any\) =>/g, "(prevConfig) =>");
  return modified;
}

function fixTenderDashboard(content) {
  let modified = content;
  modified = modified.replace(/\(prev as any\) =>/g, "(prev) =>");
  return modified;
}

// Process each file with the appropriate fix function
for (const file of problemFiles) {
  try {
    console.log(`Processing ${file}...`);
    const filePath = path.join(process.cwd(), file);

    if (!fs.existsSync(filePath)) {
      console.error(`File not found: ${filePath}`);
      continue;
    }

    let content = fs.readFileSync(filePath, "utf8");
    let modified = false;
    let newContent = content;

    // Apply the appropriate fix function based on the file
    if (file.includes("AgentMonitorDebug")) {
      newContent = fixAgentMonitorDebug(content);
      modified = newContent !== content;
    } else if (file.includes("AgentMonitorExample")) {
      newContent = fixAgentMonitorExample(content);
      modified = newContent !== content;
    } else if (file.includes("AgentMonitorFixed")) {
      newContent = fixAgentMonitorFixed(content);
      modified = newContent !== content;
    } else if (file.includes("AgentMonitor.tsx")) {
      newContent = fixAgentMonitor(content);
      modified = newContent !== content;
    } else if (file.includes("ContentDashboard")) {
      newContent = fixContentDashboard(content);
      modified = newContent !== content;
    } else if (file.includes("AgentPerformanceChart")) {
      newContent = fixAgentPerformanceChart(content);
      modified = newContent !== content;
    } else if (file.includes("AgentRealTimeStatus")) {
      newContent = fixAgentRealTimeStatus(content);
      modified = newContent !== content;
    } else if (file.includes("EmailAnalyticsDashboard")) {
      newContent = fixEmailAnalyticsDashboard(content);
      modified = newContent !== content;
    } else if (file.includes("LeadProcessingMonitor")) {
      newContent = fixLeadProcessingMonitor(content);
      modified = newContent !== content;
    } else if (file.includes("LinkedInMessageGenerator")) {
      newContent = fixLinkedInMessageGenerator(content);
      modified = newContent !== content;
    } else if (file.includes("SEOInsightsDashboard/index")) {
      newContent = fixSEOInsightsDashboard(content);
      modified = newContent !== content;
    } else if (file.includes("TenderDashboard")) {
      newContent = fixTenderDashboard(content);
      modified = newContent !== content;
    }

    if (modified) {
      fs.writeFileSync(filePath, newContent, "utf8");
      console.log(`✅ Fixed type assertion errors in ${file}`);
      fixedFiles++;
    } else {
      console.log(`⚠️ No changes made to ${file}`);
    }
  } catch (error) {
    console.error(`❌ Error processing file ${file}:`, error);
  }
}

console.log(`\nType assertion fixing complete! Fixed ${fixedFiles} files.`);
console.log('Run "yarn tsc --noEmit" to check remaining errors.');
