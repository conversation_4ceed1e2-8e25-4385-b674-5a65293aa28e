const fs = require("fs");
const path = require("path");
const glob = require("glob");

// Function to fix component declarations
function fixComponentDeclarations(filePath) {
  console.log(`Fixing component declarations in ${filePath}`);

  let content = fs.readFileSync(filePath, "utf8");
  let modified = false;

  // Fix 1: Fix component declarations with syntax errors
  // Pattern: const ComponentName(PropsType) = ({ ... }) => {
  // Replace with: const ComponentName = ({ ... }: PropsType) => {
  const componentDeclarationRegex =
    /const\s+(\w+)(\s*):?\s*\((\w+)\)\s*=\s*\(\{\s*([^}]*)\s*\}\)\s*=>/g;
  content = content.replace(
    componentDeclarationRegex,
    (match, componentName, colon, propsType, params) => {
      modified = true;
      return `const ${componentName} = ({ ${params} }: ${propsType}) =>`;
    }
  );

  // Fix 2: Fix export component declarations with syntax errors
  // Pattern: export const ComponentName(PropsType) = ({ ... }) => {
  // Replace with: export const ComponentName = ({ ... }: PropsType) => {
  const exportComponentDeclarationRegex =
    /export\s+const\s+(\w+)(\s*):?\s*\((\w+)\)\s*=\s*\(\{\s*([^}]*)\s*\}\)\s*=>/g;
  content = content.replace(
    exportComponentDeclarationRegex,
    (match, componentName, colon, propsType, params) => {
      modified = true;
      return `export const ${componentName} = ({ ${params} }: ${propsType}) =>`;
    }
  );

  // Fix 3: Fix component declarations with syntax errors (single line)
  // Pattern: const ComponentName(PropsType) = ({ prop1, prop2 }) => {
  // Replace with: const ComponentName = ({ prop1, prop2 }: PropsType) => {
  const singleLineComponentDeclarationRegex =
    /const\s+(\w+)(\s*):?\s*\((\w+)\)\s*=\s*\(\{([^}]*)\}\)\s*=>/g;
  content = content.replace(
    singleLineComponentDeclarationRegex,
    (match, componentName, colon, propsType, params) => {
      modified = true;
      return `const ${componentName} = ({${params}}: ${propsType}) =>`;
    }
  );

  // Fix 4: Fix export component declarations with syntax errors (single line)
  // Pattern: export const ComponentName(PropsType) = ({ prop1, prop2 }) => {
  // Replace with: export const ComponentName = ({ prop1, prop2 }: PropsType) => {
  const singleLineExportComponentDeclarationRegex =
    /export\s+const\s+(\w+)(\s*):?\s*\((\w+)\)\s*=\s*\(\{([^}]*)\}\)\s*=>/g;
  content = content.replace(
    singleLineExportComponentDeclarationRegex,
    (match, componentName, colon, propsType, params) => {
      modified = true;
      return `export const ${componentName} = ({${params}}: ${propsType}) =>`;
    }
  );

  // Fix 5: Fix component declarations with syntax errors (no props)
  // Pattern: const ComponentName: = () => {
  // Replace with: const ComponentName = () => {
  const noPropsComponentDeclarationRegex = /const\s+(\w+)\s*:\s*=\s*\(\)\s*=>/g;
  content = content.replace(
    noPropsComponentDeclarationRegex,
    (match, componentName) => {
      modified = true;
      return `const ${componentName} = () =>`;
    }
  );

  // Fix 6: Fix export component declarations with syntax errors (no props)
  // Pattern: export const ComponentName: = () => {
  // Replace with: export const ComponentName = () => {
  const noPropsExportComponentDeclarationRegex =
    /export\s+const\s+(\w+)\s*:\s*=\s*\(\)\s*=>/g;
  content = content.replace(
    noPropsExportComponentDeclarationRegex,
    (match, componentName) => {
      modified = true;
      return `export const ${componentName} = () =>`;
    }
  );

  // Fix 7: Fix TenderOpportunities syntax error
  // Pattern: export const TenderOpportunities(TenderOpportunitiesProps) = ({
  // Replace with: export const TenderOpportunities = ({ ... }: TenderOpportunitiesProps) => {
  const tenderOpportunitiesRegex =
    /export\s+const\s+TenderOpportunities\((\w+)\)\s*=\s*\(\{/g;
  content = content.replace(tenderOpportunitiesRegex, (match, propsType) => {
    modified = true;
    return `export const TenderOpportunities = ({`;
  });

  // Fix 8: Fix AITool syntax issues
  // Pattern:   }>({
  // Replace with:   })({
  const aiToolBracketRegex = /\s+}>\({/g;
  content = content.replace(aiToolBracketRegex, (match) => {
    modified = true;
    return "\n  })({";
  });

  // Fix 9: Fix AITool trailing semicolon
  // Pattern: };
  // Replace with: }
  const aiToolSemicolonRegex = /};(\s*)$/g;
  content = content.replace(aiToolSemicolonRegex, (match, whitespace) => {
    modified = true;
    return `}${whitespace}`;
  });

  // Fix 10: Fix TabPanel declaration in TabsNavigation.tsx
  if (filePath.includes("TabsNavigation.tsx")) {
    const tabPanelRegex =
      /export\s+const\s+TabPanel\(\{\s*children\?:\s*any;\s*index:\s*number;\s*value:\s*number;\s*\}\)/g;
    if (tabPanelRegex.test(content)) {
      content = content.replace(
        tabPanelRegex,
        "export const TabPanel = ({ children, index, value }: { children?: any; index: number; value: number; })"
      );
      modified = true;
    }
  }

  // Save the modified file
  if (modified) {
    fs.writeFileSync(filePath, content, "utf8");
    console.log(`Fixed component declarations in ${filePath}`);
  } else {
    console.log(`No component declarations to fix in ${filePath}`);
  }
}

// Find all TypeScript files in the src directory
const files = glob.sync(
  "/Users/<USER>/Desktop/Mexel/frontend/src/**/*.{ts,tsx}"
);

console.log(`Found ${files.length} TypeScript files to process.`);

// Fix component declarations in each file
let fixedFilesCount = 0;
files.forEach((file) => {
  try {
    fixComponentDeclarations(file);
    fixedFilesCount++;
  } catch (error) {
    console.error(`Error processing file ${file}:`, error);
  }
});

console.log(
  `Done fixing component declarations! Processed ${fixedFilesCount} files.`
);
