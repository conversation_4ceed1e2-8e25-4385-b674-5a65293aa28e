/**
 * <PERSON><PERSON><PERSON> to fix TabPanel issues across the codebase
 * 
 * This script:
 * 1. Finds all files that define their own TabPanel component
 * 2. Replaces the import with the shared TabPanel component
 * 3. Removes the duplicate TabPanel definition
 * 4. Updates tab IDs to use the a11yProps helper function
 */

const fs = require('fs');
const path = require('path');

// Define the shared TabPanel import statement
const sharedTabPanelImport = "import { TabPanel, a11yProps } from '../common/TabsNavigation';";
const rootTabPanelImport = "import { TabPanel, a11yProps } from './components/common/TabsNavigation';";

// Define the TabPanel component pattern to find and remove
const tabPanelComponentPattern = /interface\s+TabPanelProps\s*\{[\s\S]*?children\??\s*:\s*ReactNode[\s\S]*?value\s*:\s*number[\s\S]*?\}[\s\S]*?function\s+TabPanel\s*\([\s\S]*?\)\s*\{[\s\S]*?return[\s\S]*?<div[\s\S]*?role="tabpanel"[\s\S]*?>\s*\{[\s\S]*?\}\s*<\/div>[\s\S]*?\}/;

// Define the pattern for fixing React.React.useState
const reactReactPattern = /React\.React\./g;

// Function to fix a file
function fixFile(filePath) {
  console.log(`Fixing file: ${filePath}`);
  
  // Read the file content
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if the file has TabPanel issues
  const hasTabPanelDefinition = tabPanelComponentPattern.test(content);
  const importsTabsNavigation = content.includes("import { TabPanel } from './TabsNavigation'") || 
                               content.includes("import { TabPanel } from '../TabsNavigation'") ||
                               content.includes("import TabPanel from './TabsNavigation'") ||
                               content.includes("import TabPanel from '../TabsNavigation'");
  
  // Fix TabPanel issues
  if (hasTabPanelDefinition || importsTabsNavigation) {
    // Determine the correct import path based on file location
    let importStatement = sharedTabPanelImport;
    
    // For files in the root components directory, use a different import path
    if (filePath.includes('/components/') && !filePath.includes('/components/common/') && 
        !filePath.includes('/components/dashboard/') && !filePath.includes('/components/email/') &&
        !filePath.includes('/components/seo/') && !filePath.includes('/components/linkedin/') &&
        !filePath.includes('/components/reports/') && !filePath.includes('/components/tasks/') &&
        !filePath.includes('/components/ai/')) {
      importStatement = rootTabPanelImport;
    }
    
    // Replace the import statement
    if (importsTabsNavigation) {
      content = content.replace(/import\s+\{\s*TabPanel\s*\}\s*from\s+['"]\.\/TabsNavigation['"];?/g, importStatement);
      content = content.replace(/import\s+\{\s*TabPanel\s*\}\s*from\s+['"]\.\.\/TabsNavigation['"];?/g, importStatement);
      content = content.replace(/import\s+TabPanel\s*from\s+['"]\.\/TabsNavigation['"];?/g, importStatement);
      content = content.replace(/import\s+TabPanel\s*from\s+['"]\.\.\/TabsNavigation['"];?/g, importStatement);
    } else if (content.includes('import React from \'react\';')) {
      // Add the import after React import
      content = content.replace(/import React from ['"]react['"];/, `import React from 'react';\n${importStatement}`);
    } else {
      // Add the import at the top of the file
      content = `${importStatement}\n${content}`;
    }
    
    // Remove the TabPanel component definition
    if (hasTabPanelDefinition) {
      content = content.replace(tabPanelComponentPattern, '');
    }
    
    // Clean up extra blank lines
    content = content.replace(/\n{3,}/g, '\n\n');
    
    // Fix React.React.useState issues
    content = content.replace(reactReactPattern, 'React.');
    
    // Update tab IDs to use a11yProps
    content = content.replace(/id="[^"]*-tab-(\d+)"\s+aria-controls="[^"]*-tabpanel-\1"/g, (match, tabIndex) => {
      return `{...a11yProps(${tabIndex})}`;
    });
    
    // Write the updated content back to the file
    fs.writeFileSync(filePath, content);
    console.log(`Fixed TabPanel issues in ${filePath}`);
  }
}

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      processDirectory(filePath);
    } else if (stats.isFile() && (filePath.endsWith('.tsx') || filePath.endsWith('.ts'))) {
      fixFile(filePath);
    }
  }
}

// Create a backup directory
const backupDir = path.join(__dirname, 'backups');
if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir);
}

// Backup the src directory
const srcDir = path.join(__dirname, 'src');
const backupSrcDir = path.join(backupDir, 'src-' + new Date().toISOString().replace(/:/g, '-'));
fs.mkdirSync(backupSrcDir, { recursive: true });

function copyDirectory(source, destination) {
  const files = fs.readdirSync(source);
  
  for (const file of files) {
    const sourcePath = path.join(source, file);
    const destPath = path.join(destination, file);
    const stats = fs.statSync(sourcePath);
    
    if (stats.isDirectory()) {
      fs.mkdirSync(destPath, { recursive: true });
      copyDirectory(sourcePath, destPath);
    } else {
      fs.copyFileSync(sourcePath, destPath);
    }
  }
}

console.log(`Backing up src directory to ${backupSrcDir}`);
copyDirectory(srcDir, backupSrcDir);

// Start processing from the src directory
console.log('Starting to fix TabPanel issues...');
processDirectory(srcDir);
console.log('Done fixing TabPanel issues!');
