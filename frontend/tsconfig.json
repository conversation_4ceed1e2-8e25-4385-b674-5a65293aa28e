{"extends": "../tsconfig.json", "compilerOptions": {"composite": true, "outDir": "./dist", "typeRoots": ["../node_modules/@types", "./node_modules/@types", "./src/types", "../types"], "types": ["node", "jest"], "lib": ["dom", "dom.iterable", "esnext", "es2015", "es2016", "es2017", "es2018", "es2019", "es2020", "es2021", "webworker"], "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src", "*.ts", "*.js"], "exclude": ["node_modules", "dist"]}