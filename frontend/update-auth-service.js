// Update the AuthService.ts file to work with our unified error handling

import { replace_string_in_file } from '@/utils/fileUtils';

// First update the imports
replace_string_in_file(
  '/src/services/AuthService.ts',
  `import React from 'react';
import axios from "axios";`,
  `import React from 'react';
import axios from "axios";
import { AuthErrorType, handleAuthenticationError } from "./authErrorHandling";`
);

// Update the login method to use our error handling
replace_string_in_file(
  '/src/services/AuthService.ts',
  `  /**
   * Login a user
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const response = await axios.post<ApiSuccessResponse<LoginResponse>>(
        \`\${this.baseUrl}/auth/login\`,
        credentials
      );

      const { token, user } = response.data.data;

      // Store token and user in localStorage
      localStorage.setItem(this.tokenKey, token);
      localStorage.setItem(this.userKey, JSON.stringify(user));

      // Set default Authorization header for all future requests
      axios.defaults.headers.common["Authorization"] = \`Bearer \${token}\`;

      return { token, user };
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    }
  }`,
  `  /**
   * Login a user
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const response = await axios.post<ApiSuccessResponse<LoginResponse>>(
        \`\${this.baseUrl}/auth/login\`,
        credentials
      );

      const { token, user } = response.data.data;

      // Store token and user in localStorage
      localStorage.setItem(this.tokenKey, token);
      localStorage.setItem(this.userKey, JSON.stringify(user));

      // Set default Authorization header for all future requests
      axios.defaults.headers.common["Authorization"] = \`Bearer \${token}\`;

      return { token, user };
    } catch (error: any) {
      console.error("Login failed:", error);
      
      // Use our unified error handling
      handleAuthenticationError(error);
      throw error;
    }
  }`
);

// Add a method to handle token expiration
replace_string_in_file(
  '/src/services/AuthService.ts',
  `  /**
   * Log out the current user
   */
  logout(): void {
    // Remove token and user from localStorage
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);

    // Remove Authorization header
    delete axios.defaults.headers.common["Authorization"];
  }`,
  `  /**
   * Log out the current user
   */
  logout(): void {
    // Remove token and user from localStorage
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);

    // Remove Authorization header
    delete axios.defaults.headers.common["Authorization"];
  }
  
  /**
   * Handle a token expiration or authentication error
   * @param redirectToLogin Whether to redirect to the login page
   */
  handleAuthError(redirectToLogin: boolean = true): void {
    this.logout();
    
    if (redirectToLogin) {
      // Redirect to login page, preserving the current location for a redirect back
      const currentPath = window.location.pathname;
      window.location.href = \`/login?redirect=\${encodeURIComponent(currentPath)}\`;
    }
  }`
);
