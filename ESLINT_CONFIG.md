# ESLint Configuration Guide for Mexel

This document explains the ESLint configuration structure used in the Mexel project.

## Configuration Structure

The project uses a hierarchical ESLint configuration approach:

1. **Base Configuration**: `.eslintrc.json` in the project root
2. **Project-Specific Configurations**:
   - Root: Used for server/utility scripts
   - Frontend: `frontend/.eslintrc.json` (extends the base config)
   - Shared: `shared/.eslintrc.json` (extends the base config)

## Base Configuration (`.eslintrc.json`)

The base configuration provides common settings used across all parts of the project:

- Standard TypeScript linting rules
- ECMAScript 2022 support
- Base plugin configuration
- Common rule overrides for different file types

## Frontend Configuration (`frontend/.eslintrc.json`)

Configures ESLint for the React frontend:

- Extends the base configuration
- Adds React-specific settings (via react-app preset)
- Includes Jest testing configuration
- Contains React and frontend-specific rule overrides

## Shared Configuration (`shared/.eslintrc.json`) 

Configures ESLint for the shared TypeScript code:

- Extends the base configuration
- Uses stricter rules for type checking
- Disallows `console` statements in production code

## Ignore Patterns

The `.eslintignore` file in the project root specifies which files and directories should be ignored by ESLint:

- Build output directories (`dist/`, `build/`)
- Node modules and dependencies
- Generated files (`.js.map`, `.d.ts.map`)
- Backend Python code
- Test results and reports

## Running ESLint

Use these commands to run ESLint:

### Root Project
```bash
# Check for linting issues
yarn lint

# Fix automatically fixable issues
yarn lint:fix

# Lint all packages
yarn lint:all

# Fix issues in all packages
yarn lint:all:fix
```

### Frontend
```bash
# From the frontend directory
yarn lint

# Fix automatically fixable issues
yarn lint:fix

# Lint frontend as part of the whole project
yarn lint:all
```

### Shared Package
```bash
# From the shared directory
yarn lint

# Fix automatically fixable issues
yarn lint:fix

# Lint shared as part of the whole project
yarn lint:all
```

## Customizing ESLint Rules

To customize ESLint rules for a specific package:

1. Edit the `.eslintrc.json` file in the package directory
2. Add or modify rules in the `rules` section
3. Add file-specific overrides in the `overrides` section

Example:
```json
{
  "rules": {
    "no-console": "error"
  },
  "overrides": [
    {
      "files": ["**/*.test.ts"],
      "rules": {
        "no-console": "off"
      }
    }
  ]
}
```

## Key Rules and Settings

The base configuration includes these important rules:

- `@typescript-eslint/no-unused-vars`: Set to warn with patterns to ignore variables starting with underscore
- `@typescript-eslint/explicit-module-boundary-types`: Turned off by default
- `@typescript-eslint/no-explicit-any`: Set to warn in general code, error in shared package
- `no-console`: Warns in general code, errors in shared package

## Best Practices

1. **Run linting before commits**: `yarn lint:all` to check all packages
2. **Fix automatically fixable issues**: Use `yarn lint:all:fix`
3. **Package-specific rules**: Add them to the package's own `.eslintrc.json`
4. **Testing-specific rules**: Add them in the `overrides` section for test files
5. **Add new plugins**: Install and configure them in the root configuration first

## Troubleshooting

If you encounter ESLint errors:

1. Run `yarn lint:fix` to automatically fix simple issues
2. For "Unable to resolve path to module" errors, check your import paths
3. For TypeScript-specific errors, ensure your types are correctly defined
4. If needed, add specific rule exceptions in the appropriate config file