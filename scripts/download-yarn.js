#!/usr/bin/env node

/**
 * Download Yarn Binary Script
 * 
 * This script downloads the Yarn binary for the version specified in .yarnrc.yml
 * It's used to ensure the correct Yarn version is available for the project.
 * 
 * Usage: node scripts/download-yarn.js
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');

// Configuration
const YARN_VERSION = '3.6.3';
const PROJECT_ROOT = path.resolve(__dirname, '..');
const YARN_RELEASES_DIR = path.join(PROJECT_ROOT, '.yarn', 'releases');
const YARN_BINARY_PATH = path.join(YARN_RELEASES_DIR, `yarn-${YARN_VERSION}.cjs`);

/**
 * Create directory if it doesn't exist
 */
function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
  }
}

/**
 * Download a file from URL to destination
 */
function downloadFile(url, destination) {
  return new Promise((resolve, reject) => {
    console.log(`Downloading ${url} to ${destination}...`);
    
    const file = fs.createWriteStream(destination);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${url}: ${response.statusCode} ${response.statusMessage}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded successfully: ${destination}`);
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(destination, () => {}); // Delete the file on error
      reject(err);
    });
  });
}

/**
 * Download the Yarn plugin
 */
async function downloadPlugin(pluginName) {
  const YARN_PLUGINS_DIR = path.join(PROJECT_ROOT, '.yarn', 'plugins');
  ensureDirectoryExists(YARN_PLUGINS_DIR);
  
  const pluginPath = path.join(YARN_PLUGINS_DIR, `${pluginName}.cjs`);
  
  if (fs.existsSync(pluginPath)) {
    console.log(`Plugin ${pluginName} already exists at ${pluginPath}`);
    return;
  }
  
  const url = `https://raw.githubusercontent.com/yarnpkg/berry/master/packages/${pluginName}/bin/${pluginName}.js`;
  
  try {
    await downloadFile(url, pluginPath);
    console.log(`Plugin ${pluginName} downloaded to ${pluginPath}`);
  } catch (error) {
    console.error(`Failed to download plugin ${pluginName}:`, error.message);
  }
}

/**
 * Download the specified Yarn version
 */
async function downloadYarn() {
  ensureDirectoryExists(YARN_RELEASES_DIR);
  
  if (fs.existsSync(YARN_BINARY_PATH)) {
    console.log(`Yarn ${YARN_VERSION} already exists at ${YARN_BINARY_PATH}`);
    return;
  }
  
  const yarnUrl = `https://github.com/yarnpkg/berry/releases/download/v${YARN_VERSION}/yarn-${YARN_VERSION}.cjs`;
  
  try {
    await downloadFile(yarnUrl, YARN_BINARY_PATH);
    fs.chmodSync(YARN_BINARY_PATH, '755'); // Make executable
    console.log(`Yarn ${YARN_VERSION} downloaded and set as executable.`);
  } catch (error) {
    console.error(`Failed to download Yarn:`, error.message);
    process.exit(1);
  }
}

/**
 * Download Yarn plugins
 */
async function downloadPlugins() {
  const plugins = [
    '@yarnpkg/plugin-workspace-tools',
    '@yarnpkg/plugin-interactive-tools'
  ];
  
  for (const plugin of plugins) {
    const pluginName = plugin.replace('@yarnpkg/', '');
    await downloadPlugin(pluginName);
  }
}

/**
 * Run the download process
 */
async function main() {
  console.log(`Downloading Yarn ${YARN_VERSION}...`);
  
  try {
    await downloadYarn();
    await downloadPlugins();
    
    console.log('\n✅ Yarn download completed successfully!');
    console.log(`You can now use Yarn ${YARN_VERSION} with:\n`);
    console.log(`node ${YARN_BINARY_PATH} <command>\n`);
    console.log('Or set up your project to use this version with:');
    console.log('node scripts/setup-yarn.js');
    
  } catch (error) {
    console.error('\n❌ Yarn download failed:');
    console.error(error);
    process.exit(1);
  }
}

// Run the script
main();