#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test the new tender schema implementation
 */

const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

// Ensure the script is run from the project root
const projectRoot = process.cwd();
if (!fs.existsSync(path.join(projectRoot, 'package.json'))) {
    console.error('This script must be run from the project root directory');
    process.exit(1);
}

// Build the project first
console.log('Building the project...');
try {
    execSync('yarn build', { stdio: 'inherit' });
} catch (error) {
    console.error('Failed to build the project:', error.message);
    process.exit(1);
}

// Run the migration to update the schema
console.log('Running migrations...');
try {
    execSync('yarn db:migrate', { stdio: 'inherit' });
} catch (error) {
    console.error('Failed to run migrations:', error.message);
    process.exit(1);
}

// Import the required modules
const { DatabaseValidator } = require('../dist/utils/DatabaseValidator');
const { TenderRepository } = require('../dist/repositories/TenderRepository');
const { TenderStatus } = require('../dist/shared/types/tender');

// Test the database schema
async function testDatabaseSchema() {
    console.log('Testing database schema...');
    
    try {
        // Validate the schema
        const validator = DatabaseValidator.getInstance();
        const validationResult = validator.validateTendersSchema();
        
        console.log('Schema validation result:', validationResult);
        
        if (!validationResult.valid) {
            console.log('Fixing schema issues...');
            validator.fixSchemaIssues(validationResult);
            
            // Validate again after fixing
            const newValidationResult = validator.validateTendersSchema();
            console.log('Schema validation result after fixes:', newValidationResult);
            
            if (!newValidationResult.valid) {
                console.error('Failed to fix all schema issues');
                process.exit(1);
            }
        }
        
        console.log('Schema validation successful');
    } catch (error) {
        console.error('Schema validation failed:', error.message);
        process.exit(1);
    }
}

// Test the tender repository
async function testTenderRepository() {
    console.log('Testing tender repository...');
    
    try {
        // Initialize the repository
        const repository = TenderRepository.getInstance();
        await repository.initialize();
        
        // Create a test tender
        const testTender = {
            id: `test-${Date.now()}`,
            title: 'Test Tender',
            description: 'This is a test tender',
            url: 'https://example.com/test',
            issuer: 'Test Issuer',
            status: TenderStatus.NEW,
            publishDate: new Date(),
            closingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
            value: 1000000,
            category: 'Test Category',
            confidence: 0.85,
            tags: ['test', 'example'],
            metadata: { source: 'test-script' },
            documents: [{ title: 'Test Document', url: 'https://example.com/doc' }],
            requirements: ['Requirement 1', 'Requirement 2']
        };
        
        // Save the tender
        console.log('Saving test tender...');
        await repository.saveTender(testTender);
        
        // Get the tender
        console.log('Retrieving test tender...');
        const retrievedTender = await repository.getTender(testTender.id);
        
        if (!retrievedTender) {
            console.error('Failed to retrieve test tender');
            process.exit(1);
        }
        
        console.log('Retrieved tender:', retrievedTender);
        
        // Update the tender status
        console.log('Updating tender status...');
        await repository.updateTenderStatus(testTender.id, TenderStatus.PROCESSING);
        
        // Get the updated tender
        const updatedTender = await repository.getTender(testTender.id);
        
        if (!updatedTender || updatedTender.status !== TenderStatus.PROCESSING) {
            console.error('Failed to update tender status');
            process.exit(1);
        }
        
        console.log('Updated tender status:', updatedTender.status);
        
        // Get all tenders
        console.log('Retrieving all tenders...');
        const allTenders = await repository.getTenders();
        console.log(`Retrieved ${allTenders.length} tenders`);
        
        // Get tenders with filter
        console.log('Retrieving tenders with filter...');
        const filteredTenders = await repository.getTenders({
            status: TenderStatus.PROCESSING,
            category: 'Test Category',
            minConfidence: 0.8
        });
        console.log(`Retrieved ${filteredTenders.length} filtered tenders`);
        
        // Clean up
        console.log('Cleaning up...');
        await repository.updateTenderStatus(testTender.id, TenderStatus.CANCELLED);
        
        // Close the repository
        repository.close();
        
        console.log('Tender repository tests completed successfully');
    } catch (error) {
        console.error('Tender repository tests failed:', error.message);
        process.exit(1);
    }
}

// Run the tests
async function runTests() {
    try {
        await testDatabaseSchema();
        await testTenderRepository();
        
        console.log('All tests completed successfully');
    } catch (error) {
        console.error('Tests failed:', error.message);
        process.exit(1);
    }
}

runTests();
