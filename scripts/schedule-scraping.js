/**
 * <PERSON>ript to schedule regular scraping using node-cron
 */
const cron = require('node-cron');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const { Logger } = require('../src/utils/Logger');

const execPromise = promisify(exec);
const logger = Logger.getInstance('ScrapingScheduler');

// Schedule options
const SCHEDULE_OPTIONS = {
  // Run every day at 6 AM
  DAILY: '0 6 * * *',
  // Run every Monday at 8 AM
  WEEKLY: '0 8 * * 1',
  // Run every hour
  HOURLY: '0 * * * *',
  // Run every 15 minutes (for testing)
  TEST: '*/15 * * * *'
};

// Default schedule
const DEFAULT_SCHEDULE = SCHEDULE_OPTIONS.DAILY;

/**
 * Run the scraper
 */
async function runScraper() {
  try {
    logger.info('Starting scheduled scraping...');
    
    // Get the project root directory
    const projectRoot = path.resolve(__dirname, '..');
    
    // Run the scraper script
    const { stdout, stderr } = await execPromise('yarn test:scrapers', {
      cwd: projectRoot
    });
    
    if (stderr) {
      logger.warn('Scraper produced warnings:', { stderr });
    }
    
    logger.info('Scheduled scraping completed successfully:', { stdout });
    
    // Send email notification if enabled
    if (process.env.ENABLE_EMAIL_NOTIFICATIONS === 'true') {
      await sendEmailNotification('Scraping completed successfully');
    }
  } catch (error) {
    logger.error('Scheduled scraping failed:', { error });
    
    // Send email notification about failure if enabled
    if (process.env.ENABLE_EMAIL_NOTIFICATIONS === 'true') {
      await sendEmailNotification('Scraping failed', error);
    }
  }
}

/**
 * Send email notification
 * @param {string} subject - Email subject
 * @param {Error} error - Optional error object
 */
async function sendEmailNotification(subject, error = null) {
  try {
    // Import the email service
    const { EmailService } = require('../src/services/EmailService');
    const emailService = EmailService.getInstance();
    
    // Prepare email content
    const timestamp = new Date().toISOString();
    let content = `<h1>${subject}</h1>`;
    content += `<p>Timestamp: ${timestamp}</p>`;
    
    if (error) {
      content += `<h2>Error Details</h2>`;
      content += `<p>Error: ${error.message}</p>`;
      if (error.stack) {
        content += `<pre>${error.stack}</pre>`;
      }
    }
    
    // Send the email
    const recipients = (process.env.NOTIFICATION_EMAILS || '').split(',');
    if (recipients.length === 0 || recipients[0] === '') {
      logger.warn('No notification recipients configured');
      return;
    }
    
    for (const recipient of recipients) {
      await emailService.sendTransactionalEmail(
        recipient.trim(),
        `Mexel Scraping: ${subject}`,
        content
      );
    }
    
    logger.info('Email notification sent successfully');
  } catch (emailError) {
    logger.error('Failed to send email notification:', { emailError });
  }
}

/**
 * Schedule the scraper
 * @param {string} schedule - Cron schedule expression
 */
function scheduleScraper(schedule = DEFAULT_SCHEDULE) {
  logger.info(`Scheduling scraper with cron expression: ${schedule}`);
  
  // Schedule the scraper
  const job = cron.schedule(schedule, runScraper, {
    scheduled: true,
    timezone: 'Africa/Johannesburg' // Use local timezone
  });
  
  // Start the scheduler
  job.start();
  
  logger.info('Scraper scheduled successfully');
  
  return job;
}

// If this script is run directly, schedule the scraper
if (require.main === module) {
  // Get schedule from command line arguments or use default
  const scheduleArg = process.argv[2];
  const schedule = SCHEDULE_OPTIONS[scheduleArg] || DEFAULT_SCHEDULE;
  
  // Schedule the scraper
  scheduleScraper(schedule);
  
  // Keep the process running
  logger.info('Scraper scheduler is running. Press Ctrl+C to exit.');
}

module.exports = {
  scheduleScraper,
  runScraper,
  SCHEDULE_OPTIONS
};
