#!/usr/bin/env ts-node

/**
 * Agent system startup script (v2)
 * This script starts the v2 agent system
 */

import * as dotenv from 'dotenv';
import { CoordinatorAgent } from '../src/agents/v2/implementations/CoordinatorAgent';
import { TenderMonitorAgent } from '../src/agents/v2/implementations/TenderMonitorAgent';
import { OutreachEmailAgent } from '../src/agents/v2/implementations/OutreachEmailAgent';
import { AnalyticsAgent } from '../src/agents/v2/implementations/AnalyticsAgent';
import { Logger } from '../src/utils/Logger';
import { AgentStatus } from '../src/agents/v2/interfaces/IAgent';

// Load environment variables
dotenv.config();

// Create logger
const logger = Logger.getInstance('AgentSystem');

// Parse command line arguments
const args = process.argv.slice(2);
const runMode = args[0] || 'all';

// Helper function to initialize an agent
async function initializeAgent(agent: any, name: string): Promise<void> {
  try {
    logger.info(`Initializing ${name} agent`);
    await agent.initialize();
    logger.info(`${name} agent initialized successfully`);
  } catch (error) {
    logger.error(`Failed to initialize ${name} agent`, {
      error: error instanceof Error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : String(error)
    });
    throw error;
  }
}

// Main function
async function main() {
  logger.info('Starting Mexel Agent System (v2)');
  logger.info(`Run mode: ${runMode}`);
  
  try {
    // Create coordinator agent
    const coordinator = new CoordinatorAgent();
    
    // Initialize coordinator
    await initializeAgent(coordinator, 'Coordinator');
    
    // Create and register agents based on run mode
    if (runMode === 'all' || runMode === 'tender') {
      const tenderMonitor = new TenderMonitorAgent();
      await initializeAgent(tenderMonitor, 'TenderMonitor');
      coordinator.registerAgent(tenderMonitor);
    }
    
    if (runMode === 'all' || runMode === 'email') {
      const outreachEmail = new OutreachEmailAgent();
      await initializeAgent(outreachEmail, 'OutreachEmail');
      coordinator.registerAgent(outreachEmail);
    }
    
    if (runMode === 'all' || runMode === 'analytics') {
      const analytics = new AnalyticsAgent();
      await initializeAgent(analytics, 'Analytics');
      coordinator.registerAgent(analytics);
    }
    
    // Start the coordinator
    logger.info('Starting coordinator agent');
    await coordinator.start();
    
    // Execute the coordinator once to start the agent cycle
    logger.info('Executing coordinator agent');
    await coordinator.execute();
    
    // Print agent status
    const agentStatus = coordinator.getAgentStatus();
    logger.info('Agent status:', agentStatus as unknown as Record<string, unknown>);
    
    // Keep the process running
    logger.info('Agent system started successfully');
    logger.info('Press Ctrl+C to stop');
    
    // Handle process termination
    process.on('SIGINT', async () => {
      logger.info('Shutting down agent system');
      
      try {
        await coordinator.shutdown();
        logger.info('Agent system shut down successfully');
        process.exit(0);
      } catch (error) {
        logger.error('Error shutting down agent system', {
          error: error instanceof Error ? error : new Error(String(error))
        });
        process.exit(1);
      }
    });
  } catch (error) {
    logger.error('Failed to start agent system', {
      error: error instanceof Error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : String(error)
    });
    process.exit(1);
  }
}

// Run main function
main().catch(error => {
  logger.error('Unhandled error', {
    error: error instanceof Error ? error : new Error(String(error))
  });
  process.exit(1);
});
