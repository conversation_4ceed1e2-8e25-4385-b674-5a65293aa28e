#!/usr/bin/env node

/**
 * <PERSON>ript to test the tender API endpoints
 */

const axios = require('axios');
const chalk = require('chalk');

// Configuration
const API_BASE_URL = 'http://localhost:3001/api';
const TENDER_API_URL = `${API_BASE_URL}/tenders`;

// Helper function to log results
function logResult(testName, success, data, error) {
  if (success) {
    console.log(chalk.green(`✓ ${testName}`));
    if (data) {
      console.log(chalk.gray('  Response:'), typeof data === 'object' ? JSON.stringify(data, null, 2) : data);
    }
  } else {
    console.log(chalk.red(`✗ ${testName}`));
    if (error) {
      console.log(chalk.red('  Error:'), error);
    }
  }
  console.log(); // Add a blank line for readability
}

// Test the GET /api/tenders endpoint
async function testGetTenders() {
  try {
    console.log(chalk.blue('Testing GET /api/tenders'));
    const response = await axios.get(TENDER_API_URL);
    
    if (response.status === 200 && response.data && response.data.status === 'success') {
      const { tenders, pagination } = response.data.data;
      logResult('Get all tenders', true, {
        count: tenders.length,
        pagination
      });
    } else {
      logResult('Get all tenders', false, null, 'Invalid response format');
    }
  } catch (error) {
    logResult('Get all tenders', false, null, error.message);
  }
}

// Test the GET /api/tenders with filtering
async function testGetTendersWithFiltering() {
  try {
    console.log(chalk.blue('Testing GET /api/tenders with filtering'));
    
    // Test with status filter
    const statusResponse = await axios.get(`${TENDER_API_URL}?status=NEW&limit=5`);
    if (statusResponse.status === 200 && statusResponse.data && statusResponse.data.status === 'success') {
      const { tenders, pagination } = statusResponse.data.data;
      logResult('Get tenders with status filter', true, {
        count: tenders.length,
        pagination
      });
    } else {
      logResult('Get tenders with status filter', false, null, 'Invalid response format');
    }
    
    // Test with multiple filters
    const multiFilterResponse = await axios.get(`${TENDER_API_URL}?minConfidence=0.7&limit=3&sortBy=closingDate&sortOrder=asc`);
    if (multiFilterResponse.status === 200 && multiFilterResponse.data && multiFilterResponse.data.status === 'success') {
      const { tenders, pagination } = multiFilterResponse.data.data;
      logResult('Get tenders with multiple filters', true, {
        count: tenders.length,
        pagination
      });
    } else {
      logResult('Get tenders with multiple filters', false, null, 'Invalid response format');
    }
    
    // Test with search
    const searchResponse = await axios.get(`${TENDER_API_URL}?search=water&limit=5`);
    if (searchResponse.status === 200 && searchResponse.data && searchResponse.data.status === 'success') {
      const { tenders, pagination } = searchResponse.data.data;
      logResult('Get tenders with search', true, {
        count: tenders.length,
        pagination
      });
    } else {
      logResult('Get tenders with search', false, null, 'Invalid response format');
    }
  } catch (error) {
    logResult('Get tenders with filtering', false, null, error.message);
  }
}

// Test the GET /api/tenders with pagination
async function testGetTendersWithPagination() {
  try {
    console.log(chalk.blue('Testing GET /api/tenders with pagination'));
    
    // Get first page
    const page1Response = await axios.get(`${TENDER_API_URL}?page=1&limit=3`);
    if (page1Response.status === 200 && page1Response.data && page1Response.data.status === 'success') {
      const { tenders, pagination } = page1Response.data.data;
      logResult('Get tenders page 1', true, {
        count: tenders.length,
        pagination
      });
      
      // Get second page if there is one
      if (pagination.hasNextPage) {
        const page2Response = await axios.get(`${TENDER_API_URL}?page=2&limit=3`);
        if (page2Response.status === 200 && page2Response.data && page2Response.data.status === 'success') {
          const { tenders: page2Tenders, pagination: page2Pagination } = page2Response.data.data;
          logResult('Get tenders page 2', true, {
            count: page2Tenders.length,
            pagination: page2Pagination
          });
          
          // Verify different tenders on different pages
          const page1Ids = new Set(tenders.map(t => t.id));
          const page2Ids = new Set(page2Tenders.map(t => t.id));
          const overlap = [...page1Ids].filter(id => page2Ids.has(id));
          
          if (overlap.length === 0) {
            logResult('Pagination returns different tenders', true);
          } else {
            logResult('Pagination returns different tenders', false, null, `Found ${overlap.length} overlapping tenders`);
          }
        } else {
          logResult('Get tenders page 2', false, null, 'Invalid response format');
        }
      } else {
        logResult('Get tenders page 2', false, null, 'No second page available');
      }
    } else {
      logResult('Get tenders page 1', false, null, 'Invalid response format');
    }
  } catch (error) {
    logResult('Get tenders with pagination', false, null, error.message);
  }
}

// Test the GET /api/tenders/categories endpoint
async function testGetCategories() {
  try {
    console.log(chalk.blue('Testing GET /api/tenders/categories'));
    const response = await axios.get(`${TENDER_API_URL}/categories`);
    
    if (response.status === 200 && response.data && response.data.status === 'success') {
      const categories = response.data.data;
      logResult('Get tender categories', true, {
        count: categories.length,
        categories: categories.slice(0, 5) // Show only first 5 categories
      });
    } else {
      logResult('Get tender categories', false, null, 'Invalid response format');
    }
  } catch (error) {
    logResult('Get tender categories', false, null, error.message);
  }
}

// Test the GET /api/tenders/stats endpoint
async function testGetStats() {
  try {
    console.log(chalk.blue('Testing GET /api/tenders/stats'));
    const response = await axios.get(`${TENDER_API_URL}/stats`);
    
    if (response.status === 200 && response.data && response.data.status === 'success') {
      const stats = response.data.data;
      logResult('Get tender statistics', true, stats);
    } else {
      logResult('Get tender statistics', false, null, 'Invalid response format');
    }
  } catch (error) {
    logResult('Get tender statistics', false, null, error.message);
  }
}

// Run all tests
async function runTests() {
  try {
    console.log(chalk.yellow('=== Testing Tender API Endpoints ===\n'));
    
    // Test if the server is running
    try {
      await axios.get(`${API_BASE_URL}/health`);
      console.log(chalk.green('✓ API server is running\n'));
    } catch (error) {
      console.log(chalk.red('✗ API server is not running. Please start the server first.\n'));
      process.exit(1);
    }
    
    // Run the tests
    await testGetTenders();
    await testGetTendersWithFiltering();
    await testGetTendersWithPagination();
    await testGetCategories();
    await testGetStats();
    
    console.log(chalk.yellow('\n=== All tests completed ==='));
  } catch (error) {
    console.error(chalk.red('Error running tests:'), error);
    process.exit(1);
  }
}

// Run the tests
runTests();
