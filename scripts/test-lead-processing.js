// This script simulates lead processing events for testing Socket.IO integration

const axios = require("axios");

// Sample lead data
const sampleLead = {
  id: "test-lead-" + Date.now(),
  fictionalCompanyName: "TechInnovate Solutions",
  description:
    "A technology consulting firm specializing in digital transformation",
  website: "https://example.com",
  industry: "Technology",
  employees: "50-100",
  location: "San Francisco, CA",
  contactName: "Jane Smith",
  contactTitle: "Chief Technology Officer",
  contactEmail: "<EMAIL>",
};

// Function to simulate lead processing request
async function simulateLeadProcessing() {
  try {
    console.log("Sending lead processing request...");

    // Send the request to the /process-lead endpoint
    const response = await axios.post(
      "http://localhost:3001/api/process-lead",
      sampleLead
    );

    console.log("Lead processing initiated successfully:");
    console.log("Response:", response.data);
  } catch (error) {
    console.error("Error simulating lead processing:", error.message);
    if (error.response) {
      console.error("Response data:", error.response.data);
    }
  }
}

// Run the simulation
simulateLeadProcessing();
