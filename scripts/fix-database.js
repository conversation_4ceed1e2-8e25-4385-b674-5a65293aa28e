#!/usr/bin/env node

/**
 * Database fix script
 * This script fixes common database issues
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

// Configuration
const dbPath = path.join(process.cwd(), 'data/mexel.db');

// Ensure data directory exists
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
    console.log(`Creating data directory: ${dataDir}`);
    fs.mkdirSync(dataDir, { recursive: true });
}

// Check if database file exists
if (!fs.existsSync(dbPath)) {
    console.log(`Database file does not exist: ${dbPath}`);
    console.log('Creating new database file...');

    // Create an empty database file
    fs.writeFileSync(dbPath, '');
}

// Connect to the database
console.log(`Connecting to database: ${dbPath}`);
const db = new Database(dbPath);

// Fix migrations table
console.log('Fixing migrations table...');
try {
    // Check if migrations table exists
    const migrationsExists = db.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='migrations'
    `).get();

    if (!migrationsExists) {
        console.log('Creating migrations table...');
        db.exec(`
            CREATE TABLE migrations (
                version INTEGER PRIMARY KEY,
                description TEXT NOT NULL,
                appliedAt TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'APPLIED',
                rollbackSql TEXT,
                timestamp INTEGER
            );
        `);
    } else {
        // Check if status column exists
        try {
            db.prepare('SELECT status FROM migrations LIMIT 1').get();
            console.log('Status column exists in migrations table.');
        } catch (error) {
            console.log('Adding status column to migrations table...');
            db.exec(`
                ALTER TABLE migrations
                ADD COLUMN status TEXT NOT NULL DEFAULT 'APPLIED';
            `);
        }

        // Check if rollbackSql column exists
        try {
            db.prepare('SELECT rollbackSql FROM migrations LIMIT 1').get();
            console.log('RollbackSql column exists in migrations table.');
        } catch (error) {
            console.log('Adding rollbackSql column to migrations table...');
            db.exec(`
                ALTER TABLE migrations
                ADD COLUMN rollbackSql TEXT;
            `);
        }

        // Check if timestamp column exists
        try {
            db.prepare('SELECT timestamp FROM migrations LIMIT 1').get();
            console.log('Timestamp column exists in migrations table.');
        } catch (error) {
            console.log('Adding timestamp column to migrations table...');
            db.exec(`
                ALTER TABLE migrations
                ADD COLUMN timestamp INTEGER;
            `);
        }
    }
} catch (error) {
    console.error('Failed to fix migrations table:', error.message);
    console.log('Recreating migrations table from scratch...');

    try {
        db.exec(`
            DROP TABLE IF EXISTS migrations;
            CREATE TABLE migrations (
                version INTEGER PRIMARY KEY,
                description TEXT NOT NULL,
                appliedAt TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'APPLIED',
                rollbackSql TEXT,
                timestamp INTEGER
            );
        `);
    } catch (recreateError) {
        console.error('Failed to recreate migrations table:', recreateError.message);
        process.exit(1);
    }
}

// Fix email_templates table
console.log('Fixing email_templates table...');
try {
    // Check if email_templates table exists
    const emailTemplatesExists = db.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='email_templates'
    `).get();

    if (!emailTemplatesExists) {
        console.log('Creating email_templates table...');
        db.exec(`
            CREATE TABLE email_templates (
                name TEXT PRIMARY KEY,
                subject TEXT NOT NULL,
                body TEXT NOT NULL,
                metadata TEXT,
                followUpScheduleDays TEXT,
                abTestCampaignId TEXT,
                createdAt TEXT NOT NULL,
                updatedAt TEXT NOT NULL
            );

            CREATE INDEX IF NOT EXISTS idx_email_templates_name ON email_templates(name);
        `);
    }
} catch (error) {
    console.error('Failed to fix email_templates table:', error.message);
}

// Fix tenders table
console.log('Fixing tenders table...');
try {
    // Check if tenders table exists
    const tendersExists = db.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='tenders'
    `).get();

    if (!tendersExists) {
        console.log('Creating tenders table...');
        db.exec(`
            CREATE TABLE tenders (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                description TEXT,
                reference TEXT,
                issuer TEXT,
                publishDate TEXT,
                closingDate TEXT,
                status TEXT NOT NULL DEFAULT 'NEW',
                url TEXT NOT NULL,
                value REAL,
                category TEXT,
                tags TEXT,
                confidence REAL,
                metadata TEXT,
                documents TEXT,
                requirements TEXT,
                location TEXT,

                -- Legacy fields for compatibility with existing code
                source TEXT,
                closing_date TEXT,
                scrape_date TEXT,
                contact_person TEXT,
                contact_email TEXT,
                estimated_value REAL,
                categories TEXT,
                relevance_score REAL,
                historical_avg_value REAL,
                historical_success_rate REAL,
                opportunity_score REAL,
                first_seen TEXT,
                last_updated TEXT
            );

            -- Create indexes for common query patterns
            CREATE INDEX IF NOT EXISTS idx_tenders_status ON tenders(status);
            CREATE INDEX IF NOT EXISTS idx_tenders_category ON tenders(category);
            CREATE INDEX IF NOT EXISTS idx_tenders_confidence ON tenders(confidence);
            CREATE INDEX IF NOT EXISTS idx_tenders_closingDate ON tenders(closingDate);
        `);
    }
} catch (error) {
    console.error('Failed to fix tenders table:', error.message);
}

// Apply migrations directly
console.log('Applying migrations directly...');
try {
    // Check if tenders table exists
    const tendersExists = db.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='tenders'
    `).get();

    if (!tendersExists) {
        // Apply migration 5 (tenders table) directly
        console.log('Creating tenders table...');
        db.exec(`
            CREATE TABLE tenders (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                description TEXT,
                reference TEXT,
                issuer TEXT,
                publishDate TEXT,
                closingDate TEXT,
                status TEXT NOT NULL DEFAULT 'NEW',
                url TEXT NOT NULL,
                value REAL,
                category TEXT,
                tags TEXT,
                confidence REAL,
                metadata TEXT,
                documents TEXT,
                requirements TEXT,
                location TEXT,

                -- Legacy fields for compatibility with existing code
                source TEXT,
                closing_date TEXT,
                scrape_date TEXT,
                contact_person TEXT,
                contact_email TEXT,
                estimated_value REAL,
                categories TEXT,
                relevance_score REAL,
                historical_avg_value REAL,
                historical_success_rate REAL,
                opportunity_score REAL,
                first_seen TEXT NOT NULL DEFAULT (datetime('now')),
                last_updated TEXT NOT NULL DEFAULT (datetime('now'))
            )
        `);

        console.log('Creating indexes for tenders table...');
        // Create indexes in separate statements to avoid errors
        db.exec('CREATE INDEX IF NOT EXISTS idx_tenders_status ON tenders(status)');
        db.exec('CREATE INDEX IF NOT EXISTS idx_tenders_category ON tenders(category)');
        db.exec('CREATE INDEX IF NOT EXISTS idx_tenders_closingDate ON tenders(closingDate)');

        // Record the migration in the migrations table
        db.prepare(`
            INSERT OR IGNORE INTO migrations (
                version, description, appliedAt, status, timestamp
            ) VALUES (?, ?, ?, ?, ?)
        `).run(
            5,
            'Add tenders table',
            new Date().toISOString(),
            'APPLIED',
            Date.now()
        );

        console.log('Tenders table created successfully.');
    } else {
        console.log('Tenders table already exists, checking columns...');

        // Get existing columns
        const columns = db.prepare('PRAGMA table_info(tenders)').all();
        const columnNames = columns.map(col => col.name);

        // Check for required columns and add them if missing
        const requiredColumns = [
            { name: 'confidence', type: 'REAL' },
            { name: 'metadata', type: 'TEXT' },
            { name: 'documents', type: 'TEXT' },
            { name: 'requirements', type: 'TEXT' },
            { name: 'first_seen', type: 'TEXT NOT NULL DEFAULT (datetime(\'now\'))' },
            { name: 'last_updated', type: 'TEXT NOT NULL DEFAULT (datetime(\'now\'))' }
        ];

        for (const column of requiredColumns) {
            if (!columnNames.includes(column.name)) {
                console.log(`Adding ${column.name} column to tenders table...`);
                db.exec(`ALTER TABLE tenders ADD COLUMN ${column.name} ${column.type}`);
            }
        }

        console.log('Tenders table updated successfully.');
    }

    console.log('Migrations applied directly.');
} catch (error) {
    console.error('Failed to apply migrations directly:', error.message);
}

// Close the database connection
db.close();
console.log('Database fixes completed.');
console.log('Database fix script completed.');
