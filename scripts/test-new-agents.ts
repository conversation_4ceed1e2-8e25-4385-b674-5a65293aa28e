#!/usr/bin/env ts-node

/**
 * New Agent system test script
 * This script tests the new agent system implementation
 */

import * as dotenv from "dotenv";
// Use the existing CoordinatorAgent implementation
import { CoordinatorAgent } from "../src/agents/CoordinatorAgent";
// Import our new agent implementations
import { TenderMonitorAgent } from "../src/agents/implementations/TenderMonitorAgent";
import { OutreachEmailAgent } from "../src/agents/implementations/OutreachEmailAgent";
import { AnalyticsAgent } from "../src/agents/implementations/AnalyticsAgent";
import { Logger } from "../src/utils/Logger";
import { AgentRole, AgentStatus, Agent } from "../src/types/agents/AgentTypes";
import { BaseAgent } from "../src/agents/base/BaseAgent";

// Load environment variables
dotenv.config();

// Create logger
const logger = Logger.getInstance("NewAgentTest");

// Parse command line arguments
const args = process.argv.slice(2);
const testMode = args[0] || "all";

/**
 * Simple test agent implementation
 */
class TestAgent extends BaseAgent {
  private testData: any = {};

  constructor(name: string, role: AgentRole) {
    super(name, role);
  }

  protected async initializeDependencies(): Promise<void> {
    logger.info(`Initializing test agent: ${this.name}`);
    // Simulate initialization
    await new Promise((resolve) => setTimeout(resolve, 500));
    this.testData = { initialized: true, timestamp: new Date() };
  }

  protected async executeImpl(): Promise<void> {
    logger.info(`Executing test agent: ${this.name}`);
    // Simulate work
    await new Promise((resolve) => setTimeout(resolve, 1000));
    this.testData.lastExecution = new Date();
    this.testData.executionCount = (this.testData.executionCount || 0) + 1;
  }

  protected async cleanupResources(): Promise<void> {
    logger.info(`Shutting down test agent: ${this.name}`);
    // Simulate cleanup
    await new Promise((resolve) => setTimeout(resolve, 500));
    this.testData = {};
  }

  getPerformanceMetrics() {
    const baseMetrics = super.getPerformanceMetrics();
    return {
      ...baseMetrics,
      executionCount: this.testData.executionCount || 0,
      lastExecution: this.testData.lastExecution,
    };
  }
}

/**
 * Main function
 */
async function main() {
  logger.info("Starting Mexel New Agent Test");
  logger.info(`Test mode: ${testMode}`);

  try {
    // Create coordinator agent
    const coordinator = new CoordinatorAgent();

    // Initialize coordinator
    logger.info("Initializing coordinator agent");
    await coordinator.initialize();
    logger.info("Coordinator agent initialized successfully");

    // Create and register agents
    if (testMode === "all" || testMode === "tender") {
      // Use our new TenderMonitorAgent implementation
      const tenderAgent = new TenderMonitorAgent("TenderMonitor");
      await tenderAgent.initialize();
      coordinator.registerAgent(tenderAgent);
      logger.info("Tender monitor agent registered");
    }

    if (testMode === "all" || testMode === "email") {
      // Use our new OutreachEmailAgent implementation
      const emailAgent = new OutreachEmailAgent("OutreachEmail");
      await emailAgent.initialize();
      coordinator.registerAgent(emailAgent);
      logger.info("Outreach email agent registered");
    }

    if (testMode === "all" || testMode === "analytics") {
      // Use our new AnalyticsAgent implementation
      const analyticsAgent = new AnalyticsAgent("Analytics");
      await analyticsAgent.initialize();
      coordinator.registerAgent(analyticsAgent);
      logger.info("Analytics agent registered");
    }

    // Start the coordinator
    logger.info("Starting coordinator agent");
    await coordinator.start();

    // Execute the coordinator
    logger.info("Executing coordinator agent");
    await coordinator.execute();

    // Get all agents
    const agents = coordinator.getAllAgents();
    logger.info("Registered agents:", {
      agents: agents.map((a) => ({ id: a.id, name: a.name, role: a.role })),
    });

    // Execute each agent directly
    for (const agent of coordinator.getAllAgents()) {
      logger.info(`Executing agent directly: ${agent.name}`);
      await agent.execute();

      // Skip metrics for now - we'll need to cast to BaseAgent to access getPerformanceMetrics
      logger.info(`Agent ${agent.name} executed successfully`);
    }

    // Execute the coordinator again
    logger.info("Executing coordinator agent again");
    await coordinator.execute();

    // Get updated agent status
    const updatedAgents = coordinator.getAllAgents();
    logger.info("Updated agent status:", {
      agents: updatedAgents.map((a) => ({
        id: a.id,
        name: a.name,
        role: a.role,
        status: a.getStatus(),
      })),
    });

    // Shutdown the coordinator
    logger.info("Shutting down coordinator agent");
    await coordinator.shutdown();

    logger.info("Agent test completed successfully");
  } catch (error) {
    logger.error("Failed to run agent test", {
      error:
        error instanceof Error
          ? {
              message: error.message,
              stack: error.stack,
              name: error.name,
            }
          : String(error),
    });
    process.exit(1);
  }
}

// Run main function
main().catch((error) => {
  logger.error("Unhandled error", {
    error: error instanceof Error ? error : new Error(String(error)),
  });
  process.exit(1);
});
