#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to test the tender database directly
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Configuration
const dbPath = path.join(process.cwd(), 'data/mexel.db');

// Ensure data directory exists
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
    console.log(`Creating data directory: ${dataDir}`);
    fs.mkdirSync(dataDir, { recursive: true });
}

// Connect to the database
console.log(`Connecting to database: ${dbPath}`);
const db = new Database(dbPath);

// Test the database schema
function testDatabaseSchema() {
    console.log('Testing database schema...');
    
    try {
        // Check if tenders table exists
        const tendersExists = db.prepare(`
            SELECT name FROM sqlite_master
            WHERE type='table' AND name='tenders'
        `).get();
        
        if (!tendersExists) {
            console.log('Tenders table does not exist, creating it...');
            createTendersTable();
        } else {
            console.log('Tenders table exists, checking schema...');
            const columns = db.prepare('PRAGMA table_info(tenders)').all();
            const columnNames = columns.map(col => col.name);
            
            console.log('Tenders table columns:', columnNames);
            
            // Check for required columns
            const requiredColumns = [
                'id', 'title', 'description', 'url', 'status', 'issuer',
                'publishDate', 'closingDate', 'value', 'category', 'confidence',
                'metadata', 'documents', 'requirements', 'tags',
                'source', 'relevanceScore', 'opportunityScore'
            ];
            
            const missingColumns = requiredColumns.filter(col => !columnNames.includes(col));
            
            if (missingColumns.length > 0) {
                console.log('Missing columns:', missingColumns);
                console.log('Recreating tenders table with proper schema...');
                recreateTendersTable();
            } else {
                console.log('Tenders table schema is correct');
            }
        }
    } catch (error) {
        console.error('Schema validation failed:', error.message);
        process.exit(1);
    }
}

// Create tenders table
function createTendersTable() {
    try {
        db.exec(`
            CREATE TABLE tenders (
                -- Primary fields
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                description TEXT,
                reference TEXT,
                
                -- Source information
                source TEXT NOT NULL DEFAULT 'unknown',
                url TEXT NOT NULL,
                issuer TEXT,
                
                -- Dates
                publishDate TEXT,
                closingDate TEXT,
                scrapeDate TEXT NOT NULL DEFAULT (datetime('now')),
                firstSeen TEXT NOT NULL DEFAULT (datetime('now')),
                lastUpdated TEXT NOT NULL DEFAULT (datetime('now')),
                
                -- Status and categorization
                status TEXT NOT NULL DEFAULT 'NEW',
                category TEXT,
                tags TEXT, -- JSON array
                
                -- Financial information
                value REAL,
                estimatedValue REAL,
                
                -- Scoring and relevance
                relevanceScore REAL,
                opportunityScore REAL,
                confidence REAL,
                
                -- Contact information
                contactPerson TEXT,
                contactEmail TEXT,
                location TEXT,
                
                -- Structured data (stored as JSON)
                documents TEXT, -- JSON array of {title, url}
                requirements TEXT, -- JSON array
                metadata TEXT -- JSON object
            );
            
            -- Create indexes for common query patterns
            CREATE INDEX idx_tenders_status ON tenders(status);
            CREATE INDEX idx_tenders_category ON tenders(category);
            CREATE INDEX idx_tenders_confidence ON tenders(confidence);
            CREATE INDEX idx_tenders_closingDate ON tenders(closingDate);
            CREATE INDEX idx_tenders_source ON tenders(source);
            CREATE INDEX idx_tenders_issuer ON tenders(issuer);
            CREATE INDEX idx_tenders_relevanceScore ON tenders(relevanceScore);
        `);
        
        console.log('Tenders table created successfully');
    } catch (error) {
        console.error('Failed to create tenders table:', error.message);
        process.exit(1);
    }
}

// Recreate tenders table
function recreateTendersTable() {
    try {
        // Begin transaction
        db.exec('BEGIN TRANSACTION');
        
        // Create a temporary table with the new schema
        db.exec(`
            CREATE TABLE tenders_new (
                -- Primary fields
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                description TEXT,
                reference TEXT,
                
                -- Source information
                source TEXT NOT NULL DEFAULT 'unknown',
                url TEXT NOT NULL,
                issuer TEXT,
                
                -- Dates
                publishDate TEXT,
                closingDate TEXT,
                scrapeDate TEXT NOT NULL DEFAULT (datetime('now')),
                firstSeen TEXT NOT NULL DEFAULT (datetime('now')),
                lastUpdated TEXT NOT NULL DEFAULT (datetime('now')),
                
                -- Status and categorization
                status TEXT NOT NULL DEFAULT 'NEW',
                category TEXT,
                tags TEXT, -- JSON array
                
                -- Financial information
                value REAL,
                estimatedValue REAL,
                
                -- Scoring and relevance
                relevanceScore REAL,
                opportunityScore REAL,
                confidence REAL,
                
                -- Contact information
                contactPerson TEXT,
                contactEmail TEXT,
                location TEXT,
                
                -- Structured data (stored as JSON)
                documents TEXT, -- JSON array of {title, url}
                requirements TEXT, -- JSON array
                metadata TEXT -- JSON object
            )
        `);
        
        // Copy data from the old table to the new table
        db.exec(`
            INSERT INTO tenders_new (
                id, title, description, reference, source, url, issuer,
                publishDate, closingDate, scrapeDate, firstSeen, lastUpdated,
                status, category, tags, value, estimatedValue,
                relevanceScore, opportunityScore, confidence,
                contactPerson, contactEmail, location,
                documents, requirements, metadata
            )
            SELECT
                id, 
                title, 
                description, 
                reference,
                COALESCE(source, 'unknown') AS source,
                url,
                issuer,
                COALESCE(publishDate, scrape_date) AS publishDate,
                COALESCE(closingDate, closing_date) AS closingDate,
                COALESCE(scrape_date, datetime('now')) AS scrapeDate,
                COALESCE(first_seen, datetime('now')) AS firstSeen,
                COALESCE(last_updated, datetime('now')) AS lastUpdated,
                status,
                category,
                tags,
                COALESCE(value, estimated_value) AS value,
                estimated_value AS estimatedValue,
                relevance_score AS relevanceScore,
                opportunity_score AS opportunityScore,
                COALESCE(confidence, relevance_score, opportunity_score) AS confidence,
                contact_person AS contactPerson,
                contact_email AS contactEmail,
                location,
                documents,
                requirements,
                metadata
            FROM tenders
        `);
        
        // Drop the old table
        db.exec('DROP TABLE tenders');
        
        // Rename the new table to the original name
        db.exec('ALTER TABLE tenders_new RENAME TO tenders');
        
        // Create indexes
        db.exec(`
            CREATE INDEX idx_tenders_status ON tenders(status);
            CREATE INDEX idx_tenders_category ON tenders(category);
            CREATE INDEX idx_tenders_confidence ON tenders(confidence);
            CREATE INDEX idx_tenders_closingDate ON tenders(closingDate);
            CREATE INDEX idx_tenders_source ON tenders(source);
            CREATE INDEX idx_tenders_issuer ON tenders(issuer);
            CREATE INDEX idx_tenders_relevanceScore ON tenders(relevanceScore);
        `);
        
        // Commit transaction
        db.exec('COMMIT');
        
        console.log('Tenders table recreated successfully');
    } catch (error) {
        // Rollback transaction on error
        db.exec('ROLLBACK');
        console.error('Failed to recreate tenders table:', error.message);
        process.exit(1);
    }
}

// Test inserting a tender
function testInsertTender() {
    console.log('Testing tender insertion...');
    
    try {
        const testTender = {
            id: `test-${Date.now()}`,
            title: 'Test Tender',
            description: 'This is a test tender',
            url: 'https://example.com/test',
            issuer: 'Test Issuer',
            status: 'NEW',
            publishDate: new Date().toISOString(),
            closingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
            value: 1000000,
            category: 'Test Category',
            confidence: 0.85,
            tags: JSON.stringify(['test', 'example']),
            metadata: JSON.stringify({ source: 'test-script' }),
            documents: JSON.stringify([{ title: 'Test Document', url: 'https://example.com/doc' }]),
            requirements: JSON.stringify(['Requirement 1', 'Requirement 2']),
            source: 'test-script',
            relevanceScore: 0.85,
            opportunityScore: 0.75
        };
        
        // Prepare the insert statement
        const insert = db.prepare(`
            INSERT INTO tenders (
                id, title, description, url, issuer, status, publishDate, closingDate,
                value, category, confidence, tags, metadata, documents, requirements,
                source, relevanceScore, opportunityScore
            ) VALUES (
                @id, @title, @description, @url, @issuer, @status, @publishDate, @closingDate,
                @value, @category, @confidence, @tags, @metadata, @documents, @requirements,
                @source, @relevanceScore, @opportunityScore
            )
        `);
        
        // Execute the insert
        insert.run(testTender);
        
        console.log(`Test tender inserted with ID: ${testTender.id}`);
        
        // Retrieve the tender
        const retrievedTender = db.prepare('SELECT * FROM tenders WHERE id = ?').get(testTender.id);
        
        if (!retrievedTender) {
            console.error('Failed to retrieve test tender');
            process.exit(1);
        }
        
        console.log('Retrieved tender:', retrievedTender);
        
        // Update the tender status
        console.log('Updating tender status...');
        const updateResult = db.prepare('UPDATE tenders SET status = ? WHERE id = ?').run('PROCESSING', testTender.id);
        
        if (updateResult.changes === 0) {
            console.error('Failed to update tender status');
            process.exit(1);
        }
        
        // Retrieve the updated tender
        const updatedTender = db.prepare('SELECT * FROM tenders WHERE id = ?').get(testTender.id);
        
        if (!updatedTender || updatedTender.status !== 'PROCESSING') {
            console.error('Failed to update tender status');
            process.exit(1);
        }
        
        console.log('Updated tender status:', updatedTender.status);
        
        // Clean up
        console.log('Cleaning up...');
        db.prepare('UPDATE tenders SET status = ? WHERE id = ?').run('CANCELLED', testTender.id);
        
        console.log('Tender insertion test completed successfully');
    } catch (error) {
        console.error('Tender insertion test failed:', error.message);
        process.exit(1);
    }
}

// Run the tests
function runTests() {
    try {
        testDatabaseSchema();
        testInsertTender();
        
        console.log('All tests completed successfully');
    } catch (error) {
        console.error('Tests failed:', error.message);
        process.exit(1);
    } finally {
        // Close the database connection
        db.close();
    }
}

runTests();
