// This script directly emits Socket.IO events to test the client's handling

const { Server } = require("socket.io");
const http = require("http");

// Create a simple HTTP server
const server = http.createServer((req, res) => {
  res.writeHead(200, { "Content-Type": "text/plain" });
  res.end("Socket.IO Test Server\n");
});

// Create a Socket.IO server
const io = new Server(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"],
  },
});

// Sample lead processing events
const leadEvents = [
  {
    status: "started",
    leadId: "test-lead-1",
    companyName: "TechInnovate Solutions",
    timestamp: new Date().toISOString(),
  },
  {
    status: "completed",
    leadId: "test-lead-1",
    companyName: "TechInnovate Solutions",
    timestamp: new Date().toISOString(),
    result: {
      analysisSummary:
        "This company shows strong potential as a partner for our water treatment solutions. They have demonstrated interest in sustainable technologies and have a history of investing in infrastructure upgrades.",
      sector: "Technology",
    },
  },
  {
    status: "started",
    leadId: "test-lead-2",
    companyName: "GreenEarth Manufacturing",
    timestamp: new Date().toISOString(),
  },
  {
    status: "error",
    leadId: "test-lead-2",
    companyName: "GreenEarth Manufacturing",
    timestamp: new Date().toISOString(),
    error: "Failed to process company data due to incomplete information",
  },
];

// Connection event handler
io.on("connection", (socket) => {
  console.log("Client connected:", socket.id);

  // Inform connected
  socket.emit("connection_established", {
    message: "Connected to test socket server",
  });

  socket.on("disconnect", () => {
    console.log("Client disconnected:", socket.id);
  });
});

// Start the server
const PORT = 3001;
server.listen(PORT, () => {
  console.log(`Socket.IO test server running on port ${PORT}`);

  // Send test events with delays
  console.log("Will start sending test events in 5 seconds...");

  setTimeout(() => {
    console.log(`Emitting 'leadProcessing' event - started`);
    io.emit("leadProcessing", leadEvents[0]);

    setTimeout(() => {
      console.log(`Emitting 'leadProcessing' event - completed`);
      io.emit("leadProcessing", leadEvents[1]);

      setTimeout(() => {
        console.log(`Emitting 'leadProcessing' event - started (second lead)`);
        io.emit("leadProcessing", leadEvents[2]);

        setTimeout(() => {
          console.log(`Emitting 'leadProcessing' event - error`);
          io.emit("leadProcessing", leadEvents[3]);
        }, 3000);
      }, 3000);
    }, 3000);
  }, 5000);
});

// Keep the server running for 20 seconds, then shut down
setTimeout(() => {
  console.log("Test complete. Shutting down test server.");
  server.close();
  process.exit(0);
}, 20000);
