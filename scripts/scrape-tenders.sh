#!/bin/bash

# Script to run the tender scrapers

# Change to the project root directory
cd "$(dirname "$0")/.."

# Ensure the data directory exists
mkdir -p data

# Activate the virtual environment
source venv/bin/activate

# Run the scrapers
echo "Running eTenders spider..."
python src/python/run_scrapers.py --url https://www.etenders.gov.za/ --type etenders --auth false --filters '{}'

echo "Running Eskom spider..."
python src/python/run_scrapers.py --url https://www.eskom.co.za/suppliers/tenders/ --type eskom --auth false --filters '{}'

# Check if the scraping was successful
if [ $? -eq 0 ]; then
    echo "✅ Scraping completed successfully"
else
    echo "❌ Scraping failed"
    exit 1
fi

# Make the script executable
chmod +x scripts/scrape-tenders.sh

echo "Done! You can now access the scraped data through the API at http://localhost:3001/api/tenders"
