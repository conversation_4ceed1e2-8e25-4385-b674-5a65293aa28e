#!/usr/bin/env node

/**
 * Enhanced Yarn Workspaces Management Script
 * 
 * This script provides utilities for managing Yarn workspaces in the Mexel project.
 * It helps with dependency synchronization, workspace validation, and maintenance.
 * 
 * Usage: node scripts/manage-workspaces.js <command> [options]
 * 
 * Commands:
 *   list                 List all workspaces
 *   sync-deps            Synchronize dependencies across workspaces
 *   run <command>        Run a yarn command in all workspaces
 *   validate             Validate workspace configuration
 *   dedupe               Find and fix duplicate dependencies
 *   audit                Audit dependencies for issues
 *   doctor               Run comprehensive workspace health checks
 *   help                 Show this help information
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const PROJECT_ROOT = path.resolve(__dirname, '..');
const WORKSPACE_DIRS = ['frontend', 'shared'];
const COMMON_DEPS = [
  'typescript', 
  'socket.io-client', 
  'recharts', 
  'react',
  'react-dom',
  '@types/react',
  '@types/react-dom'
];

// Command line arguments
const command = process.argv[2] || 'help';
const options = process.argv.slice(3);

/**
 * Load package.json for a specific workspace
 */
function loadPackageJson(workspacePath) {
  const packageJsonPath = path.join(
    workspacePath === 'root' ? PROJECT_ROOT : path.join(PROJECT_ROOT, workspacePath),
    'package.json'
  );
  
  try {
    const content = fs.readFileSync(packageJsonPath, 'utf8');
    return JSON.parse(content);
  } catch (err) {
    console.error(`Error loading package.json from ${packageJsonPath}:`, err.message);
    return null;
  }
}

/**
 * Save package.json back to file
 */
function savePackageJson(workspacePath, packageJson) {
  const packageJsonPath = path.join(
    workspacePath === 'root' ? PROJECT_ROOT : path.join(PROJECT_ROOT, workspacePath),
    'package.json'
  );
  
  try {
    fs.writeFileSync(
      packageJsonPath, 
      JSON.stringify(packageJson, null, 2) + '\n',
      'utf8'
    );
    return true;
  } catch (err) {
    console.error(`Error saving package.json to ${packageJsonPath}:`, err.message);
    return false;
  }
}

/**
 * Run a yarn command and return the output
 */
function runYarnCommand(command, options = {}) {
  const defaultOptions = {
    cwd: PROJECT_ROOT,
    encoding: 'utf8',
    stdio: options.silent ? 'pipe' : 'inherit'
  };
  
  try {
    return execSync(`yarn ${command}`, { ...defaultOptions, ...options });
  } catch (error) {
    if (options.ignoreError) {
      return error.stdout || '';
    }
    throw error;
  }
}

/**
 * List all workspaces with detailed information
 */
function listWorkspaces() {
  console.log('Mexel Project Workspaces:\n');
  
  // Root project
  const rootPkg = loadPackageJson('root');
  if (rootPkg) {
    console.log(`📦 Root (${rootPkg.name}@${rootPkg.version})`);
    console.log(`   Description: ${rootPkg.description || 'No description'}`);
    console.log(`   Dependencies: ${Object.keys(rootPkg.dependencies || {}).length}`);
    console.log(`   DevDependencies: ${Object.keys(rootPkg.devDependencies || {}).length}`);
    console.log('');
  }
  
  // Workspaces
  WORKSPACE_DIRS.forEach(dir => {
    const pkg = loadPackageJson(dir);
    if (pkg) {
      console.log(`📦 ${dir} (${pkg.name}@${pkg.version})`);
      console.log(`   Description: ${pkg.description || 'No description'}`);
      console.log(`   Dependencies: ${Object.keys(pkg.dependencies || {}).length}`);
      console.log(`   DevDependencies: ${Object.keys(pkg.devDependencies || {}).length}`);
      
      // Show main entry point for libraries
      if (pkg.main) {
        console.log(`   Main: ${pkg.main}`);
      }
      
      console.log('');
    } else {
      console.log(`❌ ${dir} (Unable to load package.json)`);
      console.log('');
    }
  });
  
  // Show Yarn workspaces info
  try {
    console.log('Workspace Info from Yarn:');
    const workspaceInfo = runYarnCommand('workspaces info', { silent: true });
    console.log(workspaceInfo);
  } catch (error) {
    console.error('Error getting workspace info from Yarn:', error.message);
  }
}

/**
 * Synchronize dependencies across workspaces
 */
function syncDependencies() {
  // Load root package.json
  const rootPkg = loadPackageJson('root');
  if (!rootPkg || !rootPkg.resolutions) {
    console.error('Root package.json or resolutions not found.');
    return;
  }
  
  console.log('Synchronizing dependencies across workspaces...');
  
  // For each workspace
  WORKSPACE_DIRS.forEach(dir => {
    const pkg = loadPackageJson(dir);
    if (!pkg) return;
    
    let changed = false;
    
    // Remove resolutions from workspace package.json (should only be in root)
    if (pkg.resolutions && Object.keys(pkg.resolutions).length > 0) {
      delete pkg.resolutions;
      changed = true;
      console.log(`✅ Removed resolutions from ${dir} (these should only be in root)`);
    }
    
    // Ensure engines are consistent
    if (!pkg.engines || 
        pkg.engines.node !== rootPkg.engines.node || 
        pkg.engines.yarn !== rootPkg.engines.yarn) {
      pkg.engines = { ...rootPkg.engines };
      changed = true;
      console.log(`✅ Updated engines in ${dir} to match root`);
    }
    
    // Move common dependencies to peer dependencies for shared packages
    if (dir === 'shared') {
      const dependencies = pkg.dependencies || {};
      const peerDependencies = pkg.peerDependencies || {};
      
      COMMON_DEPS.forEach(dep => {
        if (dependencies[dep] && rootPkg.resolutions[dep]) {
          // Move to peer dependencies if it's not already there
          if (!peerDependencies[dep]) {
            peerDependencies[dep] = rootPkg.resolutions[dep];
            delete dependencies[dep];
            changed = true;
            console.log(`✅ Moved ${dep} from dependencies to peerDependencies in ${dir}`);
          }
        }
      });
      
      if (Object.keys(peerDependencies).length > 0) {
        pkg.peerDependencies = peerDependencies;
      }
      pkg.dependencies = dependencies;
    }
    
    // Update dependencies based on root resolutions
    ['dependencies', 'devDependencies'].forEach(depType => {
      if (!pkg[depType]) return;
      
      Object.keys(pkg[depType]).forEach(dep => {
        if (rootPkg.resolutions && rootPkg.resolutions[dep]) {
          const newVersion = rootPkg.resolutions[dep];
          if (pkg[depType][dep] !== newVersion) {
            pkg[depType][dep] = newVersion;
            changed = true;
            console.log(`✅ Updated ${dep} in ${dir} ${depType} to ${newVersion}`);
          }
        }
      });
    });
    
    // Use workspace protocol for internal dependencies
    if (pkg.dependencies) {
      WORKSPACE_DIRS.forEach(workspace => {
        if (pkg.dependencies[workspace]) {
          // Replace file: with workspace:
          if (pkg.dependencies[workspace].startsWith('file:')) {
            pkg.dependencies[workspace] = 'workspace:*';
            changed = true;
            console.log(`✅ Updated ${workspace} dependency to use workspace protocol in ${dir}`);
          }
        }
      });
    }
    
    // Save if changed
    if (changed) {
      if (savePackageJson(dir, pkg)) {
        console.log(`✅ Updated package.json in ${dir}`);
      }
    } else {
      console.log(`ℹ️ No changes needed for ${dir}`);
    }
  });
  
  console.log('\nDependency synchronization complete!');
  console.log('\nTo apply changes, run: yarn install');
}

/**
 * Run a yarn command in all workspaces
 */
function runInWorkspaces(command) {
  console.log(`Running 'yarn ${command}' in all workspaces...`);
  
  try {
    runYarnCommand(`workspaces foreach run ${command}`);
    console.log(`\n✅ Command 'yarn ${command}' completed successfully in all workspaces`);
  } catch (err) {
    console.error(`\n❌ Error running command in workspaces:`, err.message);
  }
}

/**
 * Validate all workspaces
 */
function validateWorkspaces() {
  console.log('Validating workspace configuration...');
  
  // Check root package.json
  const rootPkg = loadPackageJson('root');
  if (!rootPkg) {
    console.error('❌ Root package.json not found or invalid');
    return;
  }
  
  // Check workspaces config
  if (!rootPkg.workspaces || !Array.isArray(rootPkg.workspaces)) {
    console.error('❌ No workspaces configuration found in root package.json');
    return;
  }
  
  const configuredWorkspaces = rootPkg.workspaces;
  console.log(`ℹ️ Configured workspaces in package.json: ${configuredWorkspaces.join(', ')}`);
  
  // Check if all WORKSPACE_DIRS are included
  const missingWorkspaces = WORKSPACE_DIRS.filter(dir => !configuredWorkspaces.includes(dir));
  if (missingWorkspaces.length > 0) {
    console.error(`❌ Some directories are not configured as workspaces: ${missingWorkspaces.join(', ')}`);
  }
  
  // Check each workspace
  WORKSPACE_DIRS.forEach(dir => {
    const fullPath = path.join(PROJECT_ROOT, dir);
    
    // Check if directory exists
    if (!fs.existsSync(fullPath)) {
      console.error(`❌ Workspace directory does not exist: ${dir}`);
      return;
    }
    
    // Check package.json
    const pkg = loadPackageJson(dir);
    if (!pkg) {
      console.error(`❌ Unable to load package.json for workspace: ${dir}`);
      return;
    }
    
    console.log(`✅ Workspace ${dir} (${pkg.name}@${pkg.version}) is valid`);
    
    // Check for version mismatches
    if (pkg.version !== rootPkg.version) {
      console.warn(`⚠️ Version mismatch: root=${rootPkg.version}, ${dir}=${pkg.version}`);
    }
    
    // Check for resolutions (should only be in root)
    if (pkg.resolutions && Object.keys(pkg.resolutions).length > 0) {
      console.warn(`⚠️ Workspace ${dir} has resolutions that should be moved to root`);
    }
    
    // Check engines
    if (!pkg.engines || 
        pkg.engines.node !== rootPkg.engines.node || 
        pkg.engines.yarn !== rootPkg.engines.yarn) {
      console.warn(`⚠️ Workspace ${dir} has inconsistent engines configuration`);
    }
  });
  
  // Verify yarn.lock is at the root
  if (!fs.existsSync(path.join(PROJECT_ROOT, 'yarn.lock'))) {
    console.error('❌ yarn.lock not found at project root');
  } else {
    console.log('✅ yarn.lock exists at project root');
  }
  
  // Verify .yarnrc.yml
  if (!fs.existsSync(path.join(PROJECT_ROOT, '.yarnrc.yml'))) {
    console.error('❌ .yarnrc.yml not found at project root');
  } else {
    console.log('✅ .yarnrc.yml exists at project root');
  }
  
  console.log('\nWorkspace validation complete!');
}

/**
 * Find and fix duplicate dependencies
 */
function dedupeDependencies() {
  console.log('Finding and fixing duplicate dependencies...');
  
  try {
    // Check for duplicates
    const checkOutput = runYarnCommand('dedupe --check', { 
      silent: true, 
      ignoreError: true,
      stdio: ['ignore', 'pipe', 'pipe']
    });
    
    if (checkOutput.includes('No duplicates found')) {
      console.log('✅ No duplicate dependencies found!');
      return;
    }
    
    console.log('Found duplicate dependencies. Fixing...');
    
    // Fix duplicates
    runYarnCommand('dedupe');
    console.log('✅ Duplicates have been fixed!');
    
  } catch (error) {
    console.error('❌ Error deduplicating dependencies:', error.message);
  }
}

/**
 * Audit dependencies for security issues
 */
function auditDependencies() {
  console.log('Auditing dependencies for security issues...');
  
  try {
    const auditOutput = runYarnCommand('npm audit', { 
      silent: true,
      ignoreError: true,
      stdio: ['ignore', 'pipe', 'pipe']
    });
    
    console.log(auditOutput);
    
    if (auditOutput.includes('found 0 vulnerabilities')) {
      console.log('✅ No vulnerabilities found!');
    } else {
      console.log('\nTo fix vulnerabilities, you can try:');
      console.log('- yarn npm audit fix');
      console.log('- Update affected dependencies in package.json');
      console.log('- Add resolutions in the root package.json for specific packages');
    }
  } catch (error) {
    console.error('❌ Error auditing dependencies:', error.message);
  }
}

/**
 * Run comprehensive workspace health checks
 */
function runDoctor() {
  console.log('Running comprehensive workspace health checks...\n');
  
  // Validate workspaces
  validateWorkspaces();
  console.log('\n--------------------\n');
  
  // Check for duplicate dependencies
  dedupeDependencies();
  console.log('\n--------------------\n');
  
  // Audit dependencies
  auditDependencies();
  console.log('\n--------------------\n');
  
  // Check for outdated dependencies
  console.log('Checking for outdated dependencies...');
  try {
    runYarnCommand('outdated');
  } catch (error) {
    // yarn outdated exits with non-zero code if outdated dependencies are found
    // This is expected behavior, so we ignore the error
  }
  
  console.log('\n✅ Health check complete!');
  console.log('\nRecommended next steps:');
  console.log('1. Fix any warnings or errors reported above');
  console.log('2. Run `yarn workspaces:sync` to synchronize dependencies');
  console.log('3. Run `yarn install` to update node_modules');
}

/**
 * Show help information
 */
function showHelp() {
  console.log('Enhanced Yarn Workspaces Management Script\n');
  console.log('Usage: node scripts/manage-workspaces.js <command> [options]\n');
  console.log('Commands:');
  console.log('  list                 List all workspaces with details');
  console.log('  sync-deps            Synchronize dependencies across workspaces');
  console.log('  run <command>        Run a yarn command in all workspaces');
  console.log('  validate             Validate workspace configuration');
  console.log('  dedupe               Find and fix duplicate dependencies');
  console.log('  audit                Audit dependencies for security issues');
  console.log('  doctor               Run comprehensive workspace health checks');
  console.log('  help                 Show this help information');
}

// Main execution
try {
  switch (command) {
    case 'list':
      listWorkspaces();
      break;
    case 'sync-deps':
      syncDependencies();
      break;
    case 'run':
      if (options.length === 0) {
        console.error('Error: No command specified to run in workspaces');
        console.log('Usage: node manage-workspaces.js run <command>');
      } else {
        runInWorkspaces(options[0]);
      }
      break;
    case 'validate':
      validateWorkspaces();
      break;
    case 'dedupe':
      dedupeDependencies();
      break;
    case 'audit':
      auditDependencies();
      break;
    case 'doctor':
      runDoctor();
      break;
    case 'help':
    default:
      showHelp();
      break;
  }
} catch (error) {
  console.error('Error executing command:', error.message);
  process.exit(1);
}