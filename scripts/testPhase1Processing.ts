import axios from 'axios';

// Define the TargetSector enum locally for the script
// This should ideally be imported if the script is part of the same TS project and configured for module resolution
enum TargetSector {
  WaterTreatment,
  PowerPlantWetCooled,
  PowerPlantDryCooled,
  PowerPlantNuclear,
  HVAC,
  Mines,
  OilAndGas,
  Agriculture,
  CoolingTowers,
}

// Define ThemedLeadProfile interface locally
interface ThemedLeadProfile {
  id: string;
  fictionalCompanyName: string;
  contactPerson?: string;
  role?: string;
  sector: TargetSector | string; // Allow string for sending, server will convert
  fictionalNeeds: string[];
  email?: string;
}

// Define ProcessedLeadOutput interface locally
interface ProcessedLeadOutput {
  leadId: string;
  sector: TargetSector; // Assuming server returns the numeric enum value
  analysisSummary: string;
  draftEmail: string;
}

const API_URL = 'http://localhost:3001/api/v1/process-lead';

const sampleLeads: ThemedLeadProfile[] = [
  {
    id: 'lead-001-mine',
    fictionalCompanyName: 'DeepRock Mining Corp',
    contactPerson: '<PERSON>',
    role: 'Procurement Manager',
    sector: TargetSector.Mines, // Using numeric value
    fictionalNeeds: ['Improve efficiency of ore extraction', 'Reduce water usage in processing plants', 'Enhance safety protocols for underground operations'],
    email: '<EMAIL>'
  },
  {
    id: 'lead-002-nuclear',
    fictionalCompanyName: 'Atomic Future Energy',
    sector: TargetSector.PowerPlantNuclear, // Using numeric value
    fictionalNeeds: ['Upgrade cooling system monitoring', 'Implement AI for predictive maintenance', 'Source new radiation shielding materials'],
    contactPerson: 'Dr. Emmett Brown',
    role: 'Chief Scientist'
  },
  {
    id: 'lead-003-agri',
    fictionalCompanyName: 'GreenFields Agriculture Ltd.',
    sector: TargetSector.Agriculture, // Using numeric value
    fictionalNeeds: ['Optimize irrigation systems for water conservation', 'Implement drone technology for crop monitoring', 'Source organic pest control solutions'],
    email: '<EMAIL>'
  },
  {
    id: 'lead-004-hvac',
    fictionalCompanyName: 'ClimateControl Solutions Inc.',
    contactPerson: 'John Doe',
    role: 'Operations Director',
    sector: "HVAC", // Sending as string, server should handle this
    fictionalNeeds: ['Develop smart thermostats with energy-saving algorithms', 'Source eco-friendly refrigerants', 'Improve air filtration systems for commercial buildings'],
  },
  {
    id: 'lead-005-oilgas',
    fictionalCompanyName: 'Offshore Drilling Experts',
    sector: TargetSector.OilAndGas, // Using numeric value
    fictionalNeeds: ['Enhance subsea pipeline inspection technology', 'Reduce emissions from offshore platforms', 'Improve emergency response systems'],
    email: '<EMAIL>'
  }
];

async function testLeadProcessing() {
  console.log('Starting Phase 1 Lead Processing Test...\n');

  for (const lead of sampleLeads) {
    console.log('--------------------------------------------------');
    console.log(`Sending Lead Data for: ${lead.fictionalCompanyName} (ID: ${lead.id})`);
    console.log('Original Lead Data:');
    console.dir(lead, { depth: null });
    console.log('\n');

    try {
      const response = await axios.post<ProcessedLeadOutput>(API_URL, lead);
      console.log('Response Received (Status Code:', response.status, '):');
      console.dir(response.data, { depth: null });
    } catch (error: any) { // Changed to error: any
      console.error(`Error processing lead ${lead.id}:`);
      // Attempt to check if it's an Axios error by looking for characteristic properties
      // as axios.isAxiosError and imported AxiosError type might be affected by type conflicts
      if (error && (error.isAxiosError === true || (error.response && error.request))) {
        // If it looks like an Axios error, treat it as such (though type might be 'any')
        if (error.response) {
          console.error('Status:', error.response.status);
          console.error('Data:', error.response.data);
        } else if (error.request) {
          // The request was made but no response was received
          console.error('No response received (request made):', error.request);
        } else {
          // Something else happened while setting up the request
          console.error('Error message:', error.message);
        }
      } else {
        // Not an Axios error or doesn't fit the expected structure
        console.error('Non-Axios error or unknown error structure:', error);
      }
    }
    console.log('--------------------------------------------------\n');
  }
}

testLeadProcessing().catch(err => {
  console.error("Unhandled error in test script:", err);
});
