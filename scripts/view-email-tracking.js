#!/usr/bin/env node

/**
 * View Email Tracking Script
 *
 * This script displays tracking information for a specific email
 * to demonstrate the CRM integration and email tracking features.
 *
 * Usage:
 *   npx ts-node scripts/view-email-tracking.js --id email_12345
 */

const sqlite3 = require('better-sqlite3');
const path = require('path');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('id', {
    alias: 'i',
    description: 'Email ID to view tracking for',
    type: 'string',
    demandOption: true
  })
  .option('watch', {
    alias: 'w',
    description: 'Watch for changes (refresh every 5 seconds)',
    type: 'boolean',
    default: false
  })
  .help()
  .alias('help', 'h')
  .argv;

// Configuration
const config = {
  dbPath: path.join(__dirname, '..', 'data', 'test-crm.db')
};

// Get email details
function getEmailDetails(db, emailId) {
  const email = db.prepare('SELECT * FROM emails WHERE id = ?').get(emailId);

  if (!email) {
    console.error(`Email with ID ${emailId} not found`);
    process.exit(1);
  }

  // Parse metadata
  let metadata = {};
  try {
    metadata = JSON.parse(email.metadata);
  } catch (error) {
    console.warn('Failed to parse email metadata');
  }

  // Get lead information if available
  let lead = null;
  if (metadata.leadId) {
    lead = db.prepare('SELECT * FROM leads WHERE id = ?').get(metadata.leadId);
  }

  return { email, lead, metadata };
}

// Get email engagements
function getEmailEngagements(db, emailId) {
  return db.prepare(`
    SELECT * FROM email_engagements
    WHERE emailId = ?
    ORDER BY timestamp DESC
  `).all(emailId);
}

// Get tracking pixels
function getTrackingPixels(db, emailId) {
  return db.prepare(`
    SELECT * FROM email_tracking_pixels
    WHERE emailId = ?
  `).all(emailId);
}

// Get tracking links
function getTrackingLinks(db, emailId) {
  return db.prepare(`
    SELECT * FROM email_tracking_links
    WHERE emailId = ?
  `).all(emailId);
}

// Display email tracking information
function displayEmailTracking(emailId) {
  try {
    // Initialize database
    const db = new sqlite3(config.dbPath);

    // Get email details
    const { email, lead, metadata } = getEmailDetails(db, emailId);

    // Get engagements
    const engagements = getEmailEngagements(db, emailId);

    // Get tracking pixels
    const trackingPixels = getTrackingPixels(db, emailId);

    // Get tracking links
    const trackingLinks = getTrackingLinks(db, emailId);

    // Clear console if watching
    if (argv.watch) {
      console.clear();
    }

    // Display email information
    console.log('\n=== EMAIL INFORMATION ===');
    console.log('ID:', email.id);
    console.log('Recipient:', email.recipient);
    console.log('Subject:', email.subject);
    console.log('Status:', email.status);
    console.log('Sent At:', email.sentAt ? new Date(email.sentAt).toLocaleString() : 'Not sent');

    // Display lead information if available
    if (lead) {
      console.log('\n=== LEAD INFORMATION ===');
      console.log('ID:', lead.id);
      console.log('Name:', `${lead.firstName} ${lead.lastName}`);
      console.log('Company:', lead.company);
      console.log('Status:', lead.status);
    }

    // Display engagement information
    console.log('\n=== ENGAGEMENT SUMMARY ===');

    const openCount = engagements.filter(e => e.type === 'open').length;
    const clickCount = engagements.filter(e => e.type === 'click').length;
    const replyCount = engagements.filter(e => e.type === 'reply').length;

    console.log('Opens:', openCount);
    console.log('Clicks:', clickCount);
    console.log('Replies:', replyCount);

    // Display detailed engagement information
    if (engagements.length > 0) {
      console.log('\n=== ENGAGEMENT DETAILS ===');

      engagements.forEach((engagement, index) => {
        let engagementMetadata = {};
        try {
          engagementMetadata = JSON.parse(engagement.metadata);
        } catch (error) {
          // Ignore parsing errors
        }

        console.log(`\nEngagement #${index + 1}:`);
        console.log('Type:', getEngagementTypeEmoji(engagement.type), engagement.type);
        console.log('Time:', new Date(engagement.timestamp).toLocaleString());

        if (engagement.type === 'click' && engagementMetadata.url) {
          console.log('URL:', engagementMetadata.url);
        }

        if (engagementMetadata.userAgent) {
          console.log('User Agent:', engagementMetadata.userAgent);
        }

        if (engagementMetadata.ipAddress) {
          console.log('IP Address:', engagementMetadata.ipAddress);
        }
      });
    }

    // Display tracking information
    console.log('\n=== TRACKING INFORMATION ===');
    console.log('Tracking Pixels:', trackingPixels.length);
    console.log('Tracking Links:', trackingLinks.length);

    if (trackingLinks.length > 0) {
      console.log('\nTracking Links:');
      trackingLinks.forEach((link, index) => {
        console.log(`  ${index + 1}. ${link.originalUrl} -> ${link.trackingUrl}`);
      });
    }

    // Close database connection
    db.close();

    // If watching, set up timer to refresh
    if (argv.watch) {
      console.log(`\nRefreshing every 5 seconds... Press Ctrl+C to exit`);
      setTimeout(() => displayEmailTracking(emailId), 5000);
    }
  } catch (error) {
    console.error('Error displaying email tracking:', error);
    process.exit(1);
  }
}

// Get emoji for engagement type
function getEngagementTypeEmoji(type) {
  switch (type) {
    case 'open':
      return '👁️ ';
    case 'click':
      return '🖱️ ';
    case 'reply':
      return '💬 ';
    default:
      return '';
  }
}

// Main function
function main() {
  try {
    displayEmailTracking(argv.id);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run the main function
main();
