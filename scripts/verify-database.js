#!/usr/bin/env node

/**
 * Database verification script
 * This script checks the database for common issues and fixes them
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Configuration
const dbPath = path.join(process.cwd(), 'data/mexel.db');
const requiredTables = [
    'migrations',
    'leads',
    'activities',
    'sequences',
    'tags',
    'lead_tags',
    'email_templates',
    'tenders'
];

// Ensure data directory exists
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
    console.log(`Creating data directory: ${dataDir}`);
    fs.mkdirSync(dataDir, { recursive: true });
}

// Check if database file exists
if (!fs.existsSync(dbPath)) {
    console.log(`Database file does not exist: ${dbPath}`);
    console.log('Run migrations to create the database:');
    console.log('  yarn db:migrate');
    process.exit(1);
}

// Connect to the database
console.log(`Connecting to database: ${dbPath}`);
const db = new Database(dbPath);

// Check for required tables
console.log('Checking for required tables...');
const existingTables = db.prepare(`
    SELECT name FROM sqlite_master
    WHERE type='table'
`).all().map(row => row.name);

const missingTables = requiredTables.filter(table => !existingTables.includes(table));

if (missingTables.length > 0) {
    console.log(`Missing tables: ${missingTables.join(', ')}`);
    console.log('Run migrations to create missing tables:');
    console.log('  yarn db:migrate');
} else {
    console.log('All required tables exist.');
}

// Check migrations table schema
console.log('Checking migrations table schema...');
const migrationsColumns = db.prepare(`
    PRAGMA table_info(migrations)
`).all().map(row => row.name);

const requiredMigrationsColumns = ['version', 'description', 'appliedAt', 'status', 'rollbackSql', 'timestamp'];
const missingMigrationsColumns = requiredMigrationsColumns.filter(column => !migrationsColumns.includes(column));

if (missingMigrationsColumns.length > 0) {
    console.log(`Missing columns in migrations table: ${missingMigrationsColumns.join(', ')}`);
    console.log('Run the fix-migrations-table migration:');
    console.log('  yarn db:migrate');
} else {
    console.log('Migrations table schema is correct.');
}

// Check email_templates table schema
if (existingTables.includes('email_templates')) {
    console.log('Checking email_templates table schema...');
    const emailTemplatesColumns = db.prepare(`
        PRAGMA table_info(email_templates)
    `).all().map(row => row.name);

    const requiredEmailTemplatesColumns = ['name', 'subject', 'body', 'metadata', 'followUpScheduleDays', 'abTestCampaignId', 'createdAt', 'updatedAt'];
    const missingEmailTemplatesColumns = requiredEmailTemplatesColumns.filter(column => !emailTemplatesColumns.includes(column));

    if (missingEmailTemplatesColumns.length > 0) {
        console.log(`Missing columns in email_templates table: ${missingEmailTemplatesColumns.join(', ')}`);
    } else {
        console.log('Email templates table schema is correct.');
    }
}

// Check tenders table schema
if (existingTables.includes('tenders')) {
    console.log('Checking tenders table schema...');
    const tendersColumns = db.prepare(`
        PRAGMA table_info(tenders)
    `).all().map(row => row.name);

    const requiredTendersColumns = ['id', 'title', 'description', 'url', 'status', 'issuer', 'publishDate', 'closingDate', 'value', 'category', 'confidence', 'metadata'];
    const missingTendersColumns = requiredTendersColumns.filter(column => !tendersColumns.includes(column));

    if (missingTendersColumns.length > 0) {
        console.log(`Missing columns in tenders table: ${missingTendersColumns.join(', ')}`);
        console.log('Run the add-tenders-table migration:');
        console.log('  yarn db:migrate');
    } else {
        console.log('Tenders table schema is correct.');
    }
}

// Close the database connection
db.close();
console.log('Database verification completed.');
