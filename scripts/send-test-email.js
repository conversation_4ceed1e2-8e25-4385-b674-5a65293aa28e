#!/usr/bin/env node

/**
 * Send Test Email Script
 *
 * This script sends a real test email with tracking pixels and links
 * to demonstrate the CRM integration and email tracking features.
 *
 * Usage:
 *   npx ts-node scripts/send-test-email.js --to <EMAIL>
 */

const nodemailer = require('nodemailer');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const sqlite3 = require('better-sqlite3');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('to', {
    alias: 't',
    description: 'Email recipient',
    type: 'string',
    demandOption: true
  })
  .option('name', {
    alias: 'n',
    description: 'Recipient name',
    type: 'string',
    default: 'Valued Customer'
  })
  .option('company', {
    alias: 'c',
    description: 'Recipient company',
    type: 'string',
    default: 'Your Company'
  })
  .help()
  .alias('help', 'h')
  .argv;

// Configuration
const config = {
  smtp: {
    host: process.env.SMTP_HOST || 'smtp-relay.brevo.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER || 'your-brevo-username',
      pass: process.env.SMTP_PASS || 'your-brevo-password'
    }
  },
  sender: process.env.EMAIL_SENDER || '<EMAIL>',
  trackingDomain: process.env.TRACKING_DOMAIN || 'localhost:3001',
  dbPath: path.join(__dirname, '..', 'data', 'test-crm.db')
};

// Initialize database
function initializeDatabase() {
  // Ensure data directory exists
  const dataDir = path.join(__dirname, '..', 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }

  // Initialize SQLite database
  const db = new sqlite3(config.dbPath);

  // Create necessary tables if they don't exist
  db.exec(`
    CREATE TABLE IF NOT EXISTS leads (
      id TEXT PRIMARY KEY,
      email TEXT NOT NULL,
      firstName TEXT,
      lastName TEXT,
      company TEXT,
      position TEXT,
      industry TEXT,
      status TEXT NOT NULL,
      lastContactDate TEXT,
      metadata TEXT
    );

    CREATE TABLE IF NOT EXISTS emails (
      id TEXT PRIMARY KEY,
      recipient TEXT NOT NULL,
      subject TEXT NOT NULL,
      body TEXT NOT NULL,
      status TEXT NOT NULL DEFAULT 'queued',
      scheduledDate TEXT,
      sentAt TEXT,
      metadata TEXT,
      createdAt TEXT NOT NULL DEFAULT (datetime('now')),
      updatedAt TEXT NOT NULL DEFAULT (datetime('now'))
    );

    CREATE TABLE IF NOT EXISTS email_engagements (
      id TEXT PRIMARY KEY,
      emailId TEXT NOT NULL,
      type TEXT NOT NULL,
      timestamp TEXT NOT NULL DEFAULT (datetime('now')),
      metadata TEXT,
      FOREIGN KEY (emailId) REFERENCES emails(id)
    );

    CREATE TABLE IF NOT EXISTS email_tracking_pixels (
      id TEXT PRIMARY KEY,
      emailId TEXT NOT NULL,
      token TEXT NOT NULL,
      createdAt TEXT NOT NULL DEFAULT (datetime('now')),
      FOREIGN KEY (emailId) REFERENCES emails(id)
    );

    CREATE TABLE IF NOT EXISTS email_tracking_links (
      id TEXT PRIMARY KEY,
      emailId TEXT NOT NULL,
      originalUrl TEXT NOT NULL,
      trackingUrl TEXT NOT NULL,
      createdAt TEXT NOT NULL DEFAULT (datetime('now')),
      FOREIGN KEY (emailId) REFERENCES emails(id)
    );
  `);

  return db;
}

// Generate tracking pixel
function generateTrackingPixel(db, emailId) {
  const token = uuidv4();

  // Store tracking pixel in database
  db.prepare(`
    INSERT INTO email_tracking_pixels (
      id, emailId, token
    ) VALUES (?, ?, ?)
  `).run(`pixel_${uuidv4()}`, emailId, token);

  // Return tracking pixel HTML
  return `<img src="http://${config.trackingDomain}/track/open/${token}" width="1" height="1" alt="" style="display:none;"/>`;
}

// Generate tracking link
function generateTrackingLink(db, emailId, originalUrl, linkText) {
  const token = uuidv4();

  // Store tracking link in database
  db.prepare(`
    INSERT INTO email_tracking_links (
      id, emailId, originalUrl, trackingUrl
    ) VALUES (?, ?, ?, ?)
  `).run(
    `link_${uuidv4()}`,
    emailId,
    originalUrl,
    `http://${config.trackingDomain}/track/click/${token}`
  );

  // Return tracking link HTML
  return `<a href="http://${config.trackingDomain}/track/click/${token}">${linkText}</a>`;
}

// Create or get lead
function createOrGetLead(db, email, name, company) {
  // Check if lead exists
  const existingLead = db.prepare('SELECT * FROM leads WHERE email = ?').get(email);

  if (existingLead) {
    return existingLead;
  }

  // Parse name into first and last name
  const nameParts = name.split(' ');
  const firstName = nameParts[0];
  const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

  // Create new lead
  const leadId = `lead_${uuidv4()}`;

  db.prepare(`
    INSERT INTO leads (
      id, email, firstName, lastName, company, status
    ) VALUES (?, ?, ?, ?, ?, ?)
  `).run(
    leadId,
    email,
    firstName,
    lastName,
    company,
    'NEW'
  );

  return {
    id: leadId,
    email,
    firstName,
    lastName,
    company,
    status: 'NEW'
  };
}

// Send email with tracking
async function sendEmailWithTracking(recipient, recipientName, recipientCompany) {
  try {
    console.log(`Sending test email to ${recipient}...`);

    // Initialize database
    const db = initializeDatabase();

    // Create or get lead
    const lead = createOrGetLead(db, recipient, recipientName, recipientCompany);
    console.log(`Using lead: ${lead.id} (${lead.firstName} ${lead.lastName} from ${lead.company})`);

    // Create email record
    const emailId = `email_${uuidv4()}`;
    const now = new Date().toISOString();
    const subject = `Mexel Water Treatment Solutions for ${lead.company}`;

    // First create the email record in the database
    db.prepare(`
      INSERT INTO emails (
        id, recipient, subject, body, status, metadata, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      emailId,
      recipient,
      subject,
      '<p>Placeholder body - will be updated</p>',
      'draft',
      JSON.stringify({
        leadId: lead.id,
        type: 'test_email'
      }),
      now,
      now
    );

    console.log(`Created email record with ID: ${emailId}`);

    // Generate tracking pixel
    const trackingPixel = generateTrackingPixel(db, emailId);

    // Generate tracking links
    const websiteLink = generateTrackingLink(
      db,
      emailId,
      'https://www.mexelenergysustain.com',
      'Visit our website'
    );

    const productLink = generateTrackingLink(
      db,
      emailId,
      'https://www.mexelenergysustain.com/products',
      'Learn about our products'
    );

    const meetingLink = generateTrackingLink(
      db,
      emailId,
      'https://calendly.com/mexel/meeting',
      'Schedule a meeting'
    );

    // Create email body
    const body = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; color: #333;">
        <p>Dear ${lead.firstName},</p>

        <p>I hope this email finds you well. I'm reaching out regarding Mexel's water treatment solutions that might be of interest to ${lead.company}.</p>

        <p>Our Film-Forming Amine (FFA) technology has been proven to:</p>

        <ul>
          <li>Reduce chemical usage by up to 40%</li>
          <li>Improve energy efficiency by 15-25%</li>
          <li>Extend equipment lifespan</li>
          <li>Ensure environmental compliance</li>
        </ul>

        <p>Would you be available for a brief call to discuss how our solutions can benefit your operations?</p>

        <p>
          ${websiteLink} | ${productLink} | ${meetingLink}
        </p>

        <p>Best regards,<br>
        Mexel Water Treatment Solutions Team</p>

        ${trackingPixel}
      </div>
    `;

    // Update email in database with final body
    db.prepare(`
      UPDATE emails
      SET body = ?, status = ?, updatedAt = ?
      WHERE id = ?
    `).run(
      body,
      'queued',
      now,
      emailId
    );

    // Create test SMTP account using Ethereal
    const testAccount = await nodemailer.createTestAccount();
    console.log('Created Ethereal test account:', testAccount.user);

    // Create SMTP transport using Ethereal
    const transporter = nodemailer.createTransport({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: testAccount.user,
        pass: testAccount.pass
      }
    });

    // Send email
    const info = await transporter.sendMail({
      from: `"Mexel Energy Solutions" <${testAccount.user}>`,
      to: recipient,
      subject: subject,
      html: body
    });

    // Update email status
    db.prepare(`
      UPDATE emails
      SET status = ?, sentAt = ?
      WHERE id = ?
    `).run('sent', now, emailId);

    console.log(`Email sent successfully: ${info.messageId}`);
    console.log(`Email ID for tracking: ${emailId}`);

    // Get and display the Ethereal URL where you can view the email
    const previewUrl = nodemailer.getTestMessageUrl(info);
    console.log(`Preview URL: ${previewUrl}`);
    console.log('Open this URL in your browser to view the email');

    // Close database connection
    db.close();

    return {
      success: true,
      emailId,
      messageId: info.messageId,
      previewUrl
    };
  } catch (error) {
    console.error('Failed to send email:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Main function
async function main() {
  try {
    const result = await sendEmailWithTracking(
      argv.to,
      argv.name,
      argv.company
    );

    if (result.success) {
      console.log('\n========================================================');
      console.log('EMAIL SENT SUCCESSFULLY!');
      console.log('========================================================');
      console.log('\nView the email in your browser:');
      console.log(result.previewUrl);
      console.log('\nTo view tracking data, run:');
      console.log(`npx ts-node scripts/view-email-tracking.js --id ${result.emailId}`);
      console.log('\nTo simulate email opens and clicks, run:');
      console.log(`npx ts-node scripts/simulate-engagement.js --id ${result.emailId} --type open`);
      console.log(`npx ts-node scripts/simulate-engagement.js --id ${result.emailId} --type click`);
      console.log('========================================================');
    } else {
      process.exit(1);
    }
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run the main function
main();
