/**
 * Simple script to test the TenderMonitor Agent
 */

const { TenderMonitorAgent } = require('../src/agents/TenderMonitorAgent');
const { Logger } = require('../src/utils/Logger');

// Initialize logger
const logger = Logger.getInstance('TestTenderMonitor');

// Create agent instance
const agent = new TenderMonitorAgent();

// Set up event listeners
agent.on('monitoring_complete', (data) => {
    logger.info('Monitoring complete', data);
});

agent.on('tender_processed', (tender) => {
    logger.info('Tender processed', {
        id: tender.id,
        title: tender.title,
        confidence: tender.confidence
    });
});

agent.on('high_priority_tender', (tender) => {
    logger.info('High priority tender identified', {
        id: tender.id,
        title: tender.title,
        confidence: tender.confidence
    });
});

// Main function
async function main() {
    try {
        logger.info('Initializing TenderMonitor Agent...');
        await agent.initialize();

        logger.info('Starting TenderMonitor Agent...');
        await agent.start();

        logger.info('Executing TenderMonitor Agent...');
        await agent.execute();

        logger.info('Test completed successfully');
    } catch (error) {
        logger.error('Error testing TenderMonitor Agent', { error });
    } finally {
        // Clean up
        await agent.stop();
        setTimeout(() => process.exit(0), 1000); // Give time for logs to flush
    }
}

// Run the test
main().catch(error => {
    logger.error('Unhandled error in test script', { error });
    setTimeout(() => process.exit(1), 1000); // Give time for logs to flush
});
