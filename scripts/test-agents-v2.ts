#!/usr/bin/env ts-node

/**
 * Agent system test script (v2)
 * This script tests the v2 agent system
 */

import * as dotenv from "dotenv";
import { CoordinatorAgent } from "../src/agents/v2/implementations/CoordinatorAgent";
import { TenderMonitorAgent } from "../src/agents/v2/implementations/TenderMonitorAgent";
import { OutreachEmailAgent } from "../src/agents/v2/implementations/OutreachEmailAgent";
import { AnalyticsAgent } from "../src/agents/v2/implementations/AnalyticsAgent";
import { Logger } from "../src/utils/Logger";
import { AgentRole, AgentStatus } from "../src/agents/v2/interfaces/IAgent";
import { BaseAgent } from "../src/agents/v2/base/BaseAgent";

// Load environment variables
dotenv.config();

// Create logger
const logger = Logger.getInstance("AgentTest");

// Parse command line arguments
const args = process.argv.slice(2);
const testMode = args[0] || "all";

/**
 * Simple test agent implementation
 */
class TestAgent extends BaseAgent {
  private testData: any = {};

  constructor(name: string, role: AgentRole) {
    super(name, role);
  }

  protected async initializeImpl(): Promise<void> {
    this.logger.info("Initializing test agent");
    // Simulate initialization
    await new Promise((resolve) => setTimeout(resolve, 500));
    this.testData = { initialized: true, timestamp: new Date() };
  }

  protected async executeImpl(): Promise<void> {
    this.logger.info("Executing test agent");
    // Simulate work
    await new Promise((resolve) => setTimeout(resolve, 1000));
    this.testData.lastExecution = new Date();
    this.testData.executionCount = (this.testData.executionCount || 0) + 1;
  }

  protected async shutdownImpl(): Promise<void> {
    this.logger.info("Shutting down test agent");
    // Simulate cleanup
    await new Promise((resolve) => setTimeout(resolve, 500));
    this.testData = {};
  }

  protected async resetImpl(): Promise<void> {
    this.logger.info("Resetting test agent");
    this.testData = { initialized: true, timestamp: new Date() };
  }

  protected getCustomMetrics(): Record<string, any> {
    return {
      executionCount: this.testData.executionCount || 0,
      lastExecution: this.testData.lastExecution,
    };
  }
}

/**
 * Main function
 */
async function main() {
  logger.info("Starting Mexel Agent Test (v2)");
  logger.info(`Test mode: ${testMode}`);

  try {
    // Create coordinator agent
    const coordinator = new CoordinatorAgent("TestCoordinator");

    // Initialize coordinator
    logger.info("Initializing coordinator agent");
    await coordinator.initialize();
    logger.info("Coordinator agent initialized successfully");

    // Create and register real agents
    if (testMode === "all" || testMode === "tender") {
      const tenderAgent = new TenderMonitorAgent("TenderMonitor");
      await tenderAgent.initialize();
      coordinator.registerAgent(tenderAgent);
      logger.info("Tender monitor agent registered");
    }

    if (testMode === "all" || testMode === "email") {
      const emailAgent = new OutreachEmailAgent("OutreachEmail");
      await emailAgent.initialize();
      coordinator.registerAgent(emailAgent);
      logger.info("Outreach email agent registered");
    }

    if (testMode === "all" || testMode === "analytics") {
      const analyticsAgent = new AnalyticsAgent("Analytics");
      await analyticsAgent.initialize();
      coordinator.registerAgent(analyticsAgent);
      logger.info("Analytics agent registered");
    }

    // Start the coordinator
    logger.info("Starting coordinator agent");
    await coordinator.start();

    // Execute the coordinator
    logger.info("Executing coordinator agent");
    await coordinator.execute();

    // Get agent status
    const agentStatus = coordinator.getAgentStatus();
    logger.info(
      "Agent status:",
      agentStatus as unknown as Record<string, unknown>
    );

    // Execute each agent directly
    for (const agent of coordinator.getAgents()) {
      logger.info(`Executing agent directly: ${agent.name}`);
      await agent.execute();

      // Get agent metrics
      const metrics = agent.getMetrics();
      logger.info(
        `Agent metrics for ${agent.name}:`,
        metrics as unknown as Record<string, unknown>
      );
    }

    // Execute the coordinator again
    logger.info("Executing coordinator agent again");
    await coordinator.execute();

    // Get updated agent status
    const updatedAgentStatus = coordinator.getAgentStatus();
    logger.info(
      "Updated agent status:",
      updatedAgentStatus as unknown as Record<string, unknown>
    );

    // Shutdown the coordinator
    logger.info("Shutting down coordinator agent");
    await coordinator.shutdown();

    logger.info("Agent test completed successfully");
  } catch (error) {
    logger.error("Failed to run agent test", {
      error:
        error instanceof Error
          ? {
              message: error.message,
              stack: error.stack,
              name: error.name,
            }
          : String(error),
    });
    process.exit(1);
  }
}

// Run main function
main().catch((error) => {
  logger.error("Unhandled error", {
    error: error instanceof Error ? error : new Error(String(error)),
  });
  process.exit(1);
});
