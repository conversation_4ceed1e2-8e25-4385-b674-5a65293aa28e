#!/usr/bin/env node

/**
 * Mexel Project ESLint Runner
 * 
 * This script runs ESLint across the entire project or specific packages.
 * It ensures consistent linting across all parts of the codebase.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const PROJECT_ROOT = path.resolve(__dirname, '..');
const PACKAGES = {
  root: PROJECT_ROOT,
  shared: path.join(PROJECT_ROOT, 'shared'),
  frontend: path.join(PROJECT_ROOT, 'frontend')
};

// Command line arguments
const args = process.argv.slice(2);
const FIX_MODE = args.includes('--fix');
const QUIET_MODE = args.includes('--quiet');
const SPECIFIED_PACKAGES = args
  .filter(arg => !arg.startsWith('--'))
  .filter(arg => Object.keys(PACKAGES).includes(arg));

// If no packages specified, lint all
const packagesToLint = SPECIFIED_PACKAGES.length > 0 
  ? SPECIFIED_PACKAGES 
  : Object.keys(PACKAGES);

// Build options
const eslintOptions = [
  FIX_MODE ? '--fix' : '',
  QUIET_MODE ? '--quiet' : '',
  '--max-warnings=0'
].filter(Boolean).join(' ');

console.log(`🔍 Linting the following packages: ${packagesToLint.join(', ')}`);
if (FIX_MODE) console.log('🔧 Auto-fix mode enabled');

let hasErrors = false;

// Run ESLint for each package
packagesToLint.forEach(pkg => {
  const packagePath = PACKAGES[pkg];
  
  // Skip if package directory doesn't exist
  if (!fs.existsSync(packagePath)) {
    console.log(`⚠️ Package directory not found: ${packagePath}`);
    return;
  }
  
  // Skip if no ESLint config exists
  const hasEslintConfig = fs.existsSync(path.join(packagePath, '.eslintrc.json')) || 
                        fs.existsSync(path.join(packagePath, '.eslintrc.js')) ||
                        pkg === 'root'; // Root always has config
  
  if (!hasEslintConfig) {
    console.log(`⚠️ No ESLint config found for package: ${pkg}`);
    return;
  }
  
  // Determine which paths to lint
  let pathsToLint;
  
  if (pkg === 'root') {
    pathsToLint = [
      './src',
      './scripts',
      './*.js',
      // Exclude other packages
      '!./frontend/**',
      '!./shared/**',
      '!./node_modules/**'
    ].join(' ');
  } else {
    pathsToLint = '.';
  }
  
  // Build and execute command
  const command = `npx eslint ${pathsToLint} ${eslintOptions}`;
  
  console.log(`\n🔍 Linting ${pkg} package...`);
  try {
    execSync(command, { 
      cwd: packagePath, 
      stdio: QUIET_MODE ? 'pipe' : 'inherit' 
    });
    console.log(`✅ ${pkg}: No linting errors found`);
  } catch (error) {
    hasErrors = true;
    if (QUIET_MODE) {
      console.error(`❌ ${pkg}: Linting errors found`);
    } else {
      console.error(`❌ ${pkg}: Linting errors found (see above)`);
    }
  }
});

// Summary
console.log('\n=== ESLint Summary ===');
if (hasErrors) {
  console.error('❌ Linting errors found in one or more packages');
  process.exit(1);
} else {
  console.log('✅ All packages passed linting');
  process.exit(0);
}