#!/bin/bash

# Comprehensive Testing Script for Mexel Project
# This script runs all tests for the project, including unit tests, integration tests, and real data tests.

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
print_header() {
  echo -e "\n${BLUE}======================================${NC}"
  echo -e "${BLUE}$1${NC}"
  echo -e "${BLUE}======================================${NC}\n"
}

# Function to print success messages
print_success() {
  echo -e "${GREEN}✓ $1${NC}"
}

# Function to print error messages
print_error() {
  echo -e "${RED}✗ $1${NC}"
}

# Function to print warning messages
print_warning() {
  echo -e "${YELLOW}! $1${NC}"
}

# Function to run a command and check its exit status
run_command() {
  echo -e "Running: $1"
  eval $1
  if [ $? -eq 0 ]; then
    print_success "$2"
    return 0
  else
    print_error "$3"
    return 1
  fi
}

# Start the testing process
print_header "Starting Comprehensive Testing"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
  print_error "Node.js is not installed. Please install Node.js to run the tests."
  exit 1
fi

# Check if yarn is installed
if ! command -v yarn &> /dev/null; then
  print_error "yarn is not installed. Please install yarn to run the tests."
  exit 1
fi

# Check if Jest is installed
if ! yarn list jest &> /dev/null; then
  print_warning "Jest is not installed. Using local installation."
fi

# Create a directory for test results if it doesn't exist
mkdir -p test-results

# 1. Run unit tests for all agents
print_header "1. Running Unit Tests for Agents"

# TenderMonitorAgent
run_command "npx jest src/tests/agents/TenderMonitorAgent.test.ts --verbose" \
  "TenderMonitorAgent unit tests passed" \
  "TenderMonitorAgent unit tests failed"
TENDER_MONITOR_RESULT=$?

# OutreachEmailAgent
run_command "npx jest src/tests/agents/OutreachEmailAgent.test.ts --verbose" \
  "OutreachEmailAgent unit tests passed" \
  "OutreachEmailAgent unit tests failed"
OUTREACH_EMAIL_RESULT=$?

# AnalyticsAgent
run_command "npx jest src/tests/agents/AnalyticsAgent.test.ts --verbose" \
  "AnalyticsAgent unit tests passed" \
  "AnalyticsAgent unit tests failed"
ANALYTICS_RESULT=$?

# CoordinatorAgent
run_command "npx jest src/tests/agents/CoordinatorAgent.test.ts --verbose" \
  "CoordinatorAgent unit tests passed" \
  "CoordinatorAgent unit tests failed"
COORDINATOR_RESULT=$?

# 2. Run integration tests
print_header "2. Running Integration Tests"

run_command "npx jest src/tests/integration/AgentIntegration.test.ts --verbose" \
  "Integration tests passed" \
  "Integration tests failed"
INTEGRATION_RESULT=$?

# 3. Run real data tests (if requested)
print_header "3. Real Data Tests"
echo "Real data tests require actual external services and should be run manually."
echo "To run real data tests, use: node dist/tests/real-data/RealDataTest.js"

# Compile TypeScript files for real data tests
run_command "yarn tsc" \
  "TypeScript compilation successful" \
  "TypeScript compilation failed"
TS_COMPILE_RESULT=$?

# Ask if user wants to run real data tests
read -p "Do you want to run real data tests now? (y/n): " RUN_REAL_DATA_TESTS

if [ "$RUN_REAL_DATA_TESTS" = "y" ] || [ "$RUN_REAL_DATA_TESTS" = "Y" ]; then
  if [ $TS_COMPILE_RESULT -eq 0 ]; then
    run_command "node dist/tests/real-data/RealDataTest.js" \
      "Real data tests passed" \
      "Real data tests failed"
    REAL_DATA_RESULT=$?
  else
    print_error "Cannot run real data tests due to TypeScript compilation failure"
    REAL_DATA_RESULT=1
  fi
else
  print_warning "Skipping real data tests"
  REAL_DATA_RESULT=0
fi

# 4. Generate test report
print_header "4. Test Results Summary"

# Calculate total results
TOTAL_TESTS=5
PASSED_TESTS=0

if [ $TENDER_MONITOR_RESULT -eq 0 ]; then
  PASSED_TESTS=$((PASSED_TESTS + 1))
fi

if [ $OUTREACH_EMAIL_RESULT -eq 0 ]; then
  PASSED_TESTS=$((PASSED_TESTS + 1))
fi

if [ $ANALYTICS_RESULT -eq 0 ]; then
  PASSED_TESTS=$((PASSED_TESTS + 1))
fi

if [ $COORDINATOR_RESULT -eq 0 ]; then
  PASSED_TESTS=$((PASSED_TESTS + 1))
fi

if [ $INTEGRATION_RESULT -eq 0 ]; then
  PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Print summary
echo "Unit Tests:"
if [ $TENDER_MONITOR_RESULT -eq 0 ]; then
  print_success "TenderMonitorAgent: PASSED"
else
  print_error "TenderMonitorAgent: FAILED"
fi

if [ $OUTREACH_EMAIL_RESULT -eq 0 ]; then
  print_success "OutreachEmailAgent: PASSED"
else
  print_error "OutreachEmailAgent: FAILED"
fi

if [ $ANALYTICS_RESULT -eq 0 ]; then
  print_success "AnalyticsAgent: PASSED"
else
  print_error "AnalyticsAgent: FAILED"
fi

if [ $COORDINATOR_RESULT -eq 0 ]; then
  print_success "CoordinatorAgent: PASSED"
else
  print_error "CoordinatorAgent: FAILED"
fi

echo -e "\nIntegration Tests:"
if [ $INTEGRATION_RESULT -eq 0 ]; then
  print_success "Agent Integration: PASSED"
else
  print_error "Agent Integration: FAILED"
fi

if [ "$RUN_REAL_DATA_TESTS" = "y" ] || [ "$RUN_REAL_DATA_TESTS" = "Y" ]; then
  echo -e "\nReal Data Tests:"
  if [ $REAL_DATA_RESULT -eq 0 ]; then
    print_success "Real Data Tests: PASSED"
  else
    print_error "Real Data Tests: FAILED"
  fi
fi

# Print overall summary
echo -e "\nOverall Summary:"
echo -e "Passed: $PASSED_TESTS/$TOTAL_TESTS tests"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
  print_success "All tests passed!"
  exit 0
else
  print_error "Some tests failed. Please check the logs for details."
  exit 1
fi
