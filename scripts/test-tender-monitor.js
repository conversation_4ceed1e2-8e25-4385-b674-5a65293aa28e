"use strict";
/**
 * <PERSON><PERSON><PERSON> to test the TenderMonitor Agent
 *
 * This script initializes and runs the TenderMonitor Agent to verify it works correctly.
 * It will scrape tenders, process them, and send notifications for high-priority tenders.
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var TenderMonitorAgent_1 = require("../src/agents/TenderMonitorAgent");
var Logger_1 = require("../src/utils/Logger");
// Initialize logger
var logger = Logger_1.Logger.getInstance('TestTenderMonitor');
// Create agent instance
var agent = new TenderMonitorAgent_1.TenderMonitorAgent();
// Set up event listeners
agent.on('monitoring_complete', function (data) {
    logger.info('Monitoring complete', data);
});
agent.on('tender_processed', function (tender) {
    logger.info('Tender processed', {
        id: tender.id,
        title: tender.title,
        confidence: tender.confidence
    });
});
agent.on('high_priority_tender', function (tender) {
    logger.info('High priority tender identified', {
        id: tender.id,
        title: tender.title,
        confidence: tender.confidence
    });
});
// Main function
function main() {
    return __awaiter(this, void 0, void 0, function () {
        var error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 5, 6, 8]);
                    logger.info('Initializing TenderMonitor Agent...');
                    return [4 /*yield*/, agent.initialize()];
                case 1:
                    _a.sent();
                    logger.info('Starting TenderMonitor Agent...');
                    return [4 /*yield*/, agent.start()];
                case 2:
                    _a.sent();
                    logger.info('Executing TenderMonitor Agent...');
                    return [4 /*yield*/, agent.execute()];
                case 3:
                    _a.sent();
                    logger.info('Processing tenders...');
                    return [4 /*yield*/, agent.processTenders()];
                case 4:
                    _a.sent();
                    logger.info('Test completed successfully');
                    return [3 /*break*/, 8];
                case 5:
                    error_1 = _a.sent();
                    logger.error('Error testing TenderMonitor Agent', { error: error_1 });
                    return [3 /*break*/, 8];
                case 6: 
                // Clean up
                return [4 /*yield*/, agent.stop()];
                case 7:
                    // Clean up
                    _a.sent();
                    process.exit(0);
                    return [7 /*endfinally*/];
                case 8: return [2 /*return*/];
            }
        });
    });
}
// Run the test
main().catch(function (error) {
    logger.error('Unhandled error in test script', { error: error });
    process.exit(1);
});
