#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to recreate the tenders table from scratch
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Configuration
const dbPath = path.join(process.cwd(), 'data/mexel.db');

// Ensure data directory exists
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
    console.log(`Creating data directory: ${dataDir}`);
    fs.mkdirSync(dataDir, { recursive: true });
}

// Connect to the database
console.log(`Connecting to database: ${dbPath}`);
const db = new Database(dbPath);

// Recreate tenders table
console.log('Recreating tenders table...');
try {
    // Drop the existing table
    db.exec('DROP TABLE IF EXISTS tenders');
    
    // Create the table with the correct schema
    db.exec(`
        CREATE TABLE tenders (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT,
            reference TEXT,
            issuer TEXT,
            publishDate TEXT,
            closingDate TEXT,
            status TEXT NOT NULL DEFAULT 'NEW',
            url TEXT NOT NULL,
            value REAL,
            category TEXT,
            tags TEXT,
            confidence REAL,
            metadata TEXT,
            documents TEXT,
            requirements TEXT,
            location TEXT,
            
            -- Legacy fields for compatibility with existing code
            source TEXT,
            closing_date TEXT,
            scrape_date TEXT,
            contact_person TEXT,
            contact_email TEXT,
            estimated_value REAL,
            categories TEXT,
            relevance_score REAL,
            historical_avg_value REAL,
            historical_success_rate REAL,
            opportunity_score REAL,
            first_seen TEXT NOT NULL DEFAULT (datetime('now')),
            last_updated TEXT NOT NULL DEFAULT (datetime('now'))
        )
    `);
    
    // Create indexes
    console.log('Creating indexes...');
    db.exec('CREATE INDEX IF NOT EXISTS idx_tenders_status ON tenders(status)');
    db.exec('CREATE INDEX IF NOT EXISTS idx_tenders_category ON tenders(category)');
    db.exec('CREATE INDEX IF NOT EXISTS idx_tenders_closingDate ON tenders(closingDate)');
    
    // Record the migration in the migrations table
    console.log('Recording migration...');
    db.prepare(`
        INSERT OR REPLACE INTO migrations (
            version, description, appliedAt, status, timestamp
        ) VALUES (?, ?, ?, ?, ?)
    `).run(
        5,
        'Add tenders table',
        new Date().toISOString(),
        'APPLIED',
        Date.now()
    );
    
    console.log('Tenders table recreated successfully.');
} catch (error) {
    console.error('Failed to recreate tenders table:', error.message);
}

// Close the database connection
db.close();
console.log('Script completed.');
