#!/usr/bin/env node

/**
 * Yarn Setup Script for Mexel
 * 
 * This script sets up the Yarn environment for the Mexel project:
 * - Downloads the correct version of Yarn
 * - Sets up necessary plugins
 * - Configures workspace settings
 * 
 * Usage: node scripts/setup-yarn.js
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');

// Configuration
const YARN_VERSION = '3.6.3';
const PROJECT_ROOT = path.resolve(__dirname, '..');
const YARN_RELEASES_DIR = path.join(PROJECT_ROOT, '.yarn', 'releases');
const YARN_PLUGINS_DIR = path.join(PROJECT_ROOT, '.yarn', 'plugins');
const YARN_BINARY_PATH = path.join(YARN_RELEASES_DIR, `yarn-${YARN_VERSION}.cjs`);

// Plugins to install
const PLUGINS = [
  '@yarnpkg/plugin-workspace-tools',
  '@yarnpkg/plugin-interactive-tools'
];

/**
 * Create directory if it doesn't exist
 */
function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
  }
}

/**
 * Download a file from URL to destination
 */
function downloadFile(url, destination) {
  return new Promise((resolve, reject) => {
    console.log(`Downloading ${url} to ${destination}...`);
    
    const file = fs.createWriteStream(destination);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${url}: ${response.statusCode} ${response.statusMessage}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded successfully: ${destination}`);
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(destination, () => {}); // Delete the file on error
      reject(err);
    });
  });
}

/**
 * Execute shell command and return output
 */
function execCommand(command, options = {}) {
  try {
    return execSync(command, {
      cwd: PROJECT_ROOT,
      stdio: options.silent ? 'pipe' : 'inherit',
      encoding: 'utf8',
      ...options
    });
  } catch (error) {
    if (options.ignoreError) {
      return '';
    }
    console.error(`Error executing command: ${command}`);
    console.error(error.message);
    process.exit(1);
  }
}

/**
 * Download the specified Yarn version
 */
async function downloadYarn() {
  if (fs.existsSync(YARN_BINARY_PATH)) {
    console.log(`Yarn ${YARN_VERSION} already downloaded.`);
    return;
  }
  
  ensureDirectoryExists(YARN_RELEASES_DIR);
  
  const yarnUrl = `https://github.com/yarnpkg/berry/releases/download/v${YARN_VERSION}/yarn-${YARN_VERSION}.cjs`;
  
  try {
    await downloadFile(yarnUrl, YARN_BINARY_PATH);
    fs.chmodSync(YARN_BINARY_PATH, '755'); // Make executable
    console.log(`Yarn ${YARN_VERSION} downloaded and set as executable.`);
  } catch (error) {
    console.error(`Failed to download Yarn: ${error.message}`);
    process.exit(1);
  }
}

/**
 * Install Yarn plugins
 */
async function installPlugins() {
  ensureDirectoryExists(YARN_PLUGINS_DIR);
  
  console.log('Installing Yarn plugins...');
  
  for (const plugin of PLUGINS) {
    const pluginName = plugin.split('/').pop();
    const pluginPath = path.join(YARN_PLUGINS_DIR, `${plugin}.cjs`);
    
    if (fs.existsSync(pluginPath)) {
      console.log(`Plugin ${plugin} already installed.`);
      continue;
    }
    
    console.log(`Installing plugin: ${plugin}`);
    
    // Use Yarn's plugin import feature
    execCommand(`node ${YARN_BINARY_PATH} plugin import ${plugin}`);
  }
}

/**
 * Update project configuration files
 */
function updateConfiguration() {
  // Check and update package.json
  const packageJsonPath = path.join(PROJECT_ROOT, 'package.json');
  
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Update packageManager field
    packageJson.packageManager = `yarn@${YARN_VERSION}`;
    
    // Ensure engines field is correct
    packageJson.engines = packageJson.engines || {};
    packageJson.engines.yarn = `>=3.0.0`;
    packageJson.engines.npm = `please-use-yarn`;
    
    // Add yarn setup script if it doesn't exist
    packageJson.scripts = packageJson.scripts || {};
    if (!packageJson.scripts['yarn:setup']) {
      packageJson.scripts['yarn:setup'] = 'node scripts/setup-yarn.js';
    }
    
    // Write updated package.json
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
    console.log('Updated package.json with Yarn configuration.');
  }
  
  // Create/update .yarnrc.yml if it doesn't reference the correct yarn version
  const yarnrcPath = path.join(PROJECT_ROOT, '.yarnrc.yml');
  
  if (fs.existsSync(yarnrcPath)) {
    let yarnrc = fs.readFileSync(yarnrcPath, 'utf8');
    
    // Update yarnPath
    if (!yarnrc.includes(`yarn-${YARN_VERSION}.cjs`)) {
      yarnrc = yarnrc.replace(/yarnPath:.*/, `yarnPath: .yarn/releases/yarn-${YARN_VERSION}.cjs`);
      fs.writeFileSync(yarnrcPath, yarnrc);
      console.log(`Updated .yarnrc.yml with Yarn ${YARN_VERSION} path.`);
    }
  }
}

/**
 * Create .gitignore entries for Yarn if needed
 */
function updateGitignore() {
  const gitignorePath = path.join(PROJECT_ROOT, '.gitignore');
  
  if (fs.existsSync(gitignorePath)) {
    let gitignore = fs.readFileSync(gitignorePath, 'utf8');
    let modified = false;
    
    // Add Yarn entries if they don't exist
    const yarnEntries = [
      '.yarn/*',
      '!.yarn/patches',
      '!.yarn/plugins',
      '!.yarn/releases',
      '!.yarn/sdks',
      '!.yarn/versions',
      '.pnp.*'
    ];
    
    for (const entry of yarnEntries) {
      if (!gitignore.includes(entry)) {
        gitignore += `\n${entry}`;
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(gitignorePath, gitignore);
      console.log('Updated .gitignore with Yarn entries.');
    }
  }
}

/**
 * Install/update dependencies
 */
function installDependencies() {
  console.log('Installing dependencies...');
  execCommand(`node ${YARN_BINARY_PATH} install`);
  
  console.log('Checking for duplicate dependencies...');
  execCommand(`node ${YARN_BINARY_PATH} dedupe`);
}

/**
 * Run the setup process
 */
async function main() {
  console.log(`Setting up Yarn ${YARN_VERSION} for Mexel project...`);
  
  try {
    await downloadYarn();
    await installPlugins();
    updateConfiguration();
    updateGitignore();
    installDependencies();
    
    console.log('\n✅ Yarn setup completed successfully!');
    console.log(`\nYou can now use Yarn ${YARN_VERSION} for this project.`);
    console.log('Run the following commands to start development:');
    console.log('  - yarn install      # Install dependencies');
    console.log('  - yarn start        # Start the development servers');
    console.log('  - yarn build:all    # Build all packages');
    
  } catch (error) {
    console.error('\n❌ Yarn setup failed:');
    console.error(error);
    process.exit(1);
  }
}

// Run the script
main();