#!/bin/bash

# Script to install dependencies for the Mexel agent system

echo "Installing dependencies for Mexel agent system..."

# Navigate to project root
cd "$(dirname "$0")/.."

# Install backend dependencies
echo "Installing backend dependencies..."
yarn install --save better-sqlite3 uuid axios jsdom dotenv winston ts-node

# Install development dependencies
echo "Installing development dependencies..."
yarn install --save-dev @types/better-sqlite3 @types/uuid @types/jsdom @types/node typescript

echo "Dependencies installed successfully!"
