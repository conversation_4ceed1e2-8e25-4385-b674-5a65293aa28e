const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const rootDir = path.resolve(__dirname, '..');

const devDependencies = {
  testing: {
    jest: '^29.7.0',
    '@testing-library/react': '^14.1.2',
    '@testing-library/jest-dom': '^6.1.5',
    '@testing-library/user-event': '^14.5.1',
    'ts-jest': '^29.1.1',
    'jest-environment-jsdom': '^29.7.0',
    'identity-obj-proxy': '^3.0.0',
  },
  linting: {
    prettier: '^3.1.0',
    'eslint-config-prettier': '^9.0.0',
    'eslint-plugin-prettier': '^5.0.1',
    'eslint-plugin-jest': '^27.6.0',
    'eslint-plugin-testing-library': '^6.2.0',
    'eslint-plugin-jsx-a11y': '^6.8.0',
  },
  quality: {
    husky: '^8.0.3',
    'lint-staged': '^15.1.0',
    commitlint: '^18.4.3',
    '@commitlint/cli': '^18.4.3',
    '@commitlint/config-conventional': '^18.4.3',
  },
  performance: {
    'source-map-explorer': '^2.5.3',
    lighthouse: '^11.3.0',
    'webpack-bundle-analyzer': '^4.10.1',
  }
};

const configurations = {
  jest: {
    filename: 'jest.config.js',
    content: `module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};`
  },
  prettier: {
    filename: '.prettierrc',
    content: `{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}`
  },
  commitlint: {
    filename: 'commitlint.config.js',
    content: `module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [2, 'always', [
      'feat',
      'fix',
      'docs',
      'style',
      'refactor',
      'perf',
      'test',
      'build',
      'ci',
      'chore',
      'revert'
    ]],
  },
};`
  },
  husky: {
    filename: '.husky/pre-commit',
    content: `#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

yarn lint-staged`
  },
  lintStaged: {
    filename: '.lintstagedrc',
    content: `{
  "*.{js,jsx,ts,tsx}": [
    "eslint --fix",
    "prettier --write"
  ],
  "*.{json,md,yml,yaml}": [
    "prettier --write"
  ]
}`
  },
  setupTests: {
    filename: 'src/setupTests.ts',
    content: `import '@testing-library/jest-dom';

// Add any global test setup here
`
  }
};

async function prompt(question) {
  return new Promise(resolve => {
    rl.question(question, answer => {
      resolve(answer.toLowerCase() === 'y');
    });
  });
}

async function installDependencies(type) {
  console.log(`\nInstalling ${type} dependencies...`);
  const deps = devDependencies[type];
  const depsArray = Object.entries(deps).map(([pkg, version]) => `${pkg}@${version}`);
  
  try {
    execSync(`yarn add -D ${depsArray.join(' ')}`, {
      stdio: 'inherit',
      cwd: rootDir
    });
    console.log(`✅ Successfully installed ${type} dependencies`);
  } catch (error) {
    console.error(`❌ Failed to install ${type} dependencies:`, error.message);
  }
}

function createConfig(config) {
  const filePath = path.join(rootDir, config.filename);
  
  try {
    fs.writeFileSync(filePath, config.content);
    if (config.filename.startsWith('.husky/')) {
      fs.chmodSync(filePath, '755');
    }
    console.log(`✅ Created ${config.filename}`);
  } catch (error) {
    console.error(`❌ Failed to create ${config.filename}:`, error.message);
  }
}

async function setupHusky() {
  try {
    execSync('yarn husky install', { stdio: 'inherit', cwd: rootDir });
    console.log('✅ Husky installed successfully');
  } catch (error) {
    console.error('❌ Failed to install Husky:', error.message);
  }
}

async function main() {
  console.log('🚀 Setting up development environment...\n');

  const setupOptions = {
    testing: await prompt('Set up testing tools? (Jest, Testing Library) [y/N]: '),
    linting: await prompt('Set up linting tools? (Prettier, additional ESLint plugins) [y/N]: '),
    quality: await prompt('Set up code quality tools? (Husky, lint-staged, commitlint) [y/N]: '),
    performance: await prompt('Set up performance tools? (Bundle analyzer, Lighthouse) [y/N]: ')
  };

  // Create necessary directories
  fs.mkdirSync(path.join(rootDir, '.husky'), { recursive: true });
  fs.mkdirSync(path.join(rootDir, 'src'), { recursive: true });

  // Install selected dependencies
  for (const [type, selected] of Object.entries(setupOptions)) {
    if (selected) {
      await installDependencies(type);
    }
  }

  // Create configuration files
  if (setupOptions.testing) {
    createConfig(configurations.jest);
    createConfig(configurations.setupTests);
  }

  if (setupOptions.linting) {
    createConfig(configurations.prettier);
  }

  if (setupOptions.quality) {
    createConfig(configurations.commitlint);
    createConfig(configurations.lintStaged);
    await setupHusky();
    createConfig(configurations.husky);
  }

  // Update package.json scripts
  const packageJson = require(path.join(rootDir, 'package.json'));
  const newScripts = {
    ...(setupOptions.testing && {
      'test:watch': 'jest --watch',
      'test:coverage': 'jest --coverage',
    }),
    ...(setupOptions.performance && {
      'analyze:bundle': 'source-map-explorer build/static/js/*.js',
      'analyze:lighthouse': 'lighthouse http://localhost:3000',
    }),
  };

  packageJson.scripts = { ...packageJson.scripts, ...newScripts };

  fs.writeFileSync(
    path.join(rootDir, 'package.json'),
    JSON.stringify(packageJson, null, 2)
  );

  console.log('\n✨ Development environment setup complete!');
  console.log('\nNext steps:');
  console.log('1. Review the installed dependencies in package.json');
  console.log('2. Check the configuration files');
  console.log('3. Run yarn install to ensure all dependencies are properly installed');
  console.log('4. Start developing! 🚀');

  rl.close();
}

main().catch(error => {
  console.error('❌ Setup failed:', error);
  process.exit(1);
});