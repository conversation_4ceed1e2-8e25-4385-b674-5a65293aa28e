#!/usr/bin/env node

/**
 * Update Script References in package.json
 * 
 * This script updates package.json files to reference the new script locations
 * from the script organization overhaul. It adds new script entries with the new 
 * paths and optionally keeps the old references as aliases.
 * 
 * Usage:
 *   node scripts/update-script-references.js [options]
 * 
 * Options:
 *   --remove-old     Remove old script references instead of keeping them as aliases
 *   --dry-run        Show what would be updated without making changes
 *   --verbose        Show detailed output
 *   --help           Show this help message
 */

const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  removeOld: args.includes('--remove-old'),
  dryRun: args.includes('--dry-run'),
  verbose: args.includes('--verbose'),
  help: args.includes('--help')
};

// Show help
if (options.help) {
  console.log(`
Update Script References in package.json

Usage:
  node scripts/update-script-references.js [options]

Options:
  --remove-old     Remove old script references instead of keeping them as aliases
  --dry-run        Show what would be updated without making changes
  --verbose        Show detailed output
  --help           Show this help message
  `);
  process.exit(0);
}

// Script mapping from old to new paths
const scriptMappings = {
  // TypeScript fixes
  "fix-typescript": "node scripts/frontend/typescript/fix-all-typescript.js",
  "fix-typescript-errors": "node scripts/frontend/typescript/fix-all-typescript.js",
  "fix-react-types": "node scripts/frontend/typescript/fix-react-types.js",
  "fix-component-declarations": "node scripts/frontend/typescript/fix-component-declarations.js",
  
  // Import fixes
  "fix-imports": "node scripts/frontend/imports/fix-imports.js",
  "fix-unused-imports": "node scripts/frontend/imports/fix-unused-imports.js",
  
  // UI library fixes
  "fix-ui-libraries": "node scripts/frontend/ui-libraries/fix-ui-libraries.js",
  "fix-mui": "node scripts/frontend/ui-libraries/fix-ui-libraries.js --mui",
  "fix-recharts": "node scripts/frontend/ui-libraries/fix-ui-libraries.js --recharts",
  
  // Dependency fixes
  "fix-packages": "node scripts/frontend/dependencies/fix-packages.js",
  "fix-dependencies": "node scripts/frontend/dependencies/fix-packages.js",
  
  // Master frontend fixer
  "fix-frontend": "node scripts/frontend/fix-all-frontend.js",
  "fix-all": "node scripts/frontend/fix-all-frontend.js"
};

// New script entries to add
const newScriptEntries = {
  "fix:all": "node scripts/frontend/fix-all-frontend.js",
  "fix:typescript": "node scripts/frontend/typescript/fix-all-typescript.js",
  "fix:react": "node scripts/frontend/typescript/fix-react-types.js",
  "fix:components": "node scripts/frontend/typescript/fix-component-declarations.js",
  "fix:imports": "node scripts/frontend/imports/fix-imports.js",
  "fix:ui": "node scripts/frontend/ui-libraries/fix-ui-libraries.js",
  "fix:packages": "node scripts/frontend/dependencies/fix-packages.js"
};

// Statistics tracking
let packagesUpdated = 0;
let scriptsUpdated = 0;
let scriptsAdded = 0;
let scriptsRemoved = 0;

// Utility function for logging
function log(message, isVerbose = false) {
  if (!isVerbose || options.verbose) {
    console.log(message);
  }
}

// Update package.json file
function updatePackageJson(packageJsonPath) {
  log(`Processing: ${packageJsonPath}`);
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    let modified = false;
    
    // Make sure scripts object exists
    packageJson.scripts = packageJson.scripts || {};
    
    // Update existing script references
    for (const [oldScript, newCommand] of Object.entries(scriptMappings)) {
      for (const [scriptName, scriptCommand] of Object.entries(packageJson.scripts)) {
        // Check if this script needs updating
        if (scriptCommand.includes(oldScript) || 
            scriptCommand.includes(oldScript.replace(/-/g, '_'))) {
          
          log(`  Found script to update: ${scriptName}`, true);
          
          // Update the script command
          const updatedCommand = newCommand;
          
          if (options.removeOld) {
            // Remove the old script
            if (options.dryRun) {
              log(`  Would remove: ${scriptName}: ${scriptCommand}`);
            } else {
              delete packageJson.scripts[scriptName];
              modified = true;
              scriptsRemoved++;
              log(`  ✓ Removed: ${scriptName}`);
            }
          } else {
            // Update the existing script
            if (options.dryRun) {
              log(`  Would update: ${scriptName}: ${scriptCommand} → ${updatedCommand}`);
            } else {
              packageJson.scripts[scriptName] = updatedCommand;
              modified = true;
              scriptsUpdated++;
              log(`  ✓ Updated: ${scriptName}`);
            }
          }
        }
      }
    }
    
    // Add new script entries
    for (const [scriptName, scriptCommand] of Object.entries(newScriptEntries)) {
      if (!packageJson.scripts[scriptName]) {
        if (options.dryRun) {
          log(`  Would add: ${scriptName}: ${scriptCommand}`);
        } else {
          packageJson.scripts[scriptName] = scriptCommand;
          modified = true;
          scriptsAdded++;
          log(`  ✓ Added: ${scriptName}`);
        }
      }
    }
    
    // Write updated package.json
    if (modified && !options.dryRun) {
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
      packagesUpdated++;
      log(`✓ Updated ${packageJsonPath}`);
    } else if (options.dryRun) {
      log(`Would update ${packageJsonPath}`);
    } else {
      log(`No changes needed in ${packageJsonPath}`);
    }
    
    return modified;
  } catch (error) {
    console.error(`Error updating ${packageJsonPath}:`, error.message);
    return false;
  }
}

// Find and update all package.json files
function updateAllPackageJsonFiles() {
  const packageJsonPaths = [
    path.join(process.cwd(), 'package.json'),
    path.join(process.cwd(), 'frontend', 'package.json')
  ];
  
  let updated = 0;
  
  packageJsonPaths.forEach(packageJsonPath => {
    if (fs.existsSync(packageJsonPath)) {
      const wasUpdated = updatePackageJson(packageJsonPath);
      if (wasUpdated && !options.dryRun) {
        updated++;
      }
    } else {
      log(`Package file not found: ${packageJsonPath}`, true);
    }
  });
  
  return updated;
}

// Main execution
console.log("📝 Update Script References");
console.log("==========================");
console.log(`Mode: ${options.dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE (making changes)'}`);
console.log(`Old script references: ${options.removeOld ? 'REMOVE' : 'KEEP AS ALIASES'}`);
console.log("");

// Update all package.json files
const updatedCount = updateAllPackageJsonFiles();

console.log("");
console.log("📊 Summary");
console.log("==========");
console.log(`Packages processed: ${packagesUpdated}`);
console.log(`Scripts updated: ${scriptsUpdated}`);
console.log(`Scripts added: ${scriptsAdded}`);
console.log(`Scripts removed: ${scriptsRemoved}`);

if (options.dryRun) {
  console.log("\n💡 This was a dry run. Run without --dry-run to apply changes.");
} else if (updatedCount > 0) {
  console.log("\n✅ Script references updated successfully!");
  console.log("\nNext steps:");
  console.log("1. Review the updated package.json files");
  console.log("2. Run 'yarn install' to ensure everything is up-to-date");
  console.log("3. Test the new script commands");
} else {
  console.log("\nNo changes were needed in package.json files.");
}