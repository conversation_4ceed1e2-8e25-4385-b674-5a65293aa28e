#!/bin/bash

# Create a temporary directory for backups
mkdir -p /Users/<USER>/Desktop/Mexel/frontend/temp_backups

# Function to clean up React imports in TypeScript files
clean_react_imports() {
  local file=$1
  echo "Processing $file..."
  
  # Make a backup
  cp "$file" "/Users/<USER>/Desktop/Mexel/frontend/temp_backups/$(basename "$file").bak"
  
  # Remove unused React import (if it's alone on a line)
  sed -i '' 's/^import React from '\''react'\'';$//' "$file"
  
  # Remove React from import { React } statements
  sed -i '' 's/import { React, /import { /g' "$file"
  sed -i '' 's/import { React }//' "$file"
  sed -i '' 's/, React / /g' "$file"
  sed -i '' 's/ React,/ /g' "$file"
  
  # Fix FC, ReactNode, ChangeEvent, MouseEvent
  sed -i '' 's/{ FC, /{ /g' "$file"
  sed -i '' 's/, FC / /g' "$file"
  sed -i '' 's/ FC,/ /g' "$file"
  
  sed -i '' 's/{ ReactNode, /{ /g' "$file"
  sed -i '' 's/, ReactNode / /g' "$file"
  sed -i '' 's/ ReactNode,/ /g' "$file"
  
  sed -i '' 's/{ ChangeEvent, /{ /g' "$file"
  sed -i '' 's/, ChangeEvent / /g' "$file"
  sed -i '' 's/ ChangeEvent,/ /g' "$file"
  
  sed -i '' 's/{ MouseEvent, /{ /g' "$file"
  sed -i '' 's/, MouseEvent / /g' "$file"
  sed -i '' 's/ MouseEvent,/ /g' "$file"
  
  # Clean up empty imports
  sed -i '' 's/import {  } from '\''react'\'';$//' "$file"
  
  # Fix any trailing commas
  sed -i '' 's/, } from/} from/g' "$file"
  
  echo "Completed $file"
}

# Find all TypeScript files in the frontend/src directory
find /Users/<USER>/Desktop/Mexel/frontend/src -type f -name "*.ts" -o -name "*.tsx" | while read -r file; do
  clean_react_imports "$file"
done

echo "Cleaning complete! Backups are in frontend/temp_backups/"
