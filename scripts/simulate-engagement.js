#!/usr/bin/env node

/**
 * Simulate Email Engagement Script
 *
 * This script simulates email engagement events (opens, clicks, replies)
 * to demonstrate the CRM integration and lead status updates.
 *
 * Usage:
 *   npx ts-node scripts/simulate-engagement.js --id email_12345 --type open
 *   npx ts-node scripts/simulate-engagement.js --id email_12345 --type click
 *   npx ts-node scripts/simulate-engagement.js --id email_12345 --type reply
 */

const sqlite3 = require('better-sqlite3');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('id', {
    alias: 'i',
    description: 'Email ID to simulate engagement for',
    type: 'string',
    demandOption: true
  })
  .option('type', {
    alias: 't',
    description: 'Type of engagement to simulate',
    choices: ['open', 'click', 'reply'],
    default: 'open'
  })
  .option('url', {
    alias: 'u',
    description: 'URL that was clicked (for click engagement)',
    type: 'string',
    default: 'https://www.mexelenergysustain.com/products'
  })
  .option('content', {
    alias: 'c',
    description: 'Reply content (for reply engagement)',
    type: 'string',
    default: 'Thank you for reaching out. I would like to schedule a call to discuss your solutions.'
  })
  .help()
  .alias('help', 'h')
  .argv;

// Configuration
const config = {
  dbPath: path.join(__dirname, '..', 'data', 'test-crm.db')
};

// Simulate email engagement
async function simulateEngagement(emailId, type, options = {}) {
  try {
    console.log(`Simulating ${type} engagement for email ${emailId}...`);

    // Initialize database
    const db = new sqlite3(config.dbPath);

    // Check if email exists
    const email = db.prepare('SELECT * FROM emails WHERE id = ?').get(emailId);

    if (!email) {
      console.error(`Email with ID ${emailId} not found`);
      process.exit(1);
    }

    // Parse metadata
    let metadata = {};
    try {
      metadata = JSON.parse(email.metadata || '{}');
    } catch (error) {
      console.warn('Failed to parse email metadata');
    }

    // Get lead information if available
    let lead = null;
    if (metadata.leadId) {
      lead = db.prepare('SELECT * FROM leads WHERE id = ?').get(metadata.leadId);
    }

    // Create engagement record
    const engagementId = `engagement_${uuidv4()}`;
    const now = new Date().toISOString();

    // Prepare engagement metadata
    const engagementMetadata = {
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
      ipAddress: '***********',
      timestamp: now,
      leadId: metadata.leadId || null
    };

    // Add type-specific metadata
    if (type === 'click' && options.url) {
      engagementMetadata.url = options.url;
    } else if (type === 'reply' && options.content) {
      engagementMetadata.contentPreview = options.content.substring(0, 100);
      engagementMetadata.replyId = `reply_${uuidv4()}`;
    }

    // Insert engagement record
    db.prepare(`
      INSERT INTO email_engagements (
        id, emailId, type, timestamp, metadata
      ) VALUES (?, ?, ?, ?, ?)
    `).run(
      engagementId,
      emailId,
      type,
      now,
      JSON.stringify(engagementMetadata)
    );

    console.log(`✓ Engagement recorded successfully`);

    // Update lead status if lead exists
    if (lead) {
      let newStatus = lead.status;

      switch (type) {
        case 'open':
          // If lead opened the email but hasn't engaged further yet
          if (lead.status === 'NEW' || lead.status === 'CONTACTED') {
            newStatus = 'EMAIL_SEQUENCE_STARTED';
          }
          break;

        case 'click':
          // If lead clicked on a link in the email, they're showing interest
          newStatus = 'ENGAGED';
          break;

        case 'reply':
          // If lead replied to the email, they're definitely engaged
          newStatus = 'ENGAGED';
          break;
      }

      // Update lead status if it changed
      if (newStatus !== lead.status) {
        db.prepare(`
          UPDATE leads
          SET status = ?, lastContactDate = ?
          WHERE id = ?
        `).run(newStatus, now, lead.id);

        console.log(`✓ Lead status updated: ${lead.status} → ${newStatus}`);
      } else {
        console.log(`ℹ Lead status unchanged: ${lead.status}`);
      }

      // Log activity
      const activityId = `activity_${uuidv4()}`;

      db.prepare(`
        INSERT INTO lead_activities (
          id, leadId, type, emailId, timestamp, metadata
        ) VALUES (?, ?, ?, ?, ?, ?)
      `).run(
        activityId,
        lead.id,
        `email_${type}`,
        emailId,
        now,
        JSON.stringify({
          timestamp: now,
          ...options
        })
      );

      console.log(`✓ Lead activity logged`);
    } else {
      console.log(`ℹ No lead associated with this email`);
    }

    // Close database connection
    db.close();

    console.log('\n========================================================');
    console.log(`EMAIL ${type.toUpperCase()} SIMULATED SUCCESSFULLY!`);
    console.log('========================================================');
    console.log('\nTo view tracking data, run:');
    console.log(`npx ts-node scripts/view-email-tracking.js --id ${emailId}`);
    console.log('========================================================');

    return {
      success: true,
      engagementId,
      type,
      emailId
    };
  } catch (error) {
    console.error('Failed to simulate engagement:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Main function
async function main() {
  try {
    const result = await simulateEngagement(
      argv.id,
      argv.type,
      {
        url: argv.url,
        content: argv.content
      }
    );

    if (!result.success) {
      process.exit(1);
    }
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run the main function
main();
