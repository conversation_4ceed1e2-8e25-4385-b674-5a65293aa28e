/**
 * Database migration script to add the category column to the tenders table
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Ensure data directory exists
const dataDir = path.join(process.cwd(), 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

// Connect to the database
const dbPath = path.join(process.cwd(), 'data/mexel.db');
const db = new Database(dbPath);

console.log(`Connected to database at ${dbPath}`);

// Check if the tenders table exists
const tableExists = db.prepare(`
    SELECT name FROM sqlite_master
    WHERE type='table' AND name='tenders'
`).get();

if (!tableExists) {
    console.log('Tenders table does not exist. Creating it...');

    // Create the tenders table with all required columns
    db.prepare(`
        CREATE TABLE tenders (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT,
            reference TEXT,
            closing_date TEXT,
            source TEXT,
            contact_person TEXT,
            contact_email TEXT,
            estimated_value REAL,
            url TEXT NOT NULL,
            categories TEXT,
            category TEXT,
            documents TEXT,
            requirements TEXT,
            location TEXT,
            scrape_date TEXT,
            status TEXT NOT NULL DEFAULT 'NEW',
            relevance_score REAL,
            historical_avg_value REAL,
            historical_success_rate REAL,
            opportunity_score REAL,
            issuer TEXT,
            publishDate TEXT,
            closingDate TEXT,
            value REAL,
            tags TEXT,
            metadata TEXT
        )
    `).run();

    console.log('Tenders table created successfully.');
} else {
    console.log('Tenders table exists. Checking for category column...');

    // Check for missing columns
    const columnInfo = db.prepare(`PRAGMA table_info(tenders)`).all();
    const columnNames = columnInfo.map(col => col.name);

    // Check for category column
    if (!columnNames.includes('category')) {
        console.log('Adding category column to tenders table...');

        // Add the category column
        db.prepare(`ALTER TABLE tenders ADD COLUMN category TEXT`).run();

        console.log('Category column added successfully.');

        // Update the category column based on categories column
        console.log('Updating category values from categories column...');

        // Get all tenders with categories
        const tenders = db.prepare(`
            SELECT id, categories FROM tenders
            WHERE categories IS NOT NULL
        `).all();

        console.log(`Found ${tenders.length} tenders with categories.`);

        // Update statement
        const updateStmt = db.prepare(`
            UPDATE tenders SET category = ? WHERE id = ?
        `);

        // Begin transaction
        const transaction = db.transaction((tenders) => {
            for (const tender of tenders) {
                let category = null;

                try {
                    // Try to parse categories as JSON
                    const categories = JSON.parse(tender.categories);
                    if (Array.isArray(categories) && categories.length > 0) {
                        category = categories[0];
                    } else if (typeof categories === 'string') {
                        category = categories;
                    }
                } catch (e) {
                    // If not JSON, use as is
                    category = tender.categories;
                }

                if (category) {
                    updateStmt.run(category, tender.id);
                }
            }
        });

        // Execute transaction
        transaction(tenders);

        console.log('Category values updated successfully.');
    } else {
        console.log('Category column already exists.');
    }

    // Check for issuer column
    if (!columnNames.includes('issuer')) {
        console.log('Adding issuer column to tenders table...');

        // Add the issuer column
        db.prepare(`ALTER TABLE tenders ADD COLUMN issuer TEXT`).run();

        console.log('Issuer column added successfully.');

        // Update the issuer column based on source column
        console.log('Updating issuer values from source column...');

        // Get all tenders with source
        const tenders = db.prepare(`
            SELECT id, source FROM tenders
            WHERE source IS NOT NULL
        `).all();

        console.log(`Found ${tenders.length} tenders with source.`);

        // Update statement
        const updateStmt = db.prepare(`
            UPDATE tenders SET issuer = ? WHERE id = ?
        `);

        // Begin transaction
        const transaction = db.transaction((tenders) => {
            for (const tender of tenders) {
                if (tender.source) {
                    updateStmt.run(tender.source, tender.id);
                }
            }
        });

        // Execute transaction
        transaction(tenders);

        console.log('Issuer values updated successfully.');
    } else {
        console.log('Issuer column already exists.');
    }

    // Check for publishDate column
    if (!columnNames.includes('publishDate')) {
        console.log('Adding publishDate column to tenders table...');

        // Add the publishDate column
        db.prepare(`ALTER TABLE tenders ADD COLUMN publishDate TEXT`).run();

        console.log('publishDate column added successfully.');

        // Update the publishDate column based on scrape_date column
        console.log('Updating publishDate values from scrape_date column...');

        // Get all tenders with scrape_date
        const tenders = db.prepare(`
            SELECT id, scrape_date FROM tenders
            WHERE scrape_date IS NOT NULL
        `).all();

        console.log(`Found ${tenders.length} tenders with scrape_date.`);

        // Update statement
        const updateStmt = db.prepare(`
            UPDATE tenders SET publishDate = ? WHERE id = ?
        `);

        // Begin transaction
        const transaction = db.transaction((tenders) => {
            for (const tender of tenders) {
                if (tender.scrape_date) {
                    updateStmt.run(tender.scrape_date, tender.id);
                }
            }
        });

        // Execute transaction
        transaction(tenders);

        console.log('publishDate values updated successfully.');
    } else {
        console.log('publishDate column already exists.');
    }

    // Check for closingDate column
    if (!columnNames.includes('closingDate')) {
        console.log('Adding closingDate column to tenders table...');

        // Add the closingDate column
        db.prepare(`ALTER TABLE tenders ADD COLUMN closingDate TEXT`).run();

        console.log('closingDate column added successfully.');

        // Update the closingDate column based on closing_date column
        console.log('Updating closingDate values from closing_date column...');

        // Get all tenders with closing_date
        const tenders = db.prepare(`
            SELECT id, closing_date FROM tenders
            WHERE closing_date IS NOT NULL
        `).all();

        console.log(`Found ${tenders.length} tenders with closing_date.`);

        // Update statement
        const updateStmt = db.prepare(`
            UPDATE tenders SET closingDate = ? WHERE id = ?
        `);

        // Begin transaction
        const transaction = db.transaction((tenders) => {
            for (const tender of tenders) {
                if (tender.closing_date) {
                    updateStmt.run(tender.closing_date, tender.id);
                }
            }
        });

        // Execute transaction
        transaction(tenders);

        console.log('closingDate values updated successfully.');
    } else {
        console.log('closingDate column already exists.');
    }

    // Check for value column
    if (!columnNames.includes('value')) {
        console.log('Adding value column to tenders table...');

        // Add the value column
        db.prepare(`ALTER TABLE tenders ADD COLUMN value REAL`).run();

        console.log('value column added successfully.');

        // Update the value column based on estimated_value column
        console.log('Updating value from estimated_value column...');

        // Get all tenders with estimated_value
        const tenders = db.prepare(`
            SELECT id, estimated_value FROM tenders
            WHERE estimated_value IS NOT NULL
        `).all();

        console.log(`Found ${tenders.length} tenders with estimated_value.`);

        // Update statement
        const updateStmt = db.prepare(`
            UPDATE tenders SET value = ? WHERE id = ?
        `);

        // Begin transaction
        const transaction = db.transaction((tenders) => {
            for (const tender of tenders) {
                if (tender.estimated_value) {
                    updateStmt.run(tender.estimated_value, tender.id);
                }
            }
        });

        // Execute transaction
        transaction(tenders);

        console.log('value values updated successfully.');
    } else {
        console.log('value column already exists.');
    }

    // Check for tags column
    if (!columnNames.includes('tags')) {
        console.log('Adding tags column to tenders table...');

        // Add the tags column
        db.prepare(`ALTER TABLE tenders ADD COLUMN tags TEXT`).run();

        console.log('tags column added successfully.');
    } else {
        console.log('tags column already exists.');
    }
}

// Close the database connection
db.close();
console.log('Database migration completed successfully.');
