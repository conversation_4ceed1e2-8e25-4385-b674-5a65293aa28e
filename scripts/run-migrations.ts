#!/usr/bin/env ts-node

/**
 * Database migration script
 * This script runs database migrations
 */

import * as path from "path";
import * as fs from "fs";
import BetterSqlite3 from "better-sqlite3";
import { MigrationRunner } from "../src/database/MigrationRunner";
import { Logger } from "../src/utils/Logger";
import { config } from "../src/config/config";

// Create logger
const logger = Logger.getInstance("MigrationScript");

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0] || "run";
const targetVersion = args[1] ? parseInt(args[1], 10) : undefined;

// Main function
async function main() {
  logger.info("Database Migration Script");

  try {
    // Get database path
    const dbPath =
      config.database?.path || path.join(process.cwd(), "data", "mexel.db");

    // Ensure data directory exists
    const dataDir = path.dirname(dbPath);
    if (!fs.existsSync(dataDir)) {
      logger.info(`Creating data directory: ${dataDir}`);
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Connect to database
    logger.info(`Connecting to database: ${dbPath}`);
    const db = new BetterSqlite3(dbPath, {
      verbose:
        process.env.NODE_ENV === "development"
          ? (message?: any) => logger.debug(String(message))
          : undefined,
    });

    // Enable foreign keys
    db.pragma("foreign_keys = ON");

    // Create migration runner
    const migrationRunner = new MigrationRunner(db);

    // Initialize migration system
    await migrationRunner.initialize();

    // Get migration status
    const status = migrationRunner.getMigrationStatus();
    logger.info(
      `Migration status: ${status.applied} applied, ${status.pending} pending, latest version: ${status.latest}`
    );

    // Execute command
    switch (command) {
      case "run":
        // Run migrations
        const appliedCount = await migrationRunner.runMigrations();
        logger.info(`Applied ${appliedCount} migrations`);
        break;

      case "rollback":
        // Rollback last migration
        const rolledBack = await migrationRunner.rollbackLastMigration();
        logger.info(`Rolled back ${rolledBack ? 1 : 0} migrations`);
        break;

      case "rollback-to":
        // Rollback to specific version
        if (targetVersion === undefined) {
          logger.error("Target version is required for rollback-to command");
          process.exit(1);
        }

        const rolledBackCount = await migrationRunner.rollbackToVersion(
          targetVersion
        );
        logger.info(
          `Rolled back ${rolledBackCount} migrations to version ${targetVersion}`
        );
        break;

      case "status":
        // Status already shown above
        break;

      default:
        logger.error(`Unknown command: ${command}`);
        logger.info("Available commands: run, rollback, rollback-to, status");
        process.exit(1);
    }

    // Close database connection
    db.close();

    logger.info("Migration script completed successfully");
  } catch (error) {
    logger.error("Error running migrations", {
      error: error instanceof Error ? error : new Error(String(error)),
    });
    process.exit(1);
  }
}

// Run main function
main().catch((error) => {
  logger.error("Unhandled error", {
    error: error instanceof Error ? error : new Error(String(error)),
  });
  process.exit(1);
});
