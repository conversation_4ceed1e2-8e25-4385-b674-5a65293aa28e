#!/usr/bin/env ts-node

/**
 * Test Email Templates <PERSON><PERSON><PERSON>
 * 
 * This script demonstrates the role-based email templates for different sectors.
 * It generates sample templates and saves them as HTML files for review.
 */

import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { EmailTemplateFactory, ProfessionalRole, Sector } from '../src/templates/email/EmailTemplateFactory';
import { Lead } from '../src/services/CRMService';

// Sample lead data
const sampleLeads: Record<string, Lead> = {
  manager: {
    id: `lead_${uuidv4()}`,
    email: '<EMAIL>',
    firstName: 'John',
    lastName: '<PERSON>',
    company: 'Acme Power Generation',
    position: 'Plant Manager',
    industry: 'Energy',
    status: 'NEW'
  },
  engineer: {
    id: `lead_${uuidv4()}`,
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    company: 'TechCool Systems',
    position: 'Mechanical Engineer',
    industry: 'HVAC',
    status: 'NEW'
  },
  buyer: {
    id: `lead_${uuidv4()}`,
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: 'Wong',
    company: 'Global Mining Corp',
    position: 'Procurement Manager',
    industry: 'Mining',
    status: 'NEW'
  },
  technician: {
    id: `lead_${uuidv4()}`,
    email: '<EMAIL>',
    firstName: 'Robert',
    lastName: 'Garcia',
    company: 'PetroTech Industries',
    position: 'Maintenance Supervisor',
    industry: 'Oil & Gas',
    status: 'NEW'
  }
};

// Define roles and sectors to test
const roles: ProfessionalRole[] = ['manager', 'engineer', 'buyer', 'technician'];
const sectors: Sector[] = ['power', 'hvac', 'mining', 'oil_gas', 'agriculture', 'cooling'];

// Create output directory
const outputDir = path.join(__dirname, '..', 'output', 'email-templates');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Generate templates for each role and sector
roles.forEach(role => {
  sectors.forEach(sector => {
    try {
      console.log(`Generating template for ${role} in ${sector} sector...`);
      
      // Create template
      const template = EmailTemplateFactory.createTemplate({
        role,
        sector,
        lead: sampleLeads[role],
        meetingLink: 'https://calendly.com/mexel/meeting',
        caseStudyLink: 'https://www.mexelenergysustain.com/case-studies',
        productLink: 'https://www.mexelenergysustain.com/products'
      });
      
      // Save template to file
      const fileName = `${role}_${sector}.html`;
      const filePath = path.join(outputDir, fileName);
      fs.writeFileSync(filePath, template.body);
      
      console.log(`✅ Template saved to ${fileName}`);
    } catch (error) {
      console.error(`❌ Error generating template for ${role} in ${sector} sector:`, error);
    }
  });
});

console.log(`\nAll templates generated in ${outputDir}`);

// Generate an index file to easily view all templates
const indexContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mexel Email Templates</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2 {
      color: #0066cc;
    }
    .grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 15px;
      transition: transform 0.2s;
    }
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .card h3 {
      margin-top: 0;
      color: #333;
    }
    .card a {
      display: inline-block;
      margin-top: 10px;
      padding: 8px 15px;
      background-color: #0066cc;
      color: white;
      text-decoration: none;
      border-radius: 4px;
    }
    .card a:hover {
      background-color: #0055aa;
    }
  </style>
</head>
<body>
  <h1>Mexel Email Templates</h1>
  <p>Click on a template below to view it:</p>
  
  <h2>Manager Templates</h2>
  <div class="grid">
    ${sectors.map(sector => `
      <div class="card">
        <h3>${sector.charAt(0).toUpperCase() + sector.slice(1)} Sector</h3>
        <p>Template for managers in the ${sector} sector</p>
        <a href="manager_${sector}.html" target="_blank">View Template</a>
      </div>
    `).join('')}
  </div>
  
  <h2>Engineer Templates</h2>
  <div class="grid">
    ${sectors.map(sector => `
      <div class="card">
        <h3>${sector.charAt(0).toUpperCase() + sector.slice(1)} Sector</h3>
        <p>Template for engineers in the ${sector} sector</p>
        <a href="engineer_${sector}.html" target="_blank">View Template</a>
      </div>
    `).join('')}
  </div>
  
  <h2>Buyer Templates</h2>
  <div class="grid">
    ${sectors.map(sector => `
      <div class="card">
        <h3>${sector.charAt(0).toUpperCase() + sector.slice(1)} Sector</h3>
        <p>Template for buyers in the ${sector} sector</p>
        <a href="buyer_${sector}.html" target="_blank">View Template</a>
      </div>
    `).join('')}
  </div>
  
  <h2>Technician Templates</h2>
  <div class="grid">
    ${sectors.map(sector => `
      <div class="card">
        <h3>${sector.charAt(0).toUpperCase() + sector.slice(1)} Sector</h3>
        <p>Template for technicians in the ${sector} sector</p>
        <a href="technician_${sector}.html" target="_blank">View Template</a>
      </div>
    `).join('')}
  </div>
</body>
</html>
`;

fs.writeFileSync(path.join(outputDir, 'index.html'), indexContent);
console.log(`✅ Index file generated at ${path.join(outputDir, 'index.html')}`);
