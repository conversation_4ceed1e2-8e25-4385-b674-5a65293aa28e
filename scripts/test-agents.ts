#!/usr/bin/env ts-node

/**
 * Agent system test script
 * This script tests the agent system
 */

import * as dotenv from "dotenv";
import { TestTenderMonitorAgent } from "../src/agents/test/TestTenderMonitorAgent";
import { TestOutreachEmailAgent } from "../src/agents/test/TestOutreachEmailAgent";
import { TestAnalyticsAgent } from "../src/agents/test/TestAnalyticsAgent";
import { TestCoordinatorAgent } from "../src/agents/test/TestCoordinatorAgent";
import { Logger } from "../src/utils/Logger";
import { TestDatabaseService } from "../src/services/test/TestDatabaseService";

// Load environment variables
dotenv.config();

// Create logger
const logger = Logger.getInstance("AgentTest");

// Parse command line arguments
const args = process.argv.slice(2);
const testMode = args[0] || "all";

// Helper function to initialize an agent
async function initializeAgent(agent: any, name: string): Promise<void> {
  try {
    logger.info(`Initializing ${name} agent`);
    await agent.initialize();
    logger.info(`${name} agent initialized successfully`);
  } catch (error) {
    logger.error(`Failed to initialize ${name} agent`, {
      error:
        error instanceof Error
          ? {
              message: error.message,
              stack: error.stack,
              name: error.name,
            }
          : String(error),
    });
    throw error;
  }
}

// Main function
async function main() {
  logger.info("Starting Mexel Agent Test");
  logger.info(`Test mode: ${testMode}`);

  try {
    // Initialize database service
    const dbService = new TestDatabaseService();
    await dbService.initialize();

    // Test TenderMonitorAgent
    if (testMode === "all" || testMode === "tender") {
      logger.info("Testing TenderMonitorAgent");

      const tenderMonitor = new TestTenderMonitorAgent();
      await initializeAgent(tenderMonitor, "TenderMonitor");

      // Execute the agent
      logger.info("Executing TenderMonitorAgent");
      await tenderMonitor.execute();

      // Get agent metrics
      const metrics = tenderMonitor.getMetrics();
      logger.info("TenderMonitorAgent metrics:", metrics);

      // Shutdown the agent
      await tenderMonitor.shutdown();
    }

    // Test OutreachEmailAgent
    if (testMode === "all" || testMode === "email") {
      logger.info("Testing OutreachEmailAgent");

      const outreachEmail = new TestOutreachEmailAgent();
      await initializeAgent(outreachEmail, "OutreachEmail");

      // Execute the agent
      logger.info("Executing OutreachEmailAgent");
      await outreachEmail.execute();

      // Get agent metrics
      const metrics = outreachEmail.getMetrics();
      logger.info("OutreachEmailAgent metrics:", metrics);

      // Shutdown the agent
      await outreachEmail.shutdown();
    }

    // Test AnalyticsAgent
    if (testMode === "all" || testMode === "analytics") {
      logger.info("Testing AnalyticsAgent");

      const analytics = new TestAnalyticsAgent();
      await initializeAgent(analytics, "AnalyticsAgent");

      // Execute the agent
      logger.info("Executing AnalyticsAgent");
      await analytics.execute();

      // Get agent metrics
      const metrics = analytics.getMetrics();
      logger.info("AnalyticsAgent metrics:", metrics);

      // Shutdown the agent
      await analytics.shutdown();
    }

    // Test CoordinatorAgent
    if (testMode === "all" || testMode === "coordinator") {
      logger.info("Testing CoordinatorAgent");

      const coordinator = new TestCoordinatorAgent();
      await initializeAgent(coordinator, "Coordinator");

      // Register other agents
      if (testMode === "all") {
        const tenderMonitor = new TestTenderMonitorAgent();
        await initializeAgent(tenderMonitor, "TenderMonitor");
        coordinator.registerAgent(tenderMonitor);

        const outreachEmail = new TestOutreachEmailAgent();
        await initializeAgent(outreachEmail, "OutreachEmail");
        coordinator.registerAgent(outreachEmail);

        const analytics = new TestAnalyticsAgent();
        await initializeAgent(analytics, "AnalyticsAgent");
        coordinator.registerAgent(analytics);
      }

      // Execute the agent
      logger.info("Executing CoordinatorAgent");
      await coordinator.execute();

      // Get agent status
      const status = coordinator.getAgentStatus();
      logger.info("Agent status:", status);

      // Shutdown the agent
      await coordinator.shutdown();
    }

    // Close database connection
    await dbService.close();

    logger.info("Agent test completed successfully");
  } catch (error) {
    logger.error("Failed to run agent test", {
      error:
        error instanceof Error
          ? {
              message: error.message,
              stack: error.stack,
              name: error.name,
            }
          : String(error),
    });
    process.exit(1);
  }
}

// Run main function
main().catch((error) => {
  logger.error("Unhandled error", {
    error: error instanceof Error ? error : new Error(String(error)),
  });
  process.exit(1);
});
