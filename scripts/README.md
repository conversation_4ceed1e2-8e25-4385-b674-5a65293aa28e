# Mexel Scripts Directory

This directory contains all utility scripts for the Mexel project, organized by functionality and purpose.

## 📁 Directory Structure

```
scripts/
├── README.md                           # This documentation
├── frontend/                           # Frontend-specific scripts
│   ├── typescript/                     # TypeScript fixes and utilities
│   │   ├── fix-all-typescript.js       # Consolidated TypeScript fixer
│   │   ├── fix-react-types.js          # React-specific type fixes
│   │   └── fix-component-declarations.js # Component declaration fixes
│   ├── imports/                        # Import management scripts
│   │   ├── fix-imports.js              # Consolidated import fixer
│   │   └── fix-unused-imports.js       # Remove unused imports
│   ├── dependencies/                   # Package and dependency management
│   │   ├── fix-packages.js             # Package management fixes
│   │   └── update-dependencies.sh      # Dependency updates
│   ├── ui-libraries/                   # UI library specific fixes
│   │   ├── fix-mui.js                  # Material-UI fixes
│   │   └── fix-recharts.js             # Recharts fixes
│   └── fix-all-frontend.js             # Master frontend fixer
├── backend/                            # Backend-specific scripts
├── database/                           # Database management
│   ├── fix-database.js                 # Database fixes
│   └── migrate-database.js             # Database migrations
└── maintenance/                        # General maintenance scripts
    ├── clean-unused-imports.sh         # Project-wide import cleanup
    └── lint-all.js                     # Comprehensive linting
```

## 🚀 Quick Start

### Most Common Tasks

```bash
# Fix all frontend issues (recommended first step)
node scripts/frontend/fix-all-frontend.js

# Fix TypeScript issues only
node scripts/frontend/typescript/fix-all-typescript.js

# Fix import issues only
node scripts/frontend/imports/fix-imports.js

# Fix package dependencies
node scripts/frontend/dependencies/fix-packages.js
```

### Command-Line Options

All scripts support common options:
- `--dry-run` - Preview changes without applying them
- `--path <path>` - Specify custom file path
- `--help` - Show detailed help
- `--verbose` - Show detailed output

## 📋 Script Categories

### Frontend Scripts

#### TypeScript (`frontend/typescript/`)

| Script | Purpose | Consolidates |
|--------|---------|-------------|
| `fix-all-typescript.js` | Master TypeScript fixer | All TypeScript-related fixes |
| `fix-react-types.js` | React type issues | `fix-fc-reactnode-issues.js`, `fix-react-namespace.js` |
| `fix-component-declarations.js` | Component declarations | `fix-component-declarations.js`, `fix-component-types.js` |

**Handles:**
- React.FC → FC conversions
- ReactNode → proper typing
- useState type issues
- Component declaration problems
- Namespace issues (React.React → React)
- SyntheticEvent, ChangeEvent, MouseEvent fixes

#### Imports (`frontend/imports/`)

| Script | Purpose | Consolidates |
|--------|---------|-------------|
| `fix-imports.js` | Master import fixer | All import-related scripts |
| `fix-unused-imports.js` | Remove unused imports | ESLint-based unused import removal |

**Handles:**
- Unused import removal
- Duplicate import consolidation
- Import path corrections
- Missing import additions

#### Dependencies (`frontend/dependencies/`)

| Script | Purpose | Consolidates |
|--------|---------|-------------|
| `fix-packages.js` | Package management | `fix-frontend-packages.sh`, `fix-package-json.sh` |
| `update-dependencies.sh` | Dependency updates | Various dependency scripts |

**Handles:**
- Package.json fixes
- Yarn workspace issues
- Version conflicts
- Missing dependencies

#### UI Libraries (`frontend/ui-libraries/`)

| Script | Purpose | Consolidates |
|--------|---------|-------------|
| `fix-mui.js` | Material-UI fixes | `fix-mui-chip-icons.js`, MUI-related fixes |
| `fix-recharts.js` | Recharts fixes | `fix-recharts-issues.js`, `fix-recharts-direct-usage.js` |

**Handles:**
- Material-UI component issues
- Recharts integration problems
- UI library type issues

### Database Scripts (`database/`)

| Script | Purpose |
|--------|---------|
| `fix-database.js` | Database schema and connection fixes |
| `migrate-database.js` | Database migrations |

### Maintenance Scripts (`maintenance/`)

| Script | Purpose |
|--------|---------|
| `clean-unused-imports.sh` | Project-wide import cleanup |
| `lint-all.js` | Comprehensive linting across all packages |

## 🔧 Script Consolidation Map

### Replaced Scripts

The following scripts have been **consolidated** and can be **removed**:

#### Root Level Scripts (to be removed)
- `fix-frontend-packages.sh` → `scripts/frontend/dependencies/fix-packages.js`
- `fix-frontend.sh` → `scripts/frontend/fix-all-frontend.js`
- `fix-package-json.sh` → `scripts/frontend/dependencies/fix-packages.js`
- `fix-yarn-frontend.sh` → `scripts/frontend/dependencies/fix-packages.js`

#### Frontend Directory Scripts (to be removed)
- `frontend/fix-typescript-errors.js` → `scripts/frontend/typescript/fix-all-typescript.js`
- `frontend/fix-specific-typescript-errors.js` → `scripts/frontend/typescript/fix-all-typescript.js`
- `frontend/fix-component-declarations.js` → `scripts/frontend/typescript/fix-component-declarations.js`
- `frontend/fix-component-declarations-new.js` → `scripts/frontend/typescript/fix-component-declarations.js`
- `frontend/fix-component-types.js` → `scripts/frontend/typescript/fix-react-types.js`
- `frontend/fix-fc-reactnode-issues.js` → `scripts/frontend/typescript/fix-react-types.js`
- `frontend/fix-react-namespace.js` → `scripts/frontend/typescript/fix-react-types.js`
- `frontend/fix-react-types.js` → `scripts/frontend/typescript/fix-react-types.js`
- `frontend/fix-react-usestate-types.js` → `scripts/frontend/typescript/fix-all-typescript.js`
- `frontend/fix-usestate-issues.js` → `scripts/frontend/typescript/fix-all-typescript.js`
- `frontend/fix-use-state-issues.js` → `scripts/frontend/typescript/fix-all-typescript.js`
- `frontend/fix-import-paths.js` → `scripts/frontend/imports/fix-imports.js`
- `frontend/fix-import-statements.js` → `scripts/frontend/imports/fix-imports.js`
- `frontend/fix-duplicate-imports.js` → `scripts/frontend/imports/fix-imports.js`
- `frontend/fix-mui-chip-icons.js` → `scripts/frontend/ui-libraries/fix-mui.js`
- `frontend/fix-recharts-issues.js` → `scripts/frontend/ui-libraries/fix-recharts.js`
- `frontend/fix-recharts-direct-usage.js` → `scripts/frontend/ui-libraries/fix-recharts.js`

## 🎯 Usage Examples

### Development Workflow

```bash
# 1. Start with comprehensive frontend fixes
node scripts/frontend/fix-all-frontend.js --dry-run

# 2. Apply fixes if dry-run looks good
node scripts/frontend/fix-all-frontend.js

# 3. Fix specific issues if needed
node scripts/frontend/typescript/fix-react-types.js
node scripts/frontend/imports/fix-unused-imports.js

# 4. Run maintenance
node scripts/maintenance/lint-all.js
```

### Targeted Fixes

```bash
# TypeScript issues in specific directory
node scripts/frontend/typescript/fix-all-typescript.js --path "src/components/**/*.tsx"

# Import issues with verbose output
node scripts/frontend/imports/fix-imports.js --verbose

# UI library fixes only
node scripts/frontend/ui-libraries/fix-mui.js
node scripts/frontend/ui-libraries/fix-recharts.js
```

## 📝 Migration Instructions

### For Developers

1. **Update your bookmarks/scripts** to use the new paths
2. **Remove old scripts** after confirming the new ones work
3. **Update package.json scripts** to reference new locations
4. **Update documentation** that references old script paths

### Script Migration Commands

```bash
# Create aliases in package.json for backward compatibility
npm pkg set scripts.fix-frontend="node scripts/frontend/fix-all-frontend.js"
npm pkg set scripts.fix-typescript="node scripts/frontend/typescript/fix-all-typescript.js"
npm pkg set scripts.fix-imports="node scripts/frontend/imports/fix-imports.js"
```

## 🛠️ Development Guidelines

### Adding New Scripts

1. **Choose the appropriate directory** based on functionality
2. **Follow naming conventions**: `fix-[specific-issue].js`
3. **Include standard CLI options** (`--dry-run`, `--help`, etc.)
4. **Add documentation** to this README
5. **Include tests** where appropriate

### Script Standards

All scripts should:
- Accept `--dry-run` for safe testing
- Provide `--help` documentation
- Use consistent error handling
- Log changes clearly
- Support custom paths via `--path`
- Follow the established CLI pattern

## 🔍 Troubleshooting

### Common Issues

1. **Script not found**: Check if you're using the new path structure
2. **Permission denied**: Ensure scripts are executable (`chmod +x`)
3. **Path issues**: Use `--path` to specify custom file locations
4. **Dry-run first**: Always test with `--dry-run` before applying changes

### Getting Help

```bash
# Get help for any script
node scripts/frontend/fix-all-frontend.js --help

# Check script status
ls -la scripts/frontend/typescript/

# Verify script functionality
node scripts/frontend/typescript/fix-all-typescript.js --dry-run --verbose
```

## 📊 Script Performance

### Benchmarks

| Category | Script Count Before | Script Count After | Reduction |
|----------|-------------------|------------------|-----------|
| TypeScript | 15+ | 3 | 80% |
| Imports | 6+ | 2 | 67% |
| Dependencies | 8+ | 2 | 75% |
| UI Libraries | 4+ | 2 | 50% |
| **Total** | **33+** | **9** | **73%** |

### Benefits

- **Reduced complexity**: 73% fewer scripts to maintain
- **Better organization**: Clear functional grouping
- **Eliminated duplication**: No overlapping functionality
- **Improved documentation**: Clear purpose and usage
- **Easier maintenance**: Centralized logic and updates