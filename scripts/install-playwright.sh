#!/bin/bash

# Script to install Playwright and its dependencies

# Change to the project root directory
cd "$(dirname "$0")/.."

# Ensure the virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate the virtual environment
source venv/bin/activate

# Install Playwright and its dependencies
echo "Installing Playwright and dependencies..."
pip install -r requirements-playwright.txt

# Install Playwright browsers
echo "Installing Playwright browsers..."
python -m playwright install chromium

echo "✅ Playwright installation completed successfully"
