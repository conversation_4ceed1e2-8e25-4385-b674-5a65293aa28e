#!/bin/bash

# <PERSON>ript to backup the SQLite database

# Change to the project root directory
cd "$(dirname "$0")/.."

# Default values
RETENTION_DAYS=30
BACKUP_DIR="./data/backups"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --retention=*)
      RETENTION_DAYS="${1#*=}"
      shift
      ;;
    --output=*)
      BACKUP_DIR="${1#*=}"
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--retention=<days>] [--output=<directory>]"
      exit 1
      ;;
  esac
done

# Run the backup script
echo "Running database backup with retention: $RETENTION_DAYS days, output directory: $BACKUP_DIR"
npx ts-node src/scripts/backup-database.ts --retention=$RETENTION_DAYS --output=$BACKUP_DIR

# Check if the backup was successful
if [ $? -eq 0 ]; then
    echo "✅ Database backup completed successfully"
else
    echo "❌ Database backup failed"
    exit 1
fi
