#!/usr/bin/env ts-node

/**
 * Test script for the OutreachEmailAgent
 * This script tests the OutreachEmailAgent's ability to process tenders and generate emails
 */

import { OutreachEmailAgent } from '../src/agents/email/OutreachEmailAgent';
import { DatabaseService } from '../src/services/DatabaseService';
import { Tender, TenderStatus } from '../src/shared/types/tender';
import { v4 as uuidv4 } from 'uuid';
import { config } from '../src/config/config';

// Configure the environment
process.env.NODE_ENV = 'development';

// Sample tender data for testing
const sampleTenders: Partial<Tender>[] = [
    {
        id: uuidv4(),
        title: 'Water Treatment System for Municipal Plant',
        description: 'Supply and installation of a comprehensive water treatment system for the municipal water treatment plant. The system should include filtration, chemical treatment, and monitoring capabilities.',
        reference: 'WT-2023-001',
        issuer: 'City of Johannesburg',
        publishDate: new Date('2023-10-15'),
        closingDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
        status: TenderStatus.SUBMITTED,
        url: 'https://example.com/tenders/wt-2023-001',
        value: 2500000,
        category: 'Water Treatment',
        confidence: 0.92,
        requirements: [
            'Experience in municipal water treatment',
            'ISO 9001 certification',
            'Minimum 5 years in business',
            'Local support and maintenance capabilities'
        ]
    },
    {
        id: uuidv4(),
        title: 'Cooling Tower Chemical Treatment Program',
        description: 'Implementation of a chemical treatment program for industrial cooling towers at the power station. The program should prevent scaling, corrosion, and biological growth while optimizing water usage.',
        reference: 'CT-2023-045',
        issuer: 'Eskom Holdings',
        publishDate: new Date('2023-10-20'),
        closingDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000), // 21 days from now
        status: TenderStatus.SUBMITTED,
        url: 'https://example.com/tenders/ct-2023-045',
        value: 1800000,
        category: 'Cooling Towers',
        confidence: 0.88,
        requirements: [
            'Experience with power plant cooling systems',
            'Proven track record in similar installations',
            'Compliance with environmental regulations',
            'Performance guarantees required'
        ]
    },
    {
        id: uuidv4(),
        title: 'Mine Water Management Solutions',
        description: 'Comprehensive water management solutions for a gold mining operation. The solution should address acid mine drainage, heavy metal contamination, and water recycling capabilities.',
        reference: 'MW-2023-078',
        issuer: 'AngloGold Ashanti',
        publishDate: new Date('2023-10-25'),
        closingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        status: TenderStatus.SUBMITTED,
        url: 'https://example.com/tenders/mw-2023-078',
        value: 3200000,
        category: 'Mining',
        confidence: 0.85,
        requirements: [
            'Experience in mine water treatment',
            'Acid mine drainage expertise',
            'Heavy metal removal capabilities',
            'Water recycling technology'
        ]
    }
];

/**
 * Initialize the database with test data
 */
async function initializeDatabase(): Promise<void> {
    console.log('Initializing database with test data...');
    
    try {
        // Initialize database service
        const dbService = DatabaseService.getInstance();
        await dbService.initialize();
        
        const db = dbService.getDatabase();
        
        // Create necessary tables if they don't exist
        db.exec(`
            CREATE TABLE IF NOT EXISTS tenders (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                description TEXT,
                reference TEXT,
                issuer TEXT,
                publishDate TEXT,
                closingDate TEXT,
                status TEXT NOT NULL,
                url TEXT,
                value REAL,
                category TEXT,
                confidence REAL,
                metadata TEXT,
                requirements TEXT,
                documents TEXT
            );
            
            CREATE TABLE IF NOT EXISTS emails (
                id TEXT PRIMARY KEY,
                to TEXT NOT NULL,
                subject TEXT NOT NULL,
                body TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'queued',
                scheduledDate TEXT,
                sentAt TEXT,
                metadata TEXT,
                createdAt TEXT NOT NULL DEFAULT (datetime('now')),
                updatedAt TEXT NOT NULL DEFAULT (datetime('now'))
            );
            
            CREATE TABLE IF NOT EXISTS email_engagements (
                id TEXT PRIMARY KEY,
                emailId TEXT NOT NULL,
                type TEXT NOT NULL,
                timestamp TEXT NOT NULL DEFAULT (datetime('now')),
                metadata TEXT,
                FOREIGN KEY (emailId) REFERENCES emails(id)
            );
        `);
        
        // Insert sample tenders
        const insertTenderStmt = db.prepare(`
            INSERT OR REPLACE INTO tenders (
                id, title, description, reference, issuer, publishDate, closingDate,
                status, url, value, category, confidence, requirements
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        for (const tender of sampleTenders) {
            insertTenderStmt.run(
                tender.id,
                tender.title,
                tender.description,
                tender.reference,
                tender.issuer,
                tender.publishDate?.toISOString(),
                tender.closingDate?.toISOString(),
                tender.status,
                tender.url,
                tender.value,
                tender.category,
                tender.confidence,
                JSON.stringify(tender.requirements)
            );
        }
        
        console.log(`Inserted ${sampleTenders.length} sample tenders into the database`);
    } catch (error) {
        console.error('Failed to initialize database:', error);
        throw error;
    }
}

/**
 * Test the OutreachEmailAgent
 */
async function testOutreachEmailAgent(): Promise<void> {
    console.log('Testing OutreachEmailAgent...');
    
    try {
        // Initialize the agent
        const agent = new OutreachEmailAgent();
        await agent.initializeDependencies();
        
        console.log('OutreachEmailAgent initialized successfully');
        
        // Execute the agent
        console.log('Executing OutreachEmailAgent...');
        await agent.execute();
        
        console.log('OutreachEmailAgent executed successfully');
        
        // Check the results
        const dbService = DatabaseService.getInstance();
        const db = dbService.getDatabase();
        
        const emails = db.prepare('SELECT * FROM emails').all();
        
        console.log(`Generated ${emails.length} emails:`);
        for (const email of emails) {
            console.log(`- Email to: ${email.to}`);
            console.log(`  Subject: ${email.subject}`);
            console.log(`  Status: ${email.status}`);
            console.log('');
        }
        
        // Shutdown the agent
        await agent.shutdown();
        
        console.log('Test completed successfully');
    } catch (error) {
        console.error('Test failed:', error);
        throw error;
    }
}

/**
 * Main function
 */
async function main(): Promise<void> {
    try {
        // Initialize the database
        await initializeDatabase();
        
        // Test the OutreachEmailAgent
        await testOutreachEmailAgent();
        
        console.log('All tests completed successfully');
        process.exit(0);
    } catch (error) {
        console.error('Tests failed:', error);
        process.exit(1);
    }
}

// Run the main function
main();
