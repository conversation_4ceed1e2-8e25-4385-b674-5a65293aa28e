#!/usr/bin/env ts-node

/**
 * New Agent system startup script
 * This script starts the new agent system
 */

import * as dotenv from "dotenv";
import { CoordinatorAgent } from "../src/agents/CoordinatorAgent";
import { TenderMonitorAgent } from "../src/agents/implementations/TenderMonitorAgent";
import { OutreachEmailAgent } from "../src/agents/implementations/OutreachEmailAgent";
import { AnalyticsAgent } from "../src/agents/implementations/AnalyticsAgent";
import { Logger } from "../src/utils/Logger";
import { AgentStatus } from "../src/types/agents/AgentTypes";

// Load environment variables
dotenv.config();

// Create logger
const logger = Logger.getInstance("NewAgentSystem");

// Parse command line arguments
const args = process.argv.slice(2);
const runMode = args[0] || "all";

// Helper function to initialize an agent
async function initializeAgent(agent: any, name: string): Promise<void> {
  try {
    logger.info(`Initializing ${name} agent`);
    await agent.initialize();
    logger.info(`${name} agent initialized successfully`);
  } catch (error) {
    logger.error(`Failed to initialize ${name} agent`, {
      error:
        error instanceof Error
          ? {
              message: error.message,
              stack: error.stack,
              name: error.name,
            }
          : String(error),
    });
    throw error;
  }
}

// Main function
async function main() {
  logger.info("Starting Mexel New Agent System");
  logger.info(`Run mode: ${runMode}`);

  try {
    // Create coordinator agent
    const coordinator = new CoordinatorAgent();

    // Initialize coordinator
    await initializeAgent(coordinator, "Coordinator");

    // Create and register agents based on run mode
    if (runMode === "all" || runMode === "tender") {
      const tenderMonitor = new TenderMonitorAgent("TenderMonitor");
      await initializeAgent(tenderMonitor, "TenderMonitor");
      coordinator.registerAgent(tenderMonitor);
      logger.info("TenderMonitor agent registered");
    }

    if (runMode === "all" || runMode === "email") {
      const outreachEmail = new OutreachEmailAgent("OutreachEmail");
      await initializeAgent(outreachEmail, "OutreachEmail");
      coordinator.registerAgent(outreachEmail);
      logger.info("OutreachEmail agent registered");
    }

    if (runMode === "all" || runMode === "analytics") {
      const analytics = new AnalyticsAgent("Analytics");
      await initializeAgent(analytics, "Analytics");
      coordinator.registerAgent(analytics);
      logger.info("Analytics agent registered");
    }

    // Start the coordinator
    logger.info("Starting coordinator agent");
    await coordinator.start();

    // Execute the coordinator once to start the agent cycle
    logger.info("Executing coordinator agent");
    await coordinator.execute();

    // Print agent status
    const agents = coordinator.getAllAgents();
    logger.info("Registered agents:", {
      agents: agents.map((a) => ({
        id: a.id,
        name: a.name,
        role: a.role,
        status: a.getStatus(),
      })),
    });

    // Keep the process running
    logger.info("Agent system started successfully");
    logger.info("Press Ctrl+C to stop");

    // Handle process termination
    process.on("SIGINT", async () => {
      logger.info("Shutting down agent system");

      try {
        await coordinator.shutdown();
        logger.info("Agent system shut down successfully");
        process.exit(0);
      } catch (error) {
        logger.error("Error shutting down agent system", {
          error: error instanceof Error ? error : new Error(String(error)),
        });
        process.exit(1);
      }
    });
  } catch (error) {
    logger.error("Failed to start agent system", {
      error:
        error instanceof Error
          ? {
              message: error.message,
              stack: error.stack,
              name: error.name,
            }
          : String(error),
    });
    process.exit(1);
  }
}

// Run main function
main().catch((error) => {
  logger.error("Unhandled error", {
    error: error instanceof Error ? error : new Error(String(error)),
  });
  process.exit(1);
});
