#!/usr/bin/env ts-node

/**
 * Test script for the enhanced OutreachEmailAgent
 * This script tests the integration with CRM, campaign management, and analytics tracking
 */

import { v4 as uuidv4 } from 'uuid';
import Database from 'better-sqlite3';
import * as path from 'path';
import * as fs from 'fs';

// Define interfaces locally to avoid import issues
interface IEmailTemplate {
  name: string;
  subject: string;
  body: string;
  emailId?: string;
  metadata?: Record<string, any>;
}

interface IEmailEngagement {
  emailId: string;
  leadId: string;
  type: 'open' | 'click' | 'reply';
  timestamp: Date;
  metadata?: Record<string, any>;
}

// Define LeadStatus enum
enum LeadStatus {
  NEW = "NEW",
  CONTACTED = "CONTACTED",
  QUALIFIED = "QUALIFIED",
  UNQUALIFIED = "UNQUALIFIED",
  CONVERTED = "CONVERTED",
  EMAIL_SEQUENCE_STARTED = "EMAIL_SEQUENCE_STARTED",
  ENGAGED = "ENGAGED"
}

// Configure the environment
process.env.NODE_ENV = 'development';

// Sample lead data for testing
const sampleLeads = [
  {
    id: `lead_${uuidv4()}`,
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    company: 'Test Company 1',
    position: 'CEO',
    industry: 'Manufacturing',
    status: LeadStatus.NEW
  },
  {
    id: `lead_${uuidv4()}`,
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Smith',
    company: 'Test Company 2',
    position: 'CTO',
    industry: 'Technology',
    status: LeadStatus.NEW
  },
  {
    id: `lead_${uuidv4()}`,
    email: '<EMAIL>',
    firstName: 'Bob',
    lastName: 'Johnson',
    company: 'Test Company 3',
    position: 'Operations Manager',
    industry: 'Energy',
    status: LeadStatus.NEW
  }
];

// Sample email template for testing
const sampleTemplate: IEmailTemplate = {
  name: 'Test Template',
  subject: 'Test Email for {{first_name}}',
  body: `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; color: #333;">
      <p>Dear {{first_name}},</p>

      <p>I hope this email finds you well. I'm reaching out regarding Mexel's water treatment solutions that might be of interest to {{company_name}}.</p>

      <p>Our Film-Forming Amine (FFA) technology has been proven to:</p>

      <ul>
        <li>Reduce chemical usage by up to 40%</li>
        <li>Improve energy efficiency by 15-25%</li>
        <li>Extend equipment lifespan</li>
        <li>Ensure environmental compliance</li>
      </ul>

      <p>Would you be available for a brief call to discuss how our solutions can benefit your operations?</p>

      <p>Best regards,<br>
      Mexel Water Treatment Solutions Team</p>
    </div>
  `,
  emailId: `template_${uuidv4()}`
};

/**
 * Initialize the database with test data
 */
async function initializeDatabase(): Promise<Database.Database> {
  console.log('Initializing database with test data...');

  try {
    // Ensure data directory exists
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Initialize SQLite database
    const dbPath = path.join(dataDir, 'test-crm.db');
    const db = new Database(dbPath);

    // Create necessary tables if they don't exist
    db.exec(`
      CREATE TABLE IF NOT EXISTS leads (
        id TEXT PRIMARY KEY,
        email TEXT NOT NULL,
        firstName TEXT,
        lastName TEXT,
        company TEXT,
        position TEXT,
        industry TEXT,
        status TEXT NOT NULL,
        lastContactDate TEXT,
        metadata TEXT
      );

      CREATE TABLE IF NOT EXISTS emails (
        id TEXT PRIMARY KEY,
        recipient TEXT NOT NULL,
        subject TEXT NOT NULL,
        body TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'queued',
        scheduledDate TEXT,
        sentAt TEXT,
        metadata TEXT,
        createdAt TEXT NOT NULL DEFAULT (datetime('now')),
        updatedAt TEXT NOT NULL DEFAULT (datetime('now'))
      );

      CREATE TABLE IF NOT EXISTS email_engagements (
        id TEXT PRIMARY KEY,
        emailId TEXT NOT NULL,
        type TEXT NOT NULL,
        timestamp TEXT NOT NULL DEFAULT (datetime('now')),
        metadata TEXT,
        FOREIGN KEY (emailId) REFERENCES emails(id)
      );

      CREATE TABLE IF NOT EXISTS email_templates (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        subject TEXT NOT NULL,
        body TEXT NOT NULL,
        metadata TEXT,
        createdAt TEXT NOT NULL DEFAULT (datetime('now')),
        updatedAt TEXT NOT NULL DEFAULT (datetime('now'))
      );

      CREATE TABLE IF NOT EXISTS email_campaigns (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        status TEXT NOT NULL,
        templateId TEXT,
        segment TEXT,
        scheduledDate TEXT,
        sentDate TEXT,
        metadata TEXT,
        createdAt TEXT NOT NULL DEFAULT (datetime('now')),
        updatedAt TEXT NOT NULL DEFAULT (datetime('now'))
      );

      CREATE TABLE IF NOT EXISTS campaign_recipients (
        id TEXT PRIMARY KEY,
        campaignId TEXT NOT NULL,
        leadId TEXT NOT NULL,
        email TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        emailId TEXT,
        sentAt TEXT,
        metadata TEXT,
        FOREIGN KEY (campaignId) REFERENCES email_campaigns(id),
        FOREIGN KEY (leadId) REFERENCES leads(id)
      );

      CREATE TABLE IF NOT EXISTS campaign_analytics (
        id TEXT PRIMARY KEY,
        campaignId TEXT NOT NULL,
        sent INTEGER DEFAULT 0,
        delivered INTEGER DEFAULT 0,
        opened INTEGER DEFAULT 0,
        clicked INTEGER DEFAULT 0,
        replied INTEGER DEFAULT 0,
        bounced INTEGER DEFAULT 0,
        unsubscribed INTEGER DEFAULT 0,
        lastUpdated TEXT NOT NULL DEFAULT (datetime('now')),
        FOREIGN KEY (campaignId) REFERENCES email_campaigns(id)
      );

      CREATE TABLE IF NOT EXISTS lead_activities (
        id TEXT PRIMARY KEY,
        leadId TEXT NOT NULL,
        type TEXT NOT NULL,
        emailId TEXT,
        timestamp TEXT NOT NULL DEFAULT (datetime('now')),
        metadata TEXT,
        FOREIGN KEY (leadId) REFERENCES leads(id)
      );

      CREATE TABLE IF NOT EXISTS email_analytics (
        id TEXT PRIMARY KEY,
        period TEXT NOT NULL,
        sent INTEGER DEFAULT 0,
        delivered INTEGER DEFAULT 0,
        opened INTEGER DEFAULT 0,
        clicked INTEGER DEFAULT 0,
        replied INTEGER DEFAULT 0,
        bounced INTEGER DEFAULT 0,
        unsubscribed INTEGER DEFAULT 0,
        metadata TEXT,
        createdAt TEXT NOT NULL DEFAULT (datetime('now')),
        updatedAt TEXT NOT NULL DEFAULT (datetime('now'))
      );

      CREATE TABLE IF NOT EXISTS email_tracking_pixels (
        id TEXT PRIMARY KEY,
        emailId TEXT NOT NULL,
        token TEXT NOT NULL,
        createdAt TEXT NOT NULL DEFAULT (datetime('now')),
        FOREIGN KEY (emailId) REFERENCES emails(id)
      );

      CREATE TABLE IF NOT EXISTS email_tracking_links (
        id TEXT PRIMARY KEY,
        emailId TEXT NOT NULL,
        originalUrl TEXT NOT NULL,
        trackingUrl TEXT NOT NULL,
        createdAt TEXT NOT NULL DEFAULT (datetime('now')),
        FOREIGN KEY (emailId) REFERENCES emails(id)
      );
    `);

    // Insert sample leads
    const insertLeadStmt = db.prepare(`
      INSERT OR REPLACE INTO leads (
        id, email, firstName, lastName, company, position, industry, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    for (const lead of sampleLeads) {
      insertLeadStmt.run(
        lead.id,
        lead.email,
        lead.firstName,
        lead.lastName,
        lead.company,
        lead.position,
        lead.industry,
        lead.status
      );
    }

    console.log(`Inserted ${sampleLeads.length} sample leads into the database`);

    // Insert sample template
    db.prepare(`
      INSERT OR REPLACE INTO email_templates (
        id, name, subject, body, metadata
      ) VALUES (?, ?, ?, ?, ?)
    `).run(
      sampleTemplate.emailId,
      sampleTemplate.name,
      sampleTemplate.subject,
      sampleTemplate.body,
      JSON.stringify({ type: 'test' })
    );

    console.log('Inserted sample email template into the database');

    return db;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

/**
 * Test CRM integration
 */
async function testCRMIntegration(db: Database.Database): Promise<void> {
  console.log('Testing CRM integration...');

  try {
    // Get a sample lead
    const lead = sampleLeads[0];

    // First create a sample email
    const emailId = `email_${uuidv4()}`;
    const now = new Date().toISOString();

    db.prepare(`
      INSERT INTO emails (
        id, recipient, subject, body, status, sentAt, metadata, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      emailId,
      lead.email,
      'Test Email Subject',
      '<p>Test email body</p>',
      'sent',
      now,
      JSON.stringify({
        leadId: lead.id
      }),
      now,
      now
    );

    // Create a sample engagement
    const engagement: IEmailEngagement = {
      emailId: emailId, // Use the email we just created
      leadId: lead.id,
      type: 'open',
      timestamp: new Date(),
      metadata: {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ipAddress: '***********'
      }
    };

    // Insert the engagement directly
    const engagementId = `engagement_${uuidv4()}`;
    db.prepare(`
      INSERT INTO email_engagements (
        id, emailId, type, timestamp, metadata
      ) VALUES (?, ?, ?, ?, ?)
    `).run(
      engagementId,
      engagement.emailId,
      engagement.type,
      engagement.timestamp.toISOString(),
      JSON.stringify(engagement.metadata || {})
    );

    console.log('✅ Email engagement recorded successfully');

    // Update lead status based on engagement
    db.prepare(`
      UPDATE leads
      SET status = ?
      WHERE id = ?
    `).run(LeadStatus.EMAIL_SEQUENCE_STARTED, lead.id);

    // Verify the lead status was updated
    const updatedLead = db.prepare('SELECT * FROM leads WHERE id = ?').get(lead.id) as any;

    console.log('Lead status before:', lead.status);
    console.log('Lead status after:', updatedLead.status);

    if (updatedLead.status === LeadStatus.EMAIL_SEQUENCE_STARTED) {
      console.log('✅ CRM integration test passed: Lead status updated correctly');
    } else {
      console.log('❌ CRM integration test failed: Lead status not updated correctly');
    }

    // Log an email activity
    const activityId = `activity_${uuidv4()}`;
    db.prepare(`
      INSERT INTO lead_activities (
        id, leadId, type, emailId, timestamp, metadata
      ) VALUES (?, ?, ?, ?, ?, ?)
    `).run(
      activityId,
      lead.id,
      'email_opened',
      engagement.emailId,
      new Date().toISOString(),
      JSON.stringify({
        timestamp: new Date().toISOString()
      })
    );

    console.log('✅ Email activity logged successfully');
  } catch (error) {
    console.error('CRM integration test failed:', error);
    throw error;
  }
}

/**
 * Test email campaign management
 */
async function testEmailCampaignManagement(db: Database.Database): Promise<void> {
  console.log('Testing email campaign management...');

  try {
    // Create a campaign
    const campaignId = `campaign_${uuidv4()}`;
    const now = new Date().toISOString();

    db.prepare(`
      INSERT INTO email_campaigns (
        id, name, description, status, templateId, segment,
        scheduledDate, metadata, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      campaignId,
      'Test Campaign',
      'A test campaign for the water treatment industry',
      'draft',
      sampleTemplate.emailId,
      'Manufacturing',
      null,
      JSON.stringify({
        type: 'outreach',
        createdAt: now
      }),
      now,
      now
    );

    console.log('✅ Campaign created successfully:', campaignId);

    // Create analytics record
    db.prepare(`
      INSERT INTO campaign_analytics (
        id, campaignId, lastUpdated
      ) VALUES (?, ?, ?)
    `).run(
      `analytics_${uuidv4()}`,
      campaignId,
      now
    );

    // Add recipients to the campaign
    let addedCount = 0;

    for (const lead of sampleLeads) {
      db.prepare(`
        INSERT INTO campaign_recipients (
          id, campaignId, leadId, email, status
        ) VALUES (?, ?, ?, ?, ?)
      `).run(
        `recipient_${uuidv4()}`,
        campaignId,
        lead.id,
        lead.email,
        'pending'
      );

      addedCount++;
    }

    console.log(`✅ Added ${addedCount} recipients to the campaign`);

    // Schedule the campaign
    const scheduledDate = new Date();
    scheduledDate.setDate(scheduledDate.getDate() + 1); // Tomorrow

    db.prepare(`
      UPDATE email_campaigns
      SET status = ?, scheduledDate = ?, updatedAt = datetime('now')
      WHERE id = ?
    `).run('scheduled', scheduledDate.toISOString(), campaignId);

    console.log('✅ Campaign scheduled successfully for', scheduledDate.toISOString());
  } catch (error) {
    console.error('Email campaign management test failed:', error);
    throw error;
  }
}

/**
 * Test email analytics tracking
 */
async function testEmailAnalyticsTracking(db: Database.Database): Promise<void> {
  console.log('Testing email analytics tracking...');

  try {
    // Create a sample email ID
    const emailId = `email_${uuidv4()}`;
    const campaignId = `campaign_${uuidv4()}`;
    const now = new Date().toISOString();

    // Insert a sample email
    db.prepare(`
      INSERT INTO emails (
        id, recipient, subject, body, status, metadata, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      emailId,
      sampleLeads[0].email,
      'Test Email Subject',
      '<p>Test email body</p>',
      'sent',
      JSON.stringify({
        leadId: sampleLeads[0].id,
        campaignId: campaignId
      }),
      now,
      now
    );

    // Record email sent in analytics
    const period = new Date().toISOString().substring(0, 7); // YYYY-MM

    db.prepare(`
      INSERT INTO email_analytics (
        id, period, sent, delivered, opened, clicked, replied, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      `analytics_${uuidv4()}`,
      period,
      1,
      1,
      0,
      0,
      0,
      now
    );

    console.log('✅ Email sent recorded successfully');

    // Track open engagement
    const openEngagementId = `engagement_${uuidv4()}`;
    const openTimestamp = new Date();

    db.prepare(`
      INSERT INTO email_engagements (
        id, emailId, type, timestamp, metadata
      ) VALUES (?, ?, ?, ?, ?)
    `).run(
      openEngagementId,
      emailId,
      'open',
      openTimestamp.toISOString(),
      JSON.stringify({
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ipAddress: '***********',
        leadId: sampleLeads[0].id
      })
    );

    // Update analytics for open
    db.prepare(`
      UPDATE email_analytics
      SET opened = opened + 1, updatedAt = ?
      WHERE period = ?
    `).run(now, period);

    console.log('✅ Open engagement tracked successfully');

    // Track click engagement
    const clickEngagementId = `engagement_${uuidv4()}`;
    const clickTimestamp = new Date();

    db.prepare(`
      INSERT INTO email_engagements (
        id, emailId, type, timestamp, metadata
      ) VALUES (?, ?, ?, ?, ?)
    `).run(
      clickEngagementId,
      emailId,
      'click',
      clickTimestamp.toISOString(),
      JSON.stringify({
        url: 'https://www.mexelenergysustain.com/products',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ipAddress: '***********',
        leadId: sampleLeads[0].id
      })
    );

    // Update analytics for click
    db.prepare(`
      UPDATE email_analytics
      SET clicked = clicked + 1, updatedAt = ?
      WHERE period = ?
    `).run(now, period);

    console.log('✅ Click engagement tracked successfully');

    // Get email engagements
    const engagements = db.prepare(`
      SELECT * FROM email_engagements
      WHERE emailId = ?
      ORDER BY timestamp DESC
    `).all(emailId);

    console.log(`✅ Retrieved ${engagements.length} engagements for the email`);

    // Get email analytics
    const analytics = db.prepare(`
      SELECT * FROM email_analytics
      WHERE period = ?
    `).get(period) as any;

    // Calculate rates
    const openRate = analytics.delivered > 0 ? analytics.opened / analytics.delivered : 0;
    const clickRate = analytics.opened > 0 ? analytics.clicked / analytics.opened : 0;

    console.log('✅ Email analytics retrieved successfully:', {
      sent: analytics.sent,
      opened: analytics.opened,
      clicked: analytics.clicked,
      openRate: openRate,
      clickRate: clickRate
    });

    // Update lead status based on engagement
    db.prepare(`
      UPDATE leads
      SET status = ?
      WHERE id = ?
    `).run(LeadStatus.ENGAGED, sampleLeads[0].id);

    // Verify the lead status was updated
    const updatedLead = db.prepare('SELECT * FROM leads WHERE id = ?').get(sampleLeads[0].id) as any;

    console.log('Lead status after engagement:', updatedLead.status);

    if (updatedLead.status === LeadStatus.ENGAGED) {
      console.log('✅ Lead status updated correctly based on email engagement');
    } else {
      console.log('❌ Lead status not updated correctly based on email engagement');
    }
  } catch (error) {
    console.error('Email analytics tracking test failed:', error);
    throw error;
  }
}

/**
 * Main function
 */
async function main(): Promise<void> {
  let db: Database.Database | null = null;

  try {
    // Initialize the database
    db = await initializeDatabase();

    // Test CRM integration
    await testCRMIntegration(db);

    // Test email campaign management
    await testEmailCampaignManagement(db);

    // Test email analytics tracking
    await testEmailAnalyticsTracking(db);

    console.log('All tests completed successfully');

    // Close the database connection
    if (db) {
      db.close();
    }

    process.exit(0);
  } catch (error) {
    console.error('Tests failed:', error);

    // Close the database connection
    if (db) {
      db.close();
    }

    process.exit(1);
  }
}

// Run the main function
main();
