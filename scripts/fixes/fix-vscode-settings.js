// fix-vscode-settings.js
const fs = require('fs');
const path = require('path');

// Paths
const rootDir = __dirname;
const settingsPath = path.join(rootDir, '.vscode', 'settings.json');

// Read settings
if (fs.existsSync(settingsPath)) {
  try {
    const settingsData = fs.readFileSync(settingsPath, 'utf8');
    const settings = JSON.parse(settingsData);

    // Fix watchOptions.fallbackPolling if it exists
    if (settings.typescript &&
        settings.typescript.tsserver &&
        settings.typescript.tsserver.watchOptions &&
        settings.typescript.tsserver.watchOptions.fallbackPolling === 'dynamicPriority') {

      console.log('Fixing typescript.tsserver.watchOptions.fallbackPolling setting...');
      settings.typescript.tsserver.watchOptions.fallbackPolling = 'dynamicPriorityPolling';
    }

    // Remove deprecated eslint.packageManager if it exists
    if (settings.eslint && settings.eslint.packageManager) {
      console.log('Removing deprecated eslint.packageManager setting...');
      delete settings.eslint.packageManager;
    }

    // Write updated settings
    fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
    console.log('Settings updated successfully.');
  } catch (e) {
    console.error('Error updating settings.json:', e);
  }
} else {
  console.error('settings.json not found at', settingsPath);
}
