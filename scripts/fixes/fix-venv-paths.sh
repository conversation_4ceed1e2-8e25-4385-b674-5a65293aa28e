#!/bin/bash

# Script to fix virtual environment path references from new-backend to backend-python
# Created on May 26, 2025

echo "===== Fixing virtual environment path references ====="
echo "This script will update references from 'new-backend' to 'backend-python'"

# Check if we're in the right directory
if [ ! -d "./backend-python" ]; then
  echo "Error: Must be run from the Mexel project root directory"
  exit 1
fi

# Recreate Python virtual environment in the correct location
echo "1. Creating new virtual environment in backend-python..."
cd backend-python

# Remove old virtual environments if they exist
if [ -d "venv" ]; then
  echo "   Removing old venv directory..."
  rm -rf venv
fi

if [ -d "venv311" ]; then
  echo "   Removing old venv311 directory..."
  rm -rf venv311
fi

# Create new virtual environment
python3 -m venv venv
echo "   New virtual environment created."

# Install dependencies in the new environment
echo "2. Installing dependencies in new virtual environment..."
source venv/bin/activate
pip install -r requirements.txt
echo "   Dependencies installed."

# Go back to the project root
cd ..

# Update Docker containers by restarting
echo "3. Updating Docker containers..."
docker-compose -f docker-compose.dev.yml down
docker-compose -f docker-compose.dev.yml build backend
echo "   Backend Docker image rebuilt."

echo ""
echo "===== Fix completed ====="
echo "You can now restart the development environment with:"
echo "./dev.sh restart"
echo ""
echo "If you encounter any issues, please check the logs with:"
echo "docker-compose -f docker-compose.dev.yml logs -f backend"
