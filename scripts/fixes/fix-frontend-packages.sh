#!/bin/bash

# Enhanced script to fix frontend package management issues

set -e  # Exit on error

MEXEL_ROOT="/Users/<USER>/Desktop/Mexel"
FRONTEND_DIR="$MEXEL_ROOT/frontend"

echo "🔧 Starting enhanced frontend fix..."

# Make sure we're in the project root
cd "$MEXEL_ROOT"

# Backup existing frontend package.json
echo "Backing up existing frontend package.json..."
if [ -f "$FRONTEND_DIR/package.json" ]; then
  cp "$FRONTEND_DIR/package.json" "$FRONTEND_DIR/package.json.bak"
fi

# Temporarily modify the frontend package.json to disable preinstall check
sed -i '' 's/"preinstall": "echo \\"Use yarn instead of npm\\" && exit 1"/"preinstall": "echo \\"Yarn installation in progress\\""/g' "$FRONTEND_DIR/package.json"

# Set up Yarn configuration
echo "Setting up Yarn configuration..."
# Create .yarn directory in frontend if it doesn't exist
mkdir -p "$FRONTEND_DIR/.yarn/releases"

# Copy the yarn binary from root to frontend
cp "$MEXEL_ROOT/.yarn/releases/yarn-3.6.3.cjs" "$FRONTEND_DIR/.yarn/releases/"

# Update frontend .yarnrc.yml to use the correct yarn binary
cat > "$FRONTEND_DIR/.yarnrc.yml" << EOF
nodeLinker: node-modules
enableGlobalCache: true
npmRegistryServer: "https://registry.yarnpkg.com"
npmAlwaysAuth: false

packageExtensions:
  "@mui/base@*": {
    "dependencies": {
      "@mui/types": "*",
      "react": "*"
    }
  }
EOF

# Clean up any nested frontend directories
if [ -d "$FRONTEND_DIR/frontend" ]; then
  echo "Removing nested frontend directories..."
  rm -rf "$FRONTEND_DIR/frontend"
fi

# Make sure we have the right .npmrc in frontend
cat > "$FRONTEND_DIR/.npmrc" << EOF
package-lock=false
engine-strict=true
EOF

# Clean node_modules directories for a fresh install
echo "🧹 Cleaning node_modules directories for a fresh install..."
rm -rf "$MEXEL_ROOT/node_modules"
rm -rf "$FRONTEND_DIR/node_modules"
rm -rf "$MEXEL_ROOT/shared/node_modules"

# Install dependencies in the root project first
echo "📦 Installing root project dependencies..."
cd "$MEXEL_ROOT"
yarn install

# Add missing dependencies
echo "📦 Adding missing dependencies to frontend..."
cd "$FRONTEND_DIR"
yarn add zustand@5.0.5 react-router-dom@6.22.3

# Restore the original preinstall script if needed for future compliance
cd "$MEXEL_ROOT"
if [ -f "$FRONTEND_DIR/package.json.bak" ]; then
  echo "Restoring original package.json..."
  cp "$FRONTEND_DIR/package.json" "$FRONTEND_DIR/package.json.fixed"
  cp "$FRONTEND_DIR/package.json.bak" "$FRONTEND_DIR/package.json"
fi

echo "✅ Fix completed! Try running the frontend with:"
echo "cd $FRONTEND_DIR && yarn start"
