#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Find all TypeScript files in frontend
const files = glob.sync('frontend/src/**/*.{ts,tsx}', { absolute: true });

let totalFixed = 0;
let totalPatterns = 0;

const patterns = [
  // Fix malformed ( as any) => patterns
  { pattern: /\(\s+as\s+any\)\s*=>/g, replacement: '() =>', description: '( as any) => to () =>' },

  // Fix malformed (param as any) in function parameters
  { pattern: /\(\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s+as\s+any\s*\)/g, replacement: '($1)', description: '(param as any) to (param)' },

  // Fix malformed setTimeout/setInterval patterns
  { pattern: /setTimeout\(\(\(\)\s*=>/g, replacement: 'setTimeout(() =>', description: 'setTimeout(() => patterns' },
  { pattern: /setInterval\(\(\(\)\s*=>/g, replacement: 'setInterval(() =>', description: 'setInterval(() => patterns' },

  // Fix malformed state setter patterns
  { pattern: /\(\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s+as\s+any\s*\)\s*=>/g, replacement: '($1) =>', description: 'state setter patterns' },

  // Fix malformed as any in function calls (more comprehensive)
  { pattern: /\(\s*([^)]+?)\s+as\s+any\s*\)/g, replacement: '($1)', description: 'function call parameters' },

  // Fix simple value as any patterns
  { pattern: /\[\]\s*as\s*any\)/g, replacement: '[]', description: 'array as any' },
  { pattern: /\{\}\s*as\s*any\)/g, replacement: '{}', description: 'object as any' },
  { pattern: /null\s*as\s*any\)/g, replacement: 'null', description: 'null as any' },
  { pattern: /undefined\s*as\s*any\)/g, replacement: 'undefined', description: 'undefined as any' },
  { pattern: /true\s*as\s*any\)/g, replacement: 'true', description: 'true as any' },
  { pattern: /false\s*as\s*any\)/g, replacement: 'false', description: 'false as any' },

  // Fix getHours( as any), getDate( as any) patterns
  { pattern: /\.getHours\(\s*as\s*any\)/g, replacement: '.getHours()', description: 'getHours() calls' },
  { pattern: /\.getDate\(\s*as\s*any\)/g, replacement: '.getDate()', description: 'getDate() calls' },
  { pattern: /\.getTime\(\s*as\s*any\)/g, replacement: '.getTime()', description: 'getTime() calls' },

  // Fix filter patterns like (item as any) =>
  { pattern: /\.filter\(\s*\(\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s+as\s+any\s*\)\s*=>/g, replacement: '.filter(($1) =>', description: 'filter callback patterns' },
  { pattern: /\.map\(\s*\(\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s+as\s+any\s*\)\s*=>/g, replacement: '.map(($1) =>', description: 'map callback patterns' },

  // Fix unnecessary as any with simple values in function calls
  { pattern: /\(\s*(['"`][^'"`]*['"`])\s+as\s+any\s*\)/g, replacement: '($1)', description: 'string literals' },
  { pattern: /\(\s*(\d+(?:\.\d+)?)\s+as\s+any\s*\)/g, replacement: '($1)', description: 'number literals' },
];

files.forEach(filePath => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    let fileModified = false;
    let filePatterns = 0;

    patterns.forEach(({ pattern, replacement, description }) => {
      const matches = content.match(pattern);
      if (matches) {
        content = content.replace(pattern, replacement);
        filePatterns += matches.length;
        fileModified = true;
        console.log(`  - Fixed ${matches.length} ${description} patterns`);
      }
    });

    if (fileModified) {
      fs.writeFileSync(filePath, content, 'utf8');
      totalFixed++;
      totalPatterns += filePatterns;
      console.log(`✓ Fixed: ${path.relative(process.cwd(), filePath)} (${filePatterns} patterns)`);
    }

  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
});

console.log(`\n🎉 Summary:`);
console.log(`Fixed ${totalFixed} files`);
console.log(`Fixed ${totalPatterns} malformed 'as any' patterns total`);

// Also create a simple ESLint rule suggestion
const eslintRuleContent = `
// Add this to your .eslintrc.js rules section to prevent future malformed 'as any' patterns:

module.exports = {
  rules: {
    // Prevent malformed 'as any' patterns
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/prefer-as-const': 'error',

    // Custom rule to catch malformed patterns (requires custom implementation)
    'no-malformed-type-assertions': 'off', // Would need custom rule

    // Better alternatives
    '@typescript-eslint/consistent-type-assertions': [
      'error',
      {
        assertionStyle: 'as',
        objectLiteralTypeAssertions: 'never'
      }
    ]
  }
};
`;

fs.writeFileSync('eslint-as-any-rules.js', eslintRuleContent);
console.log('📝 Created eslint-as-any-rules.js with recommended ESLint configuration');
