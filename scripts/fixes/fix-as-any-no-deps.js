#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Recursively find TypeScript files
function findTsFiles(dir, files = []) {
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      // Skip node_modules and other build directories
      if (!['node_modules', 'dist', 'build', '.git', 'coverage'].includes(item)) {
        findTsFiles(fullPath, files);
      }
    } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
      files.push(fullPath);
    }
  }

  return files;
}

const frontendSrc = path.join(__dirname, 'frontend', 'src');
const files = findTsFiles(frontendSrc);

let totalFixed = 0;
let totalPatterns = 0;

const patterns = [
  // Fix malformed ( as any) => patterns
  { pattern: /\(\s+as\s+any\)\s*=>/g, replacement: '() =>', description: '( as any) => to () =>' },

  // Fix malformed (param as any) in function parameters
  { pattern: /\(\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s+as\s+any\s*\)/g, replacement: '($1)', description: '(param as any) to (param)' },

  // Fix malformed setTimeout/setInterval patterns
  { pattern: /setTimeout\(\(\(\)\s*=>/g, replacement: 'setTimeout(() =>', description: 'setTimeout(() => patterns' },
  { pattern: /setInterval\(\(\(\)\s*=>/g, replacement: 'setInterval(() =>', description: 'setInterval(() => patterns' },

  // Fix malformed state setter patterns
  { pattern: /\(\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s+as\s+any\s*\)\s*=>/g, replacement: '($1) =>', description: 'state setter patterns' },

  // Fix getHours( as any), getDate( as any) patterns
  { pattern: /\.getHours\(\s*as\s*any\)/g, replacement: '.getHours()', description: 'getHours() calls' },
  { pattern: /\.getDate\(\s*as\s*any\)/g, replacement: '.getDate()', description: 'getDate() calls' },
  { pattern: /\.getTime\(\s*as\s*any\)/g, replacement: '.getTime()', description: 'getTime() calls' },

  // Fix filter patterns like (item as any) =>
  { pattern: /\.filter\(\s*\(\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s+as\s+any\s*\)\s*=>/g, replacement: '.filter(($1) =>', description: 'filter callback patterns' },
  { pattern: /\.map\(\s*\(\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s+as\s+any\s*\)\s*=>/g, replacement: '.map(($1) =>', description: 'map callback patterns' },

  // Fix malformed as any with simple values
  { pattern: /\[\]\s*as\s*any\)/g, replacement: '[]', description: 'array as any' },
  { pattern: /\{\}\s*as\s*any\)/g, replacement: '{}', description: 'object as any' },
  { pattern: /null\s*as\s*any\)/g, replacement: 'null', description: 'null as any' },
  { pattern: /undefined\s*as\s*any\)/g, replacement: 'undefined', description: 'undefined as any' },
  { pattern: /true\s*as\s*any\)/g, replacement: 'true', description: 'true as any' },
  { pattern: /false\s*as\s*any\)/g, replacement: 'false', description: 'false as any' },

  // Fix string and number literals
  { pattern: /\(\s*(['"`][^'"`]*['"`])\s+as\s+any\s*\)/g, replacement: '($1)', description: 'string literals' },
  { pattern: /\(\s*(\d+(?:\.\d+)?)\s+as\s+any\s*\)/g, replacement: '($1)', description: 'number literals' },
];

console.log(`🔍 Found ${files.length} TypeScript files to process...\n`);

files.forEach(filePath => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    let fileModified = false;
    let filePatterns = 0;

    patterns.forEach(({ pattern, replacement, description }) => {
      const matches = content.match(pattern);
      if (matches) {
        content = content.replace(pattern, replacement);
        filePatterns += matches.length;
        fileModified = true;
        console.log(`  - Fixed ${matches.length} ${description} patterns`);
      }
    });

    if (fileModified) {
      fs.writeFileSync(filePath, content, 'utf8');
      totalFixed++;
      totalPatterns += filePatterns;
      console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)} (${filePatterns} patterns)\n`);
    }

  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log(`\n🎉 Summary:`);
console.log(`📁 Files processed: ${files.length}`);
console.log(`✅ Files fixed: ${totalFixed}`);
console.log(`🔧 Total patterns fixed: ${totalPatterns}`);

if (totalFixed > 0) {
  console.log(`\n✨ All malformed 'as any' patterns have been fixed!`);
  console.log(`💡 Run 'cd frontend && npx tsc --noEmit' to check for remaining TypeScript errors.`);
}
