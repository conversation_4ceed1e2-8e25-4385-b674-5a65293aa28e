// fix-eslint-path.js
const fs = require('fs');
const path = require('path');

// Helper function to find a module in various locations
function findModulePath(moduleName) {
  const possiblePaths = [
    path.join(__dirname, 'node_modules', moduleName),
    path.join(__dirname, 'frontend', 'node_modules', moduleName),
    path.join(__dirname, 'shared', 'node_modules', moduleName)
  ];

  for (const modulePath of possiblePaths) {
    if (fs.existsSync(modulePath)) {
      return modulePath;
    }
  }

  return null;
}

// Create a custom package.json in node_modules/@typescript-eslint/parser
function createParserPackageJson() {
  const parserDir = path.join(__dirname, 'node_modules', '@typescript-eslint', 'parser');

  // Try to find the actual parser module
  const actualParserPath = findModulePath('@typescript-eslint/parser');

  if (!actualParserPath) {
    console.log('Could not find @typescript-eslint/parser module. Skipping.');
    return;
  }

  // Create directories if they don't exist
  if (!fs.existsSync(path.join(__dirname, 'node_modules', '@typescript-eslint'))) {
    fs.mkdirSync(path.join(__dirname, 'node_modules', '@typescript-eslint'), { recursive: true });
  }

  if (!fs.existsSync(parserDir)) {
    fs.mkdirSync(parserDir, { recursive: true });
  }

  // Create package.json that points to the real module
  const packageJson = {
    name: "@typescript-eslint/parser",
    version: "6.20.0",
    main: path.relative(parserDir, path.join(actualParserPath, 'dist', 'index.js')),
    types: path.relative(parserDir, path.join(actualParserPath, 'dist', 'index.d.ts'))
  };

  fs.writeFileSync(
    path.join(parserDir, 'package.json'),
    JSON.stringify(packageJson, null, 2)
  );

  console.log(`Created symlink package.json for @typescript-eslint/parser`);
}

// Create symlinks for key modules
function createSymlinks() {
  const modules = [
    'eslint',
    'typescript',
    '@typescript-eslint/eslint-plugin'
  ];

  for (const moduleName of modules) {
    const modulePath = findModulePath(moduleName);

    if (!modulePath) {
      console.log(`Could not find ${moduleName} module. Skipping.`);
      continue;
    }

    const targetDir = path.dirname(path.join(__dirname, 'node_modules', moduleName));

    // Create parent directory if it doesn't exist
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }

    const targetPath = path.join(__dirname, 'node_modules', moduleName);

    // If the target already exists and is not a symlink, skip
    if (fs.existsSync(targetPath) && !fs.lstatSync(targetPath).isSymbolicLink()) {
      console.log(`${moduleName} already exists and is not a symlink. Skipping.`);
      continue;
    }

    // If the target already exists and is a symlink, remove it
    if (fs.existsSync(targetPath)) {
      fs.unlinkSync(targetPath);
    }

    try {
      // Create symlink
      fs.symlinkSync(modulePath, targetPath, 'junction');
      console.log(`Created symlink for ${moduleName}: ${modulePath} -> ${targetPath}`);
    } catch (error) {
      console.error(`Failed to create symlink for ${moduleName}:`, error);
    }
  }
}

console.log('Starting ESLint path fix...');

// Create special package.json for TypeScript ESLint parser
createParserPackageJson();

// Create symlinks for key modules
createSymlinks();

console.log('ESLint path fix completed.');
