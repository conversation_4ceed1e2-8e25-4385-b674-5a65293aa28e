// fix-module-resolution.js
const fs = require('fs');
const path = require('path');

// This script attempts to fix module resolution issues without reinstalling packages

// Paths
const rootDir = __dirname;
const rootNodeModules = path.join(rootDir, 'node_modules');
const frontendNodeModules = path.join(rootDir, 'frontend', 'node_modules');
const sharedNodeModules = path.join(rootDir, 'shared', 'node_modules');

function createSymlinkIfNeeded(source, target) {
  if (!fs.existsSync(source)) {
    console.log(`Source directory does not exist: ${source}`);
    return false;
  }

  if (fs.existsSync(target)) {
    console.log(`Target already exists: ${target}`);
    return true;
  }

  try {
    // Ensure parent directory exists
    const parentDir = path.dirname(target);
    if (!fs.existsSync(parentDir)) {
      fs.mkdirSync(parentDir, { recursive: true });
    }

    // Create symlink
    fs.symlinkSync(source, target, 'junction');
    console.log(`Created symlink: ${source} -> ${target}`);
    return true;
  } catch (error) {
    console.error(`Failed to create symlink ${source} -> ${target}:`, error.message);
    return false;
  }
}

function ensureTypeRootsExist() {
  const typesDir = path.join(rootDir, 'types');
  if (!fs.existsSync(typesDir)) {
    fs.mkdirSync(typesDir, { recursive: true });
    console.log(`Created types directory: ${typesDir}`);
  }

  // Ensure node.d.ts exists for Node.js types
  const nodeTypesPath = path.join(typesDir, 'node.d.ts');
  if (!fs.existsSync(nodeTypesPath)) {
    const nodeTypes = `
// Custom node.d.ts to ensure Node.js globals are available
/// <reference types="node" />

declare namespace NodeJS {
  interface Process {
    env: ProcessEnv;
  }

  interface ProcessEnv {
    [key: string]: string | undefined;
    NODE_ENV?: 'development' | 'production' | 'test';
  }
}

declare var process: NodeJS.Process;
declare var global: typeof globalThis;
    `;

    fs.writeFileSync(nodeTypesPath, nodeTypes);
    console.log(`Created node.d.ts: ${nodeTypesPath}`);
  }

  // Ensure jest.d.ts exists for Jest types
  const jestTypesPath = path.join(typesDir, 'jest.d.ts');
  if (!fs.existsSync(jestTypesPath)) {
    const jestTypes = `
// Custom jest.d.ts to ensure Jest globals are available
/// <reference types="jest" />

declare namespace jest {
  interface Matchers<R> {
    // Add any custom matchers here if needed
  }
}

declare var expect: jest.Expect;
declare var test: jest.It;
declare var describe: jest.Describe;
declare var beforeEach: jest.Lifecycle;
declare var afterEach: jest.Lifecycle;
declare var beforeAll: jest.Lifecycle;
declare var afterAll: jest.Lifecycle;
    `;

    fs.writeFileSync(jestTypesPath, jestTypes);
    console.log(`Created jest.d.ts: ${jestTypesPath}`);
  }
}

function setupFolderStructure() {
  // Create necessary directories if they don't exist
  const dirs = [
    rootNodeModules,
    path.join(rootNodeModules, '@types'),
    path.join(rootNodeModules, '@typescript-eslint'),
    path.join(rootNodeModules, 'eslint-plugin-react'),
    path.join(rootNodeModules, 'eslint-plugin-react-hooks'),
    path.join(rootNodeModules, 'eslint-plugin-import'),
    path.join(rootNodeModules, 'eslint'),
    path.join(rootNodeModules, 'typescript')
  ];

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      try {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`Created directory: ${dir}`);
      } catch (error) {
        console.error(`Failed to create directory ${dir}:`, error.message);
      }
    }
  });
}

// Main execution
console.log('Starting module resolution fix script...');

// 1. Create custom type definition files
ensureTypeRootsExist();

// 2. Setup folder structure (create empty directories if needed)
setupFolderStructure();

// 3. Create symlinks if modules exist in one workspace but not another
// If you have working node_modules in frontend but not in root
if (fs.existsSync(frontendNodeModules)) {
  const modulesList = ['typescript', 'eslint', '@typescript-eslint', 'eslint-plugin-react', 'eslint-plugin-react-hooks'];
  modulesList.forEach(module => {
    const sourceModule = path.join(frontendNodeModules, module);
    const targetModule = path.join(rootNodeModules, module);
    if (fs.existsSync(sourceModule) && !fs.existsSync(targetModule)) {
      createSymlinkIfNeeded(sourceModule, targetModule);
    }
  });
}

console.log('Module resolution fix script completed.');
