#!/bin/bash

# Script to fix Playwright installation in the Mexel project
# Created on May 26, 2025

echo "===== Fixing Playwright Installation ====="

# Navigate to the frontend directory
cd /Users/<USER>/Desktop/Mexel/frontend

echo "1. Checking current environment..."
echo "Current directory: $(pwd)"

# Clean yarn cache for Playwright-related packages
echo "2. Cleaning yarn cache for Playwright..."
yarn cache clean playwright
yarn cache clean @playwright/test

# Remove node_modules directory to ensure clean installation
echo "3. Removing any existing node_modules directory..."
if [ -d "node_modules" ]; then
  rm -rf node_modules
fi

# Install dependencies with verbose logging
echo "4. Installing all dependencies..."
yarn install --verbose

# Verify Playwright installation
echo "5. Verifying Playwright package installation..."
if [ -d "node_modules/playwright" ] && [ -d "node_modules/@playwright" ]; then
  echo "   Playwright packages found in node_modules directory."
else
  echo "   Playwright packages not found. Attempting manual installation..."
  yarn add --dev playwright@1.52.0 @playwright/test@1.52.0 --verbose
fi

# Install Playwright browsers
echo "6. Installing Playwright browsers..."
yarn exec playwright install --with-deps chromium

echo ""
echo "===== Playwright Fix Completed ====="
echo "You can now run the Playwright tests with:"
echo "cd frontend && yarn test:e2e"
echo ""
