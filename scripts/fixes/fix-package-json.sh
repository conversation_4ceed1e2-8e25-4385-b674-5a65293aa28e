#!/bin/bash

# Script to fix frontend package.json

set -e  # Exit on error

MEXEL_ROOT="/Users/<USER>/Desktop/Mexel"
FRONTEND_DIR="$MEXEL_ROOT/frontend"

echo "🔧 Starting direct package.json fix..."

# Make sure we're in the project root
cd "$MEXEL_ROOT"

# Create a new package.json with all required dependencies
echo "Creating new package.json with all required dependencies..."
cat > "$FRONTEND_DIR/package.json.new" << 'EOL'
{
  "name": "@mexel/frontend",
  "version": "1.0.0",
  "private": true,
  "dependencies": {
    "@emotion/react": "^11.11.1",
    "@emotion/styled": "^11.11.0",
    "@mui/icons-material": "^5.15.15",
    "@mui/lab": "^5.0.0-alpha.169",
    "@mui/material": "^5.15.15",
    "@mui/system": "^5.15.15",
    "@mui/x-date-pickers": "^6.19.7",
    "@types/react-beautiful-dnd": "^13.1.8",
    "@types/react-color": "^3.0.13",
    "axios": "^1.6.7",
    "chart.js": "^4.4.2",
    "date-fns": "^2.16.0",
    "react": "^18.2.0",
    "react-beautiful-dnd": "^13.1.1",
    "react-chartjs-2": "^5.2.0",
    "react-color": "^2.19.3",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.22.3",
    "react-scripts": "5.0.1",
    "recharts": "^2.15.3",
    "shared": "file:../shared",
    "socket.io-client": "^4.7.5",
    "zustand": "^5.0.5"
  },
  "devDependencies": {
    "@types/jest": "^29.5.14",
    "@types/node": "^20.12.12",
    "@types/react": "^18.2.58",
    "@types/react-dom": "^18.2.19",
    "typescript": "^5.4.2"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject",
    "lint": "eslint --ext .js,.jsx,.ts,.tsx src/",
    "tsc": "tsc --noEmit",
    "fix-ts": "node fix-typescript-errors.js && node fix-specific-typescript-errors.js && node fix-type-assertions.js",
    "postinstall": "echo 'Using yarn as the standard package manager'"
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  }
}
EOL

# Replace the old package.json with the new one
mv "$FRONTEND_DIR/package.json.new" "$FRONTEND_DIR/package.json"

echo "✅ Package.json fixed! Now let's run the frontend:"
echo "cd $FRONTEND_DIR && yarn start"
