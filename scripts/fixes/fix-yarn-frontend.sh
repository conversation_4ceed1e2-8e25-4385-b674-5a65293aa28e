#!/bin/bash

set -e  # Exit on error

echo "Fixing Yarn configuration in frontend directory..."

# Change to the project root directory
cd /Users/<USER>/Desktop/Mexel

# Create .yarn directory in frontend if it doesn't exist
mkdir -p frontend/.yarn/releases

# Copy the yarn binary from root to frontend (assuming it's yarn-3.6.3.cjs)
if [ -f ".yarn/releases/yarn-3.6.3.cjs" ]; then
  cp .yarn/releases/yarn-3.6.3.cjs frontend/.yarn/releases/
else
  echo "Warning: yarn-3.6.3.cjs not found. Using yarn set version instead."
  cd frontend && yarn set version 3.6.3 && cd ..
fi

# Update frontend .yarnrc.yml to use Yarn 3.6.3
cat > frontend/.yarnrc.yml << EOF
nodeLinker: node-modules
yarnPath: ../.yarn/releases/yarn-3.6.3.cjs
enableGlobalCache: true
npmRegistryServer: "https://registry.yarnpkg.com"
npmAlwaysAuth: false

packageExtensions:
  # Fix for @mui/base
  "@mui/base@*": {
    "dependencies": {
      "@mui/types": "*",
      "react": "*"
    }
  }
  # Add other package extensions if needed
EOF

# Clean up any nested frontend directories
if [ -d "frontend/frontend" ]; then
  echo "Removing nested frontend directories..."
  rm -rf frontend/frontend
fi

# Make sure we have the right .npmrc in frontend
cat > frontend/.npmrc << EOF
package-lock=false
engine-strict=true
EOF

# Check for missing dependencies
echo "Checking for missing dependencies..."
if ! grep -q "zustand" frontend/package.json; then
  echo "Adding missing zustand dependency..."
  cd frontend
  yarn add zustand
  cd ..
fi

# Clean node_modules directories
echo "Cleaning node_modules directories for a fresh install..."
rm -rf node_modules
rm -rf frontend/node_modules
rm -rf shared/node_modules

# Re-install all dependencies
echo "Reinstalling all dependencies from scratch..."
yarn install --force

echo "Installing frontend dependencies..."
cd frontend
yarn install --force

echo "Done! You should now be able to run 'yarn start' in the frontend directory."
