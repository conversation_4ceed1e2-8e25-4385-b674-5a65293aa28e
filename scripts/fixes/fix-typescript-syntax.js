// Fix TypeScript syntax issues in the components
const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Function to fix the "as any" type cast issues
function fixTypecastSyntax(content) {
  // Fix "as any" syntax issues - replace (prev as any) with (prev: any)
  content = content.replace(/\(([a-zA-Z0-9_]+)\s+as\s+any\)/g, '($1: any)');

  // Fix component declarations like "const Component: = () => {"
  content = content.replace(/const\s+([a-zA-Z0-9_]+):\s+=\s+\(\)\s+=>/g, 'const $1 = () =>');

  // Fix component declarations with props like "const Component(ComponentProps) = ({"
  content = content.replace(/const\s+([a-zA-Z0-9_]+)\(([a-zA-Z0-9_]+)\)\s+=\s+\(\{/g, 'const $1: React.FC<$2> = ({');

  return content;
}

// Function to process a file
function processFile(filePath) {
  try {
    console.log(`Processing ${filePath}...`);
    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixTypecastSyntax(content);

    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`Fixed issues in ${filePath}`);
    } else {
      console.log(`No issues found in ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
}

// Get all TypeScript files in the frontend/src directory
const tsFiles = glob.sync(path.join(__dirname, 'frontend', 'src', '**', '*.{ts,tsx}'));

// Process each file
tsFiles.forEach(processFile);

console.log('Done fixing TypeScript syntax issues.');
