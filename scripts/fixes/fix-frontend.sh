#!/bin/bash

# <PERSON><PERSON>t to fix frontend issues

set -e  # Exit on error

echo "🔧 Fixing frontend issues..."

# Check if we need to change to the project root directory
if [ ! -d "frontend" ]; then
  echo "Changing to project root directory..."
  cd ..
fi

# Backup existing frontend package.json
echo "Backing up existing frontend package.json..."
if [ -f "frontend/package.json" ]; then
  cp frontend/package.json frontend/package.json.bak
fi

# Set up Yarn configuration
echo "Setting up Yarn configuration..."
# Create .yarn directory in frontend if it doesn't exist
mkdir -p frontend/.yarn/releases

# Copy the yarn binary from root to frontend
cp .yarn/releases/yarn-3.6.3.cjs frontend/.yarn/releases/

# Update frontend .yarnrc.yml to use the correct yarn binary
cat > frontend/.yarnrc.yml << EOF
nodeLinker: node-modules
yarnPath: .yarn/releases/yarn-3.6.3.cjs
npmRegistryServer: "https://registry.yarnpkg.com"
npmAlwaysAuth: false

packageExtensions:
  "@mui/base@*": {
    "dependencies": {
      "@mui/types": "*",
      "react": "*"
    }
  }
EOF

# Create a temporary package.json file
cat > package.json.new << EOL
{
  "name": "frontend",
  "version": "0.1.0",
  "private": true,
  "dependencies": {
    "@testing-library/jest-dom": "^5.17.0",
    "@testing-library/react": "^13.4.0",
    "@testing-library/user-event": "^13.5.0",
    "axios": "^1.6.2",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "5.0.1",
    "web-vitals": "^2.1.4"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "devDependencies": {
    "@types/jest": "^29.5.5",
    "@types/node": "^18.18.0",
    "@types/react": "^18.2.23",
    "@types/react-dom": "^18.2.8",
    "nth-check": "^2.1.1",
    "postcss": "^8.4.31",
    "typescript": "~4.9.5"
  },
  "overrides": {
    "nth-check": "^2.1.1",
    "postcss": "^8.4.31"
  }
}
EOL

# Replace the package.json
echo "Replacing package.json..."
mv package.json.new package.json

# Create the public directory if it doesn't exist
echo "Creating public directory if needed..."
mkdir -p frontend/public

# Create a simple test HTML page to verify the API
echo "Creating test HTML page..."
cat > frontend/public/test.html << 'EOL'
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mexel API Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #2c3e50;
    }
    .tender {
      border: 1px solid #ddd;
      padding: 15px;
      margin-bottom: 15px;
      border-radius: 5px;
    }
    .tender h3 {
      margin-top: 0;
      color: #3498db;
    }
    .tender p {
      margin: 5px 0;
    }
    .tender .meta {
      font-size: 0.9em;
      color: #7f8c8d;
    }
    .tender .score {
      font-weight: bold;
      color: #27ae60;
    }
    button {
      background-color: #3498db;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 5px;
      cursor: pointer;
      margin-bottom: 20px;
    }
    button:hover {
      background-color: #2980b9;
    }
    #loading {
      display: none;
      color: #7f8c8d;
    }
  </style>
</head>
<body>
  <h1>Mexel API Test</h1>
  <button id="fetchTenders">Fetch Tenders</button>
  <span id="loading">Loading...</span>
  <div id="tenders"></div>

  <script>
    document.getElementById('fetchTenders').addEventListener('click', async () => {
      const loadingEl = document.getElementById('loading');
      const tendersEl = document.getElementById('tenders');
      
      loadingEl.style.display = 'inline';
      tendersEl.innerHTML = '';
      
      try {
        const response = await fetch('http://localhost:3001/api/tenders');
        const tenders = await response.json();
        
        loadingEl.style.display = 'none';
        
        if (tenders.length === 0) {
          tendersEl.innerHTML = '<p>No tenders found</p>';
          return;
        }
        
        tenders.forEach(tender => {
          const tenderEl = document.createElement('div');
          tenderEl.className = 'tender';
          
          const closingDate = tender.closingDate ? new Date(tender.closingDate).toLocaleDateString() : 'Unknown';
          const publishDate = tender.publishDate ? new Date(tender.publishDate).toLocaleDateString() : 'Unknown';
          
          tenderEl.innerHTML = `
            <h3>${tender.title}</h3>
            <p>${tender.description}</p>
            <p class="meta">Source: ${tender.issuer || tender.source || 'Unknown'}</p>
            <p class="meta">Reference: ${tender.reference || 'N/A'}</p>
            <p class="meta">Published: ${publishDate}</p>
            <p class="meta">Closing: ${closingDate}</p>
            ${tender.confidence ? `<p class="score">Relevance Score: ${Math.round(tender.confidence)}%</p>` : ''}
            <p><a href="${tender.url}" target="_blank">View Tender</a></p>
          `;
          
          tendersEl.appendChild(tenderEl);
        });
      } catch (error) {
        loadingEl.style.display = 'none';
        tendersEl.innerHTML = `<p>Error fetching tenders: ${error.message}</p>`;
        console.error('Error fetching tenders:', error);
      }
    });
  </script>
</body>
</html>
EOL

echo "Frontend files updated successfully!"
echo ""
echo "To start the frontend, run:"
echo "cd frontend && yarn start"
echo ""
echo "To test the API connection, open http://localhost:3000/test.html after starting both backend and frontend"
