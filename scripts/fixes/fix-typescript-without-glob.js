// fix-typescript-without-glob.js
const fs = require('fs');
const path = require('path');

// Function to fix the "as any" type cast issues
function fixTypecastSyntax(content) {
  // Fix "as any" syntax issues - replace (prev as any) with (prev: any)
  content = content.replace(/\(([a-zA-Z0-9_]+)\s+as\s+any\)/g, '($1: any)');

  // Fix component declarations like "const Component: = () => {"
  content = content.replace(/const\s+([a-zA-Z0-9_]+):\s+=\s+\(\)\s+=>/g, 'const $1 = () =>');

  // Fix component declarations with props like "const Component(ComponentProps) = ({"
  content = content.replace(/const\s+([a-zA-Z0-9_]+)\(([a-zA-Z0-9_]+)\)\s+=\s+\(\{/g, 'const $1: React.FC<$2> = ({');

  return content;
}

// Function to process a file
function processFile(filePath) {
  try {
    console.log(`Processing ${filePath}...`);
    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixTypecastSyntax(content);

    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`Fixed issues in ${filePath}`);
    } else {
      console.log(`No issues found in ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
}

// List of critical files to fix
const filesToFix = [
  '/Users/<USER>/Desktop/Mexel/frontend/src/App.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/layout/Navigation.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/layout/ConnectionStatus.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/SEODashboard.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/LinkedInDashboard.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/MinimalDashboard.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/WebSocketTester.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/AgentMonitor.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/ContentDashboard.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/agent/AgentMonitorExample.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/agent/AgentMonitorFixed.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/dashboard/sections/AgentRealTimeStatus.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/lead/LeadProcessingMonitor.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/LinkedInMessageGenerator.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/SEOInsightsDashboard/index.tsx',
  '/Users/<USER>/Desktop/Mexel/frontend/src/components/TenderDashboard.tsx'
];

// Process each file
filesToFix.forEach(file => {
  if (fs.existsSync(file)) {
    processFile(file);
  } else {
    console.warn(`File does not exist: ${file}`);
  }
});

console.log('Done fixing TypeScript syntax issues.');
