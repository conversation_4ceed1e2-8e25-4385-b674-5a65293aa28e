#!/usr/bin/env ts-node

/**
 * Agent system startup script
 * This script starts the agent system
 */

import * as dotenv from 'dotenv';
import { CoordinatorAgent } from '../src/agents/CoordinatorAgent';
import { TenderMonitorAgent } from '../src/agents/TenderMonitorAgent';
import { OutreachEmailAgent } from '../src/agents/OutreachEmailAgent';
import { AnalyticsAgent } from '../src/agents/AnalyticsAgent';
import { Logger } from '../src/utils/Logger';
import { AgentStatus } from '../src/interfaces/IAgent';

// Load environment variables
dotenv.config();

// Configure logger
Logger.configure({
  level: process.env.LOG_LEVEL || 'info',
  format: process.env.LOG_FORMAT || 'simple'
});

const logger = Logger.getInstance('AgentSystem');

// Parse command line arguments
const args = process.argv.slice(2);
const runMode = args[0] || 'all';

// Helper function to initialize an agent
async function initializeAgent(agent: any, name: string): Promise<void> {
  try {
    logger.info(`Initializing ${name} agent`);
    await agent.initialize();
    logger.info(`${name} agent initialized successfully`);
  } catch (error) {
    logger.error(`Failed to initialize ${name} agent`, {
      error: error instanceof Error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : String(error)
    });
    throw error;
  }
}

// Main function
async function main() {
  logger.info('Starting Mexel Agent System');
  logger.info(`Run mode: ${runMode}`);
  
  try {
    // Create agents
    const coordinator = new CoordinatorAgent();
    const tenderMonitor = new TenderMonitorAgent();
    const outreachEmail = new OutreachEmailAgent();
    const analytics = new AnalyticsAgent();
    
    // Initialize coordinator first
    await initializeAgent(coordinator, 'Coordinator');
    
    // Initialize other agents based on run mode
    if (runMode === 'all' || runMode === 'tender') {
      await initializeAgent(tenderMonitor, 'TenderMonitor');
      coordinator.registerAgent(tenderMonitor);
    }
    
    if (runMode === 'all' || runMode === 'email') {
      await initializeAgent(outreachEmail, 'OutreachEmail');
      coordinator.registerAgent(outreachEmail);
    }
    
    if (runMode === 'all' || runMode === 'analytics') {
      await initializeAgent(analytics, 'Analytics');
      coordinator.registerAgent(analytics);
    }
    
    // Start the coordinator
    logger.info('Starting coordinator agent');
    await coordinator.start();
    
    // Execute the coordinator once to start the agent cycle
    logger.info('Executing coordinator agent');
    await coordinator.execute();
    
    // Print agent status
    const agentStatus = coordinator.getAgentStatus();
    logger.info('Agent status:', agentStatus);
    
    // Keep the process running
    logger.info('Agent system started successfully');
    logger.info('Press Ctrl+C to stop');
    
    // Handle process termination
    process.on('SIGINT', async () => {
      logger.info('Shutting down agent system');
      
      try {
        await coordinator.shutdown();
        logger.info('Agent system shut down successfully');
        process.exit(0);
      } catch (error) {
        logger.error('Error shutting down agent system', {
          error: error instanceof Error ? error : new Error(String(error))
        });
        process.exit(1);
      }
    });
  } catch (error) {
    logger.error('Failed to start agent system', {
      error: error instanceof Error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : String(error)
    });
    process.exit(1);
  }
}

// Run main function
main().catch(error => {
  logger.error('Unhandled error', {
    error: error instanceof Error ? error : new Error(String(error))
  });
  process.exit(1);
});
