#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to test the authentication API endpoints
 */

const axios = require('axios');
const chalk = require('chalk');

// Configuration
const API_BASE_URL = 'http://localhost:3001/api';
const AUTH_API_URL = `${API_BASE_URL}/auth`;

// Test credentials
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

// Helper function to log results
function logResult(testName, success, data, error) {
  if (success) {
    console.log(chalk.green(`✓ ${testName}`));
    if (data) {
      console.log(chalk.gray('  Response:'), typeof data === 'object' ? JSON.stringify(data, null, 2) : data);
    }
  } else {
    console.log(chalk.red(`✗ ${testName}`));
    if (error) {
      console.log(chalk.red('  Error:'), error);
    }
  }
  console.log(); // Add a blank line for readability
}

// Test the login endpoint
async function testLogin() {
  try {
    console.log(chalk.blue('Testing POST /api/auth/login'));
    const response = await axios.post(`${AUTH_API_URL}/login`, TEST_CREDENTIALS);
    
    if (response.status === 200 && response.data && response.data.status === 'success') {
      const { token, user } = response.data.data;
      logResult('Login', true, {
        token: `${token.substring(0, 20)}...`,
        user
      });
      return token;
    } else {
      logResult('Login', false, null, 'Invalid response format');
      return null;
    }
  } catch (error) {
    logResult('Login', false, null, error.message);
    return null;
  }
}

// Test the me endpoint
async function testGetCurrentUser(token) {
  try {
    console.log(chalk.blue('Testing GET /api/auth/me'));
    
    if (!token) {
      logResult('Get current user', false, null, 'No token available');
      return;
    }
    
    const response = await axios.get(`${AUTH_API_URL}/me`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    if (response.status === 200 && response.data && response.data.status === 'success') {
      const { user } = response.data.data;
      logResult('Get current user', true, { user });
    } else {
      logResult('Get current user', false, null, 'Invalid response format');
    }
  } catch (error) {
    logResult('Get current user', false, null, error.message);
  }
}

// Test the register endpoint
async function testRegister(token) {
  try {
    console.log(chalk.blue('Testing POST /api/auth/register'));
    
    if (!token) {
      logResult('Register user', false, null, 'No token available');
      return;
    }
    
    // Generate a unique email
    const uniqueEmail = `test-${Date.now()}@mexelenergysustain.com`;
    
    const response = await axios.post(
      `${AUTH_API_URL}/register`,
      {
        email: uniqueEmail,
        name: 'Test User',
        password: 'password123',
        role: 'user'
      },
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );
    
    if (response.status === 201 && response.data && response.data.status === 'success') {
      const { user } = response.data.data;
      logResult('Register user', true, { user });
    } else {
      logResult('Register user', false, null, 'Invalid response format');
    }
  } catch (error) {
    logResult('Register user', false, null, error.message);
  }
}

// Test protected tender endpoints
async function testProtectedTenderEndpoints(token) {
  try {
    console.log(chalk.blue('Testing protected tender endpoints'));
    
    if (!token) {
      logResult('Protected tender endpoints', false, null, 'No token available');
      return;
    }
    
    // Test refresh tenders endpoint
    try {
      const refreshResponse = await axios.post(
        `${API_BASE_URL}/tenders/refresh`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
      
      if (refreshResponse.status === 201 && refreshResponse.data && refreshResponse.data.status === 'success') {
        logResult('Refresh tenders', true, refreshResponse.data.data);
      } else {
        logResult('Refresh tenders', false, null, 'Invalid response format');
      }
    } catch (error) {
      logResult('Refresh tenders', false, null, error.message);
    }
    
    // Test update tender status endpoint
    try {
      // First get a tender to update
      const tendersResponse = await axios.get(`${API_BASE_URL}/tenders?limit=1`);
      
      if (tendersResponse.data && tendersResponse.data.data && tendersResponse.data.data.tenders.length > 0) {
        const tender = tendersResponse.data.data.tenders[0];
        
        const updateResponse = await axios.patch(
          `${API_BASE_URL}/tenders/${tender.id}/status`,
          { status: 'PROCESSING' },
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        );
        
        if (updateResponse.status === 200 && updateResponse.data && updateResponse.data.status === 'success') {
          logResult('Update tender status', true, updateResponse.data.data);
        } else {
          logResult('Update tender status', false, null, 'Invalid response format');
        }
      } else {
        logResult('Update tender status', false, null, 'No tenders available to update');
      }
    } catch (error) {
      logResult('Update tender status', false, null, error.message);
    }
  } catch (error) {
    logResult('Protected tender endpoints', false, null, error.message);
  }
}

// Test unauthorized access
async function testUnauthorizedAccess() {
  try {
    console.log(chalk.blue('Testing unauthorized access'));
    
    // Test refresh tenders endpoint without token
    try {
      await axios.post(`${API_BASE_URL}/tenders/refresh`);
      logResult('Unauthorized refresh tenders', false, null, 'Request should have failed but succeeded');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        logResult('Unauthorized refresh tenders', true, { status: error.response.status });
      } else {
        logResult('Unauthorized refresh tenders', false, null, `Expected 401 status but got ${error.message}`);
      }
    }
    
    // Test update tender status endpoint without token
    try {
      // First get a tender to update
      const tendersResponse = await axios.get(`${API_BASE_URL}/tenders?limit=1`);
      
      if (tendersResponse.data && tendersResponse.data.data && tendersResponse.data.data.tenders.length > 0) {
        const tender = tendersResponse.data.data.tenders[0];
        
        await axios.patch(`${API_BASE_URL}/tenders/${tender.id}/status`, { status: 'PROCESSING' });
        logResult('Unauthorized update tender status', false, null, 'Request should have failed but succeeded');
      } else {
        logResult('Unauthorized update tender status', false, null, 'No tenders available to update');
      }
    } catch (error) {
      if (error.response && error.response.status === 401) {
        logResult('Unauthorized update tender status', true, { status: error.response.status });
      } else {
        logResult('Unauthorized update tender status', false, null, `Expected 401 status but got ${error.message}`);
      }
    }
  } catch (error) {
    logResult('Unauthorized access', false, null, error.message);
  }
}

// Run all tests
async function runTests() {
  try {
    console.log(chalk.yellow('=== Testing Authentication API Endpoints ===\n'));
    
    // Test if the server is running
    try {
      await axios.get(`${API_BASE_URL}/health`);
      console.log(chalk.green('✓ API server is running\n'));
    } catch (error) {
      console.log(chalk.red('✗ API server is not running. Please start the server first.\n'));
      process.exit(1);
    }
    
    // Test unauthorized access first
    await testUnauthorizedAccess();
    
    // Run the login test and get the token
    const token = await testLogin();
    
    // Run the other tests with the token
    await testGetCurrentUser(token);
    await testRegister(token);
    await testProtectedTenderEndpoints(token);
    
    console.log(chalk.yellow('\n=== All tests completed ==='));
  } catch (error) {
    console.error(chalk.red('Error running tests:'), error);
    process.exit(1);
  }
}

// Run the tests
runTests();
