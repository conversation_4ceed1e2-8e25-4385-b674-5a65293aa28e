#!/usr/bin/env node

/**
 * <PERSON>ript to automatically remove unused imports from TypeScript files
 * Run with: node scripts/fix-unused-imports.js
 */

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// Get the ESLint output for the whole frontend
function getEslintOutput() {
  try {
    return execSync(
      "cd frontend && npx eslint src --ext .js,.jsx,.ts,.tsx --format json",
      {
        encoding: "utf-8",
        maxBuffer: 10 * 1024 * 1024, // Increase buffer size to 10MB
      }
    );
  } catch (error) {
    // ESLint will exit with error code 1 if it finds any warnings/errors
    // We still want to process the output
    return error.stdout;
  }
}

// Parse the JSON output
function parseEslintOutput(output) {
  try {
    const results = JSON.parse(output);
    // Map the results by file
    const fileWarningsMap = {};

    results.forEach((result) => {
      const filePath = result.filePath;
      const unused = result.messages
        .filter((msg) => msg.ruleId === "@typescript-eslint/no-unused-vars")
        .map((msg) => msg.message.match(/['`]([^'`]+)['`]/)[1]);

      if (unused.length > 0) {
        fileWarningsMap[filePath] = unused;
      }
    });

    return fileWarningsMap;
  } catch (error) {
    console.error("Error parsing ESLint output:", error);
    return {};
  }
}

// Fix the imports in the file
function fixImportsInFile(filePath, unusedImports) {
  try {
    console.log(`Processing ${filePath}`);
    let content = fs.readFileSync(filePath, "utf-8");

    // Get unique unused imports
    const uniqueUnused = [...new Set(unusedImports)];

    // Remove each unused import
    uniqueUnused.forEach((importName) => {
      // Handle different import patterns
      const patterns = [
        // import X from 'module'
        new RegExp(
          `import\\s+${importName}\\s+from\\s+['"][^'"]+['"];?\\n?`,
          "g"
        ),
        // import { X } from 'module'
        new RegExp(
          `import\\s+\\{[^}]*\\b${importName}\\b[^}]*\\}\\s+from\\s+['"][^'"]+['"];?\\n?`,
          "g"
        ),
        // import { X as Y } from 'module'
        new RegExp(
          `import\\s+\\{[^}]*\\b\\w+\\s+as\\s+${importName}\\b[^}]*\\}\\s+from\\s+['"][^'"]+['"];?\\n?`,
          "g"
        ),
      ];

      for (const pattern of patterns) {
        const matches = content.match(pattern);
        if (matches) {
          for (const match of matches) {
            // If this is a destructured import with multiple items, just remove the specific item
            if (match.includes("{") && match.includes("}")) {
              const importItems = match.match(/\{([^}]+)\}/)[1].split(",");
              const filteredItems = importItems
                .filter(
                  (item) =>
                    !item.trim().match(new RegExp(`^\\s*${importName}\\s*$`)) &&
                    !item
                      .trim()
                      .match(
                        new RegExp(`^\\s*\\w+\\s+as\\s+${importName}\\s*$`)
                      )
                )
                .map((item) => item.trim());

              if (filteredItems.length > 0) {
                // Reconstruct the import statement without the unused import
                const newImport = match.replace(
                  /\{([^}]+)\}/,
                  `{ ${filteredItems.join(", ")} }`
                );
                content = content.replace(match, newImport);
              } else {
                // If no items left, remove the entire import
                content = content.replace(match, "");
              }
            } else {
              // For non-destructured imports, remove the entire line
              content = content.replace(match, "");
            }
          }
        }
      }
    });

    // Write the updated content back to the file
    fs.writeFileSync(filePath, content);
    console.log(`✓ Fixed ${filePath}`);
  } catch (error) {
    console.error(`Error fixing imports in ${filePath}:`, error);
  }
}

// Main function
async function main() {
  try {
    console.log("Running ESLint to identify unused imports...");
    const eslintOutput = getEslintOutput();

    console.log("Parsing ESLint output...");
    const fileWarningsMap = parseEslintOutput(eslintOutput);

    console.log(
      `Found ${Object.keys(fileWarningsMap).length} files with unused imports.`
    );

    // Process each file
    for (const [filePath, unusedImports] of Object.entries(fileWarningsMap)) {
      fixImportsInFile(filePath, unusedImports);
    }

    console.log("Done!");
  } catch (error) {
    console.error("Error:", error);
    process.exit(1);
  }
}

main();
