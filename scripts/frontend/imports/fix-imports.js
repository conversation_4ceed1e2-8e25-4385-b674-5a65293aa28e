#!/usr/bin/env node

/**
 * Consolidated Import Fixer for Mexel Frontend
 * 
 * This script consolidates multiple import fix scripts into a single,
 * configurable utility that can fix import issues, remove unused imports,
 * and resolve import path problems.
 * 
 * Usage:
 *   node scripts/frontend/imports/fix-imports.js [options]
 * 
 * Options:
 *   --all            Run all fixes (default)
 *   --unused         Remove unused imports only
 *   --duplicates     Fix duplicate imports only
 *   --paths          Fix import paths only
 *   --missing        Add missing imports only
 *   --path <path>    Specify custom path (default: frontend/src/**/*.{ts,tsx})
 *   --dry-run        Show what would be fixed without making changes
 *   --verbose        Show detailed output
 *   --help           Show this help message
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const { execSync } = require('child_process');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  all: args.length === 0 || args.includes('--all'),
  unused: args.includes('--unused'),
  duplicates: args.includes('--duplicates'),
  paths: args.includes('--paths'),
  missing: args.includes('--missing'),
  dryRun: args.includes('--dry-run'),
  verbose: args.includes('--verbose'),
  help: args.includes('--help'),
  path: 'frontend/src/**/*.{ts,tsx}'
};

// Check for custom path
const pathIndex = args.indexOf('--path');
if (pathIndex !== -1 && args.length > pathIndex + 1) {
  options.path = args[pathIndex + 1];
}

// Show help
if (options.help) {
  console.log(`
Consolidated Import Fixer for Mexel Frontend

Usage:
  node scripts/frontend/imports/fix-imports.js [options]

Options:
  --all            Run all fixes (default)
  --unused         Remove unused imports only
  --duplicates     Fix duplicate imports only
  --paths          Fix import paths only
  --missing        Add missing imports only
  --path <path>    Specify custom path (default: frontend/src/**/*.{ts,tsx})
  --dry-run        Show what would be fixed without making changes
  --verbose        Show detailed output
  --help           Show this help message

Examples:
  node scripts/frontend/imports/fix-imports.js --dry-run
  node scripts/frontend/imports/fix-imports.js --unused --verbose
  node scripts/frontend/imports/fix-imports.js --path "frontend/src/components/**/*.tsx"

Consolidates these scripts:
  - fix-unused-imports.js
  - fix-duplicate-imports.js
  - fix-import-paths.js
  - fix-import-statements.js
  `);
  process.exit(0);
}

// Statistics tracking
let filesProcessed = 0;
let filesModified = 0;
let fixesApplied = 0;
let categories = {
  unused: 0,
  duplicates: 0,
  paths: 0,
  missing: 0
};

// Utility function for logging
function log(message, isVerbose = false) {
  if (!isVerbose || options.verbose) {
    console.log(message);
  }
}

function logFix(category, message) {
  categories[category]++;
  fixesApplied++;
  if (options.verbose) {
    console.log(`  ✓ ${category}: ${message}`);
  }
}

// Get ESLint output for unused imports
function getUnusedImports() {
  try {
    const output = execSync(
      "cd frontend && npx eslint src --ext .js,.jsx,.ts,.tsx --format json",
      {
        encoding: "utf-8",
        maxBuffer: 10 * 1024 * 1024,
      }
    );
    
    const results = JSON.parse(output);
    const fileWarningsMap = {};

    results.forEach((result) => {
      const filePath = result.filePath;
      const unused = result.messages
        .filter((msg) => msg.ruleId === "@typescript-eslint/no-unused-vars")
        .map((msg) => {
          const match = msg.message.match(/['`]([^'`]+)['`]/);
          return match ? match[1] : null;
        })
        .filter(Boolean);

      if (unused.length > 0) {
        fileWarningsMap[filePath] = unused;
      }
    });

    return fileWarningsMap;
  } catch (error) {
    log("Warning: Could not run ESLint for unused import detection", true);
    return {};
  }
}

// Remove unused imports
function fixUnusedImports(content, filePath) {
  let modified = false;
  
  // Get unused imports from ESLint if available
  const unusedImports = getUnusedImports()[path.resolve(filePath)] || [];
  
  if (unusedImports.length === 0) {
    return { content, modified };
  }

  // Remove each unused import
  unusedImports.forEach((importName) => {
    // Handle different import patterns
    const patterns = [
      // import X from 'module'
      new RegExp(`import\\s+${importName}\\s+from\\s+['"][^'"]+['"];?\\n?`, "g"),
      // import { X } from 'module'
      new RegExp(`import\\s+\\{[^}]*\\b${importName}\\b[^}]*\\}\\s+from\\s+['"][^'"]+['"];?\\n?`, "g"),
      // import { X as Y } from 'module'
      new RegExp(`import\\s+\\{[^}]*\\b\\w+\\s+as\\s+${importName}\\b[^}]*\\}\\s+from\\s+['"][^'"]+['"];?\\n?`, "g"),
    ];

    for (const pattern of patterns) {
      const matches = content.match(pattern);
      if (matches) {
        for (const match of matches) {
          // If this is a destructured import with multiple items, just remove the specific item
          if (match.includes("{") && match.includes("}")) {
            const importItems = match.match(/\{([^}]+)\}/)[1].split(",");
            const filteredItems = importItems
              .filter(
                (item) =>
                  !item.trim().match(new RegExp(`^\\s*${importName}\\s*$`)) &&
                  !item
                    .trim()
                    .match(new RegExp(`^\\s*\\w+\\s+as\\s+${importName}\\s*$`))
              )
              .map((item) => item.trim());

            if (filteredItems.length > 0) {
              // Reconstruct the import statement without the unused import
              const newImport = match.replace(
                /\{([^}]+)\}/,
                `{ ${filteredItems.join(", ")} }`
              );
              content = content.replace(match, newImport);
            } else {
              // If no items left, remove the entire import
              content = content.replace(match, "");
            }
          } else {
            // For non-destructured imports, remove the entire line
            content = content.replace(match, "");
          }
          modified = true;
          logFix('unused', `Removed unused import: ${importName}`);
        }
      }
    }
  });

  return { content, modified };
}

// Fix duplicate imports
function fixDuplicateImports(content, filePath) {
  let modified = false;
  
  const importLines = content.match(/import.*from\s+['"][^'"]+['"];?\n?/g);
  if (!importLines) {
    return { content, modified };
  }

  const moduleImports = {};
  const linesToRemove = [];

  // Group imports by module
  importLines.forEach(importLine => {
    const moduleMatch = importLine.match(/from\s+['"]([^'"]+)['"]/);
    if (moduleMatch) {
      const module = moduleMatch[1];
      if (!moduleImports[module]) {
        moduleImports[module] = [];
      }
      moduleImports[module].push(importLine);
    }
  });

  // Find and merge duplicate imports
  Object.entries(moduleImports).forEach(([module, imports]) => {
    if (imports.length > 1) {
      const allImportItems = [];
      const defaultImports = [];
      
      imports.forEach(importLine => {
        // Extract default imports
        const defaultMatch = importLine.match(/import\s+(\w+)\s+from/);
        if (defaultMatch && !importLine.includes('{')) {
          defaultImports.push(defaultMatch[1]);
        }
        
        // Extract named imports
        const namedMatch = importLine.match(/import\s*\{([^}]+)\}/);
        if (namedMatch) {
          const items = namedMatch[1].split(',').map(item => item.trim());
          allImportItems.push(...items);
        }
      });

      // Create consolidated import
      if (allImportItems.length > 0 || defaultImports.length > 0) {
        const uniqueItems = [...new Set(allImportItems)];
        const uniqueDefaults = [...new Set(defaultImports)];
        
        let consolidatedImport = 'import ';
        if (uniqueDefaults.length > 0) {
          consolidatedImport += uniqueDefaults[0];
          if (uniqueItems.length > 0) {
            consolidatedImport += ', ';
          }
        }
        if (uniqueItems.length > 0) {
          consolidatedImport += `{ ${uniqueItems.join(', ')} }`;
        }
        consolidatedImport += ` from '${module}';\n`;

        // Replace first occurrence with consolidated import
        content = content.replace(imports[0], consolidatedImport);
        
        // Mark other occurrences for removal
        for (let i = 1; i < imports.length; i++) {
          content = content.replace(imports[i], '');
        }
        
        modified = true;
        logFix('duplicates', `Consolidated duplicate imports for ${module}`);
      }
    }
  });

  return { content, modified };
}

// Fix import paths
function fixImportPaths(content, filePath) {
  let modified = false;
  
  // Fix common path issues
  const pathFixes = [
    {
      from: /from\s+['"]\.\/components\/common\/([^'"]+)['"]/g,
      to: "from './common/$1'",
      condition: () => filePath.includes('/components/'),
      name: 'common component path'
    },
    {
      from: /from\s+['"]\.\.\/\.\.\/components\/([^'"]+)['"]/g,
      to: "from '../components/$1'",
      name: 'relative component path'
    },
    {
      from: /from\s+['"]src\/([^'"]+)['"]/g,
      to: "from './$1'",
      condition: () => filePath.includes('/src/'),
      name: 'src absolute path'
    },
    {
      from: /from\s+['"]\.\/\.\.\/([^'"]+)['"]/g,
      to: "from '../$1'",
      name: 'redundant relative path'
    }
  ];

  pathFixes.forEach(fix => {
    if (!fix.condition || fix.condition()) {
      if (fix.from.test(content)) {
        content = content.replace(fix.from, fix.to);
        modified = true;
        logFix('paths', `Fixed ${fix.name}`);
      }
    }
  });

  return { content, modified };
}

// Add missing imports
function fixMissingImports(content, filePath) {
  let modified = false;
  
  // Common missing imports
  const missingChecks = [
    {
      usage: /\bTypography\b/g,
      import: 'Typography',
      module: '@mui/material',
      condition: () => !content.includes('import') || !content.match(/import.*Typography/)
    },
    {
      usage: /\bBox\b/g,
      import: 'Box',
      module: '@mui/material',
      condition: () => !content.includes('import') || !content.match(/import.*Box/)
    },
    {
      usage: /\bButton\b/g,
      import: 'Button',
      module: '@mui/material',
      condition: () => !content.includes('import') || !content.match(/import.*Button/)
    },
    {
      usage: /\bReact\./g,
      import: 'React',
      module: 'react',
      isDefault: true,
      condition: () => !content.match(/import\s+React/)
    },
    {
      usage: /\buseState\b/g,
      import: 'useState',
      module: 'react',
      condition: () => !content.match(/import.*useState/)
    },
    {
      usage: /\buseEffect\b/g,
      import: 'useEffect',
      module: 'react',
      condition: () => !content.match(/import.*useEffect/)
    }
  ];

  missingChecks.forEach(check => {
    if (check.usage.test(content) && check.condition()) {
      // Find existing import for the module
      const existingImport = content.match(new RegExp(`import\\s*(?:.*from\\s*)?['"]${check.module.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g'));
      
      if (existingImport && existingImport[0]) {
        // Add to existing import
        if (check.isDefault) {
          // Handle default import
          if (!existingImport[0].includes('import ' + check.import)) {
            const newImport = existingImport[0].replace('import ', `import ${check.import}, `);
            content = content.replace(existingImport[0], newImport);
            modified = true;
            logFix('missing', `Added default import: ${check.import}`);
          }
        } else {
          // Handle named import
          const namedMatch = existingImport[0].match(/import\s*\{([^}]+)\}/);
          if (namedMatch) {
            const imports = namedMatch[1].split(',').map(s => s.trim());
            if (!imports.includes(check.import)) {
              imports.push(check.import);
              const newImport = existingImport[0].replace(/\{[^}]+\}/, `{ ${imports.join(', ')} }`);
              content = content.replace(existingImport[0], newImport);
              modified = true;
              logFix('missing', `Added named import: ${check.import}`);
            }
          }
        }
      } else {
        // Add new import line
        const firstImport = content.match(/import[^;]+;/);
        if (firstImport) {
          const newImportLine = check.isDefault 
            ? `import ${check.import} from '${check.module}';`
            : `import { ${check.import} } from '${check.module}';`;
          content = content.replace(firstImport[0], `${firstImport[0]}\n${newImportLine}`);
          modified = true;
          logFix('missing', `Added new import: ${check.import} from ${check.module}`);
        }
      }
    }
  });

  return { content, modified };
}

// Process a single file
function processFile(filePath) {
  log(`Processing: ${filePath}`, true);
  filesProcessed++;

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let fileModified = false;
    
    // Apply fixes based on selected options
    if (options.all || options.unused) {
      const result = fixUnusedImports(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.duplicates) {
      const result = fixDuplicateImports(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.paths) {
      const result = fixImportPaths(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.missing) {
      const result = fixMissingImports(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    // Write file if modified and not in dry-run mode
    if (fileModified) {
      filesModified++;
      if (!options.dryRun) {
        fs.writeFileSync(filePath, content, 'utf8');
        log(`✓ Fixed imports in: ${filePath}`);
      } else {
        log(`✓ Would fix imports in: ${filePath}`);
      }
    } else {
      log(`  No import issues found in: ${filePath}`, true);
    }

  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

// Main execution
function main() {
  console.log("🔗 Mexel Import Fixer");
  console.log("=====================");
  console.log(`Mode: ${options.dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE (making changes)'}`);
  console.log(`Path: ${options.path}`);
  console.log("Fixes enabled:");
  console.log(`  Unused imports: ${options.all || options.unused ? '✓' : '✗'}`);
  console.log(`  Duplicate imports: ${options.all || options.duplicates ? '✓' : '✗'}`);
  console.log(`  Import paths: ${options.all || options.paths ? '✓' : '✗'}`);
  console.log(`  Missing imports: ${options.all || options.missing ? '✓' : '✗'}`);
  console.log("");

  // Get files to process
  const files = glob.sync(options.path, { 
    cwd: process.cwd(),
    absolute: true
  });

  if (files.length === 0) {
    console.log("No files found matching the specified path pattern.");
    return;
  }

  log(`Found ${files.length} files to process\n`);

  // Process each file
  files.forEach(processFile);

  // Show summary
  console.log("\n📊 Summary");
  console.log("===========");
  console.log(`Files processed: ${filesProcessed}`);
  console.log(`Files modified: ${filesModified}`);
  console.log(`Total fixes applied: ${fixesApplied}`);
  console.log("\nFixes by category:");
  Object.entries(categories).forEach(([category, count]) => {
    if (count > 0) {
      console.log(`  ${category}: ${count}`);
    }
  });

  if (options.dryRun) {
    console.log("\n💡 This was a dry run. Run without --dry-run to apply changes.");
  } else {
    console.log("\n✅ Import fixing completed!");
    console.log("\nNext steps:");
    console.log("1. Run 'cd frontend && yarn tsc --noEmit' to check for any remaining import errors");
    console.log("2. Run 'cd frontend && yarn lint' to verify import correctness");
    console.log("3. Test your application to ensure all imports work correctly");
  }
}

// Run the script
main();