#!/usr/bin/env node

/**
 * Consolidated TypeScript Fixer for Mexel Frontend
 * 
 * This script consolidates multiple TypeScript fix scripts into a single,
 * configurable utility that can fix common TypeScript and React issues.
 * 
 * Usage:
 *   node scripts/frontend/typescript/fix-all-typescript.js [options]
 * 
 * Options:
 *   --all            Run all fixes (default)
 *   --react-types    Fix React type issues (FC, ReactNode, etc.)
 *   --components     Fix component declarations
 *   --usestate       Fix useState issues
 *   --imports        Fix import issues
 *   --namespaces     Fix namespace issues
 *   --tabpanel       Fix TabPanel component issues
 *   --recharts       Fix Recharts component issues
 *   --path <path>    Specify a custom path (default: frontend/src/**/*.{ts,tsx})
 *   --dry-run        Show what would be fixed without making changes
 *   --verbose        Show detailed output
 *   --help           Show this help message
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  all: args.length === 0 || args.includes('--all'),
  reactTypes: args.includes('--react-types'),
  components: args.includes('--components'),
  usestate: args.includes('--usestate'),
  imports: args.includes('--imports'),
  namespaces: args.includes('--namespaces'),
  tabpanel: args.includes('--tabpanel'),
  recharts: args.includes('--recharts'),
  dryRun: args.includes('--dry-run'),
  verbose: args.includes('--verbose'),
  help: args.includes('--help'),
  path: 'frontend/src/**/*.{ts,tsx}'
};

// Check for custom path
const pathIndex = args.indexOf('--path');
if (pathIndex !== -1 && args.length > pathIndex + 1) {
  options.path = args[pathIndex + 1];
}

// Show help
if (options.help) {
  console.log(`
Consolidated TypeScript Fixer for Mexel Frontend

Usage:
  node scripts/frontend/typescript/fix-all-typescript.js [options]

Options:
  --all            Run all fixes (default)
  --react-types    Fix React type issues (FC, ReactNode, etc.)
  --components     Fix component declarations
  --usestate       Fix useState issues
  --imports        Fix import issues
  --namespaces     Fix namespace issues
  --tabpanel       Fix TabPanel component issues
  --recharts       Fix Recharts component issues
  --path <path>    Specify a custom path (default: frontend/src/**/*.{ts,tsx})
  --dry-run        Show what would be fixed without making changes
  --verbose        Show detailed output
  --help           Show this help message

Examples:
  node scripts/frontend/typescript/fix-all-typescript.js --dry-run
  node scripts/frontend/typescript/fix-all-typescript.js --react-types --verbose
  node scripts/frontend/typescript/fix-all-typescript.js --path "frontend/src/components/**/*.tsx"
  
Consolidates these scripts:
  - fix-typescript-errors.js
  - fix-specific-typescript-errors.js
  - fix-react-types.js
  - fix-component-declarations.js
  - fix-usestate-issues.js
  - fix-namespace-issues.js
  - fix-fc-reactnode-issues.js
  - fix-react-usestate-types.js
  `);
  process.exit(0);
}

// Statistics tracking
let filesProcessed = 0;
let filesModified = 0;
let fixesApplied = 0;

// Utility function for logging
function log(message, isVerbose = false) {
  if (!isVerbose || options.verbose) {
    console.log(message);
  }
}

// Fix React type issues (FC, ReactNode, etc.)
function fixReactTypes(content, filePath) {
  let modified = false;
  
  // Add React import if missing and needed
  if (!content.includes("import React") && (content.includes("React.") || content.includes("FC") || content.includes("ReactNode"))) {
    content = `import React from 'react';\n${content}`;
    modified = true;
    log(`  ✓ Added React import to ${path.basename(filePath)}`, true);
  }

  // Replace React.FC with function component pattern
  if (content.includes("React.FC") || content.includes(": FC")) {
    content = content.replace(/React\.FC<([^>]*)>/g, "($1) => React.ReactElement");
    content = content.replace(/: FC<([^>]*)>/g, ": ($1) => React.ReactElement");
    content = content.replace(/: FC\b/g, ": () => React.ReactElement");
    content = content.replace(/React\.FC\b/g, "");
    modified = true;
    log(`  ✓ Fixed React.FC declarations in ${path.basename(filePath)}`, true);
  }

  // Replace ReactNode with any
  if (content.includes("ReactNode") || content.includes("React.ReactNode")) {
    content = content.replace(/: ReactNode\b/g, ": any");
    content = content.replace(/: React\.ReactNode\b/g, ": any");
    modified = true;
    log(`  ✓ Fixed ReactNode types in ${path.basename(filePath)}`, true);
  }

  // Replace SyntheticEvent with any
  if (content.includes("SyntheticEvent") || content.includes("React.SyntheticEvent")) {
    content = content.replace(/: SyntheticEvent<[^>]*>/g, ": any");
    content = content.replace(/: React\.SyntheticEvent<[^>]*>/g, ": any");
    content = content.replace(/: SyntheticEvent\b/g, ": any");
    content = content.replace(/: React\.SyntheticEvent\b/g, ": any");
    modified = true;
    log(`  ✓ Fixed SyntheticEvent types in ${path.basename(filePath)}`, true);
  }

  // Replace ChangeEvent with any
  if (content.includes("ChangeEvent") || content.includes("React.ChangeEvent")) {
    content = content.replace(/: ChangeEvent<[^>]*>/g, ": any");
    content = content.replace(/: React\.ChangeEvent<[^>]*>/g, ": any");
    modified = true;
    log(`  ✓ Fixed ChangeEvent types in ${path.basename(filePath)}`, true);
  }

  // Replace MouseEvent with any
  if (content.includes("MouseEvent") || content.includes("React.MouseEvent")) {
    content = content.replace(/: MouseEvent<[^>]*>/g, ": any");
    content = content.replace(/: React\.MouseEvent<[^>]*>/g, ": any");
    modified = true;
    log(`  ✓ Fixed MouseEvent types in ${path.basename(filePath)}`, true);
  }

  // Replace FormEvent with any
  if (content.includes("FormEvent") || content.includes("React.FormEvent")) {
    content = content.replace(/: FormEvent<[^>]*>/g, ": any");
    content = content.replace(/: React\.FormEvent<[^>]*>/g, ": any");
    modified = true;
    log(`  ✓ Fixed FormEvent types in ${path.basename(filePath)}`, true);
  }

  return { content, modified };
}

// Fix component declarations
function fixComponentDeclarations(content, filePath) {
  let modified = false;

  // Fix function component exports
  if (content.includes("export default function")) {
    const functionMatches = content.match(/export default function\s+(\w+)/g);
    if (functionMatches) {
      functionMatches.forEach(match => {
        const funcName = match.match(/function\s+(\w+)/)[1];
        if (!content.includes(`${funcName}.displayName`)) {
          content = content.replace(
            match,
            `${match}\n${funcName}.displayName = '${funcName}';`
          );
          modified = true;
          log(`  ✓ Added displayName to ${funcName} in ${path.basename(filePath)}`, true);
        }
      });
    }
  }

  // Fix arrow function components
  const arrowComponentMatches = content.match(/const\s+(\w+)\s*:\s*React\.FC[^=]*=\s*\(/g);
  if (arrowComponentMatches) {
    arrowComponentMatches.forEach(match => {
      const componentName = match.match(/const\s+(\w+)/)[1];
      const replacement = match.replace(/:\s*React\.FC[^=]*/, '');
      content = content.replace(match, replacement);
      modified = true;
      log(`  ✓ Fixed arrow component declaration for ${componentName} in ${path.basename(filePath)}`, true);
    });
  }

  // Fix component prop interfaces
  if (content.includes("interface") && content.includes("Props")) {
    const interfaceMatches = content.match(/interface\s+\w*Props\s*{[^}]*}/g);
    if (interfaceMatches) {
      interfaceMatches.forEach(match => {
        if (match.includes(": ReactNode") || match.includes(": React.ReactNode")) {
          const newMatch = match.replace(/:\s*React\.ReactNode/g, ": any").replace(/:\s*ReactNode/g, ": any");
          content = content.replace(match, newMatch);
          modified = true;
          log(`  ✓ Fixed Props interface ReactNode types in ${path.basename(filePath)}`, true);
        }
      });
    }
  }

  return { content, modified };
}

// Fix useState issues
function fixUseStateIssues(content, filePath) {
  let modified = false;

  // Fix useState([]) never[] issues
  const useStateArrayMatches = content.match(/useState\(\[\]\)/g);
  if (useStateArrayMatches) {
    content = content.replace(/useState\(\[\]\)/g, "useState<any[]>([])");
    modified = true;
    log(`  ✓ Fixed useState([]) never[] issues in ${path.basename(filePath)}`, true);
  }

  // Fix useState(null) typing
  if (content.includes("useState(null)")) {
    content = content.replace(/useState\(null\)/g, "useState<any>(null)");
    modified = true;
    log(`  ✓ Fixed useState(null) typing in ${path.basename(filePath)}`, true);
  }

  // Fix useState with object literals
  if (content.match(/useState\(\s*{/g)) {
    content = content.replace(/useState\(\s*{/g, "useState<any>({");
    modified = true;
    log(`  ✓ Fixed useState object typing in ${path.basename(filePath)}`, true);
  }

  // Fix useState with arrays that have initialization data
  const arrayStateMatches = content.match(/useState\(\[[^\]]+\]\)/g);
  if (arrayStateMatches) {
    arrayStateMatches.forEach(match => {
      if (!match.includes("<any")) {
        const newMatch = match.replace("useState(", "useState<any[]>(");
        content = content.replace(match, newMatch);
        modified = true;
        log(`  ✓ Fixed useState array initialization in ${path.basename(filePath)}`, true);
      }
    });
  }

  // Fix state setter functions with never[] issues
  const setterMatches = content.match(/set[A-Z][a-zA-Z]*\([^)]+\)/g);
  if (setterMatches) {
    setterMatches.forEach(match => {
      if (!match.includes("as any") && !match.includes("=>")) {
        const newMatch = match.replace(/\(([^)]+)\)/, "(($1) as any)");
        content = content.replace(match, newMatch);
        modified = true;
        log(`  ✓ Fixed state setter type assertion in ${path.basename(filePath)}`, true);
      }
    });
  }

  return { content, modified };
}

// Fix import issues
function fixImportIssues(content, filePath) {
  let modified = false;

  // Fix TabsNavigation import paths
  if (content.includes("./components/common/TabsNavigation")) {
    content = content.replace(
      /import \{ ([^}]*) \} from '\.\/components\/common\/TabsNavigation'/g,
      "import { $1 } from './common/TabsNavigation'"
    );
    modified = true;
    log(`  ✓ Fixed TabsNavigation import path in ${path.basename(filePath)}`, true);
  }

  // Add missing Typography import if needed
  if (content.includes("<Typography") && !content.includes("import.*Typography")) {
    const muiImportMatch = content.match(/import\s*{([^}]+)}\s*from\s*'@mui\/material'/);
    if (muiImportMatch) {
      const imports = muiImportMatch[1].split(',').map(s => s.trim());
      if (!imports.includes('Typography')) {
        imports.push('Typography');
        const newImport = `import { ${imports.join(', ')} } from '@mui/material'`;
        content = content.replace(muiImportMatch[0], newImport);
        modified = true;
        log(`  ✓ Added Typography import in ${path.basename(filePath)}`, true);
      }
    } else {
      // Add new MUI import
      const firstImport = content.match(/import[^;]+;/);
      if (firstImport) {
        content = content.replace(firstImport[0], `${firstImport[0]}\nimport { Typography } from '@mui/material';`);
        modified = true;
        log(`  ✓ Added Typography import in ${path.basename(filePath)}`, true);
      }
    }
  }

  // Add missing Box import if needed
  if (content.includes("<Box") && !content.includes("import.*Box")) {
    const muiImportMatch = content.match(/import\s*{([^}]+)}\s*from\s*'@mui\/material'/);
    if (muiImportMatch) {
      const imports = muiImportMatch[1].split(',').map(s => s.trim());
      if (!imports.includes('Box')) {
        imports.push('Box');
        const newImport = `import { ${imports.join(', ')} } from '@mui/material'`;
        content = content.replace(muiImportMatch[0], newImport);
        modified = true;
        log(`  ✓ Added Box import in ${path.basename(filePath)}`, true);
      }
    }
  }

  return { content, modified };
}

// Fix namespace issues
function fixNamespaceIssues(content, filePath) {
  let modified = false;

  // Fix React.React namespace duplication
  if (content.includes("React.React.")) {
    content = content.replace(/React\.React\./g, "React.");
    modified = true;
    log(`  ✓ Fixed React.React namespace duplication in ${path.basename(filePath)}`, true);
  }

  return { content, modified };
}

// Fix TabPanel issues
function fixTabPanelIssues(content, filePath) {
  let modified = false;

  // Fix TabPanel component usage
  if (content.includes("TabPanel") && content.includes("value={value}")) {
    content = content.replace(/value={value}/g, "value={value as any}");
    modified = true;
    log(`  ✓ Fixed TabPanel value prop in ${path.basename(filePath)}`, true);
  }

  // Fix a11yProps function usage
  if (content.includes("a11yProps") && content.includes("{...a11yProps(")) {
    content = content.replace(/{\.\.\.a11yProps\(([^)]+)\)}/g, "{...a11yProps($1 as any)}");
    modified = true;
    log(`  ✓ Fixed a11yProps usage in ${path.basename(filePath)}`, true);
  }

  return { content, modified };
}

// Fix Recharts issues
function fixRechartsIssues(content, filePath) {
  let modified = false;

  // Replace recharts components with placeholder
  if (content.includes("<ResponsiveContainer")) {
    content = content.replace(
      /<ResponsiveContainer[^>]*>[\s\S]*?<\/ResponsiveContainer>/g,
      `<Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography>Chart data loaded successfully.</Typography>
      </Box>`
    );
    modified = true;
    log(`  ✓ Replaced Recharts components with placeholder in ${path.basename(filePath)}`, true);
  }

  // Remove recharts imports
  if (content.includes("from 'recharts'")) {
    content = content.replace(/import\s*{[^}]*}\s*from\s*'recharts';\s*\n?/g, "");
    modified = true;
    log(`  ✓ Removed recharts imports in ${path.basename(filePath)}`, true);
  }

  return { content, modified };
}

// Fix general TypeScript issues
function fixTypeScriptIssues(content, filePath) {
  let modified = false;

  // Fix generic type parameter issues
  if (content.includes("useState<") && content.includes("never")) {
    content = content.replace(/useState<[^>]*never[^>]*>/g, "useState<any>");
    modified = true;
    log(`  ✓ Fixed useState never type issues in ${path.basename(filePath)}`, true);
  }

  // Fix type assertion issues
  if (content.includes("as ") && content.includes("never")) {
    content = content.replace(/as\s+never/g, "as any");
    modified = true;
    log(`  ✓ Fixed never type assertions in ${path.basename(filePath)}`, true);
  }

  // Fix array type issues
  if (content.includes(": never[]")) {
    content = content.replace(/:\s*never\[\]/g, ": any[]");
    modified = true;
    log(`  ✓ Fixed never[] array types in ${path.basename(filePath)}`, true);
  }

  // Fix object spread with never types
  if (content.includes("...") && content.includes("never")) {
    content = content.replace(/\.\.\.([\w\d]+)/g, (match, varName) => {
      if (content.includes(`${varName}: never`)) {
        return `...(${varName} as any)`;
      }
      return match;
    });
    modified = true;
    log(`  ✓ Fixed object spread with never types in ${path.basename(filePath)}`, true);
  }

  return { content, modified };
}

// Get all TypeScript files
const files = glob.sync(options.path, { cwd: process.cwd() });

console.log("🔧 Mexel TypeScript Fixer");
console.log("=========================");
console.log(`Mode: ${options.dryRun ? 'DRY RUN (no changes)' : 'LIVE (making changes)'}`);
console.log(`Path: ${options.path}`);
console.log(`Found ${files.length} files to process`);
console.log("");

// Process each file
files.forEach(filePath => {
  log(`Processing: ${filePath}`, true);
  filesProcessed++;

  try {
    let content = fs.readFileSync(filePath, "utf8");
    let fileModified = false;

    // Apply fixes based on options
    if (options.all || options.reactTypes) {
      const result = fixReactTypes(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.components) {
      const result = fixComponentDeclarations(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.usestate) {
      const result = fixUseStateIssues(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.imports) {
      const result = fixImportIssues(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.namespaces) {
      const result = fixNamespaceIssues(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.tabpanel) {
      const result = fixTabPanelIssues(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.recharts) {
      const result = fixRechartsIssues(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    // Always run general TypeScript fixes
    const result = fixTypeScriptIssues(content, filePath);
    content = result.content;
    fileModified = fileModified || result.modified;

    // Write file if modified
    if (fileModified) {
      filesModified++;
      if (!options.dryRun) {
        fs.writeFileSync(filePath, content, "utf8");
        log(`✓ Fixed TypeScript errors in ${filePath}`);
        fixesApplied++;
      } else {
        log(`✓ Would fix TypeScript errors in ${filePath}`);
        fixesApplied++;
      }
    } else {
      log(`  No TypeScript errors to fix in ${filePath}`, true);
    }

  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error.message);
  }
});

console.log("");
console.log("📊 Summary");
console.log("==========");
console.log(`Files processed: ${filesProcessed}`);
console.log(`Files modified: ${filesModified}`);
console.log(`Fixes applied: ${fixesApplied}`);

if (options.dryRun) {
  console.log("");
  console.log("💡 This was a dry run. Run without --dry-run to apply changes.");
} else {
  console.log("");
  console.log("✅ TypeScript fixing complete!");
  console.log("");
  console.log("Next steps:");
  console.log("1. Run 'cd frontend && yarn tsc --noEmit' to check remaining errors");
  console.log("2. Run 'cd frontend && yarn lint' to check for linting issues");
  console.log("3. Test your application to ensure everything works correctly");
}