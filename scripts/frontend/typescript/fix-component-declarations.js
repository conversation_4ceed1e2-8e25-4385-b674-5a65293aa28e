#!/usr/bin/env node

/**
 * Component Declarations Fixer for Mexel Frontend
 * 
 * This script fixes React component declarations including:
 * - Adding displayName for components
 * - Fixing component prop interfaces
 * - Improving component return types
 * - Standardizing component export patterns
 * 
 * Usage:
 *   node scripts/frontend/typescript/fix-component-declarations.js [options]
 * 
 * Options:
 *   --displayname    Fix displayName issues only
 *   --props          Fix prop interfaces only
 *   --exports        Fix component exports only
 *   --all            Run all fixes (default)
 *   --path <path>    Specify a custom path (default: frontend/src/**/*.{ts,tsx})
 *   --dry-run        Show what would be fixed without making changes
 *   --verbose        Show detailed output
 *   --help           Show this help message
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  all: args.length === 0 || args.includes('--all'),
  displayname: args.includes('--displayname'),
  props: args.includes('--props'),
  exports: args.includes('--exports'),
  dryRun: args.includes('--dry-run'),
  verbose: args.includes('--verbose'),
  help: args.includes('--help'),
  path: 'frontend/src/**/*.{ts,tsx}'
};

// Check for custom path
const pathIndex = args.indexOf('--path');
if (pathIndex !== -1 && args.length > pathIndex + 1) {
  options.path = args[pathIndex + 1];
}

// Statistics tracking
let filesProcessed = 0;
let filesModified = 0;
let fixesApplied = 0;
let categories = {
  displayname: 0,
  props: 0,
  exports: 0
};

// Utility function for logging
function log(message, isVerbose = false) {
  if (!isVerbose || options.verbose) {
    console.log(message);
  }
}

function logFix(category, message) {
  categories[category]++;
  fixesApplied++;
  if (options.verbose) {
    console.log(`  ✓ ${category}: ${message}`);
  }
}

// Show help
if (options.help) {
  console.log(`
Component Declarations Fixer for Mexel Frontend

Usage:
  node scripts/frontend/typescript/fix-component-declarations.js [options]

Options:
  --displayname    Fix displayName issues only
  --props          Fix prop interfaces only
  --exports        Fix component exports only
  --all            Run all fixes (default)
  --path <path>    Specify a custom path (default: frontend/src/**/*.{ts,tsx})
  --dry-run        Show what would be fixed without making changes
  --verbose        Show detailed output
  --help           Show this help message

Examples:
  node scripts/frontend/typescript/fix-component-declarations.js --dry-run
  node scripts/frontend/typescript/fix-component-declarations.js --displayname --verbose
  node scripts/frontend/typescript/fix-component-declarations.js --path "frontend/src/components/**/*.tsx"
  `);
  process.exit(0);
}

// Fix displayName issues
function fixDisplayNameIssues(content, filePath) {
  let modified = false;
  
  // Fix named function components
  const namedComponentMatches = content.match(/export\s+(?:default\s+)?function\s+([A-Z]\w+)/g);
  if (namedComponentMatches) {
    namedComponentMatches.forEach(match => {
      const funcName = match.match(/function\s+([A-Z]\w+)/)[1];
      if (!content.includes(`${funcName}.displayName`) && !content.includes(`${funcName}["displayName"]`)) {
        // Find function end (the closing } of the function body)
        const funcStartIndex = content.indexOf(match);
        if (funcStartIndex > -1) {
          // Simple heuristic to find function end - find matching closing }
          let openBraces = 0;
          let funcEndIndex = -1;
          let inQuote = false;
          let quoteChar = '';
          
          for (let i = content.indexOf('{', funcStartIndex); i < content.length; i++) {
            const char = content[i];
            
            // Skip quoted sections
            if ((char === '"' || char === "'") && (i === 0 || content[i-1] !== '\\')) {
              if (inQuote && char === quoteChar) {
                inQuote = false;
              } else if (!inQuote) {
                inQuote = true;
                quoteChar = char;
              }
              continue;
            }
            
            if (inQuote) continue;
            
            if (char === '{') {
              openBraces++;
            } else if (char === '}') {
              openBraces--;
              if (openBraces === 0) {
                funcEndIndex = i;
                break;
              }
            }
          }
          
          if (funcEndIndex > -1) {
            // Add displayName after function
            const displayNameLine = `\n\n${funcName}.displayName = '${funcName}';\n`;
            content = content.substring(0, funcEndIndex + 1) + displayNameLine + content.substring(funcEndIndex + 1);
            modified = true;
            logFix('displayname', `Added displayName to ${funcName}`);
          }
        }
      }
    });
  }
  
  // Fix arrow function components (const X = () => {...})
  const arrowComponentMatches = content.match(/export\s+(?:default\s+)?const\s+([A-Z]\w+)\s*=\s*(?:\(|\s*\w+\s*=>\s*)/g);
  if (arrowComponentMatches) {
    arrowComponentMatches.forEach(match => {
      const compName = match.match(/const\s+([A-Z]\w+)/)[1];
      if (!content.includes(`${compName}.displayName`) && !content.includes(`${compName}["displayName"]`)) {
        // Find the line after the component (after the closing } or ; or } followed by ;)
        const compStartIndex = content.indexOf(match);
        if (compStartIndex > -1) {
          // Look for either export default X; or just the end of the component
          const exportDefaultMatch = new RegExp(`export\\s+default\\s+${compName};`, 'g');
          let insertIndex = -1;
          
          if (exportDefaultMatch.test(content)) {
            // Insert before the export default statement
            insertIndex = content.search(exportDefaultMatch);
          } else {
            // Find component end
            let openBraces = 0;
            let inQuote = false;
            let quoteChar = '';
            
            for (let i = content.indexOf('=>', compStartIndex); i < content.length; i++) {
              const char = content[i];
              
              // Skip quoted sections
              if ((char === '"' || char === "'") && (i === 0 || content[i-1] !== '\\')) {
                if (inQuote && char === quoteChar) {
                  inQuote = false;
                } else if (!inQuote) {
                  inQuote = true;
                  quoteChar = char;
                }
                continue;
              }
              
              if (inQuote) continue;
              
              if (char === '{') {
                openBraces++;
              } else if (char === '}') {
                openBraces--;
                if (openBraces === 0) {
                  // Found the end of the component
                  insertIndex = i + 1;
                  // Skip past any semicolon
                  if (content[insertIndex] === ';') {
                    insertIndex++;
                  }
                  break;
                }
              } else if (char === ';' && openBraces === 0) {
                // For inline arrow functions: const X = () => <div>...</div>;
                insertIndex = i + 1;
                break;
              }
            }
          }
          
          if (insertIndex > -1) {
            // Add displayName after component
            const displayNameLine = `\n\n${compName}.displayName = '${compName}';\n`;
            content = content.substring(0, insertIndex) + displayNameLine + content.substring(insertIndex);
            modified = true;
            logFix('displayname', `Added displayName to ${compName}`);
          }
        }
      }
    });
  }
  
  return { content, modified };
}

// Fix prop interface issues
function fixPropInterfaceIssues(content, filePath) {
  let modified = false;
  
  // Fix Props interfaces with ReactNode
  const propsInterfaceMatches = content.match(/interface\s+([A-Z]\w*Props)\s*\{[^}]*\}/g);
  if (propsInterfaceMatches) {
    propsInterfaceMatches.forEach(match => {
      const interfaceName = match.match(/interface\s+([A-Z]\w*Props)/)[1];
      
      // Fix children prop in interface
      if (match.includes('children') && (match.includes('ReactNode') || match.includes('React.ReactNode'))) {
        const newMatch = match.replace(/(children\s*:)(\s*)(?:React\.)?ReactNode(\s*;?)(\s*\/\/[^\n]*)?/g, 
          '$1$2any$3$4');
        
        if (newMatch !== match) {
          content = content.replace(match, newMatch);
          modified = true;
          logFix('props', `Fixed children prop type in ${interfaceName}`);
        }
      }
      
      // Fix missing optional marker for non-required props
      if (match.includes(':') && !match.includes('?:')) {
        const newMatch = match.replace(/(\w+)\s*:\s*(?!Required)/g, '$1?: ');
        if (newMatch !== match) {
          content = content.replace(match, newMatch);
          modified = true;
          logFix('props', `Made props optional in ${interfaceName}`);
        }
      }
    });
  }
  
  // Fix inline prop types
  if (content.includes('type Props = {') || content.includes('type Props =')) {
    // Convert type Props = X to interface Props extends X
    const typePropsMatches = content.match(/type\s+Props\s*=\s*([^{;]+);/g);
    if (typePropsMatches) {
      typePropsMatches.forEach(match => {
        const typeValue = match.match(/type\s+Props\s*=\s*([^{;]+);/)[1].trim();
        if (typeValue && !typeValue.startsWith('{')) {
          const newInterface = `interface Props extends ${typeValue} {}`;
          content = content.replace(match, newInterface);
          modified = true;
          logFix('props', `Converted type Props = ${typeValue} to interface`);
        }
      });
    }
  }
  
  return { content, modified };
}

// Fix component export issues
function fixExportIssues(content, filePath) {
  let modified = false;
  
  // Fix missing default exports for main component in file
  const componentName = path.basename(filePath).replace(/\.[jt]sx?$/, '');
  const componentNamePattern = new RegExp(`(function|const)\\s+${componentName}\\b`, 'g');
  
  if (componentNamePattern.test(content) && !content.includes(`export default ${componentName}`)) {
    // Check if the component is already exported with a different pattern
    if (!content.includes(`export default function ${componentName}`) && 
        !content.includes(`export default const ${componentName}`) &&
        !content.includes(`export { ${componentName} as default }`)) {
      
      // Add export default at the end of the file
      content = content.trim() + `\n\nexport default ${componentName};\n`;
      modified = true;
      logFix('exports', `Added missing default export for ${componentName}`);
    }
  }
  
  // Fix React.FC with renamed exports
  const renamedExportMatches = content.match(/export\s*{\s*(\w+)\s+as\s+(\w+)\s*}/g);
  if (renamedExportMatches) {
    renamedExportMatches.forEach(match => {
      const [_, origName, exportName] = match.match(/export\s*{\s*(\w+)\s+as\s+(\w+)\s*}/);
      
      // Look for displayName on the original component
      if (content.includes(`${origName}.displayName`) && !content.includes(`${exportName}.displayName`)) {
        // Add displayName for the exported name
        const displayNameMatch = content.match(new RegExp(`${origName}\\.displayName\\s*=\\s*["']([^"']+)["']`));
        if (displayNameMatch) {
          const displayNameValue = displayNameMatch[1];
          const newDisplayName = `${exportName}.displayName = '${displayNameValue}';\n`;
          
          // Add after the export
          content = content.replace(match, `${match}\n${newDisplayName}`);
          modified = true;
          logFix('exports', `Added displayName for renamed export ${exportName}`);
        }
      }
    });
  }
  
  // Fix React.FC type annotations in exports
  if (content.includes('React.FC') || content.includes(': FC')) {
    // Replace React.FC in export statements
    const fcExportMatches = content.match(/export\s+const\s+\w+\s*:\s*(?:React\.)?FC(?:<[^>]*>)?\s*=/g);
    if (fcExportMatches) {
      fcExportMatches.forEach(match => {
        const newExport = match.replace(/\s*:\s*(?:React\.)?FC(?:<[^>]*>)?\s*=/, ' =');
        content = content.replace(match, newExport);
        modified = true;
        logFix('exports', `Removed FC type from export`);
      });
    }
  }
  
  return { content, modified };
}

// Get all TypeScript files
const files = glob.sync(options.path, { cwd: process.cwd(), absolute: true });

console.log("🔧 Component Declarations Fixer");
console.log("=============================");
console.log(`Mode: ${options.dryRun ? 'DRY RUN (no changes)' : 'LIVE (making changes)'}`);
console.log(`Path: ${options.path}`);
console.log(`Found ${files.length} files to process`);
console.log("");

// Process each file
files.forEach(filePath => {
  log(`Processing: ${filePath}`, true);
  filesProcessed++;

  try {
    let content = fs.readFileSync(filePath, "utf8");
    let fileModified = false;

    // Apply fixes based on options
    if (options.all || options.displayname) {
      const result = fixDisplayNameIssues(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.props) {
      const result = fixPropInterfaceIssues(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.exports) {
      const result = fixExportIssues(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    // Write file if modified
    if (fileModified) {
      filesModified++;
      if (!options.dryRun) {
        fs.writeFileSync(filePath, content, "utf8");
        log(`✓ Fixed component declarations in ${filePath}`);
      } else {
        log(`✓ Would fix component declarations in ${filePath}`);
      }
    } else {
      log(`  No component declaration issues to fix in ${filePath}`, true);
    }

  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error.message);
  }
});

console.log("");
console.log("📊 Summary");
console.log("==========");
console.log(`Files processed: ${filesProcessed}`);
console.log(`Files modified: ${filesModified}`);
console.log(`Fixes applied: ${fixesApplied}`);
console.log("\nFixes by category:");
Object.entries(categories).forEach(([category, count]) => {
  if (count > 0) {
    console.log(`  ${category}: ${count}`);
  }
});

if (options.dryRun) {
  console.log("");
  console.log("💡 This was a dry run. Run without --dry-run to apply changes.");
} else {
  console.log("");
  console.log("✅ Component declarations fixing complete!");
  console.log("");
  console.log("Next steps:");
  console.log("1. Run 'cd frontend && yarn tsc --noEmit' to check remaining errors");
  console.log("2. Run 'cd frontend && yarn lint' to check for linting issues");
  console.log("3. Test your components to ensure they render correctly");
}