#!/usr/bin/env node

/**
 * React Types Fixer for Mexel Frontend
 * 
 * This script fixes React-specific type issues including:
 * - FC and React.FC type usage
 * - ReactNode type issues
 * - React event types (SyntheticEvent, ChangeEvent, etc.)
 * - React namespace issues
 * 
 * Usage:
 *   node scripts/frontend/typescript/fix-react-types.js [options]
 * 
 * Options:
 *   --fc             Fix FC/React.FC issues only
 *   --event-types    Fix event types only
 *   --reactnode      Fix ReactNode issues only
 *   --namespace      Fix namespace issues only
 *   --all            Run all fixes (default)
 *   --path <path>    Specify a custom path (default: frontend/src/**/*.{ts,tsx})
 *   --dry-run        Show what would be fixed without making changes
 *   --verbose        Show detailed output
 *   --help           Show this help message
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  all: args.length === 0 || args.includes('--all'),
  fc: args.includes('--fc'),
  eventTypes: args.includes('--event-types'),
  reactnode: args.includes('--reactnode'),
  namespace: args.includes('--namespace'),
  dryRun: args.includes('--dry-run'),
  verbose: args.includes('--verbose'),
  help: args.includes('--help'),
  path: 'frontend/src/**/*.{ts,tsx}'
};

// Check for custom path
const pathIndex = args.indexOf('--path');
if (pathIndex !== -1 && args.length > pathIndex + 1) {
  options.path = args[pathIndex + 1];
}

// Statistics tracking
let filesProcessed = 0;
let filesModified = 0;
let fixesApplied = 0;
let categories = {
  fc: 0,
  eventTypes: 0,
  reactnode: 0,
  namespace: 0
};

// Utility function for logging
function log(message, isVerbose = false) {
  if (!isVerbose || options.verbose) {
    console.log(message);
  }
}

function logFix(category, message) {
  categories[category]++;
  fixesApplied++;
  if (options.verbose) {
    console.log(`  ✓ ${category}: ${message}`);
  }
}

// Show help
if (options.help) {
  console.log(`
React Types Fixer for Mexel Frontend

Usage:
  node scripts/frontend/typescript/fix-react-types.js [options]

Options:
  --fc             Fix FC/React.FC issues only
  --event-types    Fix event types only
  --reactnode      Fix ReactNode issues only
  --namespace      Fix namespace issues only
  --all            Run all fixes (default)
  --path <path>    Specify a custom path (default: frontend/src/**/*.{ts,tsx})
  --dry-run        Show what would be fixed without making changes
  --verbose        Show detailed output
  --help           Show this help message

Examples:
  node scripts/frontend/typescript/fix-react-types.js --dry-run
  node scripts/frontend/typescript/fix-react-types.js --fc --verbose
  node scripts/frontend/typescript/fix-react-types.js --path "frontend/src/components/**/*.tsx"
  `);
  process.exit(0);
}

// Fix FC / React.FC issues
function fixFCIssues(content, filePath) {
  let modified = false;
  
  // Add React import if missing and needed
  if (!content.includes("import React") && (content.includes("React.") || content.includes("FC<") || content.includes(": FC"))) {
    content = `import React from 'react';\n${content}`;
    modified = true;
    logFix('fc', 'Added React import');
  }

  // Replace React.FC with function component pattern
  if (content.includes("React.FC") || content.includes(": FC")) {
    content = content.replace(/React\.FC<([^>]*)>/g, "($1) => React.ReactElement");
    content = content.replace(/: FC<([^>]*)>/g, ": ($1) => React.ReactElement");
    content = content.replace(/: FC\b/g, ": () => React.ReactElement");
    content = content.replace(/React\.FC\b/g, "");
    
    // Fix exported components with FC
    content = content.replace(/export\s+const\s+(\w+)\s*:\s*(?:React\.)?FC(?:<[^>]*>)?\s*=/g, 
      "export const $1 =");
    
    modified = true;
    logFix('fc', 'Fixed React.FC declarations');
  }

  // Fix component interfaces using FC
  if (content.includes("Props") && (content.includes("FC<") || content.includes("React.FC<"))) {
    content = content.replace(/interface\s+(\w+Props)\s*\{\s*children\s*:\s*React\.ReactNode[^}]*\}/g,
      "interface $1 { children?: React.ReactNode }");
    modified = true;
    logFix('fc', 'Fixed component Props interface');
  }

  return { content, modified };
}

// Fix React event type issues
function fixEventTypeIssues(content, filePath) {
  let modified = false;
  
  // Replace common event types with any
  const eventTypeReplacements = [
    { from: /: SyntheticEvent<[^>]*>/g, to: ": any", name: "SyntheticEvent" },
    { from: /: React\.SyntheticEvent<[^>]*>/g, to: ": any", name: "React.SyntheticEvent" },
    { from: /: SyntheticEvent(?![<])/g, to: ": any", name: "SyntheticEvent (no generic)" },
    { from: /: React\.SyntheticEvent(?![<])/g, to: ": any", name: "React.SyntheticEvent (no generic)" },
    
    { from: /: ChangeEvent<[^>]*>/g, to: ": any", name: "ChangeEvent" },
    { from: /: React\.ChangeEvent<[^>]*>/g, to: ": any", name: "React.ChangeEvent" },
    { from: /: ChangeEvent(?![<])/g, to: ": any", name: "ChangeEvent (no generic)" },
    { from: /: React\.ChangeEvent(?![<])/g, to: ": any", name: "React.ChangeEvent (no generic)" },
    
    { from: /: MouseEvent<[^>]*>/g, to: ": any", name: "MouseEvent" },
    { from: /: React\.MouseEvent<[^>]*>/g, to: ": any", name: "React.MouseEvent" },
    { from: /: MouseEvent(?![<])/g, to: ": any", name: "MouseEvent (no generic)" },
    { from: /: React\.MouseEvent(?![<])/g, to: ": any", name: "React.MouseEvent (no generic)" },
    
    { from: /: FormEvent<[^>]*>/g, to: ": any", name: "FormEvent" },
    { from: /: React\.FormEvent<[^>]*>/g, to: ": any", name: "React.FormEvent" },
    { from: /: FormEvent(?![<])/g, to: ": any", name: "FormEvent (no generic)" },
    { from: /: React\.FormEvent(?![<])/g, to: ": any", name: "React.FormEvent (no generic)" },
    
    { from: /: KeyboardEvent<[^>]*>/g, to: ": any", name: "KeyboardEvent" },
    { from: /: React\.KeyboardEvent<[^>]*>/g, to: ": any", name: "React.KeyboardEvent" },
    { from: /: KeyboardEvent(?![<])/g, to: ": any", name: "KeyboardEvent (no generic)" },
    { from: /: React\.KeyboardEvent(?![<])/g, to: ": any", name: "React.KeyboardEvent (no generic)" },
  ];

  eventTypeReplacements.forEach(replacement => {
    if (replacement.from.test(content)) {
      content = content.replace(replacement.from, replacement.to);
      modified = true;
      logFix('eventTypes', `Fixed ${replacement.name} type`);
    }
  });
  
  // Fix event handlers
  const eventHandlerPatterns = [
    { regex: /onChange\s*=\s*\{([^=>{}]+)\}/g, handler: 'onChange' },
    { regex: /onClick\s*=\s*\{([^=>{}]+)\}/g, handler: 'onClick' },
    { regex: /onSubmit\s*=\s*\{([^=>{}]+)\}/g, handler: 'onSubmit' },
    { regex: /onBlur\s*=\s*\{([^=>{}]+)\}/g, handler: 'onBlur' },
    { regex: /onFocus\s*=\s*\{([^=>{}]+)\}/g, handler: 'onFocus' },
    { regex: /onKeyDown\s*=\s*\{([^=>{}]+)\}/g, handler: 'onKeyDown' },
    { regex: /onKeyUp\s*=\s*\{([^=>{}]+)\}/g, handler: 'onKeyUp' },
  ];
  
  eventHandlerPatterns.forEach(pattern => {
    const matches = content.match(pattern.regex);
    if (matches) {
      // Check if the handler has typing issues
      matches.forEach(match => {
        const handlerName = match.match(/\{([^}]+)\}/)[1].trim();
        // Look for handler definition that might have event type issues
        const handlerDefRegex = new RegExp(`(const|function)\\s+${handlerName}\\s*=?\\s*\\(([^)]*)\\)`, 'g');
        const handlerDefMatches = handlerDefRegex.exec(content);
        if (handlerDefMatches && (handlerDefMatches[2].includes('Event') || handlerDefMatches[2].includes(':'))) {
          const newHandler = handlerDefMatches[0].replace(/\(([^:)]+):\s*[^)]+\)/, '($1: any)');
          content = content.replace(handlerDefMatches[0], newHandler);
          modified = true;
          logFix('eventTypes', `Fixed ${pattern.handler} handler parameter type`);
        }
      });
    }
  });

  return { content, modified };
}

// Fix ReactNode type issues
function fixReactNodeIssues(content, filePath) {
  let modified = false;
  
  // Replace ReactNode with any
  if (content.includes("ReactNode") || content.includes("React.ReactNode")) {
    content = content.replace(/: ReactNode(?!\w)/g, ": any");
    content = content.replace(/: React\.ReactNode(?!\w)/g, ": any");
    
    // Special handling for children prop in interfaces
    content = content.replace(/(children\s*:)(\s*)(?:React\.)?ReactNode(\s*;?)(\s*\/\/[^\n]*)?/g, 
      '$1$2any$3$4');
    
    // Special handling for return type annotations
    content = content.replace(/\):\s*(?:React\.)?ReactNode(?!\w)/g, '): any');
    
    modified = true;
    logFix('reactnode', 'Fixed ReactNode types');
  }
  
  // Handle component prop interfaces with children
  if (content.includes("Props") && content.includes("children")) {
    // Fix required children to be optional and properly typed
    content = content.replace(/children\s*:\s*(?:React\.)?ReactNode/g, 'children?: any');
    modified = true;
    logFix('reactnode', 'Fixed children prop in interfaces');
  }

  return { content, modified };
}

// Fix React namespace issues
function fixNamespaceIssues(content, filePath) {
  let modified = false;
  
  // Fix React.React namespace duplication
  if (content.includes("React.React.")) {
    content = content.replace(/React\.React\./g, "React.");
    modified = true;
    logFix('namespace', 'Fixed React.React namespace duplication');
  }
  
  // Fix import statements for React
  if (content.includes("import * as React from 'react'")) {
    content = content.replace(/import \* as React from 'react'/g, "import React from 'react'");
    modified = true;
    logFix('namespace', 'Fixed React import statement');
  }
  
  // Ensure React import exists if namespace is used
  if (content.match(/React\.\w+/) && !content.includes("import React")) {
    content = `import React from 'react';\n${content}`;
    modified = true;
    logFix('namespace', 'Added missing React import');
  }
  
  // Fix JSX namespace issues
  if (content.includes("JSX.")) {
    // Make sure JSX is properly imported/accessible if needed
    if (!content.includes("import React") && content.includes("JSX.Element")) {
      content = `import React from 'react';\n${content}`;
      modified = true;
      logFix('namespace', 'Added React import for JSX namespace');
    }
  }

  return { content, modified };
}

// Get all TypeScript files
const files = glob.sync(options.path, { cwd: process.cwd(), absolute: true });

console.log("🔧 React Types Fixer");
console.log("====================");
console.log(`Mode: ${options.dryRun ? 'DRY RUN (no changes)' : 'LIVE (making changes)'}`);
console.log(`Path: ${options.path}`);
console.log(`Found ${files.length} files to process`);
console.log("");

// Process each file
files.forEach(filePath => {
  log(`Processing: ${filePath}`, true);
  filesProcessed++;

  try {
    let content = fs.readFileSync(filePath, "utf8");
    let fileModified = false;

    // Apply fixes based on options
    if (options.all || options.fc) {
      const result = fixFCIssues(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.eventTypes) {
      const result = fixEventTypeIssues(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.reactnode) {
      const result = fixReactNodeIssues(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    if (options.all || options.namespace) {
      const result = fixNamespaceIssues(content, filePath);
      content = result.content;
      fileModified = fileModified || result.modified;
    }

    // Write file if modified
    if (fileModified) {
      filesModified++;
      if (!options.dryRun) {
        fs.writeFileSync(filePath, content, "utf8");
        log(`✓ Fixed React types in ${filePath}`);
      } else {
        log(`✓ Would fix React types in ${filePath}`);
      }
    } else {
      log(`  No React type issues to fix in ${filePath}`, true);
    }

  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error.message);
  }
});

console.log("");
console.log("📊 Summary");
console.log("==========");
console.log(`Files processed: ${filesProcessed}`);
console.log(`Files modified: ${filesModified}`);
console.log(`Fixes applied: ${fixesApplied}`);
console.log("\nFixes by category:");
Object.entries(categories).forEach(([category, count]) => {
  if (count > 0) {
    console.log(`  ${category}: ${count}`);
  }
});

if (options.dryRun) {
  console.log("");
  console.log("💡 This was a dry run. Run without --dry-run to apply changes.");
} else {
  console.log("");
  console.log("✅ React types fixing complete!");
  console.log("");
  console.log("Next steps:");
  console.log("1. Run 'cd frontend && yarn tsc --noEmit' to check remaining errors");
  console.log("2. Run 'cd frontend && yarn lint' to check for linting issues");
  console.log("3. Test your components to ensure they render correctly");
}