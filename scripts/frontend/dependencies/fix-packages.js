#!/usr/bin/env node

/**
 * Package Dependencies Fixer for Mexel Frontend
 * 
 * This script fixes common dependency issues in package.json files,
 * resolves version conflicts, and ensures consistent dependency configuration.
 * 
 * Usage:
 *   node scripts/frontend/dependencies/fix-packages.js [options]
 * 
 * Options:
 *   --all            Run all fixes (default)
 *   --versions       Fix version conflicts only
 *   --types         Add missing type definitions only
 *   --resolutions   Add/update resolutions only
 *   --overrides     Add/update overrides only
 *   --package <path> Specify package.json path (default: frontend/package.json)
 *   --dry-run       Show what would be fixed without making changes
 *   --verbose       Show detailed output
 *   --help          Show this help message
 */

const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  all: args.length === 0 || args.includes('--all'),
  versions: args.includes('--versions'),
  types: args.includes('--types'),
  resolutions: args.includes('--resolutions'),
  overrides: args.includes('--overrides'),
  dryRun: args.includes('--dry-run'),
  verbose: args.includes('--verbose'),
  help: args.includes('--help'),
  package: 'frontend/package.json'
};

// Check for custom package path
const packageIndex = args.indexOf('--package');
if (packageIndex !== -1 && args.length > packageIndex + 1) {
  options.package = args[packageIndex + 1];
}

// Statistics tracking
let fixesApplied = 0;
let categories = {
  versions: 0,
  types: 0,
  resolutions: 0,
  overrides: 0
};

// Utility function for logging
function log(message, isVerbose = false) {
  if (!isVerbose || options.verbose) {
    console.log(message);
  }
}

function logFix(category, message) {
  categories[category]++;
  fixesApplied++;
  if (options.verbose) {
    console.log(`  ✓ ${category}: ${message}`);
  }
}

// Show help
if (options.help) {
  console.log(`
Package Dependencies Fixer for Mexel Frontend

Usage:
  node scripts/frontend/dependencies/fix-packages.js [options]

Options:
  --all            Run all fixes (default)
  --versions       Fix version conflicts only
  --types         Add missing type definitions only
  --resolutions   Add/update resolutions only
  --overrides     Add/update overrides only
  --package <path> Specify package.json path (default: frontend/package.json)
  --dry-run       Show what would be fixed without making changes
  --verbose       Show detailed output
  --help          Show this help message

Examples:
  node scripts/frontend/dependencies/fix-packages.js --dry-run
  node scripts/frontend/dependencies/fix-packages.js --versions --verbose
  node scripts/frontend/dependencies/fix-packages.js --package "otherdir/package.json"
  `);
  process.exit(0);
}

// Version requirements for key packages
const REQUIRED_VERSIONS = {
  "typescript": "~4.9.5",
  "react": "^18.2.0",
  "react-dom": "^18.2.0",
  "@types/react": "^18.2.0",
  "@types/react-dom": "^18.2.0",
  "@mui/material": "^5.14.0",
  "@emotion/react": "^11.11.0",
  "@emotion/styled": "^11.11.0",
  "recharts": "^2.7.0",
  "socket.io-client": "^4.7.0"
};

// Required type definitions for packages
const TYPE_DEPENDENCIES = {
  "react": "@types/react",
  "react-dom": "@types/react-dom",
  "socket.io-client": "@types/socket.io-client",
  "recharts": "@types/recharts",
  "jest": "@types/jest",
  "node": "@types/node"
};

// Required resolutions for security
const REQUIRED_RESOLUTIONS = {
  "nth-check": "^2.1.1",
  "postcss": "^8.4.31",
  "semver": "^7.5.2"
};

// Fix version conflicts
function fixVersions(packageJson) {
  let modified = false;

  // Ensure dependencies object exists
  packageJson.dependencies = packageJson.dependencies || {};
  packageJson.devDependencies = packageJson.devDependencies || {};

  // Check and fix versions
  Object.entries(REQUIRED_VERSIONS).forEach(([pkg, version]) => {
    if (packageJson.dependencies[pkg] && packageJson.dependencies[pkg] !== version) {
      packageJson.dependencies[pkg] = version;
      modified = true;
      logFix('versions', `Updated ${pkg} to ${version}`);
    }
    if (packageJson.devDependencies[pkg] && packageJson.devDependencies[pkg] !== version) {
      packageJson.devDependencies[pkg] = version;
      modified = true;
      logFix('versions', `Updated ${pkg} to ${version} in devDependencies`);
    }
  });

  return modified;
}

// Add missing type definitions
function fixTypes(packageJson) {
  let modified = false;

  // Ensure devDependencies object exists
  packageJson.devDependencies = packageJson.devDependencies || {};

  // Check all dependencies for missing type definitions
  const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
  Object.entries(TYPE_DEPENDENCIES).forEach(([pkg, types]) => {
    if (allDeps[pkg] && !allDeps[types]) {
      const version = REQUIRED_VERSIONS[types] || '^' + allDeps[pkg].replace(/[\^~]/, '');
      packageJson.devDependencies[types] = version;
      modified = true;
      logFix('types', `Added ${types}@${version}`);
    }
  });

  return modified;
}

// Add/update resolutions
function fixResolutions(packageJson) {
  let modified = false;

  // Ensure resolutions object exists
  packageJson.resolutions = packageJson.resolutions || {};

  // Update resolutions
  Object.entries(REQUIRED_RESOLUTIONS).forEach(([pkg, version]) => {
    if (!packageJson.resolutions[pkg] || packageJson.resolutions[pkg] !== version) {
      packageJson.resolutions[pkg] = version;
      modified = true;
      logFix('resolutions', `Added/updated resolution for ${pkg} to ${version}`);
    }
  });

  return modified;
}

// Add/update overrides
function fixOverrides(packageJson) {
  let modified = false;

  // Ensure overrides object exists
  packageJson.overrides = packageJson.overrides || {};

  // Copy resolutions to overrides for wider compatibility
  Object.entries(REQUIRED_RESOLUTIONS).forEach(([pkg, version]) => {
    if (!packageJson.overrides[pkg] || packageJson.overrides[pkg] !== version) {
      packageJson.overrides[pkg] = version;
      modified = true;
      logFix('overrides', `Added/updated override for ${pkg} to ${version}`);
    }
  });

  // Add specific overrides for known issues
  const specificOverrides = {
    "@mui/base": {
      "dependencies": {
        "@mui/types": "*",
        "react": "*"
      }
    }
  };

  Object.entries(specificOverrides).forEach(([pkg, config]) => {
    if (!packageJson.overrides[pkg] || JSON.stringify(packageJson.overrides[pkg]) !== JSON.stringify(config)) {
      packageJson.overrides[pkg] = config;
      modified = true;
      logFix('overrides', `Added/updated specific override for ${pkg}`);
    }
  });

  return modified;
}

// Main execution
function main() {
  console.log("📦 Mexel Package Dependencies Fixer");
  console.log("==================================");
  console.log(`Mode: ${options.dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE (making changes)'}`);
  console.log(`Package: ${options.package}`);
  console.log("Fixes enabled:");
  console.log(`  Version conflicts: ${options.all || options.versions ? '✓' : '✗'}`);
  console.log(`  Type definitions: ${options.all || options.types ? '✓' : '✗'}`);
  console.log(`  Resolutions: ${options.all || options.resolutions ? '✓' : '✗'}`);
  console.log(`  Overrides: ${options.all || options.overrides ? '✓' : '✗'}`);
  console.log("");

  try {
    // Read package.json
    const packageJsonPath = path.resolve(options.package);
    if (!fs.existsSync(packageJsonPath)) {
      console.error(`Error: Package file not found at ${packageJsonPath}`);
      process.exit(1);
    }

    let packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    let modified = false;

    // Apply fixes based on options
    if (options.all || options.versions) {
      modified = fixVersions(packageJson) || modified;
    }

    if (options.all || options.types) {
      modified = fixTypes(packageJson) || modified;
    }

    if (options.all || options.resolutions) {
      modified = fixResolutions(packageJson) || modified;
    }

    if (options.all || options.overrides) {
      modified = fixOverrides(packageJson) || modified;
    }

    // Write changes if modified
    if (modified) {
      if (!options.dryRun) {
        // Create backup
        fs.writeFileSync(`${packageJsonPath}.bak`, fs.readFileSync(packageJsonPath));
        // Write updated package.json
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
        log("✓ Updated package.json and created backup (.bak)");
      } else {
        log("✓ Would update package.json (dry run)");
      }
    } else {
      log("No changes needed in package.json");
    }

    // Show summary
    console.log("\n📊 Summary");
    console.log("===========");
    console.log(`Total fixes applied: ${fixesApplied}`);
    console.log("\nFixes by category:");
    Object.entries(categories).forEach(([category, count]) => {
      if (count > 0) {
        console.log(`  ${category}: ${count}`);
      }
    });

    if (options.dryRun) {
      console.log("\n💡 This was a dry run. Run without --dry-run to apply changes.");
    } else if (modified) {
      console.log("\n✅ Package fixes completed!");
      console.log("\nNext steps:");
      console.log("1. Review changes in package.json");
      console.log("2. Run 'yarn install' to update dependencies");
      console.log("3. Test your application with the updated dependencies");
    }

  } catch (error) {
    console.error("Error:", error.message);
    process.exit(1);
  }
}

// Run the script
main();