#!/usr/bin/env node

/**
 * TypeScript Module Fixer
 * 
 * This script identifies empty TypeScript files that are causing
 * "cannot be compiled under '--isolatedModules'" errors and fixes them
 * by adding an empty export statement, making them proper modules.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const PROJECT_ROOT = path.resolve(__dirname, '..');
const SRC_DIR = path.join(PROJECT_ROOT, 'src');
const EXTENSIONS = ['.ts', '.tsx'];
const DRY_RUN = process.argv.includes('--dry-run');
const VERBOSE = process.argv.includes('--verbose');

// Track statistics
let filesScanned = 0;
let filesFixed = 0;
let errors = 0;

/**
 * Check if a file needs fixing (is empty or doesn't have imports/exports)
 */
function needsFix(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // If file is completely empty
    if (!content.trim()) {
      return true;
    }
    
    // If file has no imports or exports
    const hasImportOrExport = /import\s|export\s/.test(content);
    return !hasImportOrExport;
  } catch (err) {
    console.error(`Error reading file ${filePath}:`, err.message);
    errors++;
    return false;
  }
}

/**
 * Fix a file by adding an empty export statement
 */
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // If file is completely empty or has only whitespace
    if (!content.trim()) {
      content = '// Auto-generated module marker\nexport {};\n';
    } else {
      // File has content but no imports/exports, add export at the end
      content = content.trimEnd();
      content += '\n\n// Added to make this a proper TypeScript module\nexport {};\n';
    }
    
    if (!DRY_RUN) {
      fs.writeFileSync(filePath, content);
    }
    
    filesFixed++;
    if (VERBOSE) {
      console.log(`Fixed: ${filePath}`);
    }
  } catch (err) {
    console.error(`Error fixing file ${filePath}:`, err.message);
    errors++;
  }
}

/**
 * Recursively scan directory for TypeScript files
 */
function scanDirectory(dir) {
  try {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory()) {
        // Skip node_modules and other excluded directories
        if (entry.name !== 'node_modules' && !entry.name.startsWith('.')) {
          scanDirectory(fullPath);
        }
      } else if (entry.isFile()) {
        const ext = path.extname(entry.name);
        if (EXTENSIONS.includes(ext)) {
          filesScanned++;
          
          if (needsFix(fullPath)) {
            fixFile(fullPath);
          }
        }
      }
    }
  } catch (err) {
    console.error(`Error scanning directory ${dir}:`, err.message);
    errors++;
  }
}

/**
 * Get a list of files with isolatedModules errors
 */
function getFilesWithErrors() {
  try {
    const output = execSync('npx tsc --noEmit', { cwd: PROJECT_ROOT, encoding: 'utf8', stdio: ['ignore', 'pipe', 'pipe'] });
    return [];
  } catch (err) {
    if (!err.stdout) return [];
    
    const output = err.stdout.toString();
    const regex = /([^\s]+\.tsx?):1:1 - error TS1208:/g;
    const files = [];
    let match;
    
    while ((match = regex.exec(output)) !== null) {
      files.push(path.join(PROJECT_ROOT, match[1]));
    }
    
    return files;
  }
}

// Main execution
console.log('TypeScript Module Fixer');
console.log('======================');
console.log(`Mode: ${DRY_RUN ? 'Dry run (no changes)' : 'Live (making changes)'}`);

// Option 1: Fix only files with known errors
const filesWithErrors = getFilesWithErrors();
if (filesWithErrors.length > 0) {
  console.log(`Found ${filesWithErrors.length} files with isolatedModules errors.`);
  
  for (const file of filesWithErrors) {
    filesScanned++;
    fixFile(file);
  }
} else {
  // Option 2: Scan all TypeScript files
  console.log(`Scanning directory: ${SRC_DIR}`);
  scanDirectory(SRC_DIR);
}

// Print summary
console.log('\nSummary:');
console.log(`Files scanned: ${filesScanned}`);
console.log(`Files fixed: ${filesFixed}`);
console.log(`Errors encountered: ${errors}`);

if (DRY_RUN) {
  console.log('\nThis was a dry run. No files were modified.');
  console.log('Run without --dry-run to apply changes.');
}

if (errors > 0) {
  process.exit(1);
}