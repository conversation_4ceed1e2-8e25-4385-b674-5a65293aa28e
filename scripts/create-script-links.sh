#!/bin/bash

# Create Script Links for Backward Compatibility
# This script creates symbolic links from the old script locations to the new consolidated scripts
# to maintain backward compatibility while transitioning to the new script organization

echo "Creating symbolic links for backward compatibility..."

# Function to create a symbolic link if the source doesn't exist
create_link() {
    local source=$1
    local target=$2
    
    # Create directory if it doesn't exist
    mkdir -p "$(dirname "$source")"
    
    if [ ! -f "$source" ] && [ -f "$target" ]; then
        ln -sf "$target" "$source"
        echo "✓ Created link: $source -> $target"
    elif [ -f "$source" ] && [ ! -L "$source" ]; then
        echo "⚠️ Warning: $source exists and is not a symlink. Skipping."
    fi
}

# Root level script links
create_link "Mexel/fix-frontend.sh" "Mexel/scripts/frontend/fix-all-frontend.js"
create_link "Mexel/fix-frontend-packages.sh" "Mexel/scripts/frontend/dependencies/fix-packages.js"
create_link "Mexel/fix-package-json.sh" "Mexel/scripts/frontend/dependencies/fix-packages.js"
create_link "Mexel/fix-yarn-frontend.sh" "Mexel/scripts/frontend/dependencies/fix-packages.js"

# Frontend directory script links
create_link "Mexel/frontend/fix-typescript-errors.js" "Mexel/scripts/frontend/typescript/fix-all-typescript.js"
create_link "Mexel/frontend/fix-specific-typescript-errors.js" "Mexel/scripts/frontend/typescript/fix-all-typescript.js"
create_link "Mexel/frontend/fix-component-declarations.js" "Mexel/scripts/frontend/typescript/fix-component-declarations.js"
create_link "Mexel/frontend/fix-component-declarations-new.js" "Mexel/scripts/frontend/typescript/fix-component-declarations.js"
create_link "Mexel/frontend/fix-component-types.js" "Mexel/scripts/frontend/typescript/fix-react-types.js"
create_link "Mexel/frontend/fix-fc-reactnode-issues.js" "Mexel/scripts/frontend/typescript/fix-react-types.js"
create_link "Mexel/frontend/fix-react-namespace.js" "Mexel/scripts/frontend/typescript/fix-react-types.js"
create_link "Mexel/frontend/fix-react-types.js" "Mexel/scripts/frontend/typescript/fix-react-types.js"
create_link "Mexel/frontend/fix-react-usestate-types.js" "Mexel/scripts/frontend/typescript/fix-all-typescript.js"
create_link "Mexel/frontend/fix-usestate-issues.js" "Mexel/scripts/frontend/typescript/fix-all-typescript.js"
create_link "Mexel/frontend/fix-use-state-issues.js" "Mexel/scripts/frontend/typescript/fix-all-typescript.js"
create_link "Mexel/frontend/fix-import-paths.js" "Mexel/scripts/frontend/imports/fix-imports.js"
create_link "Mexel/frontend/fix-import-statements.js" "Mexel/scripts/frontend/imports/fix-imports.js"
create_link "Mexel/frontend/fix-duplicate-imports.js" "Mexel/scripts/frontend/imports/fix-imports.js"
create_link "Mexel/frontend/fix-mui-chip-icons.js" "Mexel/scripts/frontend/ui-libraries/fix-ui-libraries.js"
create_link "Mexel/frontend/fix-recharts-issues.js" "Mexel/scripts/frontend/ui-libraries/fix-ui-libraries.js"
create_link "Mexel/frontend/fix-recharts-direct-usage.js" "Mexel/scripts/frontend/ui-libraries/fix-ui-libraries.js"
create_link "Mexel/frontend/fix-tabpanel.js" "Mexel/scripts/frontend/ui-libraries/fix-ui-libraries.js"

echo "Done creating symbolic links!"
echo ""
echo "You can now use either the old script paths or the new consolidated paths."
echo "We recommend updating your workflows to use the new paths over time."
echo ""
echo "To run the new scripts:"
echo "  node scripts/frontend/fix-all-frontend.js         # Fix all frontend issues"
echo "  node scripts/frontend/typescript/fix-all-typescript.js   # Fix TypeScript issues"
echo "  node scripts/frontend/imports/fix-imports.js      # Fix import issues"
echo "  node scripts/frontend/ui-libraries/fix-ui-libraries.js   # Fix UI library issues"
echo ""
echo "All scripts support --help flag to show available options."