#!/usr/bin/env node

/**
 * TypeScript Configuration Validator
 * 
 * This script checks that TypeScript configurations are correctly set up across the project.
 * It validates:
 * - All tsconfig files extend the base configuration
 * - Consistency in critical compiler options
 * - Path mappings are valid
 * - Type definitions are correctly exported
 */

const fs = require('fs');
const path = require('path');
const chalk = require('chalk') || { green: s => s, red: s => s, yellow: s => s, blue: s => s };
const { execSync } = require('child_process');

// Define the project structure
const PROJECT_ROOT = path.resolve(__dirname, '..');
const CONFIG_PATHS = {
  base: path.join(PROJECT_ROOT, 'tsconfig.base.json'),
  root: path.join(PROJECT_ROOT, 'tsconfig.json'),
  frontend: path.join(PROJECT_ROOT, 'frontend', 'tsconfig.json'),
  shared: path.join(PROJECT_ROOT, 'shared', 'tsconfig.json')
};

// Track validation status
let errors = 0;
let warnings = 0;

console.log(chalk.blue('TypeScript Configuration Validator'));
console.log('=====================================\n');

// Helper function to load JSON config
function loadConfig(configPath) {
  try {
    const content = fs.readFileSync(configPath, 'utf8');
    return JSON.parse(content);
  } catch (err) {
    console.error(chalk.red(`ERROR: Could not load ${configPath}: ${err.message}`));
    errors++;
    return null;
  }
}

// Check if all configs exist
console.log(chalk.blue('Checking configuration files existence...'));
Object.entries(CONFIG_PATHS).forEach(([name, path]) => {
  if (fs.existsSync(path)) {
    console.log(chalk.green(`✓ ${name} configuration exists: ${path}`));
  } else {
    console.log(chalk.red(`✗ ${name} configuration missing: ${path}`));
    errors++;
  }
});
console.log('');

// Load all configurations
const configs = {};
Object.entries(CONFIG_PATHS).forEach(([name, path]) => {
  if (fs.existsSync(path)) {
    configs[name] = loadConfig(path);
  }
});

// Check if configurations extend the base correctly
console.log(chalk.blue('Checking configuration inheritance...'));
['root', 'frontend', 'shared'].forEach(name => {
  if (!configs[name]) return;
  
  const extendsPath = configs[name].extends;
  if (!extendsPath) {
    console.log(chalk.red(`✗ ${name} does not extend any configuration`));
    errors++;
    return;
  }
  
  const resolvedPath = path.resolve(path.dirname(CONFIG_PATHS[name]), extendsPath);
  const expectedPath = name === 'root' 
    ? CONFIG_PATHS.base 
    : path.resolve(CONFIG_PATHS[name], '..', '..', 'tsconfig.base.json');
  
  if (path.normalize(resolvedPath) === path.normalize(expectedPath)) {
    console.log(chalk.green(`✓ ${name} correctly extends the base configuration`));
  } else {
    console.log(chalk.red(`✗ ${name} extends ${extendsPath}, but should extend the base config`));
    errors++;
  }
});
console.log('');

// Check critical compiler options for consistency
console.log(chalk.blue('Checking compiler options consistency...'));
const criticalOptions = [
  'strict', 
  'esModuleInterop', 
  'skipLibCheck', 
  'forceConsistentCasingInFileNames'
];

criticalOptions.forEach(option => {
  const values = {};
  Object.entries(configs).forEach(([name, config]) => {
    if (!config || !config.compilerOptions) return;
    values[name] = config.compilerOptions[option];
  });
  
  const baseValue = values.base;
  if (baseValue === undefined) {
    console.log(chalk.yellow(`? Option '${option}' not defined in base config`));
    warnings++;
    return;
  }
  
  const inconsistencies = [];
  ['root', 'frontend', 'shared'].forEach(name => {
    if (!configs[name] || !configs[name].compilerOptions) return;
    
    // Skip if the option is inherited from base
    if (configs[name].compilerOptions[option] === undefined) return;
    
    // Check if explicitly set option differs from base
    if (configs[name].compilerOptions[option] !== baseValue) {
      inconsistencies.push(name);
    }
  });
  
  if (inconsistencies.length === 0) {
    console.log(chalk.green(`✓ '${option}' is consistent across all configurations`));
  } else {
    console.log(chalk.yellow(`? '${option}' has inconsistent values in: ${inconsistencies.join(', ')}`));
    warnings++;
  }
});
console.log('');

// Validate path mappings
console.log(chalk.blue('Checking path mappings...'));
Object.entries(configs).forEach(([name, config]) => {
  if (!config || !config.compilerOptions || !config.compilerOptions.paths) return;
  
  const paths = config.compilerOptions.paths;
  const baseUrl = config.compilerOptions.baseUrl || '.';
  const configDir = path.dirname(CONFIG_PATHS[name]);
  
  Object.entries(paths).forEach(([alias, targets]) => {
    if (!Array.isArray(targets) || targets.length === 0) {
      console.log(chalk.yellow(`? In ${name}, path alias '${alias}' has no targets`));
      warnings++;
      return;
    }
    
    const resolvedTarget = path.resolve(configDir, baseUrl, targets[0].replace(/\/\*$/, ''));
    if (!fs.existsSync(resolvedTarget)) {
      console.log(chalk.yellow(`? In ${name}, path alias '${alias}' points to non-existent target: ${resolvedTarget}`));
      warnings++;
    } else {
      console.log(chalk.green(`✓ In ${name}, path alias '${alias}' points to valid target`));
    }
  });
});
console.log('');

// Try to compile each project
console.log(chalk.blue('Testing TypeScript compilation...'));
['root', 'shared', 'frontend'].forEach(project => {
  const cwd = project === 'root' ? PROJECT_ROOT : path.join(PROJECT_ROOT, project);
  
  if (!fs.existsSync(path.join(cwd, 'tsconfig.json'))) {
    console.log(chalk.yellow(`? Skipping compilation test for ${project}: no tsconfig.json`));
    return;
  }
  
  try {
    console.log(`Checking ${project}...`);
    // Use --noEmit to just check without generating files
    execSync('npx tsc --noEmit', { cwd, stdio: 'pipe' });
    console.log(chalk.green(`✓ ${project} compilation successful`));
  } catch (err) {
    console.log(chalk.red(`✗ ${project} compilation failed:`));
    console.log(err.stdout ? err.stdout.toString() : 'Unknown error');
    errors++;
  }
});
console.log('');

// Print summary
console.log(chalk.blue('Validation Summary'));
console.log('=====================================');
console.log(`Errors: ${errors}`);
console.log(`Warnings: ${warnings}`);

if (errors === 0 && warnings === 0) {
  console.log(chalk.green('\n✓ TypeScript configuration is valid!'));
  process.exit(0);
} else if (errors === 0) {
  console.log(chalk.yellow('\n⚠ TypeScript configuration has warnings but no errors.'));
  process.exit(0);
} else {
  console.log(chalk.red('\n✗ TypeScript configuration has errors.'));
  process.exit(1);
}