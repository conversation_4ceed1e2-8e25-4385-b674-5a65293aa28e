#!/usr/bin/env node

/**
 * Version Check Script for Mexel
 * 
 * This script checks if the user is using the correct versions of Node.js and Yarn.
 * It will print warnings or errors if the versions don't match the requirements.
 * 
 * Usage: node scripts/check-versions.js [--quiet]
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Default versions (fallbacks if package.json cannot be read)
const DEFAULT_NODE_VERSION = '18.0.0';
const DEFAULT_YARN_VERSION = '3.0.0';

// Strict mode - exit with error if versions don't match
const STRICT_MODE = false;

// Options
const QUIET_MODE = process.argv.includes('--quiet');

// Colors for terminal output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  bold: '\x1b[1m'
};

/**
 * Log a message (respecting quiet mode)
 */
function log(message) {
  if (!QUIET_MODE) {
    console.log(message);
  }
}

/**
 * Get the required versions from package.json
 */
function getRequiredVersions() {
  try {
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    const engines = packageJson.engines || {};
    
    return {
      node: engines.node?.replace(/[^0-9.]/g, '') || DEFAULT_NODE_VERSION,
      yarn: engines.yarn?.replace(/[^0-9.]/g, '') || DEFAULT_YARN_VERSION
    };
  } catch (error) {
    return {
      node: DEFAULT_NODE_VERSION,
      yarn: DEFAULT_YARN_VERSION
    };
  }
}

/**
 * Get the current Node.js version
 */
function getCurrentNodeVersion() {
  return process.version.replace('v', '');
}

/**
 * Get the current Yarn version
 */
function getCurrentYarnVersion() {
  try {
    const output = execSync('yarn --version', { encoding: 'utf8' }).trim();
    return output;
  } catch (error) {
    return null;
  }
}

/**
 * Compare versions
 * Returns: -1 if v1 < v2, 0 if v1 == v2, 1 if v1 > v2
 */
function compareVersions(v1, v2) {
  const parts1 = v1.split('.').map(Number);
  const parts2 = v2.split('.').map(Number);
  
  for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
    const part1 = parts1[i] || 0;
    const part2 = parts2[i] || 0;
    
    if (part1 < part2) return -1;
    if (part1 > part2) return 1;
  }
  
  return 0;
}

/**
 * Check if the current version meets the minimum requirement
 */
function checkVersion(current, required, name) {
  if (!current) {
    log(`${colors.red}✗ ${name} is not installed or not in PATH${colors.reset}`);
    return false;
  }
  
  const comparison = compareVersions(current, required);
  
  if (comparison < 0) {
    log(`${colors.red}✗ ${name} version ${current} is lower than required ${required}${colors.reset}`);
    return false;
  } else {
    log(`${colors.green}✓ ${name} version ${current} meets requirement (>= ${required})${colors.reset}`);
    return true;
  }
}

/**
 * Check if the user is using npm instead of yarn
 */
function checkForNpm() {
  // Check if this script was run via npm
  const userAgent = process.env.npm_config_user_agent || '';
  if (userAgent.includes('npm')) {
    log(`${colors.red}${colors.bold}✗ You are using npm! Please use Yarn instead.${colors.reset}`);
    log(`${colors.yellow}Run 'node scripts/setup-yarn.js' to set up Yarn.${colors.reset}`);
    return true;
  }
  return false;
}

/**
 * Print setup instructions
 */
function printSetupInstructions() {
  log('\n' + colors.blue + 'To set up the correct environment:' + colors.reset);
  log('1. Install the required Node.js version:');
  log('   - Using nvm: nvm install 18');
  log('   - Directly: https://nodejs.org/');
  log('\n2. Set up Yarn:');
  log('   node scripts/setup-yarn.js');
  log('\n3. Install dependencies:');
  log('   yarn install');
}

/**
 * Main execution
 */
function main() {
  const required = getRequiredVersions();
  const currentNode = getCurrentNodeVersion();
  const currentYarn = getCurrentYarnVersion();
  
  log(colors.bold + '🔍 Checking environment versions for Mexel project...' + colors.reset);
  
  const nodeOk = checkVersion(currentNode, required.node, 'Node.js');
  const yarnOk = checkVersion(currentYarn, required.yarn, 'Yarn');
  const usingNpm = checkForNpm();
  
  const allOk = nodeOk && yarnOk && !usingNpm;
  
  if (!allOk) {
    printSetupInstructions();
    
    if (STRICT_MODE) {
      process.exit(1);
    }
  }
  
  return allOk;
}

// Run the script if executed directly
if (require.main === module) {
  main();
}

module.exports = main;