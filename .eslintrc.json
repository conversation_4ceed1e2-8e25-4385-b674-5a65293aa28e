{"root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "import", "react", "react-hooks"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:import/typescript", "plugin:react/recommended", "plugin:react-hooks/recommended"], "env": {"node": true, "browser": true, "es2022": true}, "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "settings": {"import/resolver": {"typescript": {"alwaysTryTypes": true, "project": ["./tsconfig.json", "./frontend/tsconfig.json", "./shared/tsconfig.json"]}, "node": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}}, "react": {"version": "detect"}}, "overrides": [{"files": ["*.js", "*.jsx"], "parser": "espree", "rules": {"@typescript-eslint/no-var-requires": "off", "@typescript-eslint/explicit-module-boundary-types": "off"}}, {"files": ["*.ts", "*.tsx"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": ["./tsconfig.json", "./frontend/tsconfig.json", "./shared/tsconfig.json"]}}, {"files": ["*.d.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-redeclare": "off"}}, {"files": ["**/*.spec.ts", "**/*.test.ts", "**/*.test.tsx"], "env": {"jest": true}, "rules": {"@typescript-eslint/no-explicit-any": "off"}}], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "ignoreRestSiblings": true}], "@typescript-eslint/explicit-module-boundary-types": "warn", "@typescript-eslint/no-explicit-any": "error", "no-console": "error", "no-debugger": "error", "no-alert": "error", "no-duplicate-imports": "error", "no-var": "error", "prefer-const": "error", "import/no-unresolved": "error", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index", "object", "type"], "pathGroups": [{"pattern": "react", "group": "external", "position": "before"}, {"pattern": "@/**", "group": "internal", "position": "after"}, {"pattern": "@shared/**", "group": "internal", "position": "after"}], "pathGroupsExcludedImportTypes": ["react"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}, "warnOnUnassignedImports": true}], "import/first": "error", "import/newline-after-import": "error", "import/no-duplicates": "error", "react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off", "react/jsx-curly-brace-presence": ["error", {"props": "never", "children": "never"}], "react/jsx-boolean-value": ["error", "never"], "react/no-array-index-key": "error", "react/no-unused-prop-types": "error", "react-hooks/exhaustive-deps": "error", "react-hooks/rules-of-hooks": "error"}, "ignorePatterns": ["node_modules/", "dist/", "build/", "coverage/", "**/*.js.map", "**/*.d.ts.map"]}