#!/bin/zsh

# Script to start both frontend and backend servers for Mexel
echo "===== Mexel Development Server Launcher ====="
echo ""

# Set project directories
PROJECT_ROOT="/Users/<USER>/Desktop/Mexel"
BACKEND_DIR="$PROJECT_ROOT/backend-python"
FRONTEND_DIR="$PROJECT_ROOT/frontend"

# Function to check if a port is in use
check_port() {
  lsof -i :$1 > /dev/null 2>&1
  return $?
}

# Start the backend server
echo "🚀 Starting backend server..."
if check_port 3001; then
  echo "⚠️  Port 3001 is already in use. Backend server might already be running."
  echo "ℹ️  If you need to stop it, find and terminate the process using:"
  echo "   lsof -i :3001"
  echo "   kill -9 <PID>"
else
  echo "📡 Starting backend server on port 3001..."
  open -a Terminal.app "$PROJECT_ROOT/start-backend.sh"
  sleep 3 # Give some time for backend to start
fi

# Start the frontend server
echo ""
echo "🚀 Starting frontend server..."
if check_port 3000; then
  echo "⚠️  Port 3000 is already in use. Frontend server might already be running."
  echo "ℹ️  If you need to stop it, find and terminate the process using:"
  echo "   lsof -i :3000"
  echo "   kill -9 <PID>"
else
  echo "📡 Starting frontend server on port 3000..."
  open -a Terminal.app "$FRONTEND_DIR"
  osascript -e 'tell application "Terminal" to do script "cd '"$FRONTEND_DIR"' && yarn start"'
fi

echo ""
echo "===== Startup Initiated ====="
echo "🔍 Frontend should be available at: http://localhost:3000"
echo "🔌 Backend should be available at: http://localhost:3001"
echo ""
echo "✅ To test connectivity, use the API Tester on the frontend"
echo "❓ For help, refer to START_SERVERS_GUIDE.md"
