#!/bin/bash

# Enhanced script to enforce yarn as the only package manager in the project

echo "Enforcing yarn as the package manager for this project..."

# Check if yarn is installed
if ! command -v yarn &> /dev/null; then
    echo "❌ Yarn is not installed. Please install yarn first:"
    echo "   https://yarnpkg.com/getting-started/install"
    exit 1
fi

# Get yarn version
YARN_VERSION=$(yarn --version)
echo "✅ Using Yarn version: $YARN_VERSION"

# Find and remove all package-lock.json files
echo "Removing all package-lock.json files..."
find . -name "package-lock.json" -type f -delete

# Find all npm debug logs and remove them
echo "Removing all npm debug logs..."
find . -name "npm-debug.log*" -type f -delete

# Create or update .npmrc to prevent npm usage
echo "Creating .npmrc files to prevent npm usage..."
find . -name "package.json" -type f -exec dirname {} \; | while read dir; do
  echo 'package-lock=false' > "$dir/.npmrc"
  echo 'engine-strict=true' >> "$dir/.npmrc"
done

# Create or update .yarnrc.yml files to enforce Yarn 3.6.3
echo "Creating .yarnrc.yml files to enforce Yarn 3.6.3..."
find . -name "package.json" -type f -exec dirname {} \; | while read dir; do
  # Remove old .yarnrc files (for Yarn 1.x)
  rm -f "$dir/.yarnrc"
  
  # Determine the correct yarnPath relative to each directory
  if [ "$dir" = "." ]; then
    # Root directory
    YARN_PATH=".yarn/releases/yarn-3.6.3.cjs"
  else
    # Subdirectories (frontend, shared, etc.)
    YARN_PATH="../.yarn/releases/yarn-3.6.3.cjs"
  fi
  
  # Create .yarnrc.yml file for Yarn 3.6.3
  cat << EOF > "$dir/.yarnrc.yml"
nodeLinker: node-modules
yarnPath: $YARN_PATH
npmRegistryServer: "https://registry.yarnpkg.com"
npmAlwaysAuth: false
enableGlobalCache: true

packageExtensions:
  # Add any needed package extensions here
EOF
done

# Update scripts in package.json files to use yarn instead of npm
echo "Updating scripts in package.json files to use yarn..."
find . -name "package.json" -type f | while read file; do
  # Replace npm commands with yarn equivalents
  sed -i '' 's/"npm run /"yarn /g' "$file"
  sed -i '' 's/"npm start"/"yarn start"/g' "$file"
  sed -i '' 's/"npm test"/"yarn test"/g' "$file"
  sed -i '' 's/"npm install"/"yarn install"/g' "$file"
  sed -i '' 's/"npm i /"yarn add /g' "$file"
  sed -i '' 's/"npm ci"/"yarn install --frozen-lockfile"/g' "$file"
done

# Update shell scripts to use yarn instead of npm
echo "Updating shell scripts to use yarn instead of npm..."
find . -name "*.sh" -type f -not -path "*/enforce-yarn*.sh" | while read file; do
  # Replace npm commands with yarn equivalents
  sed -i '' 's/npm run /yarn /g' "$file"
  sed -i '' 's/npm start/yarn start/g' "$file"
  sed -i '' 's/npm test/yarn test/g' "$file"
  sed -i '' 's/npm install/yarn install/g' "$file"
  sed -i '' 's/npm i /yarn add /g' "$file"
  sed -i '' 's/npm ci/yarn install --frozen-lockfile/g' "$file"
done

# Add engines field to package.json files
echo "Adding or updating engines field in package.json files..."
find . -name "package.json" -type f | while read file; do
  # Create a temporary file with the engines field for Yarn 3.6.3
  jq 'if has("engines") then . else . + {"engines":{}} end | .engines += {"node": ">=14.0.0 <19.0.0", "yarn": ">=3.0.0", "npm": "please-use-yarn"}' "$file" > "$file.tmp"
  if [ $? -eq 0 ]; then
    mv "$file.tmp" "$file"
    echo "Updated $file with engines field for Yarn 3.6.3"
  else
    echo "Error updating $file, skipping"
    rm -f "$file.tmp"
  fi
done

# Add preinstall script to package.json files to prevent npm usage
echo "Adding preinstall script to prevent npm usage..."
find . -name "package.json" -type f | while read file; do
  if ! grep -q "\"preinstall\":" "$file"; then
    # Create a temporary file with the new content
    # First ensure there's a scripts object, then add preinstall
    jq 'if has("scripts") then . else . + {"scripts":{}} end | .scripts += {"preinstall": "node -e \"if(process.env.npm_execpath.indexOf(\'yarn\')===-1) throw new Error(\'\\\n⚠️  This project requires Yarn instead of NPM! \\\n\\\n📦 Please install Yarn: https://yarnpkg.com/getting-started/install\\\n\\\n🚀 Then run: yarn install\\\n\')\""}' "$file" > "$file.tmp"
    if [ $? -eq 0 ]; then
      mv "$file.tmp" "$file"
      echo "Updated $file with preinstall script"
    else
      echo "Error updating $file, skipping"
      rm -f "$file.tmp"
    fi
  else
    echo "File $file already has preinstall script"
  fi
done

# Update README.md to mention yarn requirement
echo "Updating README.md to mention yarn requirement..."
if [ -f "README.md" ]; then
  if ! grep -q "## Package Manager" "README.md"; then
    # Append to the end of the README
    cat << 'EOF' >> README.md

## Package Manager

This project uses Yarn as its package manager. Please ensure you have Yarn installed before working with this project.

### Installation

To install Yarn, follow the instructions at [https://yarnpkg.com/getting-started/install](https://yarnpkg.com/getting-started/install).

### Commands

- Install dependencies: `yarn install`
- Run the development server: `yarn start`
- Run tests: `yarn test`
- Add a dependency: `yarn add [package-name]`
- Add a dev dependency: `yarn add --dev [package-name]`

**Note:** Using npm commands will result in errors. Please use Yarn for all package management operations.
EOF
    echo "Updated README.md with yarn requirement information"
  else
    echo "README.md already contains package manager information"
  fi
else
  # Create a new README if it doesn't exist
  cat << 'EOF' > README.md
# Project Overview

## Package Manager

This project uses Yarn as its package manager. Please ensure you have Yarn installed before working with this project.

### Installation

To install Yarn, follow the instructions at [https://yarnpkg.com/getting-started/install](https://yarnpkg.com/getting-started/install).

### Commands

- Install dependencies: `yarn install`
- Run the development server: `yarn start`
- Run tests: `yarn test`
- Add a dependency: `yarn add [package-name]`
- Add a dev dependency: `yarn add --dev [package-name]`

**Note:** Using npm commands will result in errors. Please use Yarn for all package management operations.
EOF
  echo "Created new README.md with yarn requirement information"
fi

# Update .gitignore to ignore npm-specific files
echo "Updating .gitignore to ignore npm-specific files..."
if [ -f ".gitignore" ]; then
  if ! grep -q "# npm (disallowed)" ".gitignore"; then
    # Append to the end of .gitignore
    cat << 'EOF' >> .gitignore

# npm (disallowed)
package-lock.json
npm-debug.log*
.npm
EOF
    echo "Updated .gitignore with npm-specific exclusions"
  else
    echo ".gitignore already contains npm-specific exclusions"
  fi
else
  # Create a new .gitignore if it doesn't exist
  cat << 'EOF' > .gitignore
# npm (disallowed)
package-lock.json
npm-debug.log*
.npm

# Yarn
node_modules
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*
yarn-debug.log*
yarn-error.log*
EOF
  echo "Created new .gitignore with npm and yarn exclusions"
fi

# Add warnings about npm to bash history
echo "Adding warnings about npm to bash profile..."
BASH_WARNING='# Warning for npm usage in this project
alias npm="echo -e \"\\\n⚠️  This project requires Yarn instead of NPM! \\\n\\\n📦 Please use: yarn\\\n\" && false"
'

# Check if warning exists in bash_profile
PROFILE_FILES=("$HOME/.bash_profile" "$HOME/.bashrc" "$HOME/.zshrc")
for profile in "${PROFILE_FILES[@]}"; do
  if [ -f "$profile" ]; then
    if ! grep -q "Warning for npm usage in this project" "$profile"; then
      echo "Would you like to add npm warnings to $profile? (y/n)"
      read -r add_warning
      if [ "$add_warning" = "y" ]; then
        echo "$BASH_WARNING" >> "$profile"
        echo "Added warning to $profile. Please restart your terminal or run 'source $profile'."
      fi
    fi
  fi
done

echo "✨ Done enforcing yarn as the package manager! ✨"
echo ""
echo "📦 To install dependencies, run: yarn install"
echo "🚀 To start the frontend, run: cd frontend && yarn start"
echo ""
echo "⚠️ npm commands will now fail with an error message."
echo "🔄 You may need to restart your terminal for all changes to take effect."

# Create a verification script
echo "Creating a verification script to check yarn usage..."
cat << 'EOF' > verify-yarn-usage.sh
#!/bin/bash

# Verify that yarn is being used properly

echo "Verifying yarn usage in project..."

# Check if yarn is installed
if ! command -v yarn &> /dev/null; then
    echo "❌ Yarn is not installed. Please install yarn first:"
    echo "   https://yarnpkg.com/getting-started/install"
    exit 1
fi

# Check for package-lock.json files
PACKAGE_LOCKS=$(find . -name "package-lock.json" -type f)
if [ -n "$PACKAGE_LOCKS" ]; then
    echo "❌ Found package-lock.json files, which indicates npm usage:"
    echo "$PACKAGE_LOCKS"
    echo "   Please run enforce-yarn.sh script again."
else
    echo "✅ No package-lock.json files found."
fi

# Check for preinstall hooks in package.json files
MISSING_HOOKS=0
for pkg in $(find . -name "package.json" -type f); do
    if ! grep -q "preinstall" "$pkg"; then
        echo "❌ Missing preinstall hook in $pkg"
        MISSING_HOOKS=1
    fi
done

if [ $MISSING_HOOKS -eq 0 ]; then
    echo "✅ All package.json files have preinstall hooks."
else
    echo "   Please run enforce-yarn.sh script again."
fi

# Check for yarn.lock files
MISSING_LOCKS=0
for dir in $(find . -name "package.json" -type f -exec dirname {} \;); do
    if [ ! -f "$dir/yarn.lock" ]; then
        echo "❌ Missing yarn.lock in $dir"
        MISSING_LOCKS=1
    fi
done

if [ $MISSING_LOCKS -eq 0 ]; then
    echo "✅ All package directories have yarn.lock files."
else
    echo "   Please run 'yarn install' in the missing directories."
fi

echo "Verification complete!"
EOF

chmod +x verify-yarn-usage.sh
echo "Created verify-yarn-usage.sh script to verify yarn usage."
