# Mexel Dashboard Template

This is the official template for the Mexel Dashboard. It provides a standardized structure and design for all dashboard implementations.

## Overview

The Mexel Dashboard Template includes eight core sections:

1. **Email Generation and Preview**

   - Create, preview, and manage email templates
   - Generate AI-powered emails based on requirements
   - Track email performance

2. **SEO Insights Dashboard**

   - Track website SEO performance with detailed analytics
   - Monitor keyword rankings and competitor analysis
   - Analyze page performance

3. **Tender Opportunities**

   - Track and manage tender opportunities
   - Score and filter tenders based on relevance
   - Manage tender submission process

4. **Technical Content Generator**

   - Create AI-generated technical content for engineers
   - Publish through multiple channels (blog, LinkedIn, email)
   - Track content performance and engagement
   - Leverage user-supplied data and online research

5. **AI Recommendations**

   - Get AI-powered recommendations for marketing strategy
   - Track implementation of recommendations
   - Measure impact of implemented recommendations

6. **Task Management**

   - Manage marketing tasks with a comprehensive system
   - Track task status, priority, and deadlines
   - Assign tasks to team members

7. **Email Reports**

   - Generate detailed reports on email campaign performance
   - Schedule automated reports
   - Analyze email metrics

8. **Marketing ROI & Analytics**
   - Track ROI across different marketing channels
   - Analyze industry-specific performance metrics
   - Monitor product performance by sector
   - Measure customer lifetime value and success metrics

## Files

- `mexel-dashboard-template.html` - The official template with placeholder content
- `mexel-dashboard-exact.html` - A fully implemented example with real content
- `tabs-enhancer.js` - JavaScript for tab functionality

## Usage Guidelines

### Adding New Content

When adding new content to the dashboard, follow these guidelines:

1. Use the existing structure and design patterns
2. Maintain consistent styling (colors, fonts, spacing)
3. Ensure all interactive elements work correctly
4. Test on different screen sizes for responsiveness

### Modifying Sections

If you need to modify existing sections:

1. Keep the same section numbering and order
2. Maintain the same tab structure where applicable
3. Ensure all tabs have appropriate content
4. Update section descriptions as needed

### Adding New Sections

If you need to add new sections:

1. Follow the same structure as existing sections
2. Use the same styling for consistency
3. Add appropriate navigation items
4. Update the section numbering

## Development

### Local Development

To work on the dashboard locally:

1. Clone the repository
2. Open the HTML files in your browser
3. Make changes using your preferred code editor
4. Refresh the browser to see changes

### JavaScript Functionality

The dashboard uses vanilla JavaScript for interactivity:

- Tab switching
- Navigation highlighting
- Button interactions

### CSS Styling

The dashboard uses a custom CSS framework with these key components:

- Grid layout system
- Card components
- Tab navigation
- Stat displays
- Chip/badge elements

## Best Practices

1. Keep the design clean and focused
2. Ensure all interactive elements have clear affordances
3. Maintain consistent spacing and alignment
4. Use color purposefully (blue for primary actions, green for success, red for warnings)
5. Ensure text is readable (sufficient contrast, appropriate font sizes)

## Contact

For questions or issues related to the dashboard template, contact:

- Zola Mahlaza - <EMAIL>
