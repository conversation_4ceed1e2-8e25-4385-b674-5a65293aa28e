// Basic Playwright type definitions to resolve TypeScript errors
// This is a workaround for missing @playwright/test package

declare module '@playwright/test' {
  export interface Locator {
    click(options?: any): Promise<void>;
    fill(value: string, options?: any): Promise<void>;
    textContent(options?: any): Promise<string | null>;
    getAttribute(name: string, options?: any): Promise<string | null>;
    isVisible(options?: any): Promise<boolean>;
    isEnabled(options?: any): Promise<boolean>;
    isChecked(options?: any): Promise<boolean>;
    waitFor(options?: any): Promise<void>;
    screenshot(options?: any): Promise<Buffer>;
    scrollIntoViewIfNeeded(options?: any): Promise<void>;
    hover(options?: any): Promise<void>;
    focus(options?: any): Promise<void>;
    blur(options?: any): Promise<void>;
    selectOption(values: string | string[], options?: any): Promise<string[]>;
    selectText(options?: any): Promise<void>;
    setInputFiles(files: string | string[], options?: any): Promise<void>;
    type(text: string, options?: any): Promise<void>;
    press(key: string, options?: any): Promise<void>;
    check(options?: any): Promise<void>;
    uncheck(options?: any): Promise<void>;
    dispatchEvent(type: string, eventInit?: any, options?: any): Promise<void>;
    evaluate<R, Arg>(pageFunction: (arg: Arg) => R, arg: Arg): Promise<R>;
    evaluate<R>(pageFunction: () => R): Promise<R>;
    innerHTML(options?: any): Promise<string>;
    innerText(options?: any): Promise<string>;
    inputValue(options?: any): Promise<string>;
    count(): Promise<number>;
    first(): Locator;
    last(): Locator;
    nth(index: number): Locator;
    filter(options?: any): Locator;
    and(locator: Locator): Locator;
    or(locator: Locator): Locator;
  }

  export interface Page {
    goto(url: string, options?: any): Promise<any>;
    click(selector: string, options?: any): Promise<void>;
    fill(selector: string, value: string, options?: any): Promise<void>;
    textContent(selector: string, options?: any): Promise<string | null>;
    waitForSelector(selector: string, options?: any): Promise<any>;
    screenshot(options?: any): Promise<Buffer>;
    close(): Promise<void>;
    locator(selector: string): Locator;
    getByRole(role: string, options?: any): Locator;
    getByText(text: string | RegExp, options?: any): Locator;
    getByLabel(text: string | RegExp, options?: any): Locator;
    getByPlaceholder(text: string | RegExp, options?: any): Locator;
    getByAltText(text: string | RegExp, options?: any): Locator;
    getByTitle(text: string | RegExp, options?: any): Locator;
    getByTestId(testId: string): Locator;
    frameLocator(selector: string): any;
    focus(selector: string, options?: any): Promise<void>;
    hover(selector: string, options?: any): Promise<void>;
    selectOption(selector: string, values: string | string[], options?: any): Promise<string[]>;
    setInputFiles(selector: string, files: string | string[], options?: any): Promise<void>;
    type(selector: string, text: string, options?: any): Promise<void>;
    press(selector: string, key: string, options?: any): Promise<void>;
    check(selector: string, options?: any): Promise<void>;
    uncheck(selector: string, options?: any): Promise<void>;
    isChecked(selector: string, options?: any): Promise<boolean>;
    isDisabled(selector: string, options?: any): Promise<boolean>;
    isEditable(selector: string, options?: any): Promise<boolean>;
    isEnabled(selector: string, options?: any): Promise<boolean>;
    isHidden(selector: string, options?: any): Promise<boolean>;
    isVisible(selector: string, options?: any): Promise<boolean>;
    dispatchEvent(selector: string, type: string, eventInit?: any, options?: any): Promise<void>;
    evaluate<R, Arg>(pageFunction: (arg: Arg) => R, arg: Arg): Promise<R>;
    evaluate<R>(pageFunction: () => R): Promise<R>;
    waitForTimeout(timeout: number): Promise<void>;
    waitForFunction<R, Arg>(pageFunction: (arg: Arg) => R, arg: Arg, options?: any): Promise<any>;
    waitForFunction<R>(pageFunction: () => R, options?: any): Promise<any>;
    waitForEvent(event: string, options?: any): Promise<any>;
    waitForURL(url: string | RegExp | ((url: URL) => boolean), options?: any): Promise<void>;
    reload(options?: any): Promise<any>;
    goBack(options?: any): Promise<any>;
    goForward(options?: any): Promise<any>;
    title(): Promise<string>;
    url(): string;
    content(): Promise<string>;
    innerHTML(selector: string, options?: any): Promise<string>;
    innerText(selector: string, options?: any): Promise<string>;
    inputValue(selector: string, options?: any): Promise<string>;
    getAttribute(selector: string, name: string, options?: any): Promise<string | null>;
    setDefaultTimeout(timeout: number): void;
    setViewportSize(viewportSize: { width: number; height: number }): Promise<void>;
    viewportSize(): { width: number; height: number } | null;
  }

  export interface Browser {
    newPage(): Promise<Page>;
    close(): Promise<void>;
    newContext(options?: any): Promise<BrowserContext>;
    contexts(): BrowserContext[];
    isConnected(): boolean;
    version(): string;
  }

  export interface BrowserContext {
    newPage(): Promise<Page>;
    close(): Promise<void>;
    pages(): Page[];
    setDefaultTimeout(timeout: number): void;
    setDefaultNavigationTimeout(timeout: number): void;
    cookies(urls?: string | string[]): Promise<any[]>;
    addCookies(cookies: any[]): Promise<void>;
    clearCookies(): Promise<void>;
    grantPermissions(permissions: string[], options?: any): Promise<void>;
    clearPermissions(): Promise<void>;
    setGeolocation(geolocation: { latitude: number; longitude: number; accuracy?: number }): Promise<void>;
    setExtraHTTPHeaders(headers: { [key: string]: string }): Promise<void>;
    setOffline(offline: boolean): Promise<void>;
    addInitScript(script: Function | string | { path?: string; content?: string }, arg?: any): Promise<void>;
    exposeFunction(name: string, callback: Function): Promise<void>;
    route(url: string | RegExp | ((url: URL) => boolean), handler: Function): Promise<void>;
    unroute(url: string | RegExp | ((url: URL) => boolean), handler?: Function): Promise<void>;
    waitForEvent(event: string, options?: any): Promise<any>;
    storageState(options?: any): Promise<any>;
  }

  export interface PlaywrightTestArgs {
    page: Page;
    context: BrowserContext;
    browser: Browser;
    browserName: string;
  }

  export interface Test {
    (title: string, testFunction: (args: PlaywrightTestArgs) => Promise<void>): void;
    describe: (title: string, callback: () => void) => void;
    beforeEach: (callback: (args: PlaywrightTestArgs) => Promise<void>) => void;
    afterEach: (callback: (args: PlaywrightTestArgs) => Promise<void>) => void;
    beforeAll: (callback: () => Promise<void>) => void;
    afterAll: (callback: () => Promise<void>) => void;
    expect: (value: any) => PlaywrightExpect;
    skip: (title: string, testFunction?: (args: PlaywrightTestArgs) => Promise<void>) => void;
    only: (title: string, testFunction: (args: PlaywrightTestArgs) => Promise<void>) => void;
    fail: (title: string, testFunction: (args: PlaywrightTestArgs) => Promise<void>) => void;
    fixme: (title: string, testFunction?: (args: PlaywrightTestArgs) => Promise<void>) => void;
    slow: (title: string, testFunction: (args: PlaywrightTestArgs) => Promise<void>) => void;
    setTimeout: (timeout: number) => void;
    step: <T>(title: string, body: () => Promise<T>) => Promise<T>;
  }

  export interface PlaywrightExpect {
    (value: any): PlaywrightMatchers;
    soft: (value: any) => PlaywrightMatchers;
  }

  export interface PlaywrightMatchers {
    toBeAttached(): Promise<void>;
    toBeChecked(): Promise<void>;
    toBeDisabled(): Promise<void>;
    toBeEditable(): Promise<void>;
    toBeEmpty(): Promise<void>;
    toBeEnabled(): Promise<void>;
    toBeFocused(): Promise<void>;
    toBeHidden(): Promise<void>;
    toBeInViewport(): Promise<void>;
    toBeVisible(): Promise<void>;
    toContainText(expected: string | RegExp | (string | RegExp)[]): Promise<void>;
    toHaveAttribute(name: string, value?: string | RegExp): Promise<void>;
    toHaveClass(expected: string | RegExp | (string | RegExp)[]): Promise<void>;
    toHaveCount(count: number): Promise<void>;
    toHaveCSS(name: string, value: string | RegExp): Promise<void>;
    toHaveId(id: string | RegExp): Promise<void>;
    toHaveJSProperty(name: string, value: any): Promise<void>;
    toHaveScreenshot(name?: string | string[], options?: any): Promise<void>;
    toHaveText(expected: string | RegExp | (string | RegExp)[]): Promise<void>;
    toHaveTitle(titleOrRegExp: string | RegExp): Promise<void>;
    toHaveURL(url: string | RegExp): Promise<void>;
    toHaveValue(value: string | RegExp): Promise<void>;
    toHaveValues(values: (string | RegExp)[]): Promise<void>;
    not: PlaywrightMatchers;
  }

  export const test: Test;
  export const expect: PlaywrightExpect;
  export function chromium(): { launch(options?: any): Promise<Browser> };
  export function firefox(): { launch(options?: any): Promise<Browser> };
  export function webkit(): { launch(options?: any): Promise<Browser> };
}
