// Type definitions for better-sqlite3 7.x
// Project: https://github.com/JoshuaWise/better-sqlite3
// Definitions by: <PERSON><PERSON><PERSON> <https://github.com/mikolajb>
//                 <PERSON><PERSON><PERSON> <https://github.com/<PERSON>er<PERSON><PERSON>>
//                 <PERSON> <https://github.com/garronej>
//                 <PERSON> <https://github.com/Morfent>
//                 <PERSON> <https://github.com/FRYTG>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped

declare module 'better-sqlite3' {
    import Integer = require("integer");

    interface Statement {
        database: Database;
        source: string;
        reader: boolean;
        busy: boolean;

        run(...params: any[]): Database.RunResult;
        get(...params: any[]): any;
        all(...params: any[]): any[];
        iterate(...params: any[]): IterableIterator<any>;
        pluck(toggleState?: boolean): this;
        expand(toggleState?: boolean): this;
        raw(toggleState?: boolean): this;
        bind(...params: any[]): this;
        columns(): Database.ColumnDefinition[];
        safeIntegers(toggleState?: boolean): this;
    }

    interface Transaction {
        (...params: any[]): any;
        default(...params: any[]): any;
        deferred(...params: any[]): any;
        immediate(...params: any[]): any;
        exclusive(...params: any[]): any;
    }

    interface Database {
        memory: boolean;
        readonly: boolean;
        name: string;
        open: boolean;
        inTransaction: boolean;

        prepare<P extends any[] = any[], R = any>(source: string): Database.Statement<P, R>;
        transaction(fn: (...params: any[]) => any): Database.Transaction;
        exec(source: string): this;
        pragma(source: string, options?: Database.PragmaOptions): any;
        function(name: string, cb: (...params: any[]) => any): this;
        function(name: string, options: Database.RegistrationOptions, cb: (...params: any[]) => any): this;
        aggregate(name: string, options: Database.AggregateOptions): this;
        loadExtension(path: string, entryPoint?: string): this;
        close(): this;
        defaultSafeIntegers(toggleState?: boolean): this;
        backup(destinationFile: string, options?: Database.BackupOptions): Promise<Database.BackupMetadata>;

        [key: string]: any; // Add index signature for custom functions
    }

    interface Options {
        memory?: boolean | undefined;
        readonly?: boolean | undefined;
        fileMustExist?: boolean | undefined;
        timeout?: number | undefined;
        verbose?: ((message?: any, ...additionalArgs: any[]) => void) | undefined;
        nativeBinding?: string | undefined;
    }

    interface DatabaseConstructor {
        new (filename: string, options?: Database.Options): Database;
        (filename: string, options?: Database.Options): Database;
        prototype: Database;

        SqliteError: typeof SqliteError;
    }

    class SqliteError extends Error {
        name: string;
        code: string;
        constructor(message: string, code: string);
    }

    const Database: DatabaseConstructor;
    export = Database;

    namespace Database {
        interface RunResult {
            changes: number;
            lastInsertRowid: Integer.IntLike;
        }

        interface Options {
            memory?: boolean | undefined;
            readonly?: boolean | undefined;
            fileMustExist?: boolean | undefined;
            timeout?: number | undefined;
            verbose?: ((message?: any, ...additionalArgs: any[]) => void) | undefined;
            nativeBinding?: string | undefined;
        }

        interface PragmaOptions {
            simple?: boolean | undefined;
        }

        interface RegistrationOptions {
            varargs?: boolean | undefined;
            deterministic?: boolean | undefined;
            safeIntegers?: boolean | undefined;
            directOnly?: boolean | undefined;
        }

        interface AggregateOptions extends RegistrationOptions {
            start?: any;
            step: (total: any, next: any) => any;
            inverse?: ((total: any, dropped: any) => any) | undefined;
            result?: ((total: any) => any) | undefined;
        }

        interface BackupOptions {
            progress?: ((info: BackupMetadata) => number) | undefined;
        }

        interface BackupMetadata {
            totalPages: number;
            remainingPages: number;
        }

        interface ColumnDefinition {
            name: string;
            column: string | null;
            table: string | null;
            database: string | null;
            type: string | null;
        }

        interface Statement<BindParameters extends any[] = any[], Result = any> {
            database: Database;
            source: string;
            reader: boolean;
            busy: boolean;

            run(...params: BindParameters): RunResult;
            get(...params: BindParameters): Result;
            all(...params: BindParameters): Result[];
            iterate(...params: BindParameters): IterableIterator<Result>;
            pluck(toggleState?: boolean): this;
            expand(toggleState?: boolean): this;
            raw(toggleState?: boolean): this;
            bind(...params: BindParameters): this;
            columns(): ColumnDefinition[];
            safeIntegers(toggleState?: boolean): this;
        }

        interface Transaction {
            (...params: any[]): any;
            default(...params: any[]): any;
            deferred(...params: any[]): any;
            immediate(...params: any[]): any;
            exclusive(...params: any[]): any;
        }
    }
}
