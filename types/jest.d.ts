
// Custom jest.d.ts to ensure Jest globals are available without external dependencies

declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveClass(className: string): R;
      toHaveTextContent(text: string): R;
      toBeVisible(): R;
      toBeDisabled(): R;
      toBeEnabled(): R;
      toHaveAttribute(attr: string, value?: string): R;
      toHaveValue(value: string | number | string[]): R;
      toBeChecked(): R;
      toHaveFocus(): R;
      toHaveDisplayValue(value: string | RegExp | (string | RegExp)[]): R;
      toHaveFormValues(expectedValues: Record<string, any>): R;
      toHaveStyle(css: string | Record<string, any>): R;
      toBeEmptyDOMElement(): R;
      toBeInvalid(): R;
      toBeRequired(): R;
      toBeValid(): R;
      toHaveDescription(description?: string | RegExp): R;
      toHaveErrorMessage(message?: string | RegExp): R;
    }

    interface MockedFunction<T extends (...args: any[]) => any> {
      (...args: Parameters<T>): ReturnType<T>;
      mockReturnValue(value: ReturnType<T>): this;
      mockReturnValueOnce(value: ReturnType<T>): this;
      mockResolvedValue(value: Awaited<ReturnType<T>>): this;
      mockResolvedValueOnce(value: Awaited<ReturnType<T>>): this;
      mockRejectedValue(value: any): this;
      mockRejectedValueOnce(value: any): this;
      mockImplementation(fn: T): this;
      mockImplementationOnce(fn: T): this;
      mockClear(): this;
      mockReset(): this;
      mockRestore(): void;
      getMockImplementation(): T | undefined;
      mock: {
        calls: Parameters<T>[];
        results: Array<{ type: 'return' | 'throw'; value: any }>;
        instances: any[];
      };
    }

    interface SpyInstance<T = any> {
      mockReturnValue(value: T): this;
      mockReturnValueOnce(value: T): this;
      mockResolvedValue(value: Awaited<T>): this;
      mockResolvedValueOnce(value: Awaited<T>): this;
      mockRejectedValue(value: any): this;
      mockRejectedValueOnce(value: any): this;
      mockImplementation(fn: (...args: any[]) => T): this;
      mockImplementationOnce(fn: (...args: any[]) => T): this;
      mockClear(): this;
      mockReset(): this;
      mockRestore(): void;
      getMockImplementation(): ((...args: any[]) => T) | undefined;
      mock: {
        calls: any[][];
        results: Array<{ type: 'return' | 'throw'; value: any }>;
        instances: any[];
      };
    }

    function fn<T extends (...args: any[]) => any>(
      implementation?: T
    ): MockedFunction<T>;
    
    function spyOn<T, M extends keyof T>(
      object: T,
      method: M
    ): SpyInstance<T[M] extends (...args: any[]) => any ? ReturnType<T[M]> : any>;

    function mock(moduleName: string, factory?: () => any, options?: { virtual?: boolean }): void;
    
    function clearAllMocks(): void;
    function resetAllMocks(): void;
    function restoreAllMocks(): void;
  }

  // Jest globals
  const describe: (name: string, fn: () => void) => void;
  const it: (name: string, fn: () => void | Promise<void>) => void;
  const test: (name: string, fn: () => void | Promise<void>) => void;
  const expect: (value: any) => jest.Matchers<any>;
  const beforeEach: (fn: () => void | Promise<void>) => void;
  const afterEach: (fn: () => void | Promise<void>) => void;
  const beforeAll: (fn: () => void | Promise<void>) => void;
  const afterAll: (fn: () => void | Promise<void>) => void;
}

export {};
    