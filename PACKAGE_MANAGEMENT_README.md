# Package Management in the Mexel Project

## Overview

The Mexel project uses Yarn 3+ with workspaces to manage packages across our monorepo structure. This document provides guidance on how to work with our package management system effectively.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Project Structure](#project-structure)
3. [Common Commands](#common-commands)
4. [Adding Dependencies](#adding-dependencies)
5. [Updating Dependencies](#updating-dependencies)
6. [Troubleshooting](#troubleshooting)
7. [Best Practices](#best-practices)

## Getting Started

### Prerequisites

- Node.js >= 18.0.0
- Yarn >= 3.0.0

### First-time Setup

If you're setting up the project for the first time or need to fix Yarn issues:

```bash
# Run the Yarn setup script
node scripts/setup-yarn.js

# Install dependencies
yarn install
```

## Project Structure

The Mexel project is organized as a monorepo with multiple workspaces:

```
Mexel/
├── package.json         # Root package.json with workspaces config
├── frontend/            # Frontend application
│   └── package.json     # Frontend-specific dependencies
└── shared/              # Shared code and utilities
    └── package.json     # Shared package dependencies
```

## Common Commands

### Daily Development

```bash
# Install all dependencies
yarn install

# Start all services
yarn start

# Build all packages
yarn build:all

# Check types across all packages
yarn check:ts:all

# Run linting
yarn lint:all
```

### Workspace Management

```bash
# List all workspaces
yarn workspaces:list

# Sync dependencies across workspaces
yarn workspaces:sync

# Validate workspace configuration
yarn workspaces:validate

# Run a command in all workspaces
yarn workspaces:run <command>

# Run workspace health checks
yarn workspaces:doctor
```

### Dependency Management

```bash
# Check for duplicate dependencies
yarn deps:check

# Fix duplicate dependencies
yarn deps:fix

# Update dependencies interactively
yarn deps:update

# Find out why a package is installed
yarn deps:why <package>

# Get information about a package
yarn deps:info <package>
```

### Maintenance

```bash
# Clean node_modules
yarn clean

# Clean everything including cache
yarn clean:all

# Fix common issues
yarn fix:all
```

## Adding Dependencies

Dependencies should be added thoughtfully, following these guidelines:

### Workspace-specific Dependencies

```bash
# Add a dependency to a specific workspace
yarn workspace <workspace-name> add <package>

# Example: Add lodash to frontend
yarn workspace frontend add lodash

# Add a dev dependency
yarn workspace frontend add --dev @types/lodash
```

### Common Dependencies

Common dependencies used across workspaces should be added to the root package.json:

```bash
# Add at the root level
yarn add -W <package>

# Add as dev dependency at root level
yarn add -W --dev <package>
```

## Updating Dependencies

```bash
# Check outdated packages
yarn outdated

# Update interactively
yarn upgrade-interactive

# Update a specific package
yarn upgrade <package>
```

## Troubleshooting

### Common Issues

#### "Cannot find module" or Unexpected Behavior

```bash
# Clean and reinstall
yarn clean
yarn install
```

#### Yarn Version Issues

```bash
# Set up Yarn properly
node scripts/setup-yarn.js
```

#### Dependency Conflicts

```bash
# Check for and fix duplicates
yarn workspaces:doctor
```

### Diagnostic Commands

```bash
# Check Node.js and Yarn versions
yarn check:versions

# View dependency tree for a package
yarn why <package>

# Check for dependency issues
yarn workspaces:doctor
```

## Best Practices

1. **Use Workspace Protocol**: Reference internal packages with `workspace:*` protocol.

2. **Centralize Common Dependencies**: Common dependencies should be in the root package.json.

3. **Use Resolutions Sparingly**: Only use the `resolutions` field in the root package.json when necessary for security or compatibility.

4. **Keep Dependencies Updated**: Regularly run `yarn outdated` and update dependencies.

5. **Version Pinning Strategy**:
   - Use caret ranges (`^`) for most dependencies
   - Use tilde ranges (`~`) for sensitive packages
   - Use exact versions for critical packages

6. **Minimize Duplicates**: Run `yarn deps:check` regularly to prevent duplicates.

7. **Document Breaking Changes**: When updating major versions, document any breaking changes.

## Additional Resources

- [Full Dependency Management Guide](./DEPENDENCY_MANAGEMENT.md)
- [Yarn Documentation](https://yarnpkg.com/getting-started)
- [Yarn Workspaces Guide](https://yarnpkg.com/features/workspaces)